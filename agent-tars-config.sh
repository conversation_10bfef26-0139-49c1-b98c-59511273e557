#!/bin/bash

# Advanced Agent TARS Startup Script with Configuration
# Usage: ./agent-tars-config.sh [mode]
# Modes: ui, server, debug

MODE=${1:-ui}
PORT=${2:-8888}

echo "🤖 Agent TARS Advanced Launcher"
echo "================================"

case $MODE in
    "ui"|"start")
        echo "🖥️  Starting Agent TARS with Interactive UI"
        echo "📍 Web UI: http://localhost:$PORT"
        echo "🌐 Auto-opening browser..."
        npx @agent-tars/cli@latest start --open --port $PORT --stream
        ;;
    
    "server"|"headless")
        echo "🔧 Starting Agent TARS Headless Server"
        echo "📍 Server: http://localhost:$PORT"
        echo "📡 API endpoints available"
        npx @agent-tars/cli@latest serve --port $PORT --quiet
        ;;
    
    "debug")
        echo "🐛 Starting Agent TARS in Debug Mode"
        echo "📍 Web UI: http://localhost:$PORT"
        echo "🔍 Debug logging enabled"
        npx @agent-tars/cli@latest start --open --port $PORT --debug --stream
        ;;
    
    "help"|"-h"|"--help")
        echo "Usage: $0 [mode] [port]"
        echo ""
        echo "Modes:"
        echo "  ui       - Interactive UI (default)"
        echo "  server   - Headless server mode"
        echo "  debug    - Debug mode with verbose logging"
        echo "  help     - Show this help"
        echo ""
        echo "Examples:"
        echo "  $0                    # Start UI on port 8888"
        echo "  $0 ui 9000           # Start UI on port 9000"
        echo "  $0 server            # Start headless server"
        echo "  $0 debug             # Start with debug logging"
        exit 0
        ;;
    
    *)
        echo "❌ Unknown mode: $MODE"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
