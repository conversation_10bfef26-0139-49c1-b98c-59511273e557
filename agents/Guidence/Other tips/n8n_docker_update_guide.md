# n8n Docker 更新指南

本指南提供了安全更新 n8n Docker 容器并保留所有数据的完整步骤。

## 前提条件检查

### 1. 确认数据卷配置

首先检查您的 n8n 容器是否正确配置了数据卷：

```bash
# 查看所有 Docker 卷
docker volume ls

# 检查 n8n_data 卷详情
docker volume inspect n8n_data
```

预期输出应包含：
- **Name**: `n8n_data`
- **Driver**: `local`
- **Mountpoint**: `/var/lib/docker/volumes/n8n_data/_data`

### 2. 验证容器卷映射

```bash
# 查看运行中的容器
docker ps

# 检查容器的卷映射（替换 container_name 为实际容器名）
docker inspect container_name --format='{{json .Mounts}}'

# 或使用 grep 查看挂载信息
docker inspect container_name | grep -A 10 "Mounts"
```

确认输出中包含：
- **Source**: `n8n_data`
- **Destination**: `/home/<USER>/.n8n`
- **Type**: `volume`

## 安全更新步骤

### 步骤 1: 数据备份（可选但推荐）

```bash
# 创建备份目录
mkdir -p ~/n8n_backup/$(date +%Y%m%d)

# 备份数据卷内容
docker run --rm -v n8n_data:/data -v ~/n8n_backup/$(date +%Y%m%d):/backup alpine tar czf /backup/n8n_data_backup.tar.gz -C /data .
```

### 步骤 2: 停止并移除当前容器

```bash
# 停止容器（替换为您的容器名）
docker stop n8n_container

# 移除容器
docker rm n8n_container
```

### 步骤 3: 拉取最新镜像

```bash
# 拉取最新版本
docker pull n8nio/n8n:latest

# 或拉取特定版本（推荐）
docker pull n8nio/n8n:1.x.x
```

### 步骤 4: 启动新容器

#### 方法 A: 使用 Docker 命令

```bash
docker run -d \
  --name n8n_container \
  -p 5678:5678 \
  -v n8n_data:/home/<USER>/.n8n \
  n8nio/n8n:latest
```

#### 方法 B: 使用 Docker Compose（推荐）

创建 `docker-compose.yml` 文件：

```yaml
version: '3.8'
services:
  n8n:
    image: n8nio/n8n:latest
    container_name: n8n_container
    ports:
      - "5678:5678"
    volumes:
      - n8n_data:/home/<USER>/.n8n
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=your_password
    restart: unless-stopped

volumes:
  n8n_data:
    external: true
```

然后启动：

```bash
docker-compose up -d
```

### 步骤 5: 验证更新

```bash
# 检查容器状态
docker ps

# 查看容器日志
docker logs n8n_container

# 验证卷映射
docker inspect n8n_container --format='{{json .Mounts}}'
```

### 步骤 6: 功能验证

1. 访问 http://localhost:5678
2. 使用原有账号登录
3. 确认所有工作流都存在
4. 检查凭据和设置是否保留
5. 测试一个简单的工作流执行

## 故障排除

### 问题 1: 容器无法启动

```bash
# 查看详细错误日志
docker logs n8n_container --details

# 检查端口占用
lsof -i :5678
```

### 问题 2: 数据丢失

```bash
# 检查数据卷是否存在
docker volume ls | grep n8n_data

# 检查数据卷内容
docker run --rm -v n8n_data:/data alpine ls -la /data
```

### 问题 3: 权限问题

```bash
# 修复数据卷权限
docker run --rm -v n8n_data:/data alpine chown -R 1000:1000 /data
```

## 数据安全保障

### ✅ 数据会保留的情况：
- 使用命名卷（如 `n8n_data`）
- 正确的卷映射路径 `/home/<USER>/.n8n`
- 更新时使用相同的卷名
- 主机目录存在且可访问（如使用绑定挂载）

### ❌ 可能导致数据丢失的情况：
- 没有使用数据卷
- 使用匿名卷
- 错误的卷映射路径
- 意外删除数据卷
- 主机目录被删除（绑定挂载）

## 最佳实践

1. **使用命名卷**: 避免使用匿名卷
2. **定期备份**: 建立定期备份机制
3. **版本控制**: 使用特定版本标签而非 `latest`
4. **Docker Compose**: 使用 Docker Compose 管理容器
5. **监控日志**: 定期检查容器日志
6. **测试环境**: 在测试环境先验证更新

## 自动化脚本

创建更新脚本 `update_n8n.sh`：

```bash
#!/bin/bash

# n8n 自动更新脚本
set -e

CONTAINER_NAME="n8n_container"
IMAGE_NAME="n8nio/n8n:latest"
VOLUME_NAME="n8n_data"

echo "开始更新 n8n..."

# 备份数据
echo "创建数据备份..."
BACKUP_DIR=~/n8n_backup/$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR
docker run --rm -v $VOLUME_NAME:/data -v $BACKUP_DIR:/backup alpine tar czf /backup/n8n_backup.tar.gz -C /data .

# 停止并移除容器
echo "停止当前容器..."
docker stop $CONTAINER_NAME || true
docker rm $CONTAINER_NAME || true

# 拉取最新镜像
echo "拉取最新镜像..."
docker pull $IMAGE_NAME

# 启动新容器
echo "启动新容器..."
docker run -d \
  --name $CONTAINER_NAME \
  -p 5678:5678 \
  -v $VOLUME_NAME:/home/<USER>/.n8n \
  $IMAGE_NAME

# 等待启动
echo "等待服务启动..."
sleep 10

# 检查状态
if docker ps | grep -q $CONTAINER_NAME; then
    echo "✅ n8n 更新成功！"
    echo "访问地址: http://localhost:5678"
else
    echo "❌ 更新失败，请检查日志:"
    docker logs $CONTAINER_NAME
fi
```

使用方法：

```bash
# 给脚本执行权限
chmod +x update_n8n.sh

# 运行更新
./update_n8n.sh
```

## 总结

通过正确配置 Docker 数据卷，您可以安全地更新 n8n 而不丢失任何数据。关键要点：

1. 确保使用命名卷存储数据
2. 验证卷映射配置正确
3. 更新时保持相同的卷配置
4. 定期备份重要数据
5. 在生产环境更新前先在测试环境验证

遵循本指南，您可以放心地保持 n8n 版本更新，同时确保数据安全。