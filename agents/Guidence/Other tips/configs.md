* [X] *****302ai http request***
  **

**Get**

**URL:https://api.302.ai/searchapi/search**

Query:

api_key:{{ $json["302ai api key"] }}

engine:google

q:{{ $json["主关键词"] }}

num:10

* [X] *****jina ai http request***

Get

url:https://r.jina.ai/{{ $json.link }}

headers:

Authorization:{{ $('设置参数-综合').item.json['jina api key'] }}

X-Return_Format:html

X-No-Cache:true

* [X] *****set节点***

```
// Get industry keyword from chat input
const chatInput = $input.item.json.chatInput || "";// Clean and normalize the keyword
const rawIndustry = chatInput.trim();// Basic input validation
if (!rawIndustry) {
  throw new Error("Please provide a valid industry keyword");
}// Sanitize input: remove special characters and normalize spaces
const industry = rawIndustry
  .replace(/[^\w\s\u4e00-\u9fff]/g, '') // Keep only word chars, spaces, and Chinese characters
  .replace(/\s+/g, ' ') // Normalize spaces
  .trim();// Validate sanitized input
if (industry.length < 2) {
  throw new Error("Industry keyword must be at least 2 characters long");
}// Generate comprehensive search variants
const searchVariants = [
  // Market analysis variants
  ${industry} 市场规模,
  ${industry} 行业分析报告,
  ${industry} 产业分析,
  ${industry} 市场分析,  // Competition analysis variants
  ${industry} 竞争格局,
  ${industry} 竞争分析,
  ${industry} 主要企业,
  ${industry} 市场份额,  // Development trend variants
  ${industry} 发展趋势,
  ${industry} 未来展望,
  ${industry} 前景分析,
  ${industry} 最新动态,  // Data and statistics variants
  ${industry} 最新数据,
  ${industry} 统计数据,
  ${industry} 行业数据,
  ${industry} 市场容量
];// Add English variants if the input contains English characters
if (/[a-zA-Z]/.test(industry)) {
  const englishVariants = [
    ${industry} market size,
    ${industry} industry analysis,
    ${industry} market analysis,
    ${industry} competitive landscape,
    ${industry} market share,
    ${industry} industry trends,
    ${industry} future outlook,
    ${industry} statistics,
    ${industry} market data
  ];
  searchVariants.push(...englishVariants);
}// Return the processed data
return [{
  json: {
    industry: industry,
    searchVariants: searchVariants,
    timestamp: new Date().toISOString(),
    metadata: {
      originalInput: rawIndustry,
      sanitized: industry !== rawIndustry,
      hasEnglish: /[a-zA-Z]/.test(industry),
      variantCount: searchVariants.length
    }
  }
}];
```


* [X] ******HTTP_Request_URLs***

```
/**搜索结果处理与URL提取用于生成HTTP请求的URL列表
*/// 获取行业关键词和搜索变体
const industry = $node["Set"].json.industry || "";
const searchVariants = $node["Set"].json.searchVariants || [];// 构建搜索URL列表
const buildSearchUrls = () => {
  // 使用搜索变体构建URL
  // 这里使用公开搜索引擎，实际使用时可替换为API
  const urls = searchVariants.slice(0, 5).map(query => {
    const encodedQuery = encodeURIComponent(query);
    return https://www.bing.com/search?q=${encodedQuery};
  });  return urls;
};// 构建行业特定URL
const buildIndustrySpecificUrls = () => {
  const industryType = $node["Set"].json.industryType || "general";
  const specificUrls = [];  // 根据行业类型添加特定网站
  switch (industryType) {
    case "tech":
      specificUrls.push(
        https://www.iresearch.com.cn/search?key=${encodeURIComponent(industry)},
        https://www.cbinsights.com/search?q=${encodeURIComponent(industry)}
      );
      break;
    case "finance":
      specificUrls.push(
        https://www.eastmoney.com/search.html?q=${encodeURIComponent(industry)},
        https://www.bloomberg.com/search?query=${encodeURIComponent(industry)}
      );
      break;
    case "health":
      specificUrls.push(
        https://www.yaozh.com/search/?keyword=${encodeURIComponent(industry)},
        https://www.ncbi.nlm.nih.gov/search/all/?term=${encodeURIComponent(industry)}
      );
      break;
    case "consumer":
      specificUrls.push(
        https://www.199it.com/?s=${encodeURIComponent(industry)},
        https://www.nielsen.com/search/?q=${encodeURIComponent(industry)}
      );
      break;
    case "energy":
      specificUrls.push(
        https://www.in-en.com/search.php?q=${encodeURIComponent(industry)},
        https://www.iea.org/search?q=${encodeURIComponent(industry)}
      );
      break;
    default:
      specificUrls.push(
        https://www.qianzhan.com/search/t_${encodeURIComponent(industry)}/,
        https://www.chyxx.com/search/${encodeURIComponent(industry)}.html
      );
  }  return specificUrls;
};// 合并URL列表
const searchUrls = buildSearchUrls();
const specificUrls = buildIndustrySpecificUrls();
const allUrls = [...searchUrls, ...specificUrls];// 选择前10个URL进行抓取
const urls = allUrls.slice(0, 10);// 返回URL列表
return [{
  json: {
    industry,
    urls,
    urlCount: urls.length,
    timestamp: new Date().toISOString()
  }
}];
```

* [X] *****http request***

Get

url:{{$node["HTTP_Request_URLs"].json.urls[2]}}

follow redirects

max redirects: 21

* [X] *****News API***

Get

url:https://newsapi.org/v2/everything

query auth

query

q:{{$json["industry"]}}

language:zh

sortBy:relevancy

pageSize:10

apikey:{{$credentials.newsApiKey}}

* [X] *****if***

{{ JSON.stringify($node["TavilySearch"].json).length }}

* [X] *****tavily http request***

post

url:https://api.tavily.com/extract

Bearer auth: bearer Tavily_apikey

headers

Content-Type:application/json

body

using json

```
{{
  JSON.stringify({
    urls: $json.searchResults.map(item => item.url).slice(0, 5),
    max_tokens: 10000
  })
}}
```

* [X] **brave

Get

https://api.search.brave.com/res/v1/web/search

header auth

query

q:{{$json["industry"]}} {{$json["searchVariants"][0]}}

count:10

result_filter:web

headers

X-Subscription-Token:BSAm8FXK7HLLCAdaRaas1tePbR9pDF5

Accept:application/json

search_lang:zh-CN

* [X] *****http request : tavily***

Post

url:https://api.tavily.com/extract

bearer auth: bearer Tavily_apikey

headers

Content-Type:application/json

body

using json

```
{{
  JSON.stringify({
    urls: $json.searchResults.map(item => item.url).slice(0, 5),
    max_tokens: 10000
  })
}}
```

* [X] *****tavily search***

query

Query: `{{$json.industry}} 行业分析 市场数据 {{$json.timeRange}}`

search_depth: advanced

Max Results: 10

Include Raw Content: true

include images: true

* 基础信息搜索

"include_domains":

```
 ["gov.cn","stats.gov.cn","ndrc.gov.cn","miit.gov.cn","industry-reports.com","iresearch.cn","analysys.cn"      ]
```

"exclude_domains":

```
["facebook.com","twitter.com","instagram.com","tiktok.com"]
```

* 市场数据搜索

"include_domains":

```
["iresearch.cn","analysys.cn","statista.com","mckinsey.com","bcg.com","pwc.com","deloitte.com","kpmg.com","frost.com","gartner.com"    ]
```

* 趋势分析搜索

```
["mckinsey.com","bcg.com","accenture.com","mit.edu","harvard.edu","weforum.org","techcrunch.com","36kr.com","huxiu.com"    ]
```

* 竞争格局搜索

```
["bloomberg.com","reuters.com","wsj.com","ft.com","forbes.com","fortune.com","caixin.com","21jingji.com","eastmoney.com","hexun.com"    ]
```

* 技术创新搜索

```
["ieee.org","nature.com","science.org","arxiv.org","techcrunch.com","venturebeat.com","wired.com","36kr.com"    ]
```

* 投资融资搜索

```
["bloomberg.com","reuters.com","wsj.com","crunchbase.com","pitchbook.com","cbinsights.com","itjuzi.com","pedaily.cn"    ]
```

* 政策环境搜索

```
["gov.cn","ndrc.gov.cn","miit.gov.cn","mof.gov.cn","pboc.gov.cn","csrc.gov.cn","samr.gov.cn"    ]
```

* [X] *****binaryfile***

```
// 获取HTML报告内容
const htmlContent = $node["html_report"].json.html;  // 注意这里的节点名称和属性名
const industry = $node["Set"].json.industry || "行业";// 生成文件名
const fileName = ${industry}_行业分析报告_${new Date().toISOString().split('T')[0]}.html;// 创建二进制项
const item = {
  json: {
    filename: fileName
  },
  binary: {
    data: {
      data: Buffer.from(htmlContent).toString('base64'),
      mimeType: 'text/html',
      fileName: fileName
    }
  }
};return [item];
```
