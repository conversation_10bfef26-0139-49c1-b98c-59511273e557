## 一、最佳行业报告排版结构总结

1. 封面与元信息

* 报告标题、行业、地区、时间、生成日期、摘要、作者/机构等。
* 居中大标题，次要信息小字排列，风格简洁大气。

1. 目录导航

* 固定在顶部或左侧，支持锚点跳转。
* 目录项与实际章节一一对应，图表独立成章时也要在目录中体现。

1. 章节结构

* 每章有大标题（如“市场概述”、“竞争格局”），下设小节（如“市场规模”、“主要企业”）。
* 章节内容与图表并排或穿插，而不是简单上下堆叠。
* 章节内容可采用卡片式（如 SWOT、要点、结论）、分栏、或传统段落排版，视内容类型灵活切换。

1. 图表与文字分析的融合

* 强关联型图表（如“市场规模趋势”图）应直接嵌入对应分析段落旁边或下方，形成“图+文”并列或穿插。
* 独立型图表（如“行业结构图”、“综合对比图”）可单独成章，章节名即为图表名，配以简要解读。
* 图表下方建议有“图表解读”或“要点说明”小段，帮助读者理解数据含义。

1. 数据来源与方法论

* 单独成章，卡片式或分栏展示主要数据源、研究方法、局限性、免责声明等。

1. 整体美观性

* 主色调统一，卡片、阴影、圆角、留白充足。
* 响应式设计，兼容移动端和打印。
* 图表采用 ECharts/Chart.js，色彩区分明显，支持交互。

---

## 二、图表与文字分析融合的最佳实践

* 每个章节内，优先查找与本章内容强相关的图表（通过 key/title 智能匹配），将图表与分析文字并排（如左右两栏）或穿插（如“市场规模”段落下直接插入“市场规模趋势”图）。
* 图表下方增加“图表解读”，自动生成或由 LLM 输出，帮助读者理解数据背后的含义。
* 独立型图表自动成章，如“行业结构图”、“SWOT矩阵”等，单独作为章节，目录中有锚点，正文有标题、图表和解读。
* 章节内容较多时，图表可浮动在右侧或下方，保持页面美观和信息聚合。

---

## 三、代码实现建议（伪代码/结构）

js

Apply to 车海洋战略及行业分析报告...

**// 1. 章节渲染**

**for** **(**const** **section** **of** **sections**)** **{**

**  **// 查找与本章节相关的图表

**  **const** **relatedCharts** **=** **findRelatedCharts**(**section**.**title**, **chartData**)**;

**  **renderSection**(**{

**    **title:** **section**.**title**,**

**    **content:** **section**.**content**,**

**    **charts:** **relatedCharts**.**map**(**chart** **=>** **(**{**

**      **html:** **renderChart**(**chart**)**,

**      **interpretation:** **chart**.**interpretation** **// 图表解读

**    **}**)**)

**  **}**)**;

**}**

**// 2. 独立型图表成章**

**for** **(**const** **chart** **of** **chartData**)** **{**

**  **if** **(**!chart**.**isRelatedToAnySection**)** **{

**    **renderSection**(**{

**      **title:** **chart**.**title**,**

**      **content:** **chart**.**interpretation**,**

**      **charts:** **[**renderChart**(**chart**)**]**

**    **}**)**;

**  **}

**}**

**// 3. 目录自动生成，含所有章节和独立图表章节**

**renderTOC**(**[**...**sections**, **...**independentChartSections**]**)**;**

---

## 四、n8n节点代码优化要点

* 章节与图表智能匹配：通过章节名与图表 key/title 的模糊匹配，将相关图表插入对应章节。
* 图表与文字并排/穿插：采用 grid 或 flex 布局，左侧为分析文字，右侧为图表及解读。
* 独立型图表自动成章：未被章节引用的图表，自动生成独立章节，目录中有锚点。
* 图表解读：可由 LLM 输出或自动生成“图表解读”字段，渲染在图表下方。
* 整体美观性：主色调、卡片、阴影、圆角、响应式，参考 sample report 的 CSS。

---

## 五、内容生成与 LLM 提示词建议

* 每个图表数据结构中增加“interpretation”字段，由 LLM 输出简要解读。
* 章节内容输出时，要求 LLM 明确分段，便于代码节点自动插入图表和解读。
* 章节与图表命名规范化，便于自动匹配和融合。

---

## 六、结论

* 行业报告的最佳排版是“章节+图表+解读”三位一体，图表与分析文字紧密融合，独立型图表单独成章。
* 代码节点应实现智能匹配与融合，目录、排版、图表、解读全部自动化。
* 内容生成时，LLM 提示词需明确要求输出图表解读和章节分段。

---

如需完整的 n8n 代码节点实现（含上述所有优化），请确认你希望的章节与图表数据结构（如 interpretation 字段），我可直接输出可用代码。

temp.json

Sample reports

需要完整的代码

Restore checkpoint

6_node(HTML_ConsultingReport).js+96-111

**// 6_node(HTML_ConsultingReport)**

**// 作用：根据上游节点的结构化行业报告JSON，生成专业、图文并茂、可视化强的咨询报告HTML**

**// 输入：上游节点输出的行业报告JSON**

**// 输出：完整HTML字符串**

**const** **report** **=** **$json**.**output** **||** **$json**;

**// 目录导航**

**// **6_node(HTML_ConsultingReport)

**// 作用：根据上游节点的结构化行业报告JSON，生成专业、图文并茂、可视化强的咨询报告HTML**

**// 输入：上游节点输出的**行业报告**JSON**

**// 输出：完整**HTML**字符串**

**// n8n代码节点：行业报告HTML生成（最佳排版+图表融合+解读）**

**const** **report** **=** **$json**.**output** **||** **$json**;

**const** **chartData** **=** **report**[**"图表数据"**]** **||** **{**}**;

**const** **allChapters** **=** **(**report**[**"目录"**]** **||** **Object**.**keys**(**report**)**)**.**filter**(**chap** **=>** **chap** **!==** **"目录"** **&&** **chap** **!==** **"图表数据"** **&&** **chap** **!==** **"数据来源"** **&&** **chap** **!==** **"方法论"**)**;**

**// --------- 工具函数 ---------**

**// 目录导航**

已为你重写并优化了 n8n 行业报告 HTML 生成节点，代码已自动应用到 行业报告生成器/enhanced_n8n_workflow/6_node(HTML_ConsultingReport).js 文件。此版本具备如下特性：

1. 章节与图表智能融合：相关图表与分析文字并排展示，未被章节引用的图表自动独立成章，且每个图表支持 interpretation 字段（图表解读）。
2. 目录锚点自动生成：所有章节和独立图表章节均可跳转，目录自动适配。
3. 卡片式与常规排版混合：要点、短内容自动卡片化，长文常规排版，整体美观、专业、响应式。
4. 封面、数据来源、方法论：自动生成，风格统一。
5. 代码结构清晰，注释完善，可直接用于 n8n 代码节点。

你只需保证 LLM 输出的 JSON 结构中，图表数据含 interpretation 字段（如有），即可实现“图+文+解读”三位一体的最佳行业报告排版。

如需进一步自定义章节顺序、样式细节或 LLM 提示词优化，欢迎随时提出！
