# n8n Setup and Management Guide

## 🚀 Quick Start (Easiest Way)

**One-time setup for easy future use:**
```bash
# to stop the running process
pkill -f n8n

# Move n8n data to home directory (avoids path issues)
mv "n8n_data" ~/n8n_data

# Make environment variable permanent
echo 'export N8N_USER_FOLDER=~/n8n_data' >> ~/.zshrc
source ~/.zshrc
```

**Then simply run n8n anytime:**
```bash
n8n start
```

**That's it!** Open http://localhost:5678 in your browser.



**Only do this once:**

1. **Install n8n:**
   ```bash
   sudo npm install -g n8n
   ```

2. **Setup data folder:**
   ```bash
   mkdir ~/n8n_data
   echo 'export N8N_USER_FOLDER=~/n8n_data' >> ~/.zshrc
   source ~/.zshrc
   ```

3. **Start n8n:**
   ```bash
   n8n start
   ```

4. **Open browser:** http://localhost:5678

### Data Storage
- All workflow data is stored in: `./n8n_data/`
- Database file: `./n8n_data/.n8n/database.sqlite`
- Binary data: `./n8n_data/.n8n/binaryData/`

## Updating n8n

### Check Current Version
```bash
n8n --version
```

### Update to Latest Version
```bash
# Stop n8n if running (Ctrl+C)
# Update globally
sudo npm update -g n8n

# Verify new version
n8n --version

# Restart n8n
export N8N_USER_FOLDER=$(pwd)/n8n_data && n8n start
```

### Update to Specific Version
```bash
# Stop n8n if running
# Install specific version
sudo npm install -g n8n@1.99.0

# Restart n8n
export N8N_USER_FOLDER=$(pwd)/n8n_data && n8n start
```

## 🎯 Daily Use

**Start n8n:**
```bash
n8n start
```

**Stop n8n:**
- Press `Ctrl+C` in terminal

**Access n8n:**
- Open: http://localhost:5678

### Make Environment Variable Permanent (Recommended)

Add to your shell profile (`~/.zshrc` for zsh or `~/.bash_profile` for bash):
```bash
# Recommended: Use path without spaces
echo 'export N8N_USER_FOLDER=~/n8n_data' >> ~/.zshrc
source ~/.zshrc
```

Then you can simply run:
```bash
n8n start
```

**Note:** If you have existing data in a path with spaces, move it first:
```bash
# Stop n8n if running (Ctrl+C)
mv "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub/agents/n8n_data" ~/n8n_data
```

## 🔧 Common Issues

**Can't install community packages?**

If you get errors like `tar: could not chdir to '/Users/<USER>/Library/Mobile'`:

```bash
# Move n8n data to avoid spaces in path
mv "n8n_data" ~/n8n_data
echo 'export N8N_USER_FOLDER=~/n8n_data' >> ~/.zshrc
source ~/.zshrc
```

**Port already in use?**
```bash
# Use different port
N8N_PORT=5679 n8n start
```

### Database Issues
If you encounter database problems:
1. Stop n8n
2. Backup your data: `cp -r n8n_data n8n_data_backup`
3. Delete database: `rm n8n_data/.n8n/database.sqlite`
4. Restart n8n (will recreate database)

### Community Package Installation Issues

#### ⚠️ IMPORTANT: Path with Spaces Error (Most Common Issue)
If you encounter an error like:
```
tar: could not chdir to '/Users/<USER>/Library/Mobile'
Command failed: tar -xzf package-name.tgz -C /path/with/spaces
```

**This is the most common issue when installing community packages.** It happens because your `n8n_data` folder is in a path containing spaces (like "Mobile Documents"). The `tar` command used by n8n's package installer cannot handle spaces in paths properly.

**RECOMMENDED SOLUTION - Move n8n_data:**
```bash
# 1. Stop n8n if running (Ctrl+C in terminal)

# 2. Move your data to a path without spaces
mv "n8n_data" ~/n8n_data

# 3. Update your environment variable permanently
echo 'export N8N_USER_FOLDER=~/n8n_data' >> ~/.zshrc
source ~/.zshrc

# 4. Start n8n
n8n start
```

**Alternative Solutions:**

1. **Use a symlink:**
   ```bash
   ln -s "/full/path/to/current/n8n_data" ~/n8n_data_link
   export N8N_USER_FOLDER=~/n8n_data_link
   ```

2. **Install the community node manually:**
   ```bash
   cd n8n_data/.n8n/nodes
   npm install n8n-nodes-mcp
   ```

## 🔄 How to Start n8n Next Time

**If you followed the recommended setup (data in ~/n8n_data):**
```bash
# Simply start n8n
n8n start
```

**If you encounter "port already in use" error:**
```bash
# 1. Find existing n8n process
ps aux | grep n8n

# 2. Stop existing process (replace <PID> with actual process ID)
kill <PID>

# 3. Start n8n again
n8n start
```

**Verify your setup:**
```bash
# Check environment variable
echo $N8N_USER_FOLDER
# Should show: /Users/<USER>/n8n_data

# Access n8n
# Open: http://localhost:5678
```

**Update n8n:**
```bash
sudo npm update -g n8n
```

## Useful Configuration

### Environment Variables
```bash
# Custom port
export N8N_PORT=5679

# Enable task runners (recommended)
export N8N_RUNNERS_ENABLED=true

# Custom data folder
export N8N_USER_FOLDER=/path/to/your/data
```

### Security (Production)
```bash
# Set basic auth
export N8N_BASIC_AUTH_ACTIVE=true
export N8N_BASIC_AUTH_USER=admin
export N8N_BASIC_AUTH_PASSWORD=your_password
```

## Backup and Restore

### Backup
```bash
# Stop n8n first
# Create backup
tar -czf n8n_backup_$(date +%Y%m%d).tar.gz n8n_data/
```

### Restore
```bash
# Stop n8n
# Extract backup
tar -xzf n8n_backup_YYYYMMDD.tar.gz
# Restart n8n
```

---

**That's all you need!** The guide above covers 99% of what you'll use. For advanced features, check the [official n8n documentation](https://docs.n8n.io/).

**Last Updated**: $(date)
**n8n Version**: 1.99.1
**Installation Date**: $(date)

if need to reboot n8n in docker with a named container and volume:

docker run -it --rm --name n8n_container -v /Users/<USER>/docker_n8n_data:/home/<USER>/.n8n -p 5678:5678 n8nio/n8n