{"provider": "deepseek", "request": {"model": "deepseek-chat", "messages": [{"role": "system", "content": "\nYou are Agent TARS, a multimodal AI agent created by the ByteDance.\n\n<intro>\nYou excel at the following tasks:\n1. Information gathering, fact-checking, and documentation\n2. Data processing, analysis, and visualization\n3. Writing multi-chapter articles and in-depth research reports\n4. Creating websites, applications, and tools\n5. Using programming to solve various problems beyond development\n6. Various tasks that can be accomplished using computers and the internet\n</intro>\n\n<language_settings>\nUse the language specified by user in messages as the working language when explicitly provided\nAll thinking and responses must be in the working language\nNatural language arguments in tool calls must be in the working language\nAvoid using pure lists and bullet points format in any language\n</language_settings>\n\n<multimodal_understanding>\nWhen processing images, it's crucial to understand the difference between image types:\n1. Browser Screenshots: These are images showing the browser interface that you can interact with using browser tools\n   - Appear as part of the browser_vision_control tool output or environment input labeled as \"Browser Screenshot\"\n   - ONLY these screenshots represent interfaces you can operate on with browser tools\n   - Use these for navigation, clicking elements, scrolling, and other browser interactions\n\n2. User-Uploaded Images: These are regular images the user has shared but are NOT browser interfaces\n   - May include photos, diagrams, charts, documents, or any other visual content\n   - Cannot be operated on with browser tools - don't try to click elements in these images\n   - Should be analyzed for information only (objects, text, context, meaning)\n   - Respond to user questions about these images with observations and analysis\n\nDistinguish between these types by context and environment input descriptions to avoid confusion.\nWhen you see a new image, first determine which type it is before deciding how to interact with it.\n</multimodal_understanding>\n\n<system_capability>\nSystem capabilities:\n- Communicate with users through message tools\n- Access a Linux sandbox environment with internet connection\n- Use shell, text editor, browser, and other software\n- Write and run code in Python and various programming languages\n- Independently install required software packages and dependencies via shell\n- Deploy websites or applications and provide public access\n- Suggest users to temporarily take control of the browser for sensitive operations when necessary\n- Utilize various tools to complete user-assigned tasks step by step\n</system_capability>\n\n<agent_loop>\nYou operate in an agent loop, iteratively completing tasks through these steps:\n1. Analyze Events: Understand user needs and current state through event stream, focusing on latest user messages and execution results\n2. Select Tools: Choose next tool call based on current state, task planning, relevant knowledge and available data APIs\n3. Wait for Execution: Selected tool action will be executed by sandbox environment with new observations added to event stream\n4. Iterate: Choose only one tool call per iteration, patiently repeat above steps until task completion\n5. Submit Results: Send results to user via message tools, providing deliverables and related files as message attachments\n6. Enter Standby: Enter idle state when all tasks are completed or user explicitly requests to stop, and wait for new tasks\n</agent_loop>\n\n<file_rules>\n- Use file tools for reading, writing, appending, and editing to avoid string escape issues in shell commands\n- Actively save intermediate results and store different types of reference information in separate files\n- When merging text files, must use append mode of file writing tool to concatenate content to target file\n- Strictly follow requirements in <writing_rules>, and avoid using list formats in any files except todo.md\n</file_rules>\n\n<shell_rules>\n- Avoid commands requiring confirmation; actively use -y or -f flags for automatic confirmation\n- Avoid commands with excessive output; save to files when necessary\n- Chain multiple commands with && operator to minimize interruptions\n- Use pipe operator to pass command outputs, simplifying operations\n- Use non-interactive `bc` for simple calculations, Python for complex math; never calculate mentally\n- Use `uptime` command when users explicitly request sandbox status check or wake-up\n</shell_rules>\n\n\n\n\n<planning_approach>\nYou are a methodical agent that follows a plan-and-solve approach for complex tasks. When handling tasks:\n\n1. Analyze if the task requires a multi-step plan:\n   - For complex research, analysis, or multi-part tasks → Create a plan\n   - For simple questions or tasks → Skip planning and answer directly\n\n2. If a plan is needed:\n   - Create a clear, step-by-step plan with specific goals\n   - Execute each step in order, using appropriate tools\n   - Update the plan as you learn new information\n   - Mark steps as completed when done\n   - Once ALL steps are complete, call the \"final_answer\" tool\n\n3. During execution:\n   - Adapt your plan as needed based on new findings\n   - Be willing to simplify the plan if the task turns out simpler than expected\n   - Always complete your plan before providing final answers\n</planning_approach>\n\n<planning_constraints>\nIMPORTANT CONSTRAINTS:\n- Create AT MOST 3 key steps in your plan\n- Focus on information gathering and research steps\n- Call the \"final_answer\" tool once ALL plan steps are complete\n- For simple questions, you can skip planning entirely\n</planning_constraints>\n \n\n \n<browser_rules>\nYou have access to various browser tools to interact with web pages and extract information.\n\nYou have DOM-based browser control tools that work directly with the page structure:\n\n- Navigation: `browser_navigate`, `browser_back`, `browser_forward`, `browser_refresh`\n- Interaction: `browser_click`, `browser_type`, `browser_press`, `browser_hover`, `browser_drag`, `browser_scroll`\n- Content extraction: `browser_get_markdown`\n- Status checking: `browser_get_url`, `browser_get_title`, `browser_get_elements`\n- Tab management: `browser_tab_list`, `browser_new_tab`, `browser_close_tab`, `browser_switch_tab`\n\nUSAGE GUIDELINES:\n- Use CSS selectors or element indices to precisely target elements\n- Extract content with `browser_get_markdown` for efficient analysis\n- Find and verify elements with `browser_get_elements` before interacting\n- Leverage browser state tools to keep track of navigation\n\n- Must use browser tools to access and comprehend all URLs provided by users in messages\n- Must use browser tools to access URLs from search tool results\n- Actively explore valuable links for deeper information, either by clicking elements or accessing URLs directly\n- Browser tools only return elements in visible viewport by default\n- Due to technical limitations, not all interactive elements may be identified; use coordinates to interact with unlisted elements\n- Browser tools automatically attempt to extract page content, providing it in Markdown format if successful\n- Extracted Markdown includes text beyond viewport but omits links and images; completeness not guaranteed\n- If extracted Markdown is complete and sufficient for the task, no scrolling is needed; otherwise, must actively scroll to view the entire page\n- Use message tools to suggest user to take over the browser for sensitive operations or actions with side effects when necessary\n</browser_rules>\n\n<envirnoment>\nCurrent Working Directory: /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub\n</envirnoment>\n\n    \n\nCurrent time: 5/20/2025, 10:00:00 AM"}, {"role": "user", "content": "帮我查查下周北京的天气情况"}, {"role": "system", "content": "Current plan status:\n1. [TODO] Access a reliable weather forecasting website or API to gather weather data for Beijing for the upcoming week.\n2. [TODO] Extract and organize the weather information, including temperature ranges, precipitation chances, and any significant weather warnings or alerts.\n3. [TODO] Verify the accuracy of the gathered weather data by cross-referencing with another reputable weather source.\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_2b28ca6c-02d3-4959-8d48-f3c7ef3b984b", "type": "function", "function": {"name": "web_search", "arguments": "{\"query\":\"北京下周天气预报\",\"count\":5}"}}]}, {"role": "tool", "tool_call_id": "call_0_2b28ca6c-02d3-4959-8d48-f3c7ef3b984b", "content": "{\n  \"error\": \"Search failed: Protocol error (Target.closeTarget): No target with given id found\"\n}"}, {"role": "system", "content": "Current plan status:\n1. [DONE] Access a reliable weather forecasting website or API to gather weather data for Beijing for the upcoming week.\n2. [DONE] Extract and organize the weather information, including temperature ranges, precipitation chances, and any significant weather warnings or alerts.\n3. [DONE] Verify the accuracy of the gathered weather data by cross-referencing with another reputable weather source.\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_520da4ea-49ed-4dca-af63-014fc6e3fe9d", "type": "function", "function": {"name": "web_search", "arguments": "{\"query\":\"北京下周天气\",\"count\":5}"}}]}, {"role": "tool", "tool_call_id": "call_0_520da4ea-49ed-4dca-af63-014fc6e3fe9d", "content": "{\n  \"error\": \"Search failed: Protocol error (Target.closeTarget): No target with given id found\"\n}"}, {"role": "system", "content": "Current plan status:\n\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "看来搜索工具暂时无法使用。我可以直接访问天气预报网站来获取北京下周的天气信息。请稍等，我将为您查找。", "tool_calls": [{"id": "call_0_d95f8b0d-a466-4a6f-ba9a-ad4ea0ee90c5", "type": "function", "function": {"name": "browser_navigate", "arguments": "{\"url\":\"https://www.weather.com/weather/tenday/l/Beijing+Beijing\"}"}}]}, {"role": "tool", "tool_call_id": "call_0_d95f8b0d-a466-4a6f-ba9a-ad4ea0ee90c5", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \"Navigated to https://www.weather.com/weather/tenday/l/Beijing+Beijing\\nclickable elements(Might be outdated, if an error occurs with the index element, use browser_get_clickable_elements to refresh it): [0]<a></a>\\n[]Type at least three characters to start auto complete. Recently searched locations will be displayed if there is no search query. The first option will be automatically selected. Use up and down arrows to change selection. Use escape to clear.\\n[]Search City or Zip Code\\n[1]<input></input>\\n[2]<button>US\\n°F</button>\\n[3]<button>Menu</button>\\n[4]<a></a>\\n[5]<a></a>\\n[6]<a></a>\\n[7]<a></a>\\n[8]<a></a>\\n[9]<a></a>\\n[10]<a></a>\\n[11]<a></a>\\n[12]<button></button>\\n[]Oops! We are having trouble finding your page.\\n[]Please try again... or Check out some of our favorite areas of the site.\\n[13]<a>Video\\nWatch today's top stories and most popular videos. Looking to watch incredible tornadoes? We've got that too.</a>\\n[14]<img></img>\\n[15]<a>Maps\\nCheck out our interactive maps, which provide you with features like past/future radar and customizable layers.</a>\\n[16]<img></img>\\n[17]<a>Local Weather\\nTrust us to help you plan the best day possible, with the most accurate weather forecast available.</a>\\n[18]<img></img>\\n[19]<a></a>\\n[20]<img></img>\\n[21]<a></a>\\n[22]<img></img>\\n[23]<a></a>\\n[24]<img></img>\\n[25]<a></a>\\n[26]<img></img>\\n[]Feedback\\n[]Mission\\n[]Careers\\n[]News Room\\n[]Advertise With Us\\n[]TV\\n[]Weather Data APIs\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n1. [TODO] Unknown step\n2. [TODO] Unknown step\n3. [TODO] Unknown step\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_b35971c6-7fc5-4563-b53f-108945382048", "type": "function", "function": {"name": "browser_get_markdown", "arguments": "{}"}}]}, {"role": "tool", "tool_call_id": "call_0_b35971c6-7fc5-4563-b53f-108945382048", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \" 404 Not Found          \\n\\n[Skip to Main Content](#MainContent)[Accessibility Help](https://www.essentialaccessibility.com/the-weather-channel?utm_source=theweatherchannelhomepage&utm_medium=iconlarge&utm_term=eachannelpage&utm_content=header&utm_campaign=theweatherchannel) [The Weather Channel](/)\\n\\nType at least three characters to start auto complete. Recently searched locations will be displayed if there is no search query. The first option will be automatically selected. Use up and down arrows to change selection. Use escape to clear.Search City or Zip Code\\n\\n## recents\\n\\nClear All\\n\\nYou have no recent locations\\n\\nGlobe US°FArrow down\\n\\n*   °F\\n*   °C\\n*   Hybrid\\n\\nImperial - F / mph / miles / inches\\n\\nAmericas Add\\n\\n[Antigua and Barbuda | English](/en-AG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Argentina | Español](/es-AR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Bahamas | English](/en-BS/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Barbados | English](/en-BB/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Belize | English](/en-BZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Bolivia | Español](/es-BO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Brazil | Português](/pt-BR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Canada | English](/en-CA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Canada | Français](/fr-CA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Chile | Español](/es-CL/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Colombia | Español](/es-CO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Costa Rica | Español](/es-CR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Dominica | English](/en-DM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Dominican Republic | Español](/es-DO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Ecuador | Español](/es-EC/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[El Salvador | Español](/es-SV/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Grenada | English](/en-GD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Guatemala | Español](/es-GT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Guyana | English](/en-GY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Haiti | Français](/fr-HT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Honduras | Español](/es-HN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Jamaica | English](/en-JM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mexico | Español](/es-MX/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Nicaragua | Español](/es-NI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Panama | Español](/es-PA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Panama | English](/en-PA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Paraguay | Español](/es-PY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Peru | Español](/es-PE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[St. Kitts and Nevis | English](/en-KN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[St. Lucia | English](/en-LC/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[St. Vincent and the Grenadines | English](/en-VC/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Suriname | Nederlands](/nl-SR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Trinidad and Tobago | English](/en-TT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Uruguay | Español](/es-UY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[United States | English](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[United States | Español](/es-US/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Venezuela | Español](/es-VE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nAfrica Add\\n\\n[Algeria | العربية](/ar-DZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Algeria | Français](/fr-DZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Angola | Português](/pt-AO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Benin | Français](/fr-BJ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Burkina Faso | Français](/fr-BF/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Burundi | Français](/fr-BI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cameroon | Français](/fr-CM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cameroon | English](/en-CM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cape Verde | Português](/pt-CV/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Central African Republic | Français](/fr-CF/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Chad | Français](/fr-TD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Chad | العربية](/ar-TD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Comoros | Français](/fr-KM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Comoros | العربية](/ar-KM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Democratic Republic of the Congo | Français](/fr-CD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Republic of Congo | Français](/fr-CG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Côte d'Ivoire | Français](/fr-CI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Djibouti | Français](/fr-DJ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Djibouti | العربية](/ar-DJ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Egypt | العربية](/ar-EG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Equatorial Guinea | Español](/es-GQ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Eritrea | العربية](/ar-ER/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Gabon | Français](/fr-GA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Gambia | English](/en-GM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Ghana | English](/en-GH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Guinea | Français](/fr-GN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Guinea-Bissau | Português](/pt-GW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Kenya | English](/en-KE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Lesotho | English](/en-LS/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Liberia | English](/en-LR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Libya | العربية](/ar-LY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Madagascar | Français](/fr-MG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mali | Français](/fr-ML/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mauritania | العربية](/ar-MR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mauritius | English](/en-MU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mauritius | Français](/fr-MU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Morocco | العربية](/ar-MA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Morocco | Français](/fr-MA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mozambique | Português](/pt-MZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Namibia | English](/en-NA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Niger | Français](/fr-NE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Nigeria | English](/en-NG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Rwanda | Français](/fr-RW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Rwanda | English](/en-RW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Sao Tome and Principe | Português](/pt-ST/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Senegal | Français](/fr-SN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Sierra Leone | English](/en-SL/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Somalia | العربية](/ar-SO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[South Africa | English](/en-ZA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[South Sudan | English](/en-SS/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Sudan | العربية](/ar-SD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Swaziland | English](/en-SZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Tanzania | English](/en-TZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Togo | Français](/fr-TG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Tunisia | العربية](/ar-TN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Uganda | English](/en-UG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nAsia Pacific Add\\n\\n[Australia | English](/en-AU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Bangladesh | বাংলা](/bn-BD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Brunei | Bahasa Melayu](/ms-BN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[China | 中文](/zh-CN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Hong Kong SAR | 中文](/zh-HK/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[East Timor | Português](/pt-TP/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Fiji | English](/en-FJ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[India (English) | English](/en-IN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[India (Hindi) | हिन्दी](/hi-IN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Indonesia | Bahasa Indonesia](/id-ID/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Japan | 日本語](/ja-JP/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Kiribati | English](/en-KI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[South Korea | 한국어](/ko-KR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Kyrgyzstan | Русский](/ru-KG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Malaysia | Bahasa Melayu](/ms-MY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Marshall Islands | English](/en-MH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Micronesia | English](/en-FM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[New Zealand | English](/en-NZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Palau | English](/en-PW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Philippines | English](/en-PH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Philippines | Tagalog](/tl-PH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Samoa | English](/en-AS/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Singapore | English](/en-SG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Singapore | 中文](/zh-SG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Solomon Islands | English](/en-SB/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Taiwan | 中文](/zh-TW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Thailand | ไทย](/th-TH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Tonga | English](/en-TO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Tuvalu | English](/en-TV/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Vanuatu | English](/en-VU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Vanuatu | Français](/fr-VU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Vietnam | Tiếng Việt](/vi-VN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nEurope Add\\n\\n[Andorra | Català](/ca-AD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Andorra | Français](/fr-AD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Austria | Deutsch](/de-AT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Belarus | Русский](/ru-BY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Belgium | Dutch](/nl-BE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Belgium | Français](/fr-BE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Bosnia and Herzegovina | Hrvatski](/hr-BA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Croatia | Hrvatski](/hr-HR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cyprus | Ελληνικά](/el-CY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Czech Republic | Čeština](/cs-CZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Denmark | Dansk](/da-DK/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Estonia | Русский](/ru-EE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Estonia | Eesti](/et-EE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Finland | Suomi](/fi-FI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[France | Français](/fr-FR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Germany | Deutsch](/de-DE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Greece | Ελληνικά](/el-GR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Hungary | Magyar](/hu-HU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Ireland | English](/en-IE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Italy | Italiano](/it-IT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Liechtenstein | Deutsch](/de-LI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Luxembourg | Français](/fr-LU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Malta | English](/en-MT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Monaco | Français](/fr-MC/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Netherlands | Nederlands](/nl-NL/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Norway | Norsk](/no-NO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Poland | Polski](/pl-PL/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Portugal | Português](/pt-PT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Romania | Română](/ro-RO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Russia | Русский](/ru-RU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[San Marino | Italiano](/it-SM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Slovakia | Slovenčina](/sk-SK/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Spain | Español](/es-ES/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Spain | Català](/ca-ES/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Sweden | Svenska](/sv-SE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Switzerland | Deutsch](/de-CH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Turkey | Turkçe](/tr-TR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Ukraine | Українська](/uk-UA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[United Kingdom | English](/en-GB/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[State of Vatican City (Holy See) | Italiano](/it-VA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nMiddle East Add\\n\\n[Bahrain | العربية](/ar-BH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Iran | فارسى](/fa-IR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Iraq | العربية](/ar-IQ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Israel | עִבְרִית](/he-IL/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Jordan | العربية](/ar-JO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Kuwait | العربية](/ar-KW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Lebanon | العربية](/ar-LB/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Oman | العربية](/ar-OM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Pakistan | اردو](/ur-PK/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Pakistan | English](/en-PK/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Qatar | العربية](/ar-QA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Saudi Arabia | العربية](/ar-SA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Syria | العربية](/ar-SY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[United Arab Emirates | العربية](/ar-AE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nMenu\\n\\n## Weather Forecasts\\n\\n[Today](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Hourly](/weather/hourbyhour/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[10 Day](/weather/tenday/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\n## Radar & Maps\\n\\n[Interactive Radar Map](/weather/radar/interactive/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08?animation=true)[US Forecast](/maps/currentusweather)[US Satellite & Radar](/maps/ussatellitemap)[World Satellite](/maps/satellite/caribbeanweathermap)[US Severe Alerts](/maps/severealerts)[US Health](/maps/health/allergies/treepollen)\\n\\n## News & Media\\n\\n[Top Weather Stories](/science/space/video/buy-a-massive-rock-from-planet-mars)[Hurricane Central](/storms/hurricane-central)[Tornado CentralNEW](/storms/tornado-central)[Safety & Prep](/safety/index)[Space & Skywatching](/science/space)[Videos](/science/space/video/buy-a-massive-rock-from-planet-mars)\\n\\n## Products & Account\\n\\n[Alexa SkillExternal Link](https://www.amazon.com/The-Weather-Company-Channel/dp/B07YPYHQ1Q) [Create an Account](/signup)[Sign in](/login)\\n\\n## Lifestyle\\n\\n[Atmosphere Reviews](/atmosphere)[Recipes](/home-garden/recipes)[Home & Garden](/home-garden)[Travel & Outdoors](/travel)[Pets & Animals](/pets)[Health](/health)[Allergy Tracker](/forecast/allergy/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Air Quality Index](/forecast/air-quality/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cold & Flu Tracker](/forecast/cold-flu/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Skin Health](/health/skin-health)\\n\\n[Mostly Cloudy Day 94°](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Yuexiu District, Guangdong, People's Republic of China](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nFavorite Favorite Save Location\\n\\n[\\n\\nToday\\n\\n](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\nHourly\\n\\n](/weather/hourbyhour/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\n10 Day\\n\\n](/weather/tenday/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\nWeekend\\n\\n](/weather/weekend/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\nMonthly\\n\\n](/weather/monthly/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\nRadar\\n\\n](/weather/radar/interactive/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\nVideo Video\\n\\n](/science/space/video/buy-a-massive-rock-from-planet-mars?adRef=nav)\\n\\nMore ForecastsMoreArrow down\\n\\n### Specialty Forecasts\\n\\n[Yesterday's WeatherExternal Link](//www.wunderground.com/history/daily/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08/yesterday) [Allergy Tracker](/forecast/allergy/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cold & Flu](/forecast/cold-flu/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Air Quality Forecast](/forecast/air-quality/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\n# Oops! We are having trouble finding your page.\\n\\n## Please try again... or Check out some of our favorite areas of the site.\\n\\n[![Video](https://s.w-x.co/404-video.jpg)\\n\\n### Video\\n\\nWatch today's top stories and most popular videos. Looking to watch incredible tornadoes? We've got that too.\\n\\n\\n\\n](/video)\\n\\n[![Maps](https://s.w-x.co/404-maps.jpg)\\n\\n### Maps\\n\\nCheck out our interactive maps, which provide you with features like past/future radar and customizable layers.\\n\\n\\n\\n](/weather/radar/interactive/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\n[![Local Weather](https://s.w-x.co/404-local.jpg)\\n\\n### Local Weather\\n\\nTrust us to help you plan the best day possible, with the most accurate weather forecast available.\\n\\n\\n\\n](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\n[![The Weather Company](/not-found/assets/the-weather-company.resource.9f6305c416ac2bdfe238.svg)](https://www.weathercompany.com)[![The Weather Channel](/not-found/assets/the-weather-channel.resource.6fc09ac144dba97e6b32.svg)](https://weather.com)[![Weather Underground](/not-found/assets/weather-underground.resource.b74e28f7947cb0e3a0e5.svg)](https://www.wunderground.com)[![Storm Radar](https://s.w-x.co/Storm-Radar-App-Icon.png)](https://weather.com/storm-radar)\\n\\n*   [Feedback](//support.weather.com/s/?language=en_US&type=TWC)\\n*   [Mission](/mission)\\n*   [Careers](https://www.weathercompany.com/careers)\\n*   [News Room](https://www.weathercompany.com/newsroom)\\n*   [Advertise With Us](https://www.weathercompany.com/advertising/)\\n*   [TV](https://www.weathergroup.com/brands/the-weather-channel/)\\n*   [Weather Data APIs](https://www.weathercompany.com/weather-data-apis/)\\n\\n*   [Terms of Use](/legal)\\n*   [Privacy Policy](/en-US/twc/privacy-policy)\\n*   [AdChoicesAd Choices](/services/ad-choices)\\n*   [Accessibility Statement](/accessibility-statement)\\n*   [Data Vendors](/data-vendors)\\n\\n[![Georgia Org](/not-found/assets/georgia.resource.50fbe85820c759d46005.svg)](http://www.exploregeorgia.org/weather-and-travel-tips)[![Link to eSSENTIAL Accessibility assistive technology](/not-found/assets/eSSENTIAL.resource.728ebae71f894722ea9c.svg)](https://www.essentialaccessibility.com/the-weather-channel?utm_source=theweatherchannelhomepage&utm_medium=iconlarge&utm_term=eachannelpage&utm_content=header&utm_campaign=theweatherchannel)\\n\\nWe recognize our responsibility to use data and technology for good. We may use or share your data with our data vendors. Take control of your data.\\n\\n*   Cookie Settings\\n*   [Data Rights](/data-rights)\\n\\nThe Weather Channel is the world's most accurate forecaster according to ForecastWatch, [Global and Regional Weather Forecast Accuracy Overview](https://forecastwatch.com/AccuracyOverview2021-2024), 2021-2024, commissioned by The Weather Company.\\n\\nWeather Channel\\n\\n© The Weather Company, LLC 2025\\n\\n0\\n\\n1\\n\\n2\\n\\n3\\n\\n4\\n\\n5\\n\\n6\\n\\n7\\n\\n8\\n\\n9\\n\\n10\\n\\n11\\n\\n12\\n\\n13\\n\\n14\\n\\n15\\n\\n16\\n\\n17\\n\\n18\\n\\n19\\n\\n20\\n\\n21\\n\\n22\\n\\n23\\n\\n24\\n\\n25\\n\\n26\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "看来访问该网站时遇到了问题，页面显示为“404 Not Found”。我将尝试另一个天气预报网站来获取北京下周的天气信息。请稍等。", "tool_calls": [{"id": "call_0_c5b83c4f-ee4d-4d31-baf5-fed46288321b", "type": "function", "function": {"name": "browser_navigate", "arguments": "{\"url\":\"https://www.accuweather.com/en/cn/beijing/106571/weather-forecast/106571\"}"}}]}, {"role": "tool", "tool_call_id": "call_0_c5b83c4f-ee4d-4d31-baf5-fed46288321b", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \"Navigated to https://www.accuweather.com/en/cn/beijing/106571/weather-forecast/106571\\nclickable elements(Might be outdated, if an error occurs with the index element, use browser_get_clickable_elements to refresh it): [0]<a>For Business</a>\\n[]|\\n[1]<a>Warnings</a>\\n[2]<a>Data Suite</a>\\n[3]<a>Forensics</a>\\n[4]<a>Advertising</a>\\n[5]<a>Superior Accuracy™</a>\\n[6]<a></a>\\n[7]<a>Tai'an, Shandong\\n88°\\nF</a>\\n[8]<span></span>\\n[9]<img></img>\\n[10]<input></input>\\n[11]<div>Location</div>\\n[12]<a></a>\\n[13]<a>Today</a>\\n[14]<a>Hourly</a>\\n[15]<a>Daily</a>\\n[16]<a>Radar</a>\\n[17]<a>MinuteCast</a>\\n[18]<a>Monthly</a>\\n[19]<a>Air Quality</a>\\n[20]<a>Health & Activities</a>\\n[21]<a>Today's Weather\\nTue, Jul 15\\nMostly cloudy and hot; caution advised if doing strenuous activities outside\\nHi: 94°\\nTonight: A heavy thunderstorm around this evening; otherwise, partly cloudy, warm and humid\\nLo: 76°</a>\\n[22]<img></img>\\n[23]<img></img>\\n[24]<a>Current Weather\\n12:52 PM\\n88°\\nF\\nRealFeel Shade™\\n88°\\nWind\\nSSE 9 mph</a>\\n[]Top Stories\\n[25]<a>Weather Forecasts</a>\\n[26]<img></img>\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n1. [TODO] Unknown step\n2. [TODO] Unknown step\n3. [TODO] Unknown step\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_c9159fd4-6309-41c9-ba78-533e242e19fa", "type": "function", "function": {"name": "browser_get_markdown", "arguments": "{}"}}]}, {"role": "tool", "tool_call_id": "call_0_c9159fd4-6309-41c9-ba78-533e242e19fa", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \"   Tai'an, Shandong, China Weather Forecast | AccuWeather                                                           \\n\\n[Go Back](/pwa)\\n\\n*   [For Business](https://afb.accuweather.com)\\n*   |\\n*   [Warnings](https://afb.accuweather.com/accuweather-skyguard)\\n*   [Data Suite](https://afb.accuweather.com/accuweather-data-suite)\\n*   [Forensics](https://afb.accuweather.com/accuweather-forensicservices)\\n*   [Advertising](https://advertising.accuweather.com/for-advertising/digital-advertising/)\\n*   [Superior Accuracy™](https://afb.accuweather.com/accuweathers-proven-superior-accuracy)\\n\\n[](/)[# Tai'an, Shandong\\n\\n88°F](/en/cn/taian/106571/weather-forecast/106571) ![](https://vortex.accuweather.com/adc2010/images/branding-partners/china_weather_logo.png)\\n\\nLocation Chevron down\\n\\nLocation News Videos\\n\\nUse Current Location\\n\\nRecent\\n\\n[\\n\\nTai'an\\n\\nShandong\\n\\n88°](/web-api/three-day-redirect?key=106571&target=)\\n\\nNo results found.\\n\\nTry searching for a city, zip code or point of interest.\\n\\n[](/en/videos)\\n\\n[settings](/en/settings)\\n\\nTai'an, Shandong Weather\\n\\n[Today](/en/cn/taian/106571/weather-forecast/106571) [WinterCast](/en/cn/taian/106571/winter-weather-forecast/106571) [Local {stormName} Tracker](/en/cn/taian/106571/tropical-weather-forecast/106571?eventkey={eventkey}) [Hourly](/en/cn/taian/106571/hourly-weather-forecast/106571) [Daily](/en/cn/taian/106571/daily-weather-forecast/106571) [Radar](/en/cn/taian/106571/weather-radar/106571) [MinuteCast](/en/cn/taian/106571/minute-weather-forecast/106571) [Monthly](/en/cn/taian/106571/july-weather/106571) [Air Quality](/en/cn/taian/106571/air-quality-index/106571) [Health & Activities](/en/cn/taian/106571/health-activities/106571)\\n\\n## \\n\\nAround the Globe\\n\\n[\\n\\n### Hurricane Tracker\\n\\n](/en/hurricane)[\\n\\n### Severe Weather\\n\\n](/en/cn/severe-weather)[\\n\\n### Radar & Maps\\n\\n](/en/cn/shandong/weather-radar)\\n\\nNews\\n\\n[\\n\\n### News & Features\\n\\n](/en/weather-news)[\\n\\n### Astronomy\\n\\n](/en/space-news)[\\n\\n### Business\\n\\n](/en/business)[\\n\\n### Climate\\n\\n](/en/climate)[\\n\\n### Health\\n\\n](/en/health-wellness)[\\n\\n### Recreation\\n\\n](/en/leisure-recreation)[\\n\\n### Sports\\n\\n](/en/sports)[\\n\\n### Travel\\n\\n](/en/travel)\\n\\nFor Business\\n\\n[\\n\\n### Warnings\\n\\n](https://afb.accuweather.com/accuweather-skyguard)[\\n\\n### Data Suite\\n\\n](https://afb.accuweather.com/accuweather-data-suite)[\\n\\n### Forensics\\n\\n](https://afb.accuweather.com/accuweather-forensicservices)[\\n\\n### Advertising\\n\\n](https://advertising.accuweather.com/for-advertising/digital-advertising/)[\\n\\n### Superior Accuracy™\\n\\n](https://afb.accuweather.com/accuweathers-proven-superior-accuracy)\\n\\n[\\n\\n### Video\\n\\n](/en/videos)\\n\\n[\\n\\n## Today\\n\\n](/en/cn/taian/106571/weather-forecast/106571)[Hourly](/en/cn/taian/106571/hourly-weather-forecast/106571) [Daily](/en/cn/taian/106571/daily-weather-forecast/106571) [Radar](/en/cn/taian/106571/weather-radar/106571) [MinuteCast](/en/cn/taian/106571/minute-weather-forecast/106571) [Monthly](/en/cn/taian/106571/july-weather/106571) [Air Quality](/en/cn/taian/106571/air-quality-index/106571) [Health & Activities](/en/cn/taian/106571/health-activities/106571)\\n\\n[\\n\\n## Today's Weather\\n\\nTue, Jul 15\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/6.svg)\\n\\nMostly cloudy and hot; caution advised if doing strenuous activities outside **Hi: 94°**\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/41.svg)\\n\\nTonight: A heavy thunderstorm around this evening; otherwise, partly cloudy, warm and humid **Lo: 76°**\\n\\n\\n\\n\\n\\n](/en/cn/taian/106571/weather-today/106571)\\n\\n[\\n\\n## Current Weather\\n\\n12:52 PM\\n\\n88°F\\n\\nRealFeel® 94°\\n\\nCloudy More Details\\n\\nRealFeel Shade™ 88°\\n\\nWind SSE 9 mph\\n\\nWind Gusts 23 mph\\n\\nAir Quality Poor\\n\\n\\n\\n\\n\\n](/en/cn/taian/106571/current-weather/106571)[\\n\\n## Looking Ahead\\n\\nWatch for a heavy thunderstorm Tuesday evening\\n\\n](/en/cn/taian/106571/weather-today/106571)\\n\\n## Tai'an Weather Radar\\n\\n[![Tai'an Weather Radar](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAJAQMAAAAB5D5xAAAAA1BMVEUAAACnej3aAAAAAXRSTlMAQObYZgAAAApJREFUCNdjwA0AABsAAQrj5HwAAAAASUVORK5CYII= \\\"Tai'an Weather Radar\\\")\\n\\nStatic Radar Temporarily Unavailable\\n\\nThank you for your patience as we work to get everything up and running again.\\n\\nRefresh Page\\n\\n\\n\\n\\n\\n](/en/cn/taian/106571/weather-radar/106571)\\n\\n [![](https://www.awxcdn.com/adc-assets/images/components/maps/mapping-layers/realvue-satellite.jpg) Clouds](/en/cn/taian/106571/satellite/106571) [![](https://www.awxcdn.com/adc-assets/images/components/maps/mapping-layers/temperature.jpg) Temperature](/en/cn/shandong/current-weather-maps)\\n\\n## Hourly Weather\\n\\nChevron left\\n\\n[1 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/7.svg) 90°\\n\\n 5%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752555600)[2 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/6.svg) 92°\\n\\n 5%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752559200)[3 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/6.svg) 93°\\n\\n 6%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752562800)[4 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/6.svg) 94°\\n\\n 7%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752566400)[5 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/6.svg) 93°\\n\\n 7%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752570000)[6 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/4.svg) 91°\\n\\n 11%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752573600)[7 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/4.svg) 89°\\n\\n 20%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752577200)[8 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/35.svg) 87°\\n\\n 20%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752580800)[9 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/36.svg) 85°\\n\\n 25%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752584400)[10 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/36.svg) 84°\\n\\n 49%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752588000)[11 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/36.svg) 83°\\n\\n 49%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752591600)[12 AM ![](https://www.awxcdn.com/adc-assets/images/weathericons/42.svg) 82°\\n\\n 61%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752595200&day=2)\\n\\nChevron right\\n\\n## 10-Day Weather Forecast\\n\\n[\\n\\nToday\\n\\n7/15\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/6.svg)\\n\\n94° 76°\\n\\nMostly cloudy and hot\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/41.svg)\\n\\nNight: A heavy t-storm this evening\\n\\n25%\\n\\n](/en/cn/taian/106571/weather-today/106571)[\\n\\nWed\\n\\n7/16\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/5.svg)\\n\\n94° 72°\\n\\nHazy sun, hot and humid\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/33.svg)\\n\\nClear\\n\\n4%\\n\\n](/en/cn/taian/106571/weather-tomorrow/106571)[\\n\\nThu\\n\\n7/17\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/5.svg)\\n\\n93° 70°\\n\\nHazy, hot and less humid\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/34.svg)\\n\\nClear to partly cloudy\\n\\n1%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=3)[\\n\\nFri\\n\\n7/18\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/30.svg)\\n\\n97° 72°\\n\\nVery hot with hazy sunshine\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/33.svg)\\n\\nClear\\n\\n1%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=4)[\\n\\nSat\\n\\n7/19\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/2.svg)\\n\\n96° 76°\\n\\nMostly sunny and hot\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/38.svg)\\n\\nBecoming cloudy; humid\\n\\n1%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=5)[\\n\\nSun\\n\\n7/20\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/12.svg)\\n\\n78° 74°\\n\\nSome rain and a thunderstorm\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/15.svg)\\n\\nStrong thunderstorms; humid\\n\\n68%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=6)[\\n\\nMon\\n\\n7/21\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/15.svg)\\n\\n83° 78°\\n\\nHumid; a p.m. t-storm or two\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/15.svg)\\n\\nOvercast, a t-storm or two\\n\\n75%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=7)[\\n\\nTue\\n\\n7/22\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/15.svg)\\n\\n84° 77°\\n\\nAn afternoon t-storm or two\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/7.svg)\\n\\nAn evening thunderstorm\\n\\n76%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=8)[\\n\\nWed\\n\\n7/23\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/18.svg)\\n\\n88° 76°\\n\\nOccasional rain and a t-storm\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/12.svg)\\n\\nA bit of late-night rain\\n\\n55%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=9)[\\n\\nThu\\n\\n7/24\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/18.svg)\\n\\n87° 77°\\n\\nHumid with periods of rain\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/35.svg)\\n\\nPartly cloudy, warm and humid\\n\\n65%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=10)\\n\\n## Sun & Moon\\n\\n![](/images/whiteweathericons/1.svg) 14 hrs 24 mins\\n\\nRise 5:05 AM\\n\\nSet 7:29 PM\\n\\n![](/images/moon-phases/light/WaningGibbous.svg) Waning Gibbous\\n\\nRise 10:16 PM\\n\\nSet 10:29 AM\\n\\n[\\n\\n## Air Quality\\n\\nSee More\\n\\nAir Quality Poor\\n\\nThe air has reached a high level of pollution and is unhealthy for sensitive groups. Reduce time spent outside if you are feeling symptoms such as difficulty breathing or throat irritation.\\n\\n\\n\\n](/en/cn/taian/106571/air-quality-index/106571)\\n\\n## Allergy Outlook\\n\\n[See All](/en/cn/taian/106571/health-activities/106571)\\n\\n[Dust & Dander Extreme Data is not supported in this location](/en/cn/taian/106571/allergies-weather/106571?name=dust-dander)\\n\\nTop Stories\\n\\n[\\n\\nWeather Forecasts\\n\\nFlood-weary Texas finally drying out, but near 100-degree heat looms\\n\\n9 hours ago\\n\\n![](https://cms.accuweather.com/wp-content/uploads/2025/07/CentralPattern.jpeg?w=64&h=64&crop=1)](https://www.accuweather.com/en/weather-forecasts/flood-weary-texas-finally-drying-out-but-near-100-degree-heat-looms/1794693)[\\n\\nWeather News\\n\\nThunderstorms bring more flooding, evacuations to central Texas\\n\\n10 hours ago\\n\\n](https://www.accuweather.com/en/weather-news/slow-moving-thunderstorms-bring-more-flooding-and-some-evacuations-to-central-texas/1794648)[\\n\\nHurricane\\n\\nTropical Rainstorm to drench Florida, Gulf Coast\\n\\n4 hours ago\\n\\n](https://www.accuweather.com/en/hurricane/tropical-rainstorm-soaks-florida-could-strengthen-in-gulf-before-targeting-louisiana/1794844)[\\n\\nWeather News\\n\\nGrand Canyon fires force closures\\n\\n10 hours ago\\n\\n](https://www.accuweather.com/en/weather-news/grand-canyon-lodge-rim-wildfire/1794665)[Severe Weather\\n\\nWildfire smoke may follow storms, severe weather in eastern US\\n\\n9 hours ago](https://www.accuweather.com/en/severe-weather/wildfire-smoke-may-follow-storms-severe-weather-in-eastern-us/1793854) [More Stories](https://www.accuweather.com/en/weather-news)\\n\\nFeatured Stories\\n\\n[\\n\\nAstronomy\\n\\nNoctilucent clouds shimmer in summer sky\\n\\n10 hours ago\\n\\n](https://www.accuweather.com/en/space-news/noctilucent-clouds-shimmer-in-summer-sky-with-a-possible-boost-from-rockets/1794926)[\\n\\nRecreation\\n\\nYellowstone’s landscape shifts again with discovery of new hot pool\\n\\n12 hours ago\\n\\n](https://www.accuweather.com/en/leisure-recreation/yellowstone-national-park-new-thermal-pool/1794749)[\\n\\nWeather News\\n\\nOrcas are bringing humans gifts of food – but why?\\n\\n3 days ago\\n\\n](https://www.accuweather.com/en/weather-news/orcas-are-bringing-humans-gifts-of-food-but-why/1793505)[\\n\\nWeather News\\n\\nGiant shoes found spark mystery around the soldiers of ancient Rome\\n\\n10 hours ago\\n\\n](https://www.accuweather.com/en/weather-news/giant-shoes-found-near-hadrians-wall-spark-mystery-around-the-soldiers-of-ancient-rome/1794955)[\\n\\nWeather News\\n\\nIt’s not just humans – chimpanzees also like to follow trends\\n\\n11 hours ago\\n\\n](https://www.accuweather.com/en/weather-news/its-not-just-humans-chimpanzees-also-like-to-follow-trends-study-shows/1794900)\\n\\n[World](/en/world-weather) [Asia](/en/asia-weather) [China](/en/cn/china-weather) [Shandong](/en/cn/sd/shandong-weather) [Tai'an](/en/cn/taian/106571/weather-forecast/106571)\\n\\n## Weather Near Tai'an:\\n\\n*   [Jinan, Shandong](/en/cn/jinan/106570/weather-forecast/106570)\\n*   [Jining, Shandong](/en/cn/jining/60634/weather-forecast/60634)\\n*   [Liaocheng, Shandong](/en/cn/liaocheng/60636/weather-forecast/60636)\\n\\nCompany\\n\\n[Proven Superior Accuracy](https://corporate.accuweather.com/) [About AccuWeather](https://corporate.accuweather.com/company/about-us/) [Digital Advertising](https://advertising.accuweather.com/for-advertising/digital-advertising/) [Careers](https://corporate.accuweather.com/company/careers/) [Press](https://corporate.accuweather.com/newsroom/press-releases/) [Contact Us](/en/contact)\\n\\nProducts & Services\\n\\n[For Business](https://afb.accuweather.com/) [For Partners](https://partners.accuweather.com/) [For Advertising](https://advertising.accuweather.com/) [AccuWeather APIs](https://developer.accuweather.com/) [AccuWeather Connect](https://afb.accuweather.com/accuweather-connect-and-content-syndication) RealFeel® and RealFeel Shade™ [Personal Weather Stations](https://app.accuweather.com/products/ambient-weather-stations)\\n\\nApps & Downloads\\n\\n[iPhone App](https://go.onelink.me/app/ef253ee1) [Android App](https://go.onelink.me/app/85d14e58) [See all Apps & Downloads](https://7482826.hs-sites.com/premium-plus)\\n\\nSubscription Services\\n\\n[AccuWeather Premium](https://wwwl.accuweather.com/premium_login.php) [AccuWeather Professional](https://wwwl.accuweather.com/pro_login.php)\\n\\nMore\\n\\n[AccuWeather Ready](/en/accuweather-ready) [Business](/en/business) [Health](/en/health-wellness) [Hurricane](/en/hurricane) [Leisure and Recreation](/en/leisure-recreation) [Severe Weather](/en/cn/severe-weather) [Space and Astronomy](/en/space-news) [Sports](/en/sports) [Travel](/en/travel) [Weather News](/en/weather-news)\\n\\n[](https://www.facebook.com/AccuWeather)[](https://twitter.com/BreakingWeather)\\n\\nCompany\\n\\n[Proven Superior Accuracy](https://corporate.accuweather.com/) [About AccuWeather](https://corporate.accuweather.com/company/about-us/) [Digital Advertising](https://advertising.accuweather.com/for-advertising/digital-advertising/) [Careers](https://corporate.accuweather.com/company/careers/) [Press](https://corporate.accuweather.com/newsroom/press-releases/) [Contact Us](/en/contact)\\n\\n[](https://www.facebook.com/AccuWeather)[](https://twitter.com/BreakingWeather)\\n\\nProducts & Services\\n\\n[For Business](https://afb.accuweather.com/) [For Partners](https://partners.accuweather.com/) [For Advertising](https://advertising.accuweather.com/) [AccuWeather APIs](https://developer.accuweather.com/) [AccuWeather Connect](https://afb.accuweather.com/accuweather-connect-and-content-syndication) RealFeel® and RealFeel Shade™ [Personal Weather Stations](https://app.accuweather.com/products/ambient-weather-stations)\\n\\nApps & Downloads\\n\\n[iPhone App](https://go.onelink.me/app/ef253ee1) [Android App](https://go.onelink.me/app/85d14e58) [See all Apps & Downloads](https://7482826.hs-sites.com/premium-plus)\\n\\nSubscription Services\\n\\n[AccuWeather Premium](https://wwwl.accuweather.com/premium_login.php) [AccuWeather Professional](https://wwwl.accuweather.com/pro_login.php)\\n\\nMore\\n\\n[AccuWeather Ready](/en/accuweather-ready) [Business](/en/business) [Health](/en/health-wellness) [Hurricane](/en/hurricane) [Leisure and Recreation](/en/leisure-recreation) [Severe Weather](/en/cn/severe-weather) [Space and Astronomy](/en/space-news) [Sports](/en/sports) [Travel](/en/travel) [Weather News](/en/weather-news)\\n\\n© 2025 AccuWeather, Inc. \\\"AccuWeather\\\" and sun design are registered trademarks of AccuWeather, Inc. All Rights Reserved.\\n\\n[Terms of Use](/en/legal) | [Privacy Policy](/en/privacy) | [Cookie Policy](/en/cookiepolicy) | About Your Privacy Do Not Sell or Share My Personal Information\\n\\n       \\n\\n![](https://www.accuweather.com/akam/13/pixel_398ee9b5?a=dD03N2Q0YWI5NTBmODU0NjE2NGM0OGVlODk3Njg0ODQ5YmI0YTg5N2IzJmpzPW9mZg==)\\n\\n0\\n\\n1\\n\\n2\\n\\n3\\n\\n4\\n\\n5\\n\\n6\\n\\n7\\n\\n8\\n\\n9\\n\\n10\\n\\n11\\n\\n12\\n\\n13\\n\\n14\\n\\n15\\n\\n16\\n\\n17\\n\\n18\\n\\n19\\n\\n20\\n\\n21\\n\\n22\\n\\n23\\n\\n24\\n\\n25\\n\\n26\\n\\n![](https://ad-delivery.net/px.gif?ch=2)![](https://ad.doubleclick.net/favicon.ico?ad=300x250&ad_box_=1&adnet=1&showad=1&size=250x250)![](https://ad-delivery.net/px.gif?ch=1&e=0.06509189746796695)\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "看来访问该网站时也遇到了问题，页面显示的是泰安（Tai'an）的天气信息，而不是北京的。我将尝试另一个天气预报网站来获取北京下周的天气信息。请稍等。", "tool_calls": [{"id": "call_0_175a08db-04bc-4f38-9204-945f28b3224e", "type": "function", "function": {"name": "browser_navigate", "arguments": "{\"url\":\"https://www.timeanddate.com/weather/china/beijing/ext\"}"}}]}, {"role": "tool", "tool_call_id": "call_0_175a08db-04bc-4f38-9204-945f28b3224e", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \"Navigation timeout, but page might still be usable:\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n1. [TODO] Unknown step\n2. [TODO] Unknown step\n3. [TODO] Unknown step\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_f55eef87-54a2-47f9-9a46-a9a6b73c3c1f", "type": "function", "function": {"name": "browser_get_markdown", "arguments": "{}"}}]}, {"role": "tool", "tool_call_id": "call_0_f55eef87-54a2-47f9-9a46-a9a6b73c3c1f", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \"Beijing, Beijing Municipality, China 14 day weather forecast           \\n\\n[\\n\\n](/ \\\"Home page timeanddate.com\\\")\\n\\n[![timeanddate.com](//c.tadst.com/gfx/n/logo/logo-2021--horizontal-inverted-darkbg.svg)](/ \\\"Home page timeanddate.com\\\")\\n\\nSign in\\n\\n*   [News](/news/)\\n    *   [News Home](/news)\\n    *   [Astronomy News](/news/astronomy/)\\n    *   [Time Zone News](/news/time/)\\n    *   [Calendar & Holiday News](/news/calendar/)\\n    *   [Newsletter](/newsletter/)\\n    *   [Live events](/live/)\\n*   [World Clock](/worldclock/)\\n    *   [Main World Clock](/worldclock/)\\n    *   [Extended World Clock](/worldclock/full.html)\\n    *   [Personal World Clock](/worldclock/personal.html)\\n    *   [Clock for Your Site](/clocks/free.html)\\n    *   [UTC Time](/worldclock/timezone/utc)\\n*   [Time Zones](/time/)\\n    *   [Time Zones Home](/time)\\n    *   [Time Zone Converter](/worldclock/converter.html)\\n    *   [Meeting Planner](/worldclock/meeting.html)\\n    *   [Event Time Announcer](/worldclock/fixedform.html)\\n    *   [Time Zone Map](/time/map/)\\n    *   [Time Zone Abbreviations](/time/zones/)\\n    *   [Daylight Saving Time](/time/dst/)\\n    *   [Time Changes Worldwide](/time/change/)\\n    *   [Time Difference](/time/difference/)\\n    *   [Time Zone News](/news/time/)\\n*   [Calendar](/calendar/)\\n    *   [Calendars Home](/calendar/info.html)\\n    *   [Calendar 2025](/calendar/)\\n    *   [Calendar 2026](/calendar/?year=2026)\\n    *   [Monthly Calendar](/calendar/monthly.html)\\n    *   [Printable Calendar (PDF)](/calendar/create.html)\\n    *   [Add Your Own Calendar Events](/calendar/events/)\\n    *   [Calendar Creator](/calendar/basic.html)\\n    *   [Holidays Worldwide](/holidays/)\\n    *   [On This Day in History](/on-this-day/)\\n    *   [Fun Holidays](/holidays/fun/)\\n    *   [Months of the Year](/calendar/months/)\\n    *   [Days of the Week](/calendar/days/)\\n    *   [About Leap Years](/date/leapyear.html)\\n    *   [Calendar & Holiday News](/news/calendar/)\\n*   [Weather](/weather/)\\n    *   [Worldwide](/weather/)\\n    *   [Local Weather](/scripts/go.php)\\n    *   [2-Week Forecast](/scripts/go.php?type=ext)\\n    *   [Hour-by-Hour](/scripts/go.php?type=hourly)\\n    *   [Past Week](/scripts/go.php?type=historic)\\n    *   [Climate](/scripts/go.php?type=climate)\\n*   [Sun, Moon & Space](/astronomy/)\\n    *   [Sun & Moon Home](/astronomy)\\n    *   [Sun Calculator](/sun/)\\n    *   [Moon Calculator](/moon/)\\n    *   [Moon Phases](/moon/phases/)\\n    *   [Night Sky](/astronomy/night/)\\n    *   [Meteor Showers](/astronomy/meteor-shower/)\\n    *   [Day and Night Map](/worldclock/sunearth.html)\\n    *   [Moon Light World Map](/astronomy/moon/light.html)\\n    *   [Eclipses](/eclipse/)\\n    *   [Live Streams](/live/)\\n    *   [Seasons](/calendar/seasons.html)\\n    *   [Astronomy News](/news/astronomy/)\\n*   [Timers](/counters/)\\n    *   [Timers Home](/counters)\\n    *   [Stopwatch](/stopwatch/)\\n    *   [Timer](/timer/)\\n    *   [Countdown to Any Date](/countdown/create)\\n    *   [Eclipse Countdown](/countdown/eclipse)\\n    *   [Fall Countdown](/countdown/fall)\\n    *   [New Year Countdown](/countdown/newyear)\\n    *   [Countdown for Your Site](/clocks/freecountdown.html)\\n*   [Calculators](/date/)\\n    *   [Calculators Home](/date/)\\n    *   [Date to Date Calculator (duration)](/date/duration.html)\\n    *   [Business Date to Date (exclude holidays)](/date/workdays.html)\\n    *   [Date Calculator (add / subtract)](/date/dateadd.html)\\n    *   [Business Date (exclude holidays)](/date/weekdayadd.html)\\n    *   [Weekday Calculator](/date/weekday.html)\\n    *   [Week Number Calculator](/date/weeknumber.html)\\n    *   [Roman Numeral Converter](/date/roman-calculator.html)\\n    *   [Alternative Age Calculator](/date/birthday.html)\\n    *   [Date Pattern Calculator](/date/pattern.html)\\n    *   [Distance Calculator](/worldclock/distance.html)\\n*   [My Account](/custom/)\\n    *   [My Account](/custom)\\n    *   [My Location](/custom/location.html)\\n    *   [My Units](/custom/site.html)\\n    *   [My Events](/calendar/events/)\\n    *   [My World Clock](/worldclock/personal.html)\\n    *   [My Privacy](/custom/privacy.html)\\n    *   [Paid Services](/services/)\\n    *   [Sign in](/custom/login.html)\\n    *   [Register](/custom/create.html)\\n\\n \\n\\n[Home](/)   [Weather](/weather/)   [China](/weather/china)   [Beijing](/weather/china/beijing)   Two-week forecast\\n\\n ![Flag for China](//c.tadst.com/gfx/n/fl/48/cn.png \\\"Flag for China\\\")\\n\\n# Beijing 14 Day Extended Forecast\\n\\n*   [Time/General](/worldclock/china/beijing \\\"General/main info about Beijing\\\")\\n*   [Weather](/weather/china/beijing \\\"Current weather and forecast for Beijing\\\")\\n    *   [Weather Today/Tomorrow](/weather/china/beijing \\\"Shows a weather overview\\\")\\n    *   [Hour-by-Hour Forecast](/weather/china/beijing/hourly \\\"Hour-by-hour weather for the coming week\\\")\\n    *   [14 Day Forecast](/weather/china/beijing/ext \\\"Extended forecast for the next two weeks\\\")\\n    \\n    *   [Yesterday/Past Weather](/weather/china/beijing/historic \\\"Past weather for yesterday, the last 2 weeks, or any selected month available\\\")\\n    *   [Climate (Averages)](/weather/china/beijing/climate \\\"Historic weather and climate information\\\")\\n*   [Time Zone](/time/zone/china/beijing \\\"Past and future time change dates for Beijing\\\")\\n*   [DST Changes](/time/change/china/beijing \\\"Daylight saving time changeover dates and times for Beijing\\\")\\n*   [Sun & Moon](/astronomy/china/beijing \\\"Calculate rising and setting times for the Sun and Moon in Beijing\\\")\\n    *   [Sun & Moon Today](/astronomy/china/beijing)\\n    *   [Sunrise & Sunset](/sun/china/beijing)\\n    \\n    *   [Moonrise & Moonset](/moon/china/beijing)\\n    *   [Moon Phases](/moon/phases/china/beijing)\\n    \\n    *   [Eclipses](/eclipse/in/china/beijing)\\n    *   [Night Sky](/astronomy/night/china/beijing)\\n\\n[Weather Today](/weather/china/beijing \\\"Shows a weather overview\\\")[Weather Hourly](/weather/china/beijing/hourly \\\"Hour-by-hour weather for the coming week\\\")[14 Day Forecast](/weather/china/beijing/ext \\\"Extended forecast for the next two weeks\\\")[Yesterday/Past Weather](/weather/china/beijing/historic \\\"Past weather for yesterday, the last 2 weeks, or any selected month available\\\")[Climate (Averages)](/weather/china/beijing/climate \\\"Historic weather and climate information\\\")\\n\\n![](//c.tadst.com/gfx/w/svg/wt-33.svg)Currently: 75 °F. Mild. (Weather station: Beijing, China). [See more current weather](/weather/china/beijing)\\n\\n×\\n\\n## Beijing Extended Forecast with high and low temperatures\\n\\n[°F](/custom/site.html \\\"Change Units\\\")\\n\\n[Last 2 weeks of weather](/weather/china/beijing/historic)\\n\\n[See weather overview](/weather/china/beijing)\\n\\n## 2 Week Extended Forecast in Beijing, Beijing Municipality, China\\n\\nScroll right to see more\\n\\n|   | Conditions | Comfort | Precipitation | Sun |\\n| --- | --- | --- | --- | --- |\\n| Day |   | Temperature | Weather | Feels Like | Wind |   | Humidity | Chance | Amount | UV | Sunrise | Sunset |\\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\\n| Tue  \\nJul 15 | ![](//c.tadst.com/gfx/w/svg/wt-33.svg \\\"Rain showers. Decreasing cloudiness.\\\") | 89 / 70 °F | Rain showers. Decreasing cloudiness. | 87 °F | 8 mph | ↑ | 66% | 65% | 0.45\\\" | 5 (Moderate) | 4:58 am | 7:42 pm |\\n| Wed  \\nJul 16 | ![](//c.tadst.com/gfx/w/svg/wt-4.svg \\\"Afternoon clouds.\\\") | 90 / 69 °F | Afternoon clouds. | 94 °F | 5 mph | ↑ | 56% | 8% | 0.00\\\" | 5 (Moderate) | 4:59 am | 7:41 pm |\\n| Thu  \\nJul 17 | ![](//c.tadst.com/gfx/w/svg/wt-2.svg \\\"Mostly sunny.\\\") | 98 / 72 °F | Mostly sunny. | 98 °F | 4 mph | ↑ | 34% | 42% | 0.01\\\" | 5 (Moderate) | 4:59 am | 7:40 pm |\\n| Fri  \\nJul 18 | ![](//c.tadst.com/gfx/w/svg/wt-22.svg \\\"Thundershowers. Mostly cloudy.\\\") | 101 / 71 °F | Thundershowers. Mostly cloudy. | 105 °F | 6 mph | ↑ | 34% | 23% | 0.04\\\" | 5 (Moderate) | 5:00 am | 7:40 pm |\\n| Sat  \\nJul 19 | ![](//c.tadst.com/gfx/w/svg/wt-33.svg \\\"Showers late. Overcast.\\\") | 97 / 77 °F | Showers late. Overcast. | 100 °F | 5 mph | ↑ | 38% | 43% | 0.04\\\" | 3 (Moderate) | 5:01 am | 7:39 pm |\\n| Sun  \\nJul 20 | ![](//c.tadst.com/gfx/w/svg/wt-22.svg \\\"Thundershowers. Overcast.\\\") | 83 / 74 °F | Thundershowers. Overcast. | 83 °F | 5 mph | ↑ | 74% | 38% | 0.11\\\" | 3 (Moderate) | 5:02 am | 7:38 pm |\\n| Mon  \\nJul 21 | ![](//c.tadst.com/gfx/w/svg/wt-21.svg \\\"Isolated tstorms. Mostly cloudy.\\\") | 89 / 73 °F | Isolated tstorms. Mostly cloudy. | 96 °F | 4 mph | ↑ | 61% | 13% | 0.06\\\" | 3 (Moderate) | 5:03 am | 7:38 pm |\\n| Tue  \\nJul 22 | ![](//c.tadst.com/gfx/w/svg/wt-22.svg \\\"Thundershowers. Broken clouds.\\\") | 91 / 74 °F | Thundershowers. Broken clouds. | 100 °F | 3 mph | ↑ | 63% | 47% | 0.16\\\" | 3 (Moderate) | 5:04 am | 7:37 pm |\\n| Wed  \\nJul 23 | ![](//c.tadst.com/gfx/w/svg/wt-22.svg \\\"Tstorms early. Partly cloudy.\\\") | 90 / 77 °F | Tstorms early. Partly cloudy. | 90 °F | 6 mph | ↑ | 38% | 47% | 0.04\\\" | 5 (Moderate) | 5:04 am | 7:36 pm |\\n| Thu  \\nJul 24 | ![](//c.tadst.com/gfx/w/svg/wt-4.svg \\\"Breaks of sun late.\\\") | 92 / 80 °F | Breaks of sun late. | 91 °F | 6 mph | ↑ | 31% | 8% | 0.00\\\" | 5 (Moderate) | 5:05 am | 7:35 pm |\\n| Fri  \\nJul 25 | ![](//c.tadst.com/gfx/w/svg/wt-6.svg \\\"Broken clouds.\\\") | 93 / 80 °F | Broken clouds. | 90 °F | 6 mph | ↑ | 25% | 6% | 0.00\\\" | 3 (Moderate) | 5:06 am | 7:34 pm |\\n| Sat  \\nJul 26 | ![](//c.tadst.com/gfx/w/svg/wt-33.svg \\\"Showers late. Mostly cloudy.\\\") | 93 / 80 °F | Showers late. Mostly cloudy. | 90 °F | 5 mph | ↑ | 26% | 49% | 0.01\\\" | 5 (Moderate) | 5:07 am | 7:33 pm |\\n| Sun  \\nJul 27 | ![](//c.tadst.com/gfx/w/svg/wt-33.svg \\\"Showers late. Broken clouds.\\\") | 93 / 81 °F | Showers late. Broken clouds. | 92 °F | 5 mph | ↑ | 30% | 42% | 0.02\\\" | 7 (High) | 5:08 am | 7:33 pm |\\n| Mon  \\nJul 28 | ![](//c.tadst.com/gfx/w/svg/wt-22.svg \\\"Tstorms early. Scattered clouds.\\\") | 93 / 81 °F | Tstorms early. Scattered clouds. | 93 °F | 6 mph | ↑ | 33% | 56% | 0.06\\\" | 7 (High) | 5:09 am | 7:32 pm |\\n| Tue  \\nJul 29 | ![](//c.tadst.com/gfx/w/svg/wt-1.svg \\\"Sunny.\\\") | 93 / 81 °F | Sunny. | 92 °F | 5 mph | ↑ | 31% | 5% | 0.00\\\" | 7 (High) | 5:10 am | 7:31 pm |\\n| \\\\* Updated Tuesday, July 15, 2025 11:24:12 am Beijing time - Weather by CustomWeather, © 2025 |\\n\\n[Hour-by-hour weather for Beijing next 7 days](/weather/china/beijing/hourly)\\n\\n[Need some help?](/weather/ext-help.html)\\n\\nAdvertising\\n\\nHow was your experience? Thank you for your feedback![](# \\\"Vote Good\\\")[](# \\\"Vote Bad\\\")\\n\\n[Contact Us](https://www.timeanddate.com/information/feedback.html?url=/weather/china/beijing/ext)\\n\\n[![Illustration of a pink shield with a white heart.](//c.tadst.com/gfx/n/i/service__supporter.svg)](/services/supporter.html)\\n\\n#### [Love Our Site? Become a Supporter](/services/supporter.html)\\n\\n*   Browse our site **advert free.**\\n*   Sun & Moon times **precise to the second.**\\n*   **Exclusive calendar templates** for PDF Calendar.\\n\\n[![The timeanddate logo](//c.tadst.com/gfx/n/logo/logo-2021--horizontal-color-whitebg.svg)](/)\\n\\n[© Time and Date AS 1995–2025](/information/copyright.html)\\n\\n#### Company\\n\\n*   [About us](/company/)\\n*   [Careers/Jobs](/company/jobs)\\n*   [Contact Us](https://www.timeanddate.com/information/feedback.html?url=/weather/china/beijing/ext)\\n*   [Contact Details](/information/contact.html)\\n*   [Sitemap](/sitemap.html)\\n*   [Newsletter](/newsletter/)\\n\\n#### Legal\\n\\n*   [Link policy](/information/copyright.html)\\n*   [Advertising](/information/advertising.html)\\n*   [Disclaimer](/information/disclaimer.html)\\n*   [Terms & Conditions](/information/terms-conditions.html)\\n*   [Privacy Policy](/information/privacy.html)\\n*   [Do Not Sell My Info](/information/privacy.html)\\n*   [Privacy Settings](/custom/privacy.html)\\n\\n#### Services\\n\\n*   [World Clock](/worldclock/)\\n*   [Time Zones](/time/)\\n*   [Calendar](/calendar/)\\n*   [Weather](/weather/)\\n*   [Sun & Moon](/astronomy/)\\n*   [Timers](/counters/)\\n*   [Calculators](/date/)\\n*   [API](/services/api/)\\n*   [Apps](/extra/)\\n*   [RSS Feeds](/information/rss-feeds.html)\\n\\n#### Sites\\n\\n*   [timeanddate.no](https://www.timeanddate.no)\\n*   [timeanddate.de](https://www.timeanddate.de)\\n\\n#### Follow Us\\n\\n[](https://www.facebook.com/timeanddate/)[](https://twitter.com/timeanddate)[](https://www.linkedin.com/company/time-and-date-as/about/)[](https://www.instagram.com/timeanddatecom/)[](https://www.youtube.com/c/timeanddate)\\n\\n© Time and Date AS 1995–2025. [Privacy & Terms](/information/copyright.html)\\n\\n![](https://ad-delivery.net/px.gif?ch=2)![](https://ad.doubleclick.net/favicon.ico?ad=300x250&ad_box_=1&adnet=1&showad=1&size=250x250)![](https://ad-delivery.net/px.gif?ch=1&e=0.6183254761624293)\\n\\n0\\n\\n1\\n\\n2\\n\\n3\\n\\n4\\n\\n5\\n\\n6\\n\\n7\\n\\n8\\n\\n9\\n\\n10\\n\\n11\\n\\n12\\n\\n13\\n\\n14\\n\\n15\\n\\n16\\n\\n17\\n\\n18\\n\\n19\\n\\n20\\n\\n21\\n\\n22\\n\\n23\\n\\n24\\n\\n25\\n\\n26\\n\\n27\\n\\n28\\n\\n29\\n\\n30\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n1. [TODO] Check weather forecast for Beijing next week on a reliable weather website\n2. [TODO] Verify the accuracy of the weather information by cross-referencing with another weather source\n3. [TODO] Compile the weather information for easy understanding\n\nFollow this plan. If a step is done, move to the next step."}], "tools": [{"type": "function", "function": {"name": "web_search", "description": "⚠️ SEARCH QUERY LENGTH LIMIT: 7 WORDS MAXIMUM ⚠️\n\nSearch the web for information. For best results:\n1) Use CONCISE queries (3-5 words ideal)\n2) Include only ESSENTIAL keywords, not full questions\n3) For complex topics, use multiple simple searches instead of one long query\n4) Focus on specific terms that will appear on relevant pages", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query - MUST BE CONCISE (maximum 7 words)"}, "count": {"type": "number", "description": "Number of results to return (default: 10)"}}, "required": ["query"]}}}, {"type": "function", "function": {"name": "browser_get_markdown", "description": "[browser] Get the markdown content of the current page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_screenshot", "description": "[browser] Take a screenshot of the current page or a specific area. this tool SHOULD NOT be called unless the user requests an explicit call.", "parameters": {"type": "object", "properties": {"area": {"type": "array", "items": {"type": "number"}, "minItems": 4, "maxItems": 4, "description": "Optional area to capture as [x1, y1, x2, y2]. If not provided, captures the entire viewport."}}}}}, {"type": "function", "function": {"name": "browser_navigate", "description": "[browser] Navigate to a URL", "parameters": {"type": "object", "properties": {"url": {"type": "string"}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_click", "description": "[browser] Click an element on the page, before using the tool, use `browser_get_clickable_elements` to get the index of the element, but not call `browser_get_clickable_elements` multiple times", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to click"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_form_input_fill", "description": "[browser] Fill out an input field, before using the tool, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for input field"}, "index": {"type": "number", "description": "Index of the element to fill"}, "value": {"type": "string", "description": "Value to fill"}}, "required": ["value"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_select", "description": "[browser] Select an element on the page with index, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to select"}, "selector": {"type": "string", "description": "CSS selector for element to select"}, "value": {"type": "string", "description": "Value to select"}}, "required": ["value"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_hover", "description": "[browser] Hover an element on the page, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to hover"}, "selector": {"type": "string", "description": "CSS selector for element to hover"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_evaluate", "description": "[browser] Execute JavaScript in the browser console", "parameters": {"type": "object", "properties": {"script": {"type": "string", "description": "JavaScript code to execute"}}, "required": ["script"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_get_clickable_elements", "description": "[browser] Get the clickable or hoverable or selectable elements on the current page, don't call this tool multiple times", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_read_links", "description": "[browser] Get all links on the current page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_scroll", "description": "[browser] Scroll the page", "parameters": {"type": "object", "properties": {"amount": {"type": "number", "description": "Pixels to scroll (positive for down, negative for up), if the amount is not provided, scroll to the bottom of the page"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_go_back", "description": "[browser] Go back to the previous page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_go_forward", "description": "[browser] Go forward to the next page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_tab_list", "description": "[browser] Get the list of tabs", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_new_tab", "description": "[browser] Open a new tab", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to open in the new tab"}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_close_tab", "description": "[browser] Close the current tab", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_switch_tab", "description": "[browser] Switch to a specific tab", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Tab index to switch to"}}, "required": ["index"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_press_key", "description": "[browser] Press a key on the keyboard", "parameters": {"type": "object", "properties": {"key": {"type": "string", "enum": ["Enter", "Tab", "Escape", "Backspace", "Delete", "Insert", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12", "ArrowLeft", "ArrowRight", "ArrowUp", "ArrowDown", "PageUp", "PageDown", "Home", "End", "ShiftLeft", "ShiftRight", "ControlLeft", "ControlRight", "AltLeft", "AltRight", "MetaLeft", "MetaRight", "CapsLock", "PrintScreen", "ScrollLock", "Pause", "ContextMenu"], "description": "Name of the key to press or a character to generate, such as <PERSON><PERSON>, Tab, Escape, Backspace, Delete, Insert, F1, F2, F3, F4, F5, F6, F7, F8, F9, F10, F11, F12, ArrowLeft, ArrowRight, ArrowUp, ArrowDown, PageUp, PageDown, Home, End, ShiftLeft, ShiftRight, ControlLeft, ControlRight, AltLeft, AltRight, MetaLeft, MetaRight, CapsLock, PrintScreen, ScrollLock, Pause, ContextMenu"}}, "required": ["key"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "read_file", "description": "[filesystem] Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "read_multiple_files", "description": "[filesystem] Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file's content is returned with its path as a reference. Failed reads for individual files won't stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "write_file", "description": "[filesystem] Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "edit_file", "description": "[filesystem] Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "create_directory", "description": "[filesystem] Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "list_directory", "description": "[filesystem] Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "directory_tree", "description": "[filesystem] Get a recursive tree view of files and directories as a JSON structure. Each entry includes 'name', 'type' (file/directory), and 'children' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "move_file", "description": "[filesystem] Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "search_files", "description": "[filesystem] Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don't know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "get_file_info", "description": "[filesystem] Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "list_allowed_directories", "description": "[filesystem] Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "run_command", "description": "[commands] Run a command on this darwin machine", "parameters": {"type": "object", "properties": {"command": {"type": "string", "description": "Command with args"}, "cwd": {"type": "string", "description": "Current working directory, leave empty in most cases"}}, "required": ["command"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "run_script", "description": "[commands] Run a script on this darwin machine", "parameters": {"type": "object", "properties": {"interpreter": {"type": "string", "description": "Command with arguments. <PERSON><PERSON><PERSON> will be piped to stdin. Examples: bash, fish, zsh, python, or: bash --norc"}, "script": {"type": "string", "description": "<PERSON><PERSON><PERSON> to run"}, "cwd": {"type": "string", "description": "Current working directory, leave empty in most cases"}}, "required": ["script"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "final_answer", "description": "Generate a focused report or answer after completing research or information gathering", "parameters": {"type": "object", "properties": {"isDeepResearch": {"type": "boolean", "description": "Whether to generate a structured report (true) or simple answer (false)"}, "title": {"type": "string", "description": "Title for the report or answer"}, "format": {"type": "string", "enum": ["detailed", "concise"], "description": "Report format: detailed or concise"}}}}}, {"type": "function", "function": {"name": "start_codegen_session", "description": "[playwright] Start a new code generation session to record Playwright actions", "parameters": {"type": "object", "properties": {"options": {"type": "object", "description": "Code generation options", "properties": {"outputPath": {"type": "string", "description": "Directory path where generated tests will be saved (use absolute path)"}, "testNamePrefix": {"type": "string", "description": "Prefix to use for generated test names (default: 'GeneratedTest')"}, "includeComments": {"type": "boolean", "description": "Whether to include descriptive comments in generated tests"}}, "required": ["outputPath"]}}, "required": ["options"]}}}, {"type": "function", "function": {"name": "end_codegen_session", "description": "[playwright] End a code generation session and generate the test file", "parameters": {"type": "object", "properties": {"sessionId": {"type": "string", "description": "ID of the session to end"}}, "required": ["sessionId"]}}}, {"type": "function", "function": {"name": "get_codegen_session", "description": "[playwright] Get information about a code generation session", "parameters": {"type": "object", "properties": {"sessionId": {"type": "string", "description": "ID of the session to retrieve"}}, "required": ["sessionId"]}}}, {"type": "function", "function": {"name": "clear_codegen_session", "description": "[playwright] Clear a code generation session without generating a test", "parameters": {"type": "object", "properties": {"sessionId": {"type": "string", "description": "ID of the session to clear"}}, "required": ["sessionId"]}}}, {"type": "function", "function": {"name": "playwright_navigate", "description": "[playwright] Navigate to a URL", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to navigate to the website specified"}, "browserType": {"type": "string", "description": "Browser type to use (chromium, firefox, webkit). Defaults to chromium", "enum": ["chromium", "firefox", "webkit"]}, "width": {"type": "number", "description": "Viewport width in pixels (default: 1280)"}, "height": {"type": "number", "description": "Viewport height in pixels (default: 720)"}, "timeout": {"type": "number", "description": "Navigation timeout in milliseconds"}, "waitUntil": {"type": "string", "description": "Navigation wait condition"}, "headless": {"type": "boolean", "description": "Run browser in headless mode (default: false)"}}, "required": ["url"]}}}, {"type": "function", "function": {"name": "playwright_screenshot", "description": "[playwright] Take a screenshot of the current page or a specific element", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "Name for the screenshot"}, "selector": {"type": "string", "description": "CSS selector for element to screenshot"}, "width": {"type": "number", "description": "Width in pixels (default: 800)"}, "height": {"type": "number", "description": "Height in pixels (default: 600)"}, "storeBase64": {"type": "boolean", "description": "Store screenshot in base64 format (default: true)"}, "fullPage": {"type": "boolean", "description": "Store screenshot of the entire page (default: false)"}, "savePng": {"type": "boolean", "description": "Save screenshot as PNG file (default: false)"}, "downloadsDir": {"type": "string", "description": "Custom downloads directory path (default: user's Downloads folder)"}}, "required": ["name"]}}}, {"type": "function", "function": {"name": "playwright_click", "description": "[playwright] Click an element on the page", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for the element to click"}}, "required": ["selector"]}}}, {"type": "function", "function": {"name": "playwright_iframe_click", "description": "[playwright] Click an element in an iframe on the page", "parameters": {"type": "object", "properties": {"iframeSelector": {"type": "string", "description": "CSS selector for the iframe containing the element to click"}, "selector": {"type": "string", "description": "CSS selector for the element to click"}}, "required": ["iframeSelector", "selector"]}}}, {"type": "function", "function": {"name": "playwright_iframe_fill", "description": "[playwright] Fill an element in an iframe on the page", "parameters": {"type": "object", "properties": {"iframeSelector": {"type": "string", "description": "CSS selector for the iframe containing the element to fill"}, "selector": {"type": "string", "description": "CSS selector for the element to fill"}, "value": {"type": "string", "description": "Value to fill"}}, "required": ["iframeSelector", "selector", "value"]}}}, {"type": "function", "function": {"name": "playwright_fill", "description": "[playwright] fill out an input field", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for input field"}, "value": {"type": "string", "description": "Value to fill"}}, "required": ["selector", "value"]}}}, {"type": "function", "function": {"name": "playwright_select", "description": "[playwright] Select an element on the page with Select tag", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for element to select"}, "value": {"type": "string", "description": "Value to select"}}, "required": ["selector", "value"]}}}, {"type": "function", "function": {"name": "playwright_hover", "description": "[playwright] Hover an element on the page", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for element to hover"}}, "required": ["selector"]}}}, {"type": "function", "function": {"name": "playwright_upload_file", "description": "[playwright] Upload a file to an input[type='file'] element on the page", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for the file input element"}, "filePath": {"type": "string", "description": "Absolute path to the file to upload"}}, "required": ["selector", "filePath"]}}}, {"type": "function", "function": {"name": "playwright_evaluate", "description": "[playwright] Execute JavaScript in the browser console", "parameters": {"type": "object", "properties": {"script": {"type": "string", "description": "JavaScript code to execute"}}, "required": ["script"]}}}, {"type": "function", "function": {"name": "playwright_console_logs", "description": "[playwright] Retrieve console logs from the browser with filtering options", "parameters": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of logs to retrieve (all, error, warning, log, info, debug, exception)", "enum": ["all", "error", "warning", "log", "info", "debug", "exception"]}, "search": {"type": "string", "description": "Text to search for in logs (handles text with square brackets)"}, "limit": {"type": "number", "description": "Maximum number of logs to return"}, "clear": {"type": "boolean", "description": "Whether to clear logs after retrieval (default: false)"}}, "required": []}}}, {"type": "function", "function": {"name": "playwright_close", "description": "[playwright] Close the browser and release all resources", "parameters": {"type": "object", "properties": {}, "required": []}}}, {"type": "function", "function": {"name": "playwright_get", "description": "[playwright] Perform an HTTP GET request", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to perform GET operation"}}, "required": ["url"]}}}, {"type": "function", "function": {"name": "playwright_post", "description": "[playwright] Perform an HTTP POST request", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to perform POST operation"}, "value": {"type": "string", "description": "Data to post in the body"}, "token": {"type": "string", "description": "Bearer token for authorization"}, "headers": {"type": "object", "description": "Additional headers to include in the request", "additionalProperties": {"type": "string"}}}, "required": ["url", "value"]}}}, {"type": "function", "function": {"name": "playwright_put", "description": "[playwright] Perform an HTTP PUT request", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to perform PUT operation"}, "value": {"type": "string", "description": "Data to PUT in the body"}}, "required": ["url", "value"]}}}, {"type": "function", "function": {"name": "playwright_patch", "description": "[playwright] Perform an HTTP PATCH request", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to perform PUT operation"}, "value": {"type": "string", "description": "Data to PATCH in the body"}}, "required": ["url", "value"]}}}, {"type": "function", "function": {"name": "playwright_delete", "description": "[playwright] Perform an HTTP DELETE request", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to perform DELETE operation"}}, "required": ["url"]}}}, {"type": "function", "function": {"name": "playwright_expect_response", "description": "[playwright] Ask <PERSON><PERSON> to start waiting for a HTTP response. This tool initiates the wait operation but does not wait for its completion.", "parameters": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique & arbitrary identifier to be used for retrieving this response later with `Playwright_assert_response`."}, "url": {"type": "string", "description": "URL pattern to match in the response."}}, "required": ["id", "url"]}}}, {"type": "function", "function": {"name": "playwright_assert_response", "description": "[playwright] Wait for and validate a previously initiated HTTP response wait operation.", "parameters": {"type": "object", "properties": {"id": {"type": "string", "description": "Identifier of the HTTP response initially expected using `Playwright_expect_response`."}, "value": {"type": "string", "description": "Data to expect in the body of the HTTP response. If provided, the assertion will fail if this value is not found in the response body."}}, "required": ["id"]}}}, {"type": "function", "function": {"name": "playwright_custom_user_agent", "description": "[playwright] Set a custom User Agent for the browser", "parameters": {"type": "object", "properties": {"userAgent": {"type": "string", "description": "Custom User Agent for the Playwright browser instance"}}, "required": ["userAgent"]}}}, {"type": "function", "function": {"name": "playwright_get_visible_text", "description": "[playwright] Get the visible text content of the current page", "parameters": {"type": "object", "properties": {}, "required": []}}}, {"type": "function", "function": {"name": "playwright_get_visible_html", "description": "[playwright] Get the HTML content of the current page. By default, all <script> tags are removed from the output unless removeScripts is explicitly set to false.", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector to limit the HTML to a specific container"}, "removeScripts": {"type": "boolean", "description": "Remove all script tags from the HTML (default: true)"}, "removeComments": {"type": "boolean", "description": "Remove all HTML comments (default: false)"}, "removeStyles": {"type": "boolean", "description": "Remove all style tags from the HTML (default: false)"}, "removeMeta": {"type": "boolean", "description": "Remove all meta tags from the HTML (default: false)"}, "cleanHtml": {"type": "boolean", "description": "Perform comprehensive HTML cleaning (default: false)"}, "minify": {"type": "boolean", "description": "Minify the HTML output (default: false)"}, "maxLength": {"type": "number", "description": "Maximum number of characters to return (default: 20000)"}}, "required": []}}}, {"type": "function", "function": {"name": "playwright_go_back", "description": "[playwright] Navigate back in browser history", "parameters": {"type": "object", "properties": {}, "required": []}}}, {"type": "function", "function": {"name": "playwright_go_forward", "description": "[playwright] Navigate forward in browser history", "parameters": {"type": "object", "properties": {}, "required": []}}}, {"type": "function", "function": {"name": "playwright_drag", "description": "[playwright] Drag an element to a target location", "parameters": {"type": "object", "properties": {"sourceSelector": {"type": "string", "description": "CSS selector for the element to drag"}, "targetSelector": {"type": "string", "description": "CSS selector for the target location"}}, "required": ["sourceSelector", "targetSelector"]}}}, {"type": "function", "function": {"name": "playwright_press_key", "description": "[playwright] Press a keyboard key", "parameters": {"type": "object", "properties": {"key": {"type": "string", "description": "Key to press (e.g. 'Enter', 'ArrowDown', 'a')"}, "selector": {"type": "string", "description": "Optional CSS selector to focus before pressing key"}}, "required": ["key"]}}}, {"type": "function", "function": {"name": "playwright_save_as_pdf", "description": "[playwright] Save the current page as a PDF file", "parameters": {"type": "object", "properties": {"outputPath": {"type": "string", "description": "Directory path where PDF will be saved"}, "filename": {"type": "string", "description": "Name of the PDF file (default: page.pdf)"}, "format": {"type": "string", "description": "Page format (e.g. 'A4', 'Letter')"}, "printBackground": {"type": "boolean", "description": "Whether to print background graphics"}, "margin": {"type": "object", "description": "Page margins", "properties": {"top": {"type": "string"}, "right": {"type": "string"}, "bottom": {"type": "string"}, "left": {"type": "string"}}}}, "required": ["outputPath"]}}}, {"type": "function", "function": {"name": "playwright_click_and_switch_tab", "description": "[playwright] Click a link and switch to the newly opened tab", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for the link to click"}}, "required": ["selector"]}}}], "temperature": 0.7, "stream": true, "max_tokens": 8192, "provider": "deepseek", "thinking": {"type": "enabled"}}, "baseURL": "https://api.deepseek.com/v1"}