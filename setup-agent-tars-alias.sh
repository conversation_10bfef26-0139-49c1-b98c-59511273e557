#!/bin/bash

# Setup Agent TARS aliases in your shell profile
# Run this once: ./setup-agent-tars-alias.sh

SHELL_PROFILE=""

# Detect shell and profile file
if [[ $SHELL == *"zsh"* ]]; then
    SHELL_PROFILE="$HOME/.zshrc"
elif [[ $SHELL == *"bash"* ]]; then
    SHELL_PROFILE="$HOME/.bash_profile"
else
    echo "⚠️  Unknown shell. Please manually add aliases to your shell profile."
    exit 1
fi

echo "🔧 Setting up Agent TARS aliases in $SHELL_PROFILE"

# Backup existing profile
cp "$SHELL_PROFILE" "$SHELL_PROFILE.backup.$(date +%Y%m%d_%H%M%S)"

# Add aliases
cat >> "$SHELL_PROFILE" << 'EOF'

# Agent TARS Aliases
alias agent-tars='npx @agent-tars/cli@latest start --open'
alias agent-tars-server='npx @agent-tars/cli@latest serve'
alias agent-tars-debug='npx @agent-tars/cli@latest start --open --debug'
alias agent-tars-run='npx @agent-tars/cli@latest run'
alias agent-tars-help='npx @agent-tars/cli@latest --help'

EOF

echo "✅ Aliases added to $SHELL_PROFILE"
echo ""
echo "🔄 Reload your shell or run: source $SHELL_PROFILE"
echo ""
echo "📋 Available commands after reload:"
echo "   agent-tars        - Start with UI"
echo "   agent-tars-server - Start headless server"
echo "   agent-tars-debug  - Start with debug mode"
echo "   agent-tars-run    - Run in silent mode"
echo "   agent-tars-help   - Show help"
