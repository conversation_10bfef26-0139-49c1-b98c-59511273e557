{"mcpServers": {"chart": {"command": "node", "args": ["./node_modules/@antv/mcp-server-chart/dist/index.js"], "env": {"NODE_ENV": "production"}}}, "model": {"provider": "deepseek", "id": "deepseek-chat", "apiKey": "***********************************", "baseURL": "https://api.deepseek.com/v1"}, "workspace": {"workingDirectory": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub"}, "browser": {"control": "disabled"}, "planner": {"enable": true}, "snapshot": {"enable": true, "snapshotPath": "./snapshots"}, "toolCallEngine": "native", "stream": true, "thinking": {"type": "enabled"}}