#!/bin/bash

# Agent TARS Simple Mode - No Web Search, No Browser Control
# 专为VPN用户设计，避免所有网络冲突
# Usage: ./start-simple-no-web.sh

echo "🚀 Starting Agent TARS (Simple Mode - No Web Features)..."
echo "📊 图表生成：✅"
echo "💻 编程帮助：✅"  
echo "🤖 AI对话：✅"
echo "🔍 Web搜索：❌ 完全禁用"
echo "🌐 浏览器控制：❌ 完全禁用"
echo "📍 Web UI: http://localhost:8888"
echo ""

# 直接使用DeepSeek，不依赖环境变量
echo "🧠 使用DeepSeek模型 (编程优化)"

# 启动Agent TARS，完全禁用web功能
npx @agent-tars/cli@latest start \
  --model.provider deepseek \
  --model.id deepseek-chat \
  --model.apiKey *********************************** \
  --model.baseURL https://api.deepseek.com/v1 \
  --workspace.workingDirectory "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub" \
  --browser.control disabled \
  --open \
  --port 8888 \
  --stream

echo ""
echo "✅ Agent TARS (Simple Mode) stopped"
