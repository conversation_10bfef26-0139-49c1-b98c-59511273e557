#!/bin/bash

# Quick API Key Configuration for Agent TARS
# Usage: ./configure-api-key.sh

echo "🔑 Agent TARS API Key Configuration"
echo "=================================="
echo ""

# Check current configuration
if grep -q "your_openai_api_key_here" .env; then
    echo "❌ API key not configured yet"
else
    echo "✅ API key appears to be configured"
fi

echo ""
echo "📋 Available options:"
echo "1. Configure OpenAI API Key"
echo "2. Configure Anthropic Claude API Key"
echo "3. Use local model (Ollama)"
echo "4. View current configuration"
echo ""

read -p "Choose option (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🔗 Get your OpenAI API key from: https://platform.openai.com/api-keys"
        echo ""
        read -p "Enter your OpenAI API key: " api_key
        if [ ! -z "$api_key" ]; then
            sed -i.bak "s/OPENAI_API_KEY=your_openai_api_key_here/OPENAI_API_KEY=$api_key/" .env
            echo "✅ OpenAI API key configured!"
        else
            echo "❌ No API key entered"
        fi
        ;;
    2)
        echo ""
        echo "🔗 Get your Anthropic API key from: https://console.anthropic.com/"
        echo ""
        read -p "Enter your Anthropic API key: " api_key
        if [ ! -z "$api_key" ]; then
            sed -i.bak "s/# ANTHROPIC_API_KEY=your_anthropic_key_here/ANTHROPIC_API_KEY=$api_key/" .env
            echo "✅ Anthropic API key configured!"
            echo "ℹ️  You'll need to change the model provider in agent-tars.config.json"
        else
            echo "❌ No API key entered"
        fi
        ;;
    3)
        echo ""
        echo "🔧 To use local models, you need Ollama installed"
        echo "Visit: https://ollama.ai/"
        echo ""
        echo "After installing Ollama, you can modify agent-tars.config.json to use local models"
        ;;
    4)
        echo ""
        echo "📄 Current .env configuration:"
        cat .env
        ;;
    *)
        echo "❌ Invalid option"
        ;;
esac

echo ""
echo "🚀 After configuring, restart Agent TARS:"
echo "   ./start-agent-tars-with-chart.sh"
