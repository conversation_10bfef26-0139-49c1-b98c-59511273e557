#!/bin/bash

# Cost-Efficient API Configuration for Agent TARS
# Usage: ./configure-api-key.sh

echo "💰 Agent TARS Cost-Efficient API Configuration"
echo "=============================================="
echo ""

# Check current configuration
current_provider=$(grep "ACTIVE_PROVIDER=" .env | grep -v "^#" | cut -d'=' -f2)
if [ -z "$current_provider" ]; then
    echo "❌ No provider configured yet"
else
    echo "✅ Current provider: $current_provider"
fi

echo ""
echo "💸 COST COMPARISON (per 1M tokens):"
echo "1. 🚀 Groq (FREE tier)           - \$0.00 / \$0.00 (limited, fastest)"
echo "2. 🌐 OpenRouter (Cheapest)      - \$0.20 / \$0.20 (many free models)"
echo "3. 🧠 DeepSeek (Very Cheap)      - \$0.14 / \$0.28 (great for coding)"
echo "4. 🔮 Google Gemini (FREE tier)  - \$0.075 / \$0.30 (generous free tier)"
echo "5. 🌙 Moonshot (Chinese)         - \$0.50 / \$1.50 (good value)"
echo "6. 🤖 OpenAI GPT-4o-mini         - \$0.15 / \$0.60 (balanced)"
echo "7. 🎭 Anthropic Claude           - \$3.00 / \$15.00 (premium quality)"
echo "8. 🏠 Local Ollama (FREE)        - \$0.00 (runs on your computer)"
echo "9. 📄 View current configuration"
echo ""

read -p "Choose option (1-9): " choice

configure_provider() {
    local provider=$1
    local api_key_var=$2
    local url=$3
    local instructions=$4

    echo ""
    echo "$instructions"
    echo ""
    read -p "Enter your API key: " api_key
    if [ ! -z "$api_key" ]; then
        # Update API key
        sed -i.bak "s/${api_key_var}=your_.*_here/${api_key_var}=$api_key/" .env

        # Activate this provider
        sed -i.bak "s/^ACTIVE_PROVIDER=.*/# ACTIVE_PROVIDER=/" .env
        sed -i.bak "s/^API_KEY=.*/# API_KEY=/" .env
        sed -i.bak "s/^BASE_URL=.*/# BASE_URL=/" .env
        sed -i.bak "s/^MODEL_NAME=.*/# MODEL_NAME=/" .env

        sed -i.bak "s/# Option [0-9]: Use $provider/# Option: Use $provider/" .env
        sed -i.bak "/# Option: Use $provider/,/^$/s/^# //" .env

        echo "✅ $provider configured and activated!"
        echo "💰 This is a cost-efficient choice!"
    else
        echo "❌ No API key entered"
    fi
}

case $choice in
    1)
        configure_provider "Groq" "GROQ_API_KEY" "https://console.groq.com/keys" "🚀 Get FREE Groq API key from: https://console.groq.com/keys"
        ;;
    2)
        configure_provider "OpenRouter" "OPENROUTER_API_KEY" "https://openrouter.ai/keys" "🌐 Get OpenRouter API key from: https://openrouter.ai/keys (Many FREE models available!)"
        ;;
    3)
        configure_provider "DeepSeek" "DEEPSEEK_API_KEY" "https://platform.deepseek.com/api_keys" "🧠 Get DeepSeek API key from: https://platform.deepseek.com/api_keys (Very cheap for coding!)"
        ;;
    4)
        configure_provider "Google" "GOOGLE_API_KEY" "https://aistudio.google.com/app/apikey" "🔮 Get Google Gemini API key from: https://aistudio.google.com/app/apikey (Generous FREE tier!)"
        ;;
    5)
        configure_provider "Moonshot" "MOONSHOT_API_KEY" "https://platform.moonshot.cn/console/api-keys" "🌙 Get Moonshot API key from: https://platform.moonshot.cn/console/api-keys (Good value, Chinese support)"
        ;;
    6)
        configure_provider "OpenAI" "OPENAI_API_KEY" "https://platform.openai.com/api-keys" "🤖 Get OpenAI API key from: https://platform.openai.com/api-keys (Balanced performance)"
        ;;
    7)
        configure_provider "Anthropic" "ANTHROPIC_API_KEY" "https://console.anthropic.com/" "🎭 Get Anthropic API key from: https://console.anthropic.com/ (Premium quality)"
        ;;
    8)
        echo ""
        echo "🏠 Setting up Local Ollama (FREE)..."
        echo "📥 Install Ollama from: https://ollama.ai/"
        echo "🚀 After installation, run: ollama pull llama3.1:8b"
        echo ""
        read -p "Press Enter after installing Ollama to activate local mode..."

        # Activate Ollama
        sed -i.bak "s/^ACTIVE_PROVIDER=.*/# ACTIVE_PROVIDER=/" .env
        sed -i.bak "/# Option 8: Use Local Ollama/,/^$/s/^# //" .env
        echo "✅ Local Ollama activated! (100% FREE)"
        ;;
    9)
        echo ""
        echo "📄 Current configuration:"
        echo "========================"
        grep -E "^(ACTIVE_PROVIDER|API_KEY|BASE_URL|MODEL_NAME)=" .env | grep -v "^#"
        ;;
    *)
        echo "❌ Invalid option"
        ;;
esac

echo ""
echo "🚀 After configuring, restart Agent TARS:"
echo "   ./start-agent-tars-with-chart.sh"
