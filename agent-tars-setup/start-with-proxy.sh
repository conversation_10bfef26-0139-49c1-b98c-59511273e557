#!/bin/bash

# Agent TARS with Proxy Support for macOS + VPN
# Usage: ./start-with-proxy.sh

echo "🌐 Starting Agent TARS with Proxy Support..."
echo "📊 Chart visualization capabilities enabled"
echo "🔒 VPN/Proxy compatible mode"
echo "📍 Web UI will be available at: http://localhost:8888"
echo "⏹️  Press Ctrl+C to stop"
echo ""

# Load environment variables from .env file
if [ -f .env ]; then
    echo "🔑 Loading environment variables from .env..."
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)
    echo "✅ Environment variables loaded"
else
    echo "❌ .env file not found!"
    exit 1
fi

# Check which provider is active
echo "🤖 Active provider: $ACTIVE_PROVIDER"
echo "📝 Model: $MODEL_NAME"

# Check if API key is set
if [ -z "$API_KEY" ]; then
    echo "❌ API_KEY not set. Please run ./configure-api-key.sh first"
    exit 1
fi

# Set proxy environment variables for macOS
export HTTP_PROXY=$(scutil --proxy | grep HTTPProxy | awk '{print $3}')
export HTTPS_PROXY=$(scutil --proxy | grep HTTPSProxy | awk '{print $3}')

echo "🔒 Proxy settings detected and configured"

# Start Agent TARS with proxy-friendly browser settings
npx @agent-tars/cli@latest start \
    --config ./agent-tars.config.json \
    --open \
    --port 8888 \
    --stream \
    --browser.control gui-agent-only

echo ""
echo "✅ Agent TARS with proxy support stopped"
