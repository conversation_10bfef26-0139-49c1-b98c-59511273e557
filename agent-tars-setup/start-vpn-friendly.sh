#!/bin/bash

# Agent TARS VPN-Friendly Mode
# 保持VPN开启，禁用可能冲突的功能，保留所有核心功能
# Usage: ./start-vpn-friendly.sh

echo "🌐 Starting Agent TARS (VPN-Friendly Mode)..."
echo "🔒 VPN保持开启，Chrome正常工作"
echo "📊 图表生成功能：✅ 完全可用"
echo "💻 编程功能：✅ 完全可用"  
echo "🤖 AI对话：✅ 完全可用"
echo "🔍 Web搜索：❌ 已禁用（避免VPN冲突）"
echo "📍 Web UI: http://localhost:8888"
echo "⏹️  Press Ctrl+C to stop"
echo ""

# Load environment variables from .env file
if [ -f .env ]; then
    echo "🔑 Loading environment variables from .env..."
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)
    echo "✅ Environment variables loaded"
else
    echo "❌ .env file not found!"
    exit 1
fi

# Check which provider is active
echo "🤖 Active provider: $ACTIVE_PROVIDER"
echo "📝 Model: $MODEL_NAME"

# Check if API key is set
if [ -z "$API_KEY" ]; then
    echo "❌ API_KEY not set. Please run ./configure-api-key.sh first"
    exit 1
fi

echo ""
echo "🚀 启动中... (VPN环境优化)"

# Start Agent TARS with VPN-friendly settings
npx @agent-tars/cli@latest start \
    --config ./agent-tars-no-browser.config.json \
    --open \
    --port 8888 \
    --stream \
    --quiet

echo ""
echo "✅ Agent TARS (VPN-Friendly) stopped"
