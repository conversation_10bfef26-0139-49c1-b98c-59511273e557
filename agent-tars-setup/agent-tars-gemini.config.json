{"mcpServers": {"chart": {"command": "node", "args": ["./node_modules/@antv/mcp-server-chart/dist/index.js"], "env": {"NODE_ENV": "production"}}}, "model": {"provider": "google", "id": "gemini-1.5-flash", "apiKey": "${GOOGLE_API_KEY}", "baseURL": "https://generativelanguage.googleapis.com/v1beta"}, "workspace": {"workingDirectory": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub"}, "browser": {"control": "mixed"}, "planner": {"enable": true}, "snapshot": {"enable": true, "snapshotPath": "./snapshots"}, "toolCallEngine": "native", "stream": true, "thinking": {"type": "enabled"}}