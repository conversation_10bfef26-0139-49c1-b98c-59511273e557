// 搜索引擎可用性测试脚本
const { chromium } = require('playwright');

async function testSearchEngines() {
    console.log('🔍 Testing search engines with current proxy setup...\n');
    
    const browser = await chromium.launch({
        headless: true,
        proxy: {
            server: 'http://127.0.0.1:7890'
        },
        args: [
            '--disable-blink-features=AutomationControlled',
            '--disable-dev-shm-usage',
            '--no-first-run'
        ]
    });
    
    const context = await browser.newContext({
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        viewport: { width: 1366, height: 768 }
    });
    
    const searchEngines = [
        {
            name: 'DuckDuckG<PERSON>',
            url: 'https://duckduckgo.com/?q=test+search',
            selector: '[data-testid="result"]',
            timeout: 10000
        },
        {
            name: '<PERSON>',
            url: 'https://www.bing.com/search?q=test+search',
            selector: '.b_algo',
            timeout: 15000
        },
        {
            name: '百度',
            url: 'https://www.baidu.com/s?wd=test+search',
            selector: '.result',
            timeout: 10000
        }
    ];
    
    for (const engine of searchEngines) {
        const page = await context.newPage();
        
        try {
            console.log(`Testing ${engine.name}...`);
            
            await page.goto(engine.url, { 
                waitUntil: 'networkidle',
                timeout: engine.timeout 
            });
            
            // 等待搜索结果
            await page.waitForSelector(engine.selector, { 
                timeout: engine.timeout 
            });
            
            const results = await page.$$(engine.selector);
            console.log(`✅ ${engine.name}: ${results.length} results found`);
            
        } catch (error) {
            console.log(`❌ ${engine.name}: ${error.message}`);
        }
        
        await page.close();
        
        // 间隔避免被检测
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    await browser.close();
    console.log('\n🏁 Search engine testing completed');
}

testSearchEngines().catch(console.error);
