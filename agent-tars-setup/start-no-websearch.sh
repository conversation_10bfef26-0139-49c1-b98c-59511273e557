#!/bin/bash

# Agent TARS without Web Search (VPN-friendly)
# Usage: ./start-no-websearch.sh

echo "🚀 Starting Agent TARS (No Web Search Mode)..."
echo "📊 Chart visualization capabilities enabled"
echo "🔒 VPN-friendly mode (no browser control)"
echo "📍 Web UI will be available at: http://localhost:8888"
echo "⏹️  Press Ctrl+C to stop"
echo ""

# Load environment variables from .env file
if [ -f .env ]; then
    echo "🔑 Loading environment variables from .env..."
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)
    echo "✅ Environment variables loaded"
else
    echo "❌ .env file not found!"
    exit 1
fi

# Check which provider is active
echo "🤖 Active provider: $ACTIVE_PROVIDER"
echo "📝 Model: $MODEL_NAME"

# Check if API key is set
if [ -z "$API_KEY" ]; then
    echo "❌ API_KEY not set. Please run ./configure-api-key.sh first"
    exit 1
fi

echo "ℹ️  Web search disabled to avoid VPN conflicts"
echo "✅ Chart generation, coding, and analysis still available"

# Start Agent TARS without browser control
npx @agent-tars/cli@latest start \
    --config ./agent-tars-no-browser.config.json \
    --open \
    --port 8888 \
    --stream

echo ""
echo "✅ Agent TARS (no web search) stopped"
