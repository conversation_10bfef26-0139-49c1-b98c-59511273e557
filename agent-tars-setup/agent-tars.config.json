{"mcpServers": {"chart": {"command": "node", "args": ["./node_modules/@antv/mcp-server-chart/dist/index.js"], "env": {"NODE_ENV": "production"}}}, "model": {"provider": "openai", "id": "gpt-4o-mini", "apiKey": "${OPENAI_API_KEY}", "baseURL": "${OPENAI_BASE_URL}"}, "workspace": {"workingDirectory": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub"}, "browser": {"control": "mixed"}, "planner": {"enable": true}, "snapshot": {"enable": true, "snapshotPath": "./snapshots"}, "toolCallEngine": "native", "stream": true, "thinking": {"type": "enabled"}}