{"mcpServers": {"chart": {"command": "node", "args": ["./node_modules/@antv/mcp-server-chart/dist/index.js"], "env": {"NODE_ENV": "production"}}, "playwright": {"command": "node", "args": ["./node_modules/@executeautomation/playwright-mcp-server/dist/index.js"], "env": {"NODE_ENV": "production", "PLAYWRIGHT_HEADLESS": "true", "PLAYWRIGHT_BROWSERS_PATH": "./playwright-browsers", "HTTP_PROXY": "http://127.0.0.1:7890", "HTTPS_PROXY": "http://127.0.0.1:7890", "PLAYWRIGHT_USER_AGENT": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "PLAYWRIGHT_VIEWPORT": "1366x768", "PLAYWRIGHT_DELAY": "1500", "PLAYWRIGHT_ARGS": "--disable-blink-features=AutomationControlled,--disable-dev-shm-usage,--no-first-run"}}}, "model": {"provider": "deepseek", "id": "deepseek-chat", "apiKey": "***********************************", "baseURL": "https://api.deepseek.com/v1"}, "workspace": {"workingDirectory": "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub"}, "browser": {"control": "disabled"}, "planner": {"enable": true}, "snapshot": {"enable": true, "snapshotPath": "./snapshots"}, "toolCallEngine": "native", "stream": true, "thinking": {"type": "enabled"}}