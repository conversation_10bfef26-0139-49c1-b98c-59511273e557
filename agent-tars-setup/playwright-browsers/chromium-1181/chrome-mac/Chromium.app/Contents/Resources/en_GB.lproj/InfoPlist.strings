"Chromium Shortcut" = "Chromium shortcut";
CFBundleGetInfoString = "Chromium 139.0.7258.5, Copyright 2025 The Chromium Authors. All rights reserved.";
NSAudioCaptureUsageDescription = "Once Chromium has access, websites will be able to ask you for access.";
NSBluetoothAlwaysUsageDescription = "Once Chromium has access, websites will be able to ask you for access.";
NSBluetoothPeripheralUsageDescription = "Once Chromium has access, websites will be able to ask you for access.";
NSCameraUsageDescription = "Once Chromium has access, websites will be able to ask you for access.";
NSHumanReadableCopyright = "Copyright 2025 The Chromium Authors. All rights reserved.";
NSLocalNetworkUsageDescription = "This will allow you to select from available devices and display content on them.";
NSLocationUsageDescription = "Once Chromium has access, websites will be able to ask you for access.";
NSMicrophoneUsageDescription = "Once Chromium has access, websites will be able to ask you for access.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Once Chromium has access, websites will be able to ask you for access.";
