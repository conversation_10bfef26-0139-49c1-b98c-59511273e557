# 🔍 Agent TARS 搜索使用指南

## 🚨 **Google搜索问题解决方案**

### **问题原因：**
- VPN + 自动化工具被Google识别为机器人
- IPv6网络配置触发额外验证
- 频繁请求导致临时封禁

## 🎯 **推荐搜索策略**

### **1. 使用替代搜索引擎** ⭐ (最推荐)

#### **Bing搜索 (Microsoft)**
```
"用Bing搜索最新的AI技术发展"
"search bing: latest AI technology developments"
```

#### **DuckDuckGo (隐私友好)**
```
"用DuckDuckGo搜索Python教程"
"search duckduckgo: Python tutorials"
```

#### **百度搜索 (中文优化)**
```
"用百度搜索人工智能最新进展"
"search baidu: 人工智能 最新进展"
```

### **2. 直接访问网站**
```
"访问https://github.com/trending查看热门项目"
"browse: https://news.ycombinator.com"
```

### **3. 特定网站搜索**
```
"在GitHub上搜索机器学习项目"
"访问stackoverflow.com搜索Python问题"
```

## 🔧 **搜索技巧**

### **避免触发反机器人检测：**
1. **间隔搜索** - 不要连续快速搜索
2. **变换关键词** - 使用不同表达方式
3. **使用多个引擎** - 轮换使用不同搜索引擎
4. **直接访问** - 已知网址直接访问

### **有效的搜索命令：**
```bash
# 推荐格式
"帮我用Bing搜索关于机器学习的最新论文"
"请访问https://arxiv.org并查找AI相关论文"
"用DuckDuckGo搜索开源项目推荐"

# 避免的格式
"搜索..." (可能默认使用Google)
"google search..." (直接触发Google)
```

## 🌐 **可用搜索引擎对比**

| **搜索引擎** | **优势** | **适用场景** | **反机器人** |
|-------------|----------|-------------|-------------|
| **Bing** | 结果质量高，对自动化友好 | 英文搜索，技术内容 | 较宽松 |
| **DuckDuckGo** | 隐私保护，无跟踪 | 隐私敏感搜索 | 很宽松 |
| **百度** | 中文内容丰富 | 中文搜索，本土信息 | 较宽松 |
| **Yandex** | 国际内容 | 多语言搜索 | 较宽松 |
| **Google** | 结果最全面 | 一般搜索 | 很严格 ❌ |

## 🚀 **启动命令**

### **智能搜索模式：**
```bash
./start-smart-search.sh
```

### **测试搜索功能：**
启动后测试这些命令：
```
"用Bing搜索今天的科技新闻"
"访问https://github.com/trending查看热门项目"
"用DuckDuckGo搜索Python数据分析教程"
```

## 💡 **故障排除**

### **如果搜索仍然失败：**
1. **检查网络** - 确认VPN正常工作
2. **更换搜索引擎** - 尝试不同的搜索引擎
3. **直接访问** - 使用browse命令直接访问网站
4. **手动搜索** - 在浏览器中搜索，然后让Agent TARS分析结果

### **应急方案：**
```bash
# 如果所有搜索都失败，使用无搜索模式
./start-simple-no-web.sh
# 然后手动提供搜索结果给Agent TARS分析
```

**记住：避开Google，使用Bing和DuckDuckGo，搜索功能就能正常工作！🔍✨**
