#!/bin/bash

# Agent TARS Simple Search Mode
# 简化版本，避免复杂参数
# Usage: ./start-simple-search.sh

echo "🔍 Starting Agent TARS (Simple Search Mode)..."
echo "🦆 DuckDuckGo搜索：✅ 可用"
echo "📊 图表生成：✅ 完全可用"
echo "💻 编程帮助：✅ DeepSeek优化"
echo "🌐 网页浏览：✅ 支持VPN"
echo "📍 Web UI: http://localhost:8888"
echo ""

# 检查Playwright浏览器
if [ ! -d "./playwright-browsers" ]; then
    echo "📥 Installing Playwright browsers (first time)..."
    npx playwright install chromium
    echo "✅ Playwright browsers installed"
fi

echo "🚀 Starting Agent TARS..."

# 简单启动，使用基本配置
npx @agent-tars/cli@latest start \
    --config ./agent-tars-with-playwright.config.json \
    --open \
    --port 8888

echo ""
echo "✅ Agent TARS stopped"
