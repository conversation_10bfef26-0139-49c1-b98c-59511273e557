#!/bin/bash

# Agent TARS with DuckDuckGo Primary Search
# DuckDuckGo作为主要搜索引擎，避开Bing的问题
# Usage: ./start-duckduckgo-primary.sh

echo "🦆 Starting Agent TARS with DuckDuckGo Primary Search..."
echo "🔍 主要搜索：DuckDuckGo (已验证可用)"
echo "🔄 备用搜索：百度 (中文内容)"
echo "🌐 直接浏览：任何网站"
echo "📊 图表生成：✅ 完全可用"
echo "💻 编程帮助：✅ DeepSeek优化"
echo "🔒 VPN兼容：✅ 代理127.0.0.1:7890"
echo "📍 Web UI: http://localhost:8888"
echo "⏹️  Press Ctrl+C to stop"
echo ""

# 设置环境变量，优化DuckDuckGo搜索
export PLAYWRIGHT_BROWSERS_PATH=./playwright-browsers
export PLAYWRIGHT_HEADLESS=true
export HTTP_PROXY=http://127.0.0.1:7890
export HTTPS_PROXY=http://127.0.0.1:7890

# 设置更真实的浏览器环境
export PLAYWRIGHT_USER_AGENT="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
export PLAYWRIGHT_VIEWPORT="1366x768"
export PLAYWRIGHT_DELAY="1500"

# 检查Playwright浏览器
if [ ! -d "./playwright-browsers" ]; then
    echo "📥 Installing Playwright browsers..."
    npx playwright install chromium --with-deps
    echo "✅ Playwright browsers installed"
fi

echo "🦆 DuckDuckGo优化配置："
echo "   ✅ 隐私友好，无跟踪"
echo "   ✅ 对自动化工具友好"
echo "   ✅ 无验证码问题"
echo "   ✅ 支持VPN代理"
echo ""
echo "💡 推荐搜索方式："
echo "   '搜索最新AI技术' (默认使用DuckDuckGo)"
echo "   '用百度搜索中文内容'"
echo "   '访问https://example.com'"
echo ""
echo "🚀 Starting Agent TARS..."

# 启动Agent TARS
npx @agent-tars/cli@latest start \
    --config ./agent-tars-with-playwright.config.json \
    --open \
    --port 8888 \
    --stream \
    --browser.args "--disable-blink-features=AutomationControlled" \
    --browser.args "--disable-dev-shm-usage" \
    --browser.args "--no-first-run"

echo ""
echo "✅ Agent TARS with DuckDuckGo Primary stopped"
