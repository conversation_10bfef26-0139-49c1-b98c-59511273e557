#!/bin/bash

# Agent TARS with Clash/VPN Proxy Support (127.0.0.1:7890)
# Usage: ./start-with-clash-proxy.sh

echo "🌐 Starting Agent TARS with Clash Proxy Support..."
echo "🔒 Proxy: 127.0.0.1:7890 (HTTP/HTTPS/SOCKS)"
echo "📊 Chart visualization capabilities enabled"
echo "📍 Web UI will be available at: http://localhost:8888"
echo "⏹️  Press Ctrl+C to stop"
echo ""

# Load environment variables from .env file
if [ -f .env ]; then
    echo "🔑 Loading environment variables from .env..."
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)
    echo "✅ Environment variables loaded"
else
    echo "❌ .env file not found!"
    exit 1
fi

# Check which provider is active
echo "🤖 Active provider: $ACTIVE_PROVIDER"
echo "📝 Model: $MODEL_NAME"

# Check if API key is set
if [ -z "$API_KEY" ]; then
    echo "❌ API_KEY not set. Please run ./configure-api-key.sh first"
    exit 1
fi

# Set proxy environment variables for Clash/VPN
export HTTP_PROXY=http://127.0.0.1:7890
export HTTPS_PROXY=http://127.0.0.1:7890
export ALL_PROXY=socks5://127.0.0.1:7890
export NO_PROXY=localhost,127.0.0.1,::1

echo "🔒 Clash proxy configured: HTTP/HTTPS via 127.0.0.1:7890"

# Start Agent TARS with specific browser args for proxy
npx @agent-tars/cli@latest start \
    --config ./agent-tars.config.json \
    --open \
    --port 8888 \
    --stream \
    --browser.control mixed \
    --browser.args "--proxy-server=http://127.0.0.1:7890" \
    --browser.args "--disable-web-security" \
    --browser.args "--disable-features=VizDisplayCompositor" \
    --browser.args "--ignore-certificate-errors" \
    --browser.args "--disable-extensions"

echo ""
echo "✅ Agent TARS with Clash proxy support stopped"
