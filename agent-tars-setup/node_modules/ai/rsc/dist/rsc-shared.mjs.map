{"version": 3, "sources": ["../streamable-value/streamable-value.ts", "../streamable-value/is-streamable-value.ts", "../streamable-value/read-streamable-value.tsx", "../streamable-value/use-streamable-value.tsx", "../shared-client/context.tsx", "../../util/is-function.ts"], "sourcesContent": ["export const STREAMABLE_VALUE_TYPE = Symbol.for('ui.streamable.value');\n\nexport type StreamablePatch = undefined | [0, string]; // Append string.\n\ndeclare const __internal_curr: unique symbol;\ndeclare const __internal_error: unique symbol;\n\n/**\n * StreamableValue is a value that can be streamed over the network via AI Actions.\n * To read the streamed values, use the `readStreamableValue` or `useStreamableValue` APIs.\n */\nexport type StreamableValue<T = any, E = any> = {\n  /**\n   * @internal Use `readStreamableValue` to read the values.\n   */\n  type?: typeof STREAMABLE_VALUE_TYPE;\n  /**\n   * @internal Use `readStreamableValue` to read the values.\n   */\n  curr?: T;\n  /**\n   * @internal Use `readStreamableValue` to read the values.\n   */\n  error?: E;\n  /**\n   * @internal Use `readStreamableValue` to read the values.\n   */\n  diff?: StreamablePatch;\n  /**\n   * @internal Use `readStreamableValue` to read the values.\n   */\n  next?: Promise<StreamableValue<T, E>>;\n\n  // branded types to maintain type signature after internal properties are stripped.\n  [__internal_curr]?: T;\n  [__internal_error]?: E;\n};\n", "import { STREAMABLE_VALUE_TYPE, StreamableValue } from './streamable-value';\n\nexport function isStreamableValue(value: unknown): value is StreamableValue {\n  return (\n    value != null &&\n    typeof value === 'object' &&\n    'type' in value &&\n    value.type === STREAMABLE_VALUE_TYPE\n  );\n}\n", "import { isStreamableValue } from './is-streamable-value';\nimport { StreamableValue } from './streamable-value';\n\n/**\n * `readStreamableValue` takes a streamable value created via the `createStreamableValue().value` API,\n * and returns an async iterator.\n *\n * ```js\n * // Inside your AI action:\n *\n * async function action() {\n *   'use server'\n *   const streamable = createStreamableValue();\n *\n *   streamable.update(1);\n *   streamable.update(2);\n *   streamable.done(3);\n *   // ...\n *   return streamable.value;\n * }\n * ```\n *\n * And to read the value:\n *\n * ```js\n * const streamableValue = await action()\n * for await (const v of readStreamableValue(streamableValue)) {\n *   console.log(v)\n * }\n * ```\n *\n * This logs out 1, 2, 3 on console.\n */\nexport function readStreamableValue<T = unknown>(\n  streamableValue: StreamableValue<T>,\n): AsyncIterable<T | undefined> {\n  if (!isStreamableValue(streamableValue)) {\n    throw new Error(\n      'Invalid value: this hook only accepts values created via `createStreamableValue`.',\n    );\n  }\n\n  return {\n    [Symbol.asyncIterator]() {\n      let row: StreamableValue<T> | Promise<StreamableValue<T>> =\n        streamableValue;\n      let value = row.curr; // the current value\n      let isDone = false;\n      let isFirstIteration = true;\n\n      return {\n        async next() {\n          // the iteration is done already, return the last value:\n          if (isDone) return { value, done: true };\n\n          // resolve the promise at the beginning of each iteration:\n          row = await row;\n\n          // throw error if any:\n          if (row.error !== undefined) {\n            throw row.error;\n          }\n\n          // if there is a value or a patch, use it:\n          if ('curr' in row || row.diff) {\n            if (row.diff) {\n              // streamable patch (text only):\n              if (row.diff[0] === 0) {\n                if (typeof value !== 'string') {\n                  throw new Error(\n                    'Invalid patch: can only append to string types. This is a bug in the AI SDK.',\n                  );\n                }\n\n                // casting required to remove T & string limitation\n                (value as string) = value + row.diff[1];\n              }\n            } else {\n              // replace the value (full new value)\n              value = row.curr;\n            }\n\n            // The last emitted { done: true } won't be used as the value\n            // by the for await...of syntax.\n            if (!row.next) {\n              isDone = true;\n              return { value, done: false };\n            }\n          }\n\n          // there are no further rows to iterate over:\n          if (row.next === undefined) {\n            return { value, done: true };\n          }\n\n          row = row.next;\n\n          if (isFirstIteration) {\n            isFirstIteration = false; // TODO should this be set for every return?\n\n            if (value === undefined) {\n              // This is the initial chunk and there isn't an initial value yet.\n              // Let's skip this one.\n              return this.next();\n            }\n          }\n\n          return { value, done: false };\n        },\n      };\n    },\n  };\n}\n", "import { startTransition, useLayoutEffect, useState } from 'react';\nimport { readStreamableValue } from './read-streamable-value';\nimport { StreamableValue } from './streamable-value';\nimport { isStreamableValue } from './is-streamable-value';\n\nfunction checkStreamableValue(value: unknown): value is StreamableValue {\n  const hasSignature = isStreamableValue(value);\n\n  if (!hasSignature && value !== undefined) {\n    throw new Error(\n      'Invalid value: this hook only accepts values created via `createStreamableValue`.',\n    );\n  }\n\n  return hasSignature;\n}\n\n/**\n * `useStreamableValue` is a React hook that takes a streamable value created via the `createStreamableValue().value` API,\n * and returns the current value, error, and pending state.\n *\n * This is useful for consuming streamable values received from a component's props. For example:\n *\n * ```js\n * function MyComponent({ streamableValue }) {\n *   const [data, error, pending] = useStreamableValue(streamableValue);\n *\n *   if (pending) return <div>Loading...</div>;\n *   if (error) return <div>Error: {error.message}</div>;\n *\n *   return <div>Data: {data}</div>;\n * }\n * ```\n */\nexport function useStreamableValue<T = unknown, Error = unknown>(\n  streamableValue?: StreamableValue<T>,\n): [data: T | undefined, error: Error | undefined, pending: boolean] {\n  const [curr, setCurr] = useState<T | undefined>(\n    checkStreamableValue(streamableValue) ? streamableValue.curr : undefined,\n  );\n  const [error, setError] = useState<Error | undefined>(\n    checkStreamableValue(streamableValue) ? streamableValue.error : undefined,\n  );\n  const [pending, setPending] = useState<boolean>(\n    checkStreamableValue(streamableValue) ? !!streamableValue.next : false,\n  );\n\n  useLayoutEffect(() => {\n    if (!checkStreamableValue(streamableValue)) return;\n\n    let cancelled = false;\n\n    const iterator = readStreamableValue(streamableValue);\n    if (streamableValue.next) {\n      startTransition(() => {\n        if (cancelled) return;\n        setPending(true);\n      });\n    }\n\n    (async () => {\n      try {\n        for await (const value of iterator) {\n          if (cancelled) return;\n          startTransition(() => {\n            if (cancelled) return;\n            setCurr(value);\n          });\n        }\n      } catch (e) {\n        if (cancelled) return;\n        startTransition(() => {\n          if (cancelled) return;\n          setError(e as Error);\n        });\n      } finally {\n        if (cancelled) return;\n        startTransition(() => {\n          if (cancelled) return;\n          setPending(false);\n        });\n      }\n    })();\n\n    return () => {\n      cancelled = true;\n    };\n  }, [streamableValue]);\n\n  return [curr, error, pending];\n}\n", "/* eslint-disable react-hooks/exhaustive-deps */\n'use client';\n\nimport * as React from 'react';\n\nimport * as jsondiffpatch from 'jsondiffpatch';\nimport { isFunction } from '../../util/is-function';\nimport type {\n  AIProvider,\n  InferActions,\n  InferAIState,\n  InferUIState,\n  InternalAIProviderProps,\n  ValueOrUpdater,\n} from '../types';\n\nconst InternalUIStateProvider = React.createContext<null | any>(null);\nconst InternalAIStateProvider = React.createContext<undefined | any>(undefined);\nconst InternalActionProvider = React.createContext<null | any>(null);\nconst InternalSyncUIStateProvider = React.createContext<null | any>(null);\n\nexport function InternalAIProvider({\n  children,\n  initialUIState,\n  initialAIState,\n  initialAIStatePatch,\n  wrappedActions,\n  wrappedSyncUIState,\n}: InternalAIProviderProps) {\n  if (!('use' in React)) {\n    throw new Error('Unsupported React version.');\n  }\n\n  const uiState = React.useState(initialUIState);\n  const setUIState = uiState[1];\n\n  const resolvedInitialAIStatePatch = initialAIStatePatch\n    ? (React as any).use(initialAIStatePatch)\n    : undefined;\n  initialAIState = React.useMemo(() => {\n    if (resolvedInitialAIStatePatch) {\n      return jsondiffpatch.patch(\n        jsondiffpatch.clone(initialAIState),\n        resolvedInitialAIStatePatch,\n      );\n    }\n    return initialAIState;\n  }, [initialAIState, resolvedInitialAIStatePatch]);\n\n  const aiState = React.useState(initialAIState);\n  const setAIState = aiState[1];\n  const aiStateRef = React.useRef(aiState[0]);\n\n  React.useEffect(() => {\n    aiStateRef.current = aiState[0];\n  }, [aiState[0]]);\n\n  const clientWrappedActions = React.useMemo(\n    () =>\n      Object.fromEntries(\n        Object.entries(wrappedActions).map(([key, action]) => [\n          key,\n          async (...args: any) => {\n            const aiStateSnapshot = aiStateRef.current;\n            const [aiStateDelta, result] = await action(\n              aiStateSnapshot,\n              ...args,\n            );\n            (async () => {\n              const delta = await aiStateDelta;\n              if (delta !== undefined) {\n                aiState[1](\n                  jsondiffpatch.patch(\n                    jsondiffpatch.clone(aiStateSnapshot),\n                    delta,\n                  ),\n                );\n              }\n            })();\n            return result;\n          },\n        ]),\n      ),\n    [wrappedActions],\n  );\n\n  const clientWrappedSyncUIStateAction = React.useMemo(() => {\n    if (!wrappedSyncUIState) {\n      return () => {};\n    }\n\n    return async () => {\n      const aiStateSnapshot = aiStateRef.current;\n      const [aiStateDelta, uiState] =\n        await wrappedSyncUIState!(aiStateSnapshot);\n\n      if (uiState !== undefined) {\n        setUIState(uiState);\n      }\n\n      const delta = await aiStateDelta;\n      if (delta !== undefined) {\n        const patchedAiState = jsondiffpatch.patch(\n          jsondiffpatch.clone(aiStateSnapshot),\n          delta,\n        );\n        setAIState(patchedAiState);\n      }\n    };\n  }, [wrappedSyncUIState]);\n\n  return (\n    <InternalAIStateProvider.Provider value={aiState}>\n      <InternalUIStateProvider.Provider value={uiState}>\n        <InternalActionProvider.Provider value={clientWrappedActions}>\n          <InternalSyncUIStateProvider.Provider\n            value={clientWrappedSyncUIStateAction}\n          >\n            {children}\n          </InternalSyncUIStateProvider.Provider>\n        </InternalActionProvider.Provider>\n      </InternalUIStateProvider.Provider>\n    </InternalAIStateProvider.Provider>\n  );\n}\n\nexport function useUIState<AI extends AIProvider = any>() {\n  type T = InferUIState<AI, any>;\n\n  const state = React.useContext<\n    [T, (v: T | ((v_: T) => T)) => void] | null | undefined\n  >(InternalUIStateProvider);\n  if (state === null) {\n    throw new Error('`useUIState` must be used inside an <AI> provider.');\n  }\n  if (!Array.isArray(state)) {\n    throw new Error('Invalid state');\n  }\n  if (state[0] === undefined) {\n    throw new Error(\n      '`initialUIState` must be provided to `createAI` or `<AI>`',\n    );\n  }\n  return state;\n}\n\n// TODO: How do we avoid causing a re-render when the AI state changes but you\n// are only listening to a specific key? We need useSES perhaps?\nfunction useAIState<AI extends AIProvider = any>(): [\n  InferAIState<AI, any>,\n  (newState: ValueOrUpdater<InferAIState<AI, any>>) => void,\n];\nfunction useAIState<AI extends AIProvider = any>(\n  key: keyof InferAIState<AI, any>,\n): [\n  InferAIState<AI, any>[typeof key],\n  (newState: ValueOrUpdater<InferAIState<AI, any>[typeof key]>) => void,\n];\nfunction useAIState<AI extends AIProvider = any>(\n  ...args: [] | [keyof InferAIState<AI, any>]\n) {\n  type T = InferAIState<AI, any>;\n\n  const state = React.useContext<\n    [T, (newState: ValueOrUpdater<T>) => void] | null | undefined\n  >(InternalAIStateProvider);\n  if (state === null) {\n    throw new Error('`useAIState` must be used inside an <AI> provider.');\n  }\n  if (!Array.isArray(state)) {\n    throw new Error('Invalid state');\n  }\n  if (state[0] === undefined) {\n    throw new Error(\n      '`initialAIState` must be provided to `createAI` or `<AI>`',\n    );\n  }\n  if (args.length >= 1 && typeof state[0] !== 'object') {\n    throw new Error(\n      'When using `useAIState` with a key, the AI state must be an object.',\n    );\n  }\n\n  const key = args[0];\n  const setter = React.useCallback(\n    typeof key === 'undefined'\n      ? state[1]\n      : (newState: ValueOrUpdater<T>) => {\n          if (isFunction(newState)) {\n            return state[1](s => {\n              return { ...s, [key]: newState(s[key]) };\n            });\n          } else {\n            return state[1]({ ...state[0], [key]: newState });\n          }\n        },\n    [key],\n  );\n\n  if (args.length === 0) {\n    return state;\n  } else {\n    return [state[0][args[0]], setter];\n  }\n}\n\nexport function useActions<AI extends AIProvider = any>() {\n  type T = InferActions<AI, any>;\n\n  const actions = React.useContext<T>(InternalActionProvider);\n  return actions;\n}\n\nexport function useSyncUIState() {\n  const syncUIState = React.useContext<() => Promise<void>>(\n    InternalSyncUIStateProvider,\n  );\n\n  if (syncUIState === null) {\n    throw new Error('`useSyncUIState` must be used inside an <AI> provider.');\n  }\n\n  return syncUIState;\n}\n\nexport { useAIState };\n", "/**\n * Checks if the given value is a function.\n *\n * @param {unknown} value - The value to check.\n * @returns {boolean} True if the value is a function, false otherwise.\n */\nexport const isFunction = (value: unknown): value is Function =>\n  typeof value === 'function';\n"], "mappings": ";;;AAAO,IAAM,wBAAwB,OAAO,IAAI,qBAAqB;;;ACE9D,SAAS,kBAAkB,OAA0C;AAC1E,SACE,SAAS,QACT,OAAO,UAAU,YACjB,UAAU,SACV,MAAM,SAAS;AAEnB;;;ACwBO,SAAS,oBACd,iBAC8B;AAC9B,MAAI,CAAC,kBAAkB,eAAe,GAAG;AACvC,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL,CAAC,OAAO,aAAa,IAAI;AACvB,UAAI,MACF;AACF,UAAI,QAAQ,IAAI;AAChB,UAAI,SAAS;AACb,UAAI,mBAAmB;AAEvB,aAAO;AAAA,QACL,MAAM,OAAO;AAEX,cAAI;AAAQ,mBAAO,EAAE,OAAO,MAAM,KAAK;AAGvC,gBAAM,MAAM;AAGZ,cAAI,IAAI,UAAU,QAAW;AAC3B,kBAAM,IAAI;AAAA,UACZ;AAGA,cAAI,UAAU,OAAO,IAAI,MAAM;AAC7B,gBAAI,IAAI,MAAM;AAEZ,kBAAI,IAAI,KAAK,CAAC,MAAM,GAAG;AACrB,oBAAI,OAAO,UAAU,UAAU;AAC7B,wBAAM,IAAI;AAAA,oBACR;AAAA,kBACF;AAAA,gBACF;AAGA,gBAAC,QAAmB,QAAQ,IAAI,KAAK,CAAC;AAAA,cACxC;AAAA,YACF,OAAO;AAEL,sBAAQ,IAAI;AAAA,YACd;AAIA,gBAAI,CAAC,IAAI,MAAM;AACb,uBAAS;AACT,qBAAO,EAAE,OAAO,MAAM,MAAM;AAAA,YAC9B;AAAA,UACF;AAGA,cAAI,IAAI,SAAS,QAAW;AAC1B,mBAAO,EAAE,OAAO,MAAM,KAAK;AAAA,UAC7B;AAEA,gBAAM,IAAI;AAEV,cAAI,kBAAkB;AACpB,+BAAmB;AAEnB,gBAAI,UAAU,QAAW;AAGvB,qBAAO,KAAK,KAAK;AAAA,YACnB;AAAA,UACF;AAEA,iBAAO,EAAE,OAAO,MAAM,MAAM;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;;;AChHA,SAAS,iBAAiB,iBAAiB,gBAAgB;AAK3D,SAAS,qBAAqB,OAA0C;AACtE,QAAM,eAAe,kBAAkB,KAAK;AAE5C,MAAI,CAAC,gBAAgB,UAAU,QAAW;AACxC,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAmBO,SAAS,mBACd,iBACmE;AACnE,QAAM,CAAC,MAAM,OAAO,IAAI;AAAA,IACtB,qBAAqB,eAAe,IAAI,gBAAgB,OAAO;AAAA,EACjE;AACA,QAAM,CAAC,OAAO,QAAQ,IAAI;AAAA,IACxB,qBAAqB,eAAe,IAAI,gBAAgB,QAAQ;AAAA,EAClE;AACA,QAAM,CAAC,SAAS,UAAU,IAAI;AAAA,IAC5B,qBAAqB,eAAe,IAAI,CAAC,CAAC,gBAAgB,OAAO;AAAA,EACnE;AAEA,kBAAgB,MAAM;AACpB,QAAI,CAAC,qBAAqB,eAAe;AAAG;AAE5C,QAAI,YAAY;AAEhB,UAAM,WAAW,oBAAoB,eAAe;AACpD,QAAI,gBAAgB,MAAM;AACxB,sBAAgB,MAAM;AACpB,YAAI;AAAW;AACf,mBAAW,IAAI;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,KAAC,YAAY;AACX,UAAI;AACF,yBAAiB,SAAS,UAAU;AAClC,cAAI;AAAW;AACf,0BAAgB,MAAM;AACpB,gBAAI;AAAW;AACf,oBAAQ,KAAK;AAAA,UACf,CAAC;AAAA,QACH;AAAA,MACF,SAAS,GAAG;AACV,YAAI;AAAW;AACf,wBAAgB,MAAM;AACpB,cAAI;AAAW;AACf,mBAAS,CAAU;AAAA,QACrB,CAAC;AAAA,MACH,UAAE;AACA,YAAI;AAAW;AACf,wBAAgB,MAAM;AACpB,cAAI;AAAW;AACf,qBAAW,KAAK;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF,GAAG;AAEH,WAAO,MAAM;AACX,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,eAAe,CAAC;AAEpB,SAAO,CAAC,MAAM,OAAO,OAAO;AAC9B;;;ACvFA,YAAY,WAAW;AAEvB,YAAY,mBAAmB;;;ACCxB,IAAM,aAAa,CAAC,UACzB,OAAO,UAAU;;;AD4GT;AAnGV,IAAM,0BAAgC,oBAA0B,IAAI;AACpE,IAAM,0BAAgC,oBAA+B,MAAS;AAC9E,IAAM,yBAA+B,oBAA0B,IAAI;AACnE,IAAM,8BAAoC,oBAA0B,IAAI;AAEjE,SAAS,mBAAmB;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAA4B;AAC1B,MAAI,EAAE,SAAS,QAAQ;AACrB,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC9C;AAEA,QAAM,UAAgB,eAAS,cAAc;AAC7C,QAAM,aAAa,QAAQ,CAAC;AAE5B,QAAM,8BAA8B,sBACjB,UAAI,mBAAmB,IACtC;AACJ,mBAAuB,cAAQ,MAAM;AACnC,QAAI,6BAA6B;AAC/B,aAAqB;AAAA,QACL,oBAAM,cAAc;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,GAAG,CAAC,gBAAgB,2BAA2B,CAAC;AAEhD,QAAM,UAAgB,eAAS,cAAc;AAC7C,QAAM,aAAa,QAAQ,CAAC;AAC5B,QAAM,aAAmB,aAAO,QAAQ,CAAC,CAAC;AAE1C,EAAM,gBAAU,MAAM;AACpB,eAAW,UAAU,QAAQ,CAAC;AAAA,EAChC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;AAEf,QAAM,uBAA6B;AAAA,IACjC,MACE,OAAO;AAAA,MACL,OAAO,QAAQ,cAAc,EAAE,IAAI,CAAC,CAAC,KAAK,MAAM,MAAM;AAAA,QACpD;AAAA,QACA,UAAU,SAAc;AACtB,gBAAM,kBAAkB,WAAW;AACnC,gBAAM,CAAC,cAAc,MAAM,IAAI,MAAM;AAAA,YACnC;AAAA,YACA,GAAG;AAAA,UACL;AACA,WAAC,YAAY;AACX,kBAAM,QAAQ,MAAM;AACpB,gBAAI,UAAU,QAAW;AACvB,sBAAQ,CAAC;AAAA,gBACO;AAAA,kBACE,oBAAM,eAAe;AAAA,kBACnC;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF,GAAG;AACH,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA,IACF,CAAC,cAAc;AAAA,EACjB;AAEA,QAAM,iCAAuC,cAAQ,MAAM;AACzD,QAAI,CAAC,oBAAoB;AACvB,aAAO,MAAM;AAAA,MAAC;AAAA,IAChB;AAEA,WAAO,YAAY;AACjB,YAAM,kBAAkB,WAAW;AACnC,YAAM,CAAC,cAAcA,QAAO,IAC1B,MAAM,mBAAoB,eAAe;AAE3C,UAAIA,aAAY,QAAW;AACzB,mBAAWA,QAAO;AAAA,MACpB;AAEA,YAAM,QAAQ,MAAM;AACpB,UAAI,UAAU,QAAW;AACvB,cAAM,iBAA+B;AAAA,UACrB,oBAAM,eAAe;AAAA,UACnC;AAAA,QACF;AACA,mBAAW,cAAc;AAAA,MAC3B;AAAA,IACF;AAAA,EACF,GAAG,CAAC,kBAAkB,CAAC;AAEvB,SACE,oBAAC,wBAAwB,UAAxB,EAAiC,OAAO,SACvC,8BAAC,wBAAwB,UAAxB,EAAiC,OAAO,SACvC,8BAAC,uBAAuB,UAAvB,EAAgC,OAAO,sBACtC;AAAA,IAAC,4BAA4B;AAAA,IAA5B;AAAA,MACC,OAAO;AAAA,MAEN;AAAA;AAAA,EACH,GACF,GACF,GACF;AAEJ;AAEO,SAAS,aAA0C;AAGxD,QAAM,QAAc,iBAElB,uBAAuB;AACzB,MAAI,UAAU,MAAM;AAClB,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACtE;AACA,MAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AACA,MAAI,MAAM,CAAC,MAAM,QAAW;AAC1B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAcA,SAAS,cACJ,MACH;AAGA,QAAM,QAAc,iBAElB,uBAAuB;AACzB,MAAI,UAAU,MAAM;AAClB,UAAM,IAAI,MAAM,oDAAoD;AAAA,EACtE;AACA,MAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,UAAM,IAAI,MAAM,eAAe;AAAA,EACjC;AACA,MAAI,MAAM,CAAC,MAAM,QAAW;AAC1B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK,UAAU,KAAK,OAAO,MAAM,CAAC,MAAM,UAAU;AACpD,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,QAAM,MAAM,KAAK,CAAC;AAClB,QAAM,SAAe;AAAA,IACnB,OAAO,QAAQ,cACX,MAAM,CAAC,IACP,CAAC,aAAgC;AAC/B,UAAI,WAAW,QAAQ,GAAG;AACxB,eAAO,MAAM,CAAC,EAAE,OAAK;AACnB,iBAAO,EAAE,GAAG,GAAG,CAAC,GAAG,GAAG,SAAS,EAAE,GAAG,CAAC,EAAE;AAAA,QACzC,CAAC;AAAA,MACH,OAAO;AACL,eAAO,MAAM,CAAC,EAAE,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,SAAS,CAAC;AAAA,MAClD;AAAA,IACF;AAAA,IACJ,CAAC,GAAG;AAAA,EACN;AAEA,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT,OAAO;AACL,WAAO,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,MAAM;AAAA,EACnC;AACF;AAEO,SAAS,aAA0C;AAGxD,QAAM,UAAgB,iBAAc,sBAAsB;AAC1D,SAAO;AACT;AAEO,SAAS,iBAAiB;AAC/B,QAAM,cAAoB;AAAA,IACxB;AAAA,EACF;AAEA,MAAI,gBAAgB,MAAM;AACxB,UAAM,IAAI,MAAM,wDAAwD;AAAA,EAC1E;AAEA,SAAO;AACT;", "names": ["uiState"]}