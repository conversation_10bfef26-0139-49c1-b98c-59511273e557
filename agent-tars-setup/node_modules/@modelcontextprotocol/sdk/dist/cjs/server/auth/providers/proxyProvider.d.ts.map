{"version": 3, "file": "proxyProvider.d.ts", "sourceRoot": "", "sources": ["../../../../../src/server/auth/providers/proxyProvider.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,SAAS,CAAC;AACnC,OAAO,EAAE,2BAA2B,EAAE,MAAM,eAAe,CAAC;AAC5D,OAAO,EACL,0BAA0B,EAE1B,2BAA2B,EAC3B,WAAW,EAEZ,MAAM,yBAAyB,CAAC;AACjC,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AACvC,OAAO,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,MAAM,gBAAgB,CAAC;AAG1E,MAAM,MAAM,cAAc,GAAG;IAC3B,gBAAgB,EAAE,MAAM,CAAC;IACzB,QAAQ,EAAE,MAAM,CAAC;IACjB,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB,eAAe,CAAC,EAAE,MAAM,CAAC;CAC1B,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG;IACzB;;OAEG;IACH,SAAS,EAAE,cAAc,CAAC;IAE1B;;MAEE;IACF,iBAAiB,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC;IAExD;;MAEE;IACF,SAAS,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,OAAO,CAAC,0BAA0B,GAAG,SAAS,CAAC,CAAC;CAElF,CAAC;AAEF;;GAEG;AACH,qBAAa,wBAAyB,YAAW,mBAAmB;IAClE,SAAS,CAAC,QAAQ,CAAC,UAAU,EAAE,cAAc,CAAC;IAC9C,SAAS,CAAC,QAAQ,CAAC,kBAAkB,EAAE,CAAC,KAAK,EAAE,MAAM,KAAK,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC5E,SAAS,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,OAAO,CAAC,0BAA0B,GAAG,SAAS,CAAC,CAAC;IAErG,uBAAuB,UAAQ;IAE/B,WAAW,CAAC,EAAE,CACZ,MAAM,EAAE,0BAA0B,EAClC,OAAO,EAAE,2BAA2B,KACjC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAEP,OAAO,EAAE,YAAY;IAwCjC,IAAI,YAAY,IAAI,2BAA2B,CAuB9C;IAEK,SAAS,CACb,MAAM,EAAE,0BAA0B,EAClC,MAAM,EAAE,mBAAmB,EAC3B,GAAG,EAAE,QAAQ,GACZ,OAAO,CAAC,IAAI,CAAC;IAoBV,6BAA6B,CACjC,OAAO,EAAE,0BAA0B,EACnC,kBAAkB,EAAE,MAAM,GACzB,OAAO,CAAC,MAAM,CAAC;IAMZ,yBAAyB,CAC7B,MAAM,EAAE,0BAA0B,EAClC,iBAAiB,EAAE,MAAM,EACzB,YAAY,CAAC,EAAE,MAAM,EACrB,WAAW,CAAC,EAAE,MAAM,EACpB,QAAQ,CAAC,EAAE,GAAG,GACb,OAAO,CAAC,WAAW,CAAC;IAwCjB,oBAAoB,CACxB,MAAM,EAAE,0BAA0B,EAClC,YAAY,EAAE,MAAM,EACpB,MAAM,CAAC,EAAE,MAAM,EAAE,EACjB,QAAQ,CAAC,EAAE,GAAG,GACb,OAAO,CAAC,WAAW,CAAC;IAoCjB,iBAAiB,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC;CAG1D"}