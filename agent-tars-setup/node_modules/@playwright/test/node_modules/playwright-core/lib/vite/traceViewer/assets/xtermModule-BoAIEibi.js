var fe={exports:{}},pe;function be(){return pe||(pe=1,function(he,Se){(function(te,J){he.exports=J()})(self,()=>(()=>{var te={4567:function(I,r,a){var c=this&&this.__decorate||function(i,o,l,v){var m,h=arguments.length,p=h<3?o:v===null?v=Object.getOwnPropertyDescriptor(o,l):v;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")p=Reflect.decorate(i,o,l,v);else for(var b=i.length-1;b>=0;b--)(m=i[b])&&(p=(h<3?m(p):h>3?m(o,l,p):m(o,l))||p);return h>3&&p&&Object.defineProperty(o,l,p),p},_=this&&this.__param||function(i,o){return function(l,v){o(l,v,i)}};Object.defineProperty(r,"__esModule",{value:!0}),r.AccessibilityManager=void 0;const n=a(9042),d=a(6114),f=a(9924),g=a(844),u=a(5596),e=a(4725),s=a(3656);let t=r.AccessibilityManager=class extends g.Disposable{constructor(i,o){super(),this._terminal=i,this._renderService=o,this._liveRegionLineCount=0,this._charsToConsume=[],this._charsToAnnounce="",this._accessibilityContainer=document.createElement("div"),this._accessibilityContainer.classList.add("xterm-accessibility"),this._rowContainer=document.createElement("div"),this._rowContainer.setAttribute("role","list"),this._rowContainer.classList.add("xterm-accessibility-tree"),this._rowElements=[];for(let l=0;l<this._terminal.rows;l++)this._rowElements[l]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[l]);if(this._topBoundaryFocusListener=l=>this._handleBoundaryFocus(l,0),this._bottomBoundaryFocusListener=l=>this._handleBoundaryFocus(l,1),this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions(),this._accessibilityContainer.appendChild(this._rowContainer),this._liveRegion=document.createElement("div"),this._liveRegion.classList.add("live-region"),this._liveRegion.setAttribute("aria-live","assertive"),this._accessibilityContainer.appendChild(this._liveRegion),this._liveRegionDebouncer=this.register(new f.TimeBasedDebouncer(this._renderRows.bind(this))),!this._terminal.element)throw new Error("Cannot enable accessibility before Terminal.open");this._terminal.element.insertAdjacentElement("afterbegin",this._accessibilityContainer),this.register(this._terminal.onResize(l=>this._handleResize(l.rows))),this.register(this._terminal.onRender(l=>this._refreshRows(l.start,l.end))),this.register(this._terminal.onScroll(()=>this._refreshRows())),this.register(this._terminal.onA11yChar(l=>this._handleChar(l))),this.register(this._terminal.onLineFeed(()=>this._handleChar(`
`))),this.register(this._terminal.onA11yTab(l=>this._handleTab(l))),this.register(this._terminal.onKey(l=>this._handleKey(l.key))),this.register(this._terminal.onBlur(()=>this._clearLiveRegion())),this.register(this._renderService.onDimensionsChange(()=>this._refreshRowsDimensions())),this._screenDprMonitor=new u.ScreenDprMonitor(window),this.register(this._screenDprMonitor),this._screenDprMonitor.setListener(()=>this._refreshRowsDimensions()),this.register((0,s.addDisposableDomListener)(window,"resize",()=>this._refreshRowsDimensions())),this._refreshRows(),this.register((0,g.toDisposable)(()=>{this._accessibilityContainer.remove(),this._rowElements.length=0}))}_handleTab(i){for(let o=0;o<i;o++)this._handleChar(" ")}_handleChar(i){this._liveRegionLineCount<21&&(this._charsToConsume.length>0?this._charsToConsume.shift()!==i&&(this._charsToAnnounce+=i):this._charsToAnnounce+=i,i===`
`&&(this._liveRegionLineCount++,this._liveRegionLineCount===21&&(this._liveRegion.textContent+=n.tooMuchOutput)),d.isMac&&this._liveRegion.textContent&&this._liveRegion.textContent.length>0&&!this._liveRegion.parentNode&&setTimeout(()=>{this._accessibilityContainer.appendChild(this._liveRegion)},0))}_clearLiveRegion(){this._liveRegion.textContent="",this._liveRegionLineCount=0,d.isMac&&this._liveRegion.remove()}_handleKey(i){this._clearLiveRegion(),new RegExp("\\p{Control}","u").test(i)||this._charsToConsume.push(i)}_refreshRows(i,o){this._liveRegionDebouncer.refresh(i,o,this._terminal.rows)}_renderRows(i,o){const l=this._terminal.buffer,v=l.lines.length.toString();for(let m=i;m<=o;m++){const h=l.translateBufferLineToString(l.ydisp+m,!0),p=(l.ydisp+m+1).toString(),b=this._rowElements[m];b&&(h.length===0?b.innerText=" ":b.textContent=h,b.setAttribute("aria-posinset",p),b.setAttribute("aria-setsize",v))}this._announceCharacters()}_announceCharacters(){this._charsToAnnounce.length!==0&&(this._liveRegion.textContent+=this._charsToAnnounce,this._charsToAnnounce="")}_handleBoundaryFocus(i,o){const l=i.target,v=this._rowElements[o===0?1:this._rowElements.length-2];if(l.getAttribute("aria-posinset")===(o===0?"1":`${this._terminal.buffer.lines.length}`)||i.relatedTarget!==v)return;let m,h;if(o===0?(m=l,h=this._rowElements.pop(),this._rowContainer.removeChild(h)):(m=this._rowElements.shift(),h=l,this._rowContainer.removeChild(m)),m.removeEventListener("focus",this._topBoundaryFocusListener),h.removeEventListener("focus",this._bottomBoundaryFocusListener),o===0){const p=this._createAccessibilityTreeNode();this._rowElements.unshift(p),this._rowContainer.insertAdjacentElement("afterbegin",p)}else{const p=this._createAccessibilityTreeNode();this._rowElements.push(p),this._rowContainer.appendChild(p)}this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._terminal.scrollLines(o===0?-1:1),this._rowElements[o===0?1:this._rowElements.length-2].focus(),i.preventDefault(),i.stopImmediatePropagation()}_handleResize(i){this._rowElements[this._rowElements.length-1].removeEventListener("focus",this._bottomBoundaryFocusListener);for(let o=this._rowContainer.children.length;o<this._terminal.rows;o++)this._rowElements[o]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[o]);for(;this._rowElements.length>i;)this._rowContainer.removeChild(this._rowElements.pop());this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions()}_createAccessibilityTreeNode(){const i=document.createElement("div");return i.setAttribute("role","listitem"),i.tabIndex=-1,this._refreshRowDimensions(i),i}_refreshRowsDimensions(){if(this._renderService.dimensions.css.cell.height){this._accessibilityContainer.style.width=`${this._renderService.dimensions.css.canvas.width}px`,this._rowElements.length!==this._terminal.rows&&this._handleResize(this._terminal.rows);for(let i=0;i<this._terminal.rows;i++)this._refreshRowDimensions(this._rowElements[i])}}_refreshRowDimensions(i){i.style.height=`${this._renderService.dimensions.css.cell.height}px`}};r.AccessibilityManager=t=c([_(1,e.IRenderService)],t)},3614:(I,r)=>{function a(d){return d.replace(/\r?\n/g,"\r")}function c(d,f){return f?"\x1B[200~"+d+"\x1B[201~":d}function _(d,f,g,u){d=c(d=a(d),g.decPrivateModes.bracketedPasteMode&&u.rawOptions.ignoreBracketedPasteMode!==!0),g.triggerDataEvent(d,!0),f.value=""}function n(d,f,g){const u=g.getBoundingClientRect(),e=d.clientX-u.left-10,s=d.clientY-u.top-10;f.style.width="20px",f.style.height="20px",f.style.left=`${e}px`,f.style.top=`${s}px`,f.style.zIndex="1000",f.focus()}Object.defineProperty(r,"__esModule",{value:!0}),r.rightClickHandler=r.moveTextAreaUnderMouseCursor=r.paste=r.handlePasteEvent=r.copyHandler=r.bracketTextForPaste=r.prepareTextForTerminal=void 0,r.prepareTextForTerminal=a,r.bracketTextForPaste=c,r.copyHandler=function(d,f){d.clipboardData&&d.clipboardData.setData("text/plain",f.selectionText),d.preventDefault()},r.handlePasteEvent=function(d,f,g,u){d.stopPropagation(),d.clipboardData&&_(d.clipboardData.getData("text/plain"),f,g,u)},r.paste=_,r.moveTextAreaUnderMouseCursor=n,r.rightClickHandler=function(d,f,g,u,e){n(d,f,g),e&&u.rightClickSelect(d),f.value=u.selectionText,f.select()}},7239:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.ColorContrastCache=void 0;const c=a(1505);r.ColorContrastCache=class{constructor(){this._color=new c.TwoKeyMap,this._css=new c.TwoKeyMap}setCss(_,n,d){this._css.set(_,n,d)}getCss(_,n){return this._css.get(_,n)}setColor(_,n,d){this._color.set(_,n,d)}getColor(_,n){return this._color.get(_,n)}clear(){this._color.clear(),this._css.clear()}}},3656:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.addDisposableDomListener=void 0,r.addDisposableDomListener=function(a,c,_,n){a.addEventListener(c,_,n);let d=!1;return{dispose:()=>{d||(d=!0,a.removeEventListener(c,_,n))}}}},6465:function(I,r,a){var c=this&&this.__decorate||function(e,s,t,i){var o,l=arguments.length,v=l<3?s:i===null?i=Object.getOwnPropertyDescriptor(s,t):i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(e,s,t,i);else for(var m=e.length-1;m>=0;m--)(o=e[m])&&(v=(l<3?o(v):l>3?o(s,t,v):o(s,t))||v);return l>3&&v&&Object.defineProperty(s,t,v),v},_=this&&this.__param||function(e,s){return function(t,i){s(t,i,e)}};Object.defineProperty(r,"__esModule",{value:!0}),r.Linkifier2=void 0;const n=a(3656),d=a(8460),f=a(844),g=a(2585);let u=r.Linkifier2=class extends f.Disposable{get currentLink(){return this._currentLink}constructor(e){super(),this._bufferService=e,this._linkProviders=[],this._linkCacheDisposables=[],this._isMouseOut=!0,this._wasResized=!1,this._activeLine=-1,this._onShowLinkUnderline=this.register(new d.EventEmitter),this.onShowLinkUnderline=this._onShowLinkUnderline.event,this._onHideLinkUnderline=this.register(new d.EventEmitter),this.onHideLinkUnderline=this._onHideLinkUnderline.event,this.register((0,f.getDisposeArrayDisposable)(this._linkCacheDisposables)),this.register((0,f.toDisposable)(()=>{this._lastMouseEvent=void 0})),this.register(this._bufferService.onResize(()=>{this._clearCurrentLink(),this._wasResized=!0}))}registerLinkProvider(e){return this._linkProviders.push(e),{dispose:()=>{const s=this._linkProviders.indexOf(e);s!==-1&&this._linkProviders.splice(s,1)}}}attachToDom(e,s,t){this._element=e,this._mouseService=s,this._renderService=t,this.register((0,n.addDisposableDomListener)(this._element,"mouseleave",()=>{this._isMouseOut=!0,this._clearCurrentLink()})),this.register((0,n.addDisposableDomListener)(this._element,"mousemove",this._handleMouseMove.bind(this))),this.register((0,n.addDisposableDomListener)(this._element,"mousedown",this._handleMouseDown.bind(this))),this.register((0,n.addDisposableDomListener)(this._element,"mouseup",this._handleMouseUp.bind(this)))}_handleMouseMove(e){if(this._lastMouseEvent=e,!this._element||!this._mouseService)return;const s=this._positionFromMouseEvent(e,this._element,this._mouseService);if(!s)return;this._isMouseOut=!1;const t=e.composedPath();for(let i=0;i<t.length;i++){const o=t[i];if(o.classList.contains("xterm"))break;if(o.classList.contains("xterm-hover"))return}this._lastBufferCell&&s.x===this._lastBufferCell.x&&s.y===this._lastBufferCell.y||(this._handleHover(s),this._lastBufferCell=s)}_handleHover(e){if(this._activeLine!==e.y||this._wasResized)return this._clearCurrentLink(),this._askForLink(e,!1),void(this._wasResized=!1);this._currentLink&&this._linkAtPosition(this._currentLink.link,e)||(this._clearCurrentLink(),this._askForLink(e,!0))}_askForLink(e,s){var t,i;this._activeProviderReplies&&s||((t=this._activeProviderReplies)===null||t===void 0||t.forEach(l=>{l==null||l.forEach(v=>{v.link.dispose&&v.link.dispose()})}),this._activeProviderReplies=new Map,this._activeLine=e.y);let o=!1;for(const[l,v]of this._linkProviders.entries())s?!((i=this._activeProviderReplies)===null||i===void 0)&&i.get(l)&&(o=this._checkLinkProviderResult(l,e,o)):v.provideLinks(e.y,m=>{var h,p;if(this._isMouseOut)return;const b=m==null?void 0:m.map(L=>({link:L}));(h=this._activeProviderReplies)===null||h===void 0||h.set(l,b),o=this._checkLinkProviderResult(l,e,o),((p=this._activeProviderReplies)===null||p===void 0?void 0:p.size)===this._linkProviders.length&&this._removeIntersectingLinks(e.y,this._activeProviderReplies)})}_removeIntersectingLinks(e,s){const t=new Set;for(let i=0;i<s.size;i++){const o=s.get(i);if(o)for(let l=0;l<o.length;l++){const v=o[l],m=v.link.range.start.y<e?0:v.link.range.start.x,h=v.link.range.end.y>e?this._bufferService.cols:v.link.range.end.x;for(let p=m;p<=h;p++){if(t.has(p)){o.splice(l--,1);break}t.add(p)}}}}_checkLinkProviderResult(e,s,t){var i;if(!this._activeProviderReplies)return t;const o=this._activeProviderReplies.get(e);let l=!1;for(let v=0;v<e;v++)this._activeProviderReplies.has(v)&&!this._activeProviderReplies.get(v)||(l=!0);if(!l&&o){const v=o.find(m=>this._linkAtPosition(m.link,s));v&&(t=!0,this._handleNewLink(v))}if(this._activeProviderReplies.size===this._linkProviders.length&&!t)for(let v=0;v<this._activeProviderReplies.size;v++){const m=(i=this._activeProviderReplies.get(v))===null||i===void 0?void 0:i.find(h=>this._linkAtPosition(h.link,s));if(m){t=!0,this._handleNewLink(m);break}}return t}_handleMouseDown(){this._mouseDownLink=this._currentLink}_handleMouseUp(e){if(!this._element||!this._mouseService||!this._currentLink)return;const s=this._positionFromMouseEvent(e,this._element,this._mouseService);s&&this._mouseDownLink===this._currentLink&&this._linkAtPosition(this._currentLink.link,s)&&this._currentLink.link.activate(e,this._currentLink.link.text)}_clearCurrentLink(e,s){this._element&&this._currentLink&&this._lastMouseEvent&&(!e||!s||this._currentLink.link.range.start.y>=e&&this._currentLink.link.range.end.y<=s)&&(this._linkLeave(this._element,this._currentLink.link,this._lastMouseEvent),this._currentLink=void 0,(0,f.disposeArray)(this._linkCacheDisposables))}_handleNewLink(e){if(!this._element||!this._lastMouseEvent||!this._mouseService)return;const s=this._positionFromMouseEvent(this._lastMouseEvent,this._element,this._mouseService);s&&this._linkAtPosition(e.link,s)&&(this._currentLink=e,this._currentLink.state={decorations:{underline:e.link.decorations===void 0||e.link.decorations.underline,pointerCursor:e.link.decorations===void 0||e.link.decorations.pointerCursor},isHovered:!0},this._linkHover(this._element,e.link,this._lastMouseEvent),e.link.decorations={},Object.defineProperties(e.link.decorations,{pointerCursor:{get:()=>{var t,i;return(i=(t=this._currentLink)===null||t===void 0?void 0:t.state)===null||i===void 0?void 0:i.decorations.pointerCursor},set:t=>{var i,o;!((i=this._currentLink)===null||i===void 0)&&i.state&&this._currentLink.state.decorations.pointerCursor!==t&&(this._currentLink.state.decorations.pointerCursor=t,this._currentLink.state.isHovered&&((o=this._element)===null||o===void 0||o.classList.toggle("xterm-cursor-pointer",t)))}},underline:{get:()=>{var t,i;return(i=(t=this._currentLink)===null||t===void 0?void 0:t.state)===null||i===void 0?void 0:i.decorations.underline},set:t=>{var i,o,l;!((i=this._currentLink)===null||i===void 0)&&i.state&&((l=(o=this._currentLink)===null||o===void 0?void 0:o.state)===null||l===void 0?void 0:l.decorations.underline)!==t&&(this._currentLink.state.decorations.underline=t,this._currentLink.state.isHovered&&this._fireUnderlineEvent(e.link,t))}}}),this._renderService&&this._linkCacheDisposables.push(this._renderService.onRenderedViewportChange(t=>{if(!this._currentLink)return;const i=t.start===0?0:t.start+1+this._bufferService.buffer.ydisp,o=this._bufferService.buffer.ydisp+1+t.end;if(this._currentLink.link.range.start.y>=i&&this._currentLink.link.range.end.y<=o&&(this._clearCurrentLink(i,o),this._lastMouseEvent&&this._element)){const l=this._positionFromMouseEvent(this._lastMouseEvent,this._element,this._mouseService);l&&this._askForLink(l,!1)}})))}_linkHover(e,s,t){var i;!((i=this._currentLink)===null||i===void 0)&&i.state&&(this._currentLink.state.isHovered=!0,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(s,!0),this._currentLink.state.decorations.pointerCursor&&e.classList.add("xterm-cursor-pointer")),s.hover&&s.hover(t,s.text)}_fireUnderlineEvent(e,s){const t=e.range,i=this._bufferService.buffer.ydisp,o=this._createLinkUnderlineEvent(t.start.x-1,t.start.y-i-1,t.end.x,t.end.y-i-1,void 0);(s?this._onShowLinkUnderline:this._onHideLinkUnderline).fire(o)}_linkLeave(e,s,t){var i;!((i=this._currentLink)===null||i===void 0)&&i.state&&(this._currentLink.state.isHovered=!1,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(s,!1),this._currentLink.state.decorations.pointerCursor&&e.classList.remove("xterm-cursor-pointer")),s.leave&&s.leave(t,s.text)}_linkAtPosition(e,s){const t=e.range.start.y*this._bufferService.cols+e.range.start.x,i=e.range.end.y*this._bufferService.cols+e.range.end.x,o=s.y*this._bufferService.cols+s.x;return t<=o&&o<=i}_positionFromMouseEvent(e,s,t){const i=t.getCoords(e,s,this._bufferService.cols,this._bufferService.rows);if(i)return{x:i[0],y:i[1]+this._bufferService.buffer.ydisp}}_createLinkUnderlineEvent(e,s,t,i,o){return{x1:e,y1:s,x2:t,y2:i,cols:this._bufferService.cols,fg:o}}};r.Linkifier2=u=c([_(0,g.IBufferService)],u)},9042:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.tooMuchOutput=r.promptLabel=void 0,r.promptLabel="Terminal input",r.tooMuchOutput="Too much output to announce, navigate to rows manually to read"},3730:function(I,r,a){var c=this&&this.__decorate||function(u,e,s,t){var i,o=arguments.length,l=o<3?e:t===null?t=Object.getOwnPropertyDescriptor(e,s):t;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")l=Reflect.decorate(u,e,s,t);else for(var v=u.length-1;v>=0;v--)(i=u[v])&&(l=(o<3?i(l):o>3?i(e,s,l):i(e,s))||l);return o>3&&l&&Object.defineProperty(e,s,l),l},_=this&&this.__param||function(u,e){return function(s,t){e(s,t,u)}};Object.defineProperty(r,"__esModule",{value:!0}),r.OscLinkProvider=void 0;const n=a(511),d=a(2585);let f=r.OscLinkProvider=class{constructor(u,e,s){this._bufferService=u,this._optionsService=e,this._oscLinkService=s}provideLinks(u,e){var s;const t=this._bufferService.buffer.lines.get(u-1);if(!t)return void e(void 0);const i=[],o=this._optionsService.rawOptions.linkHandler,l=new n.CellData,v=t.getTrimmedLength();let m=-1,h=-1,p=!1;for(let b=0;b<v;b++)if(h!==-1||t.hasContent(b)){if(t.loadCell(b,l),l.hasExtendedAttrs()&&l.extended.urlId){if(h===-1){h=b,m=l.extended.urlId;continue}p=l.extended.urlId!==m}else h!==-1&&(p=!0);if(p||h!==-1&&b===v-1){const L=(s=this._oscLinkService.getLinkData(m))===null||s===void 0?void 0:s.uri;if(L){const y={start:{x:h+1,y:u},end:{x:b+(p||b!==v-1?0:1),y:u}};let k=!1;if(!(o!=null&&o.allowNonHttpProtocols))try{const x=new URL(L);["http:","https:"].includes(x.protocol)||(k=!0)}catch{k=!0}k||i.push({text:L,range:y,activate:(x,T)=>o?o.activate(x,T,y):g(0,T),hover:(x,T)=>{var O;return(O=o==null?void 0:o.hover)===null||O===void 0?void 0:O.call(o,x,T,y)},leave:(x,T)=>{var O;return(O=o==null?void 0:o.leave)===null||O===void 0?void 0:O.call(o,x,T,y)}})}p=!1,l.hasExtendedAttrs()&&l.extended.urlId?(h=b,m=l.extended.urlId):(h=-1,m=-1)}}e(i)}};function g(u,e){if(confirm(`Do you want to navigate to ${e}?

WARNING: This link could potentially be dangerous`)){const s=window.open();if(s){try{s.opener=null}catch{}s.location.href=e}else console.warn("Opening link blocked as opener could not be cleared")}}r.OscLinkProvider=f=c([_(0,d.IBufferService),_(1,d.IOptionsService),_(2,d.IOscLinkService)],f)},6193:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.RenderDebouncer=void 0,r.RenderDebouncer=class{constructor(a,c){this._parentWindow=a,this._renderCallback=c,this._refreshCallbacks=[]}dispose(){this._animationFrame&&(this._parentWindow.cancelAnimationFrame(this._animationFrame),this._animationFrame=void 0)}addRefreshCallback(a){return this._refreshCallbacks.push(a),this._animationFrame||(this._animationFrame=this._parentWindow.requestAnimationFrame(()=>this._innerRefresh())),this._animationFrame}refresh(a,c,_){this._rowCount=_,a=a!==void 0?a:0,c=c!==void 0?c:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,a):a,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,c):c,this._animationFrame||(this._animationFrame=this._parentWindow.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._animationFrame=void 0,this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return void this._runRefreshCallbacks();const a=Math.max(this._rowStart,0),c=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(a,c),this._runRefreshCallbacks()}_runRefreshCallbacks(){for(const a of this._refreshCallbacks)a(0);this._refreshCallbacks=[]}}},5596:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.ScreenDprMonitor=void 0;const c=a(844);class _ extends c.Disposable{constructor(d){super(),this._parentWindow=d,this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this.register((0,c.toDisposable)(()=>{this.clearListener()}))}setListener(d){this._listener&&this.clearListener(),this._listener=d,this._outerListener=()=>{this._listener&&(this._listener(this._parentWindow.devicePixelRatio,this._currentDevicePixelRatio),this._updateDpr())},this._updateDpr()}_updateDpr(){var d;this._outerListener&&((d=this._resolutionMediaMatchList)===null||d===void 0||d.removeListener(this._outerListener),this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this._resolutionMediaMatchList=this._parentWindow.matchMedia(`screen and (resolution: ${this._parentWindow.devicePixelRatio}dppx)`),this._resolutionMediaMatchList.addListener(this._outerListener))}clearListener(){this._resolutionMediaMatchList&&this._listener&&this._outerListener&&(this._resolutionMediaMatchList.removeListener(this._outerListener),this._resolutionMediaMatchList=void 0,this._listener=void 0,this._outerListener=void 0)}}r.ScreenDprMonitor=_},3236:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.Terminal=void 0;const c=a(3614),_=a(3656),n=a(6465),d=a(9042),f=a(3730),g=a(1680),u=a(3107),e=a(5744),s=a(2950),t=a(1296),i=a(428),o=a(4269),l=a(5114),v=a(8934),m=a(3230),h=a(9312),p=a(4725),b=a(6731),L=a(8055),y=a(8969),k=a(8460),x=a(844),T=a(6114),O=a(8437),M=a(2584),C=a(7399),w=a(5941),E=a(9074),D=a(2585),P=a(5435),H=a(4567),U=typeof window<"u"?window.document:null;class W extends y.CoreTerminal{get onFocus(){return this._onFocus.event}get onBlur(){return this._onBlur.event}get onA11yChar(){return this._onA11yCharEmitter.event}get onA11yTab(){return this._onA11yTabEmitter.event}get onWillOpen(){return this._onWillOpen.event}constructor(S={}){super(S),this.browser=T,this._keyDownHandled=!1,this._keyDownSeen=!1,this._keyPressHandled=!1,this._unprocessedDeadKey=!1,this._accessibilityManager=this.register(new x.MutableDisposable),this._onCursorMove=this.register(new k.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onKey=this.register(new k.EventEmitter),this.onKey=this._onKey.event,this._onRender=this.register(new k.EventEmitter),this.onRender=this._onRender.event,this._onSelectionChange=this.register(new k.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onTitleChange=this.register(new k.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onBell=this.register(new k.EventEmitter),this.onBell=this._onBell.event,this._onFocus=this.register(new k.EventEmitter),this._onBlur=this.register(new k.EventEmitter),this._onA11yCharEmitter=this.register(new k.EventEmitter),this._onA11yTabEmitter=this.register(new k.EventEmitter),this._onWillOpen=this.register(new k.EventEmitter),this._setup(),this.linkifier2=this.register(this._instantiationService.createInstance(n.Linkifier2)),this.linkifier2.registerLinkProvider(this._instantiationService.createInstance(f.OscLinkProvider)),this._decorationService=this._instantiationService.createInstance(E.DecorationService),this._instantiationService.setService(D.IDecorationService,this._decorationService),this.register(this._inputHandler.onRequestBell(()=>this._onBell.fire())),this.register(this._inputHandler.onRequestRefreshRows((R,B)=>this.refresh(R,B))),this.register(this._inputHandler.onRequestSendFocus(()=>this._reportFocus())),this.register(this._inputHandler.onRequestReset(()=>this.reset())),this.register(this._inputHandler.onRequestWindowsOptionsReport(R=>this._reportWindowsOptions(R))),this.register(this._inputHandler.onColor(R=>this._handleColorEvent(R))),this.register((0,k.forwardEvent)(this._inputHandler.onCursorMove,this._onCursorMove)),this.register((0,k.forwardEvent)(this._inputHandler.onTitleChange,this._onTitleChange)),this.register((0,k.forwardEvent)(this._inputHandler.onA11yChar,this._onA11yCharEmitter)),this.register((0,k.forwardEvent)(this._inputHandler.onA11yTab,this._onA11yTabEmitter)),this.register(this._bufferService.onResize(R=>this._afterResize(R.cols,R.rows))),this.register((0,x.toDisposable)(()=>{var R,B;this._customKeyEventHandler=void 0,(B=(R=this.element)===null||R===void 0?void 0:R.parentNode)===null||B===void 0||B.removeChild(this.element)}))}_handleColorEvent(S){if(this._themeService)for(const R of S){let B,A="";switch(R.index){case 256:B="foreground",A="10";break;case 257:B="background",A="11";break;case 258:B="cursor",A="12";break;default:B="ansi",A="4;"+R.index}switch(R.type){case 0:const N=L.color.toColorRGB(B==="ansi"?this._themeService.colors.ansi[R.index]:this._themeService.colors[B]);this.coreService.triggerDataEvent(`${M.C0.ESC}]${A};${(0,w.toRgbString)(N)}${M.C1_ESCAPED.ST}`);break;case 1:if(B==="ansi")this._themeService.modifyColors(F=>F.ansi[R.index]=L.rgba.toColor(...R.color));else{const F=B;this._themeService.modifyColors(j=>j[F]=L.rgba.toColor(...R.color))}break;case 2:this._themeService.restoreColor(R.index)}}}_setup(){super._setup(),this._customKeyEventHandler=void 0}get buffer(){return this.buffers.active}focus(){this.textarea&&this.textarea.focus({preventScroll:!0})}_handleScreenReaderModeOptionChange(S){S?!this._accessibilityManager.value&&this._renderService&&(this._accessibilityManager.value=this._instantiationService.createInstance(H.AccessibilityManager,this)):this._accessibilityManager.clear()}_handleTextAreaFocus(S){this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(M.C0.ESC+"[I"),this.updateCursorStyle(S),this.element.classList.add("focus"),this._showCursor(),this._onFocus.fire()}blur(){var S;return(S=this.textarea)===null||S===void 0?void 0:S.blur()}_handleTextAreaBlur(){this.textarea.value="",this.refresh(this.buffer.y,this.buffer.y),this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(M.C0.ESC+"[O"),this.element.classList.remove("focus"),this._onBlur.fire()}_syncTextArea(){if(!this.textarea||!this.buffer.isCursorInViewport||this._compositionHelper.isComposing||!this._renderService)return;const S=this.buffer.ybase+this.buffer.y,R=this.buffer.lines.get(S);if(!R)return;const B=Math.min(this.buffer.x,this.cols-1),A=this._renderService.dimensions.css.cell.height,N=R.getWidth(B),F=this._renderService.dimensions.css.cell.width*N,j=this.buffer.y*this._renderService.dimensions.css.cell.height,V=B*this._renderService.dimensions.css.cell.width;this.textarea.style.left=V+"px",this.textarea.style.top=j+"px",this.textarea.style.width=F+"px",this.textarea.style.height=A+"px",this.textarea.style.lineHeight=A+"px",this.textarea.style.zIndex="-5"}_initGlobal(){this._bindKeys(),this.register((0,_.addDisposableDomListener)(this.element,"copy",R=>{this.hasSelection()&&(0,c.copyHandler)(R,this._selectionService)}));const S=R=>(0,c.handlePasteEvent)(R,this.textarea,this.coreService,this.optionsService);this.register((0,_.addDisposableDomListener)(this.textarea,"paste",S)),this.register((0,_.addDisposableDomListener)(this.element,"paste",S)),T.isFirefox?this.register((0,_.addDisposableDomListener)(this.element,"mousedown",R=>{R.button===2&&(0,c.rightClickHandler)(R,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})):this.register((0,_.addDisposableDomListener)(this.element,"contextmenu",R=>{(0,c.rightClickHandler)(R,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})),T.isLinux&&this.register((0,_.addDisposableDomListener)(this.element,"auxclick",R=>{R.button===1&&(0,c.moveTextAreaUnderMouseCursor)(R,this.textarea,this.screenElement)}))}_bindKeys(){this.register((0,_.addDisposableDomListener)(this.textarea,"keyup",S=>this._keyUp(S),!0)),this.register((0,_.addDisposableDomListener)(this.textarea,"keydown",S=>this._keyDown(S),!0)),this.register((0,_.addDisposableDomListener)(this.textarea,"keypress",S=>this._keyPress(S),!0)),this.register((0,_.addDisposableDomListener)(this.textarea,"compositionstart",()=>this._compositionHelper.compositionstart())),this.register((0,_.addDisposableDomListener)(this.textarea,"compositionupdate",S=>this._compositionHelper.compositionupdate(S))),this.register((0,_.addDisposableDomListener)(this.textarea,"compositionend",()=>this._compositionHelper.compositionend())),this.register((0,_.addDisposableDomListener)(this.textarea,"input",S=>this._inputEvent(S),!0)),this.register(this.onRender(()=>this._compositionHelper.updateCompositionElements()))}open(S){var R;if(!S)throw new Error("Terminal requires a parent element.");S.isConnected||this._logService.debug("Terminal.open was called on an element that was not attached to the DOM"),this._document=S.ownerDocument,this.element=this._document.createElement("div"),this.element.dir="ltr",this.element.classList.add("terminal"),this.element.classList.add("xterm"),S.appendChild(this.element);const B=U.createDocumentFragment();this._viewportElement=U.createElement("div"),this._viewportElement.classList.add("xterm-viewport"),B.appendChild(this._viewportElement),this._viewportScrollArea=U.createElement("div"),this._viewportScrollArea.classList.add("xterm-scroll-area"),this._viewportElement.appendChild(this._viewportScrollArea),this.screenElement=U.createElement("div"),this.screenElement.classList.add("xterm-screen"),this._helperContainer=U.createElement("div"),this._helperContainer.classList.add("xterm-helpers"),this.screenElement.appendChild(this._helperContainer),B.appendChild(this.screenElement),this.textarea=U.createElement("textarea"),this.textarea.classList.add("xterm-helper-textarea"),this.textarea.setAttribute("aria-label",d.promptLabel),T.isChromeOS||this.textarea.setAttribute("aria-multiline","false"),this.textarea.setAttribute("autocorrect","off"),this.textarea.setAttribute("autocapitalize","off"),this.textarea.setAttribute("spellcheck","false"),this.textarea.tabIndex=0,this._coreBrowserService=this._instantiationService.createInstance(l.CoreBrowserService,this.textarea,(R=this._document.defaultView)!==null&&R!==void 0?R:window),this._instantiationService.setService(p.ICoreBrowserService,this._coreBrowserService),this.register((0,_.addDisposableDomListener)(this.textarea,"focus",A=>this._handleTextAreaFocus(A))),this.register((0,_.addDisposableDomListener)(this.textarea,"blur",()=>this._handleTextAreaBlur())),this._helperContainer.appendChild(this.textarea),this._charSizeService=this._instantiationService.createInstance(i.CharSizeService,this._document,this._helperContainer),this._instantiationService.setService(p.ICharSizeService,this._charSizeService),this._themeService=this._instantiationService.createInstance(b.ThemeService),this._instantiationService.setService(p.IThemeService,this._themeService),this._characterJoinerService=this._instantiationService.createInstance(o.CharacterJoinerService),this._instantiationService.setService(p.ICharacterJoinerService,this._characterJoinerService),this._renderService=this.register(this._instantiationService.createInstance(m.RenderService,this.rows,this.screenElement)),this._instantiationService.setService(p.IRenderService,this._renderService),this.register(this._renderService.onRenderedViewportChange(A=>this._onRender.fire(A))),this.onResize(A=>this._renderService.resize(A.cols,A.rows)),this._compositionView=U.createElement("div"),this._compositionView.classList.add("composition-view"),this._compositionHelper=this._instantiationService.createInstance(s.CompositionHelper,this.textarea,this._compositionView),this._helperContainer.appendChild(this._compositionView),this.element.appendChild(B);try{this._onWillOpen.fire(this.element)}catch{}this._renderService.hasRenderer()||this._renderService.setRenderer(this._createRenderer()),this._mouseService=this._instantiationService.createInstance(v.MouseService),this._instantiationService.setService(p.IMouseService,this._mouseService),this.viewport=this._instantiationService.createInstance(g.Viewport,this._viewportElement,this._viewportScrollArea),this.viewport.onRequestScrollLines(A=>this.scrollLines(A.amount,A.suppressScrollEvent,1)),this.register(this._inputHandler.onRequestSyncScrollBar(()=>this.viewport.syncScrollArea())),this.register(this.viewport),this.register(this.onCursorMove(()=>{this._renderService.handleCursorMove(),this._syncTextArea()})),this.register(this.onResize(()=>this._renderService.handleResize(this.cols,this.rows))),this.register(this.onBlur(()=>this._renderService.handleBlur())),this.register(this.onFocus(()=>this._renderService.handleFocus())),this.register(this._renderService.onDimensionsChange(()=>this.viewport.syncScrollArea())),this._selectionService=this.register(this._instantiationService.createInstance(h.SelectionService,this.element,this.screenElement,this.linkifier2)),this._instantiationService.setService(p.ISelectionService,this._selectionService),this.register(this._selectionService.onRequestScrollLines(A=>this.scrollLines(A.amount,A.suppressScrollEvent))),this.register(this._selectionService.onSelectionChange(()=>this._onSelectionChange.fire())),this.register(this._selectionService.onRequestRedraw(A=>this._renderService.handleSelectionChanged(A.start,A.end,A.columnSelectMode))),this.register(this._selectionService.onLinuxMouseSelection(A=>{this.textarea.value=A,this.textarea.focus(),this.textarea.select()})),this.register(this._onScroll.event(A=>{this.viewport.syncScrollArea(),this._selectionService.refresh()})),this.register((0,_.addDisposableDomListener)(this._viewportElement,"scroll",()=>this._selectionService.refresh())),this.linkifier2.attachToDom(this.screenElement,this._mouseService,this._renderService),this.register(this._instantiationService.createInstance(u.BufferDecorationRenderer,this.screenElement)),this.register((0,_.addDisposableDomListener)(this.element,"mousedown",A=>this._selectionService.handleMouseDown(A))),this.coreMouseService.areMouseEventsActive?(this._selectionService.disable(),this.element.classList.add("enable-mouse-events")):this._selectionService.enable(),this.options.screenReaderMode&&(this._accessibilityManager.value=this._instantiationService.createInstance(H.AccessibilityManager,this)),this.register(this.optionsService.onSpecificOptionChange("screenReaderMode",A=>this._handleScreenReaderModeOptionChange(A))),this.options.overviewRulerWidth&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(e.OverviewRulerRenderer,this._viewportElement,this.screenElement))),this.optionsService.onSpecificOptionChange("overviewRulerWidth",A=>{!this._overviewRulerRenderer&&A&&this._viewportElement&&this.screenElement&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(e.OverviewRulerRenderer,this._viewportElement,this.screenElement)))}),this._charSizeService.measure(),this.refresh(0,this.rows-1),this._initGlobal(),this.bindMouse()}_createRenderer(){return this._instantiationService.createInstance(t.DomRenderer,this.element,this.screenElement,this._viewportElement,this.linkifier2)}bindMouse(){const S=this,R=this.element;function B(F){const j=S._mouseService.getMouseReportCoords(F,S.screenElement);if(!j)return!1;let V,G;switch(F.overrideType||F.type){case"mousemove":G=32,F.buttons===void 0?(V=3,F.button!==void 0&&(V=F.button<3?F.button:3)):V=1&F.buttons?0:4&F.buttons?1:2&F.buttons?2:3;break;case"mouseup":G=0,V=F.button<3?F.button:3;break;case"mousedown":G=1,V=F.button<3?F.button:3;break;case"wheel":if(S.viewport.getLinesScrolled(F)===0)return!1;G=F.deltaY<0?0:1,V=4;break;default:return!1}return!(G===void 0||V===void 0||V>4)&&S.coreMouseService.triggerMouseEvent({col:j.col,row:j.row,x:j.x,y:j.y,button:V,action:G,ctrl:F.ctrlKey,alt:F.altKey,shift:F.shiftKey})}const A={mouseup:null,wheel:null,mousedrag:null,mousemove:null},N={mouseup:F=>(B(F),F.buttons||(this._document.removeEventListener("mouseup",A.mouseup),A.mousedrag&&this._document.removeEventListener("mousemove",A.mousedrag)),this.cancel(F)),wheel:F=>(B(F),this.cancel(F,!0)),mousedrag:F=>{F.buttons&&B(F)},mousemove:F=>{F.buttons||B(F)}};this.register(this.coreMouseService.onProtocolChange(F=>{F?(this.optionsService.rawOptions.logLevel==="debug"&&this._logService.debug("Binding to mouse events:",this.coreMouseService.explainEvents(F)),this.element.classList.add("enable-mouse-events"),this._selectionService.disable()):(this._logService.debug("Unbinding from mouse events."),this.element.classList.remove("enable-mouse-events"),this._selectionService.enable()),8&F?A.mousemove||(R.addEventListener("mousemove",N.mousemove),A.mousemove=N.mousemove):(R.removeEventListener("mousemove",A.mousemove),A.mousemove=null),16&F?A.wheel||(R.addEventListener("wheel",N.wheel,{passive:!1}),A.wheel=N.wheel):(R.removeEventListener("wheel",A.wheel),A.wheel=null),2&F?A.mouseup||(R.addEventListener("mouseup",N.mouseup),A.mouseup=N.mouseup):(this._document.removeEventListener("mouseup",A.mouseup),R.removeEventListener("mouseup",A.mouseup),A.mouseup=null),4&F?A.mousedrag||(A.mousedrag=N.mousedrag):(this._document.removeEventListener("mousemove",A.mousedrag),A.mousedrag=null)})),this.coreMouseService.activeProtocol=this.coreMouseService.activeProtocol,this.register((0,_.addDisposableDomListener)(R,"mousedown",F=>{if(F.preventDefault(),this.focus(),this.coreMouseService.areMouseEventsActive&&!this._selectionService.shouldForceSelection(F))return B(F),A.mouseup&&this._document.addEventListener("mouseup",A.mouseup),A.mousedrag&&this._document.addEventListener("mousemove",A.mousedrag),this.cancel(F)})),this.register((0,_.addDisposableDomListener)(R,"wheel",F=>{if(!A.wheel){if(!this.buffer.hasScrollback){const j=this.viewport.getLinesScrolled(F);if(j===0)return;const V=M.C0.ESC+(this.coreService.decPrivateModes.applicationCursorKeys?"O":"[")+(F.deltaY<0?"A":"B");let G="";for(let ie=0;ie<Math.abs(j);ie++)G+=V;return this.coreService.triggerDataEvent(G,!0),this.cancel(F,!0)}return this.viewport.handleWheel(F)?this.cancel(F):void 0}},{passive:!1})),this.register((0,_.addDisposableDomListener)(R,"touchstart",F=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchStart(F),this.cancel(F)},{passive:!0})),this.register((0,_.addDisposableDomListener)(R,"touchmove",F=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchMove(F)?void 0:this.cancel(F)},{passive:!1}))}refresh(S,R){var B;(B=this._renderService)===null||B===void 0||B.refreshRows(S,R)}updateCursorStyle(S){var R;!((R=this._selectionService)===null||R===void 0)&&R.shouldColumnSelect(S)?this.element.classList.add("column-select"):this.element.classList.remove("column-select")}_showCursor(){this.coreService.isCursorInitialized||(this.coreService.isCursorInitialized=!0,this.refresh(this.buffer.y,this.buffer.y))}scrollLines(S,R,B=0){var A;B===1?(super.scrollLines(S,R,B),this.refresh(0,this.rows-1)):(A=this.viewport)===null||A===void 0||A.scrollLines(S)}paste(S){(0,c.paste)(S,this.textarea,this.coreService,this.optionsService)}attachCustomKeyEventHandler(S){this._customKeyEventHandler=S}registerLinkProvider(S){return this.linkifier2.registerLinkProvider(S)}registerCharacterJoiner(S){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");const R=this._characterJoinerService.register(S);return this.refresh(0,this.rows-1),R}deregisterCharacterJoiner(S){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");this._characterJoinerService.deregister(S)&&this.refresh(0,this.rows-1)}get markers(){return this.buffer.markers}registerMarker(S){return this.buffer.addMarker(this.buffer.ybase+this.buffer.y+S)}registerDecoration(S){return this._decorationService.registerDecoration(S)}hasSelection(){return!!this._selectionService&&this._selectionService.hasSelection}select(S,R,B){this._selectionService.setSelection(S,R,B)}getSelection(){return this._selectionService?this._selectionService.selectionText:""}getSelectionPosition(){if(this._selectionService&&this._selectionService.hasSelection)return{start:{x:this._selectionService.selectionStart[0],y:this._selectionService.selectionStart[1]},end:{x:this._selectionService.selectionEnd[0],y:this._selectionService.selectionEnd[1]}}}clearSelection(){var S;(S=this._selectionService)===null||S===void 0||S.clearSelection()}selectAll(){var S;(S=this._selectionService)===null||S===void 0||S.selectAll()}selectLines(S,R){var B;(B=this._selectionService)===null||B===void 0||B.selectLines(S,R)}_keyDown(S){if(this._keyDownHandled=!1,this._keyDownSeen=!0,this._customKeyEventHandler&&this._customKeyEventHandler(S)===!1)return!1;const R=this.browser.isMac&&this.options.macOptionIsMeta&&S.altKey;if(!R&&!this._compositionHelper.keydown(S))return this.options.scrollOnUserInput&&this.buffer.ybase!==this.buffer.ydisp&&this.scrollToBottom(),!1;R||S.key!=="Dead"&&S.key!=="AltGraph"||(this._unprocessedDeadKey=!0);const B=(0,C.evaluateKeyboardEvent)(S,this.coreService.decPrivateModes.applicationCursorKeys,this.browser.isMac,this.options.macOptionIsMeta);if(this.updateCursorStyle(S),B.type===3||B.type===2){const A=this.rows-1;return this.scrollLines(B.type===2?-A:A),this.cancel(S,!0)}return B.type===1&&this.selectAll(),!!this._isThirdLevelShift(this.browser,S)||(B.cancel&&this.cancel(S,!0),!B.key||!!(S.key&&!S.ctrlKey&&!S.altKey&&!S.metaKey&&S.key.length===1&&S.key.charCodeAt(0)>=65&&S.key.charCodeAt(0)<=90)||(this._unprocessedDeadKey?(this._unprocessedDeadKey=!1,!0):(B.key!==M.C0.ETX&&B.key!==M.C0.CR||(this.textarea.value=""),this._onKey.fire({key:B.key,domEvent:S}),this._showCursor(),this.coreService.triggerDataEvent(B.key,!0),!this.optionsService.rawOptions.screenReaderMode||S.altKey||S.ctrlKey?this.cancel(S,!0):void(this._keyDownHandled=!0))))}_isThirdLevelShift(S,R){const B=S.isMac&&!this.options.macOptionIsMeta&&R.altKey&&!R.ctrlKey&&!R.metaKey||S.isWindows&&R.altKey&&R.ctrlKey&&!R.metaKey||S.isWindows&&R.getModifierState("AltGraph");return R.type==="keypress"?B:B&&(!R.keyCode||R.keyCode>47)}_keyUp(S){this._keyDownSeen=!1,this._customKeyEventHandler&&this._customKeyEventHandler(S)===!1||(function(R){return R.keyCode===16||R.keyCode===17||R.keyCode===18}(S)||this.focus(),this.updateCursorStyle(S),this._keyPressHandled=!1)}_keyPress(S){let R;if(this._keyPressHandled=!1,this._keyDownHandled||this._customKeyEventHandler&&this._customKeyEventHandler(S)===!1)return!1;if(this.cancel(S),S.charCode)R=S.charCode;else if(S.which===null||S.which===void 0)R=S.keyCode;else{if(S.which===0||S.charCode===0)return!1;R=S.which}return!(!R||(S.altKey||S.ctrlKey||S.metaKey)&&!this._isThirdLevelShift(this.browser,S)||(R=String.fromCharCode(R),this._onKey.fire({key:R,domEvent:S}),this._showCursor(),this.coreService.triggerDataEvent(R,!0),this._keyPressHandled=!0,this._unprocessedDeadKey=!1,0))}_inputEvent(S){if(S.data&&S.inputType==="insertText"&&(!S.composed||!this._keyDownSeen)&&!this.optionsService.rawOptions.screenReaderMode){if(this._keyPressHandled)return!1;this._unprocessedDeadKey=!1;const R=S.data;return this.coreService.triggerDataEvent(R,!0),this.cancel(S),!0}return!1}resize(S,R){S!==this.cols||R!==this.rows?super.resize(S,R):this._charSizeService&&!this._charSizeService.hasValidSize&&this._charSizeService.measure()}_afterResize(S,R){var B,A;(B=this._charSizeService)===null||B===void 0||B.measure(),(A=this.viewport)===null||A===void 0||A.syncScrollArea(!0)}clear(){var S;if(this.buffer.ybase!==0||this.buffer.y!==0){this.buffer.clearAllMarkers(),this.buffer.lines.set(0,this.buffer.lines.get(this.buffer.ybase+this.buffer.y)),this.buffer.lines.length=1,this.buffer.ydisp=0,this.buffer.ybase=0,this.buffer.y=0;for(let R=1;R<this.rows;R++)this.buffer.lines.push(this.buffer.getBlankLine(O.DEFAULT_ATTR_DATA));this._onScroll.fire({position:this.buffer.ydisp,source:0}),(S=this.viewport)===null||S===void 0||S.reset(),this.refresh(0,this.rows-1)}}reset(){var S,R;this.options.rows=this.rows,this.options.cols=this.cols;const B=this._customKeyEventHandler;this._setup(),super.reset(),(S=this._selectionService)===null||S===void 0||S.reset(),this._decorationService.reset(),(R=this.viewport)===null||R===void 0||R.reset(),this._customKeyEventHandler=B,this.refresh(0,this.rows-1)}clearTextureAtlas(){var S;(S=this._renderService)===null||S===void 0||S.clearTextureAtlas()}_reportFocus(){var S;!((S=this.element)===null||S===void 0)&&S.classList.contains("focus")?this.coreService.triggerDataEvent(M.C0.ESC+"[I"):this.coreService.triggerDataEvent(M.C0.ESC+"[O")}_reportWindowsOptions(S){if(this._renderService)switch(S){case P.WindowsOptionsReportType.GET_WIN_SIZE_PIXELS:const R=this._renderService.dimensions.css.canvas.width.toFixed(0),B=this._renderService.dimensions.css.canvas.height.toFixed(0);this.coreService.triggerDataEvent(`${M.C0.ESC}[4;${B};${R}t`);break;case P.WindowsOptionsReportType.GET_CELL_SIZE_PIXELS:const A=this._renderService.dimensions.css.cell.width.toFixed(0),N=this._renderService.dimensions.css.cell.height.toFixed(0);this.coreService.triggerDataEvent(`${M.C0.ESC}[6;${N};${A}t`)}}cancel(S,R){if(this.options.cancelEvents||R)return S.preventDefault(),S.stopPropagation(),!1}}r.Terminal=W},9924:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.TimeBasedDebouncer=void 0,r.TimeBasedDebouncer=class{constructor(a,c=1e3){this._renderCallback=a,this._debounceThresholdMS=c,this._lastRefreshMs=0,this._additionalRefreshRequested=!1}dispose(){this._refreshTimeoutID&&clearTimeout(this._refreshTimeoutID)}refresh(a,c,_){this._rowCount=_,a=a!==void 0?a:0,c=c!==void 0?c:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,a):a,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,c):c;const n=Date.now();if(n-this._lastRefreshMs>=this._debounceThresholdMS)this._lastRefreshMs=n,this._innerRefresh();else if(!this._additionalRefreshRequested){const d=n-this._lastRefreshMs,f=this._debounceThresholdMS-d;this._additionalRefreshRequested=!0,this._refreshTimeoutID=window.setTimeout(()=>{this._lastRefreshMs=Date.now(),this._innerRefresh(),this._additionalRefreshRequested=!1,this._refreshTimeoutID=void 0},f)}}_innerRefresh(){if(this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return;const a=Math.max(this._rowStart,0),c=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(a,c)}}},1680:function(I,r,a){var c=this&&this.__decorate||function(s,t,i,o){var l,v=arguments.length,m=v<3?t:o===null?o=Object.getOwnPropertyDescriptor(t,i):o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")m=Reflect.decorate(s,t,i,o);else for(var h=s.length-1;h>=0;h--)(l=s[h])&&(m=(v<3?l(m):v>3?l(t,i,m):l(t,i))||m);return v>3&&m&&Object.defineProperty(t,i,m),m},_=this&&this.__param||function(s,t){return function(i,o){t(i,o,s)}};Object.defineProperty(r,"__esModule",{value:!0}),r.Viewport=void 0;const n=a(3656),d=a(4725),f=a(8460),g=a(844),u=a(2585);let e=r.Viewport=class extends g.Disposable{constructor(s,t,i,o,l,v,m,h){super(),this._viewportElement=s,this._scrollArea=t,this._bufferService=i,this._optionsService=o,this._charSizeService=l,this._renderService=v,this._coreBrowserService=m,this.scrollBarWidth=0,this._currentRowHeight=0,this._currentDeviceCellHeight=0,this._lastRecordedBufferLength=0,this._lastRecordedViewportHeight=0,this._lastRecordedBufferHeight=0,this._lastTouchY=0,this._lastScrollTop=0,this._wheelPartialScroll=0,this._refreshAnimationFrame=null,this._ignoreNextScrollEvent=!1,this._smoothScrollState={startTime:0,origin:-1,target:-1},this._onRequestScrollLines=this.register(new f.EventEmitter),this.onRequestScrollLines=this._onRequestScrollLines.event,this.scrollBarWidth=this._viewportElement.offsetWidth-this._scrollArea.offsetWidth||15,this.register((0,n.addDisposableDomListener)(this._viewportElement,"scroll",this._handleScroll.bind(this))),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(p=>this._activeBuffer=p.activeBuffer)),this._renderDimensions=this._renderService.dimensions,this.register(this._renderService.onDimensionsChange(p=>this._renderDimensions=p)),this._handleThemeChange(h.colors),this.register(h.onChangeColors(p=>this._handleThemeChange(p))),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.syncScrollArea())),setTimeout(()=>this.syncScrollArea())}_handleThemeChange(s){this._viewportElement.style.backgroundColor=s.background.css}reset(){this._currentRowHeight=0,this._currentDeviceCellHeight=0,this._lastRecordedBufferLength=0,this._lastRecordedViewportHeight=0,this._lastRecordedBufferHeight=0,this._lastTouchY=0,this._lastScrollTop=0,this._coreBrowserService.window.requestAnimationFrame(()=>this.syncScrollArea())}_refresh(s){if(s)return this._innerRefresh(),void(this._refreshAnimationFrame!==null&&this._coreBrowserService.window.cancelAnimationFrame(this._refreshAnimationFrame));this._refreshAnimationFrame===null&&(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._charSizeService.height>0){this._currentRowHeight=this._renderService.dimensions.device.cell.height/this._coreBrowserService.dpr,this._currentDeviceCellHeight=this._renderService.dimensions.device.cell.height,this._lastRecordedViewportHeight=this._viewportElement.offsetHeight;const t=Math.round(this._currentRowHeight*this._lastRecordedBufferLength)+(this._lastRecordedViewportHeight-this._renderService.dimensions.css.canvas.height);this._lastRecordedBufferHeight!==t&&(this._lastRecordedBufferHeight=t,this._scrollArea.style.height=this._lastRecordedBufferHeight+"px")}const s=this._bufferService.buffer.ydisp*this._currentRowHeight;this._viewportElement.scrollTop!==s&&(this._ignoreNextScrollEvent=!0,this._viewportElement.scrollTop=s),this._refreshAnimationFrame=null}syncScrollArea(s=!1){if(this._lastRecordedBufferLength!==this._bufferService.buffer.lines.length)return this._lastRecordedBufferLength=this._bufferService.buffer.lines.length,void this._refresh(s);this._lastRecordedViewportHeight===this._renderService.dimensions.css.canvas.height&&this._lastScrollTop===this._activeBuffer.ydisp*this._currentRowHeight&&this._renderDimensions.device.cell.height===this._currentDeviceCellHeight||this._refresh(s)}_handleScroll(s){if(this._lastScrollTop=this._viewportElement.scrollTop,!this._viewportElement.offsetParent)return;if(this._ignoreNextScrollEvent)return this._ignoreNextScrollEvent=!1,void this._onRequestScrollLines.fire({amount:0,suppressScrollEvent:!0});const t=Math.round(this._lastScrollTop/this._currentRowHeight)-this._bufferService.buffer.ydisp;this._onRequestScrollLines.fire({amount:t,suppressScrollEvent:!0})}_smoothScroll(){if(this._isDisposed||this._smoothScrollState.origin===-1||this._smoothScrollState.target===-1)return;const s=this._smoothScrollPercent();this._viewportElement.scrollTop=this._smoothScrollState.origin+Math.round(s*(this._smoothScrollState.target-this._smoothScrollState.origin)),s<1?this._coreBrowserService.window.requestAnimationFrame(()=>this._smoothScroll()):this._clearSmoothScrollState()}_smoothScrollPercent(){return this._optionsService.rawOptions.smoothScrollDuration&&this._smoothScrollState.startTime?Math.max(Math.min((Date.now()-this._smoothScrollState.startTime)/this._optionsService.rawOptions.smoothScrollDuration,1),0):1}_clearSmoothScrollState(){this._smoothScrollState.startTime=0,this._smoothScrollState.origin=-1,this._smoothScrollState.target=-1}_bubbleScroll(s,t){const i=this._viewportElement.scrollTop+this._lastRecordedViewportHeight;return!(t<0&&this._viewportElement.scrollTop!==0||t>0&&i<this._lastRecordedBufferHeight)||(s.cancelable&&s.preventDefault(),!1)}handleWheel(s){const t=this._getPixelsScrolled(s);return t!==0&&(this._optionsService.rawOptions.smoothScrollDuration?(this._smoothScrollState.startTime=Date.now(),this._smoothScrollPercent()<1?(this._smoothScrollState.origin=this._viewportElement.scrollTop,this._smoothScrollState.target===-1?this._smoothScrollState.target=this._viewportElement.scrollTop+t:this._smoothScrollState.target+=t,this._smoothScrollState.target=Math.max(Math.min(this._smoothScrollState.target,this._viewportElement.scrollHeight),0),this._smoothScroll()):this._clearSmoothScrollState()):this._viewportElement.scrollTop+=t,this._bubbleScroll(s,t))}scrollLines(s){if(s!==0)if(this._optionsService.rawOptions.smoothScrollDuration){const t=s*this._currentRowHeight;this._smoothScrollState.startTime=Date.now(),this._smoothScrollPercent()<1?(this._smoothScrollState.origin=this._viewportElement.scrollTop,this._smoothScrollState.target=this._smoothScrollState.origin+t,this._smoothScrollState.target=Math.max(Math.min(this._smoothScrollState.target,this._viewportElement.scrollHeight),0),this._smoothScroll()):this._clearSmoothScrollState()}else this._onRequestScrollLines.fire({amount:s,suppressScrollEvent:!1})}_getPixelsScrolled(s){if(s.deltaY===0||s.shiftKey)return 0;let t=this._applyScrollModifier(s.deltaY,s);return s.deltaMode===WheelEvent.DOM_DELTA_LINE?t*=this._currentRowHeight:s.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(t*=this._currentRowHeight*this._bufferService.rows),t}getBufferElements(s,t){var i;let o,l="";const v=[],m=t??this._bufferService.buffer.lines.length,h=this._bufferService.buffer.lines;for(let p=s;p<m;p++){const b=h.get(p);if(!b)continue;const L=(i=h.get(p+1))===null||i===void 0?void 0:i.isWrapped;if(l+=b.translateToString(!L),!L||p===h.length-1){const y=document.createElement("div");y.textContent=l,v.push(y),l.length>0&&(o=y),l=""}}return{bufferElements:v,cursorElement:o}}getLinesScrolled(s){if(s.deltaY===0||s.shiftKey)return 0;let t=this._applyScrollModifier(s.deltaY,s);return s.deltaMode===WheelEvent.DOM_DELTA_PIXEL?(t/=this._currentRowHeight+0,this._wheelPartialScroll+=t,t=Math.floor(Math.abs(this._wheelPartialScroll))*(this._wheelPartialScroll>0?1:-1),this._wheelPartialScroll%=1):s.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(t*=this._bufferService.rows),t}_applyScrollModifier(s,t){const i=this._optionsService.rawOptions.fastScrollModifier;return i==="alt"&&t.altKey||i==="ctrl"&&t.ctrlKey||i==="shift"&&t.shiftKey?s*this._optionsService.rawOptions.fastScrollSensitivity*this._optionsService.rawOptions.scrollSensitivity:s*this._optionsService.rawOptions.scrollSensitivity}handleTouchStart(s){this._lastTouchY=s.touches[0].pageY}handleTouchMove(s){const t=this._lastTouchY-s.touches[0].pageY;return this._lastTouchY=s.touches[0].pageY,t!==0&&(this._viewportElement.scrollTop+=t,this._bubbleScroll(s,t))}};r.Viewport=e=c([_(2,u.IBufferService),_(3,u.IOptionsService),_(4,d.ICharSizeService),_(5,d.IRenderService),_(6,d.ICoreBrowserService),_(7,d.IThemeService)],e)},3107:function(I,r,a){var c=this&&this.__decorate||function(e,s,t,i){var o,l=arguments.length,v=l<3?s:i===null?i=Object.getOwnPropertyDescriptor(s,t):i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(e,s,t,i);else for(var m=e.length-1;m>=0;m--)(o=e[m])&&(v=(l<3?o(v):l>3?o(s,t,v):o(s,t))||v);return l>3&&v&&Object.defineProperty(s,t,v),v},_=this&&this.__param||function(e,s){return function(t,i){s(t,i,e)}};Object.defineProperty(r,"__esModule",{value:!0}),r.BufferDecorationRenderer=void 0;const n=a(3656),d=a(4725),f=a(844),g=a(2585);let u=r.BufferDecorationRenderer=class extends f.Disposable{constructor(e,s,t,i){super(),this._screenElement=e,this._bufferService=s,this._decorationService=t,this._renderService=i,this._decorationElements=new Map,this._altBufferIsActive=!1,this._dimensionsChanged=!1,this._container=document.createElement("div"),this._container.classList.add("xterm-decoration-container"),this._screenElement.appendChild(this._container),this.register(this._renderService.onRenderedViewportChange(()=>this._doRefreshDecorations())),this.register(this._renderService.onDimensionsChange(()=>{this._dimensionsChanged=!0,this._queueRefresh()})),this.register((0,n.addDisposableDomListener)(window,"resize",()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._altBufferIsActive=this._bufferService.buffer===this._bufferService.buffers.alt})),this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh())),this.register(this._decorationService.onDecorationRemoved(o=>this._removeDecoration(o))),this.register((0,f.toDisposable)(()=>{this._container.remove(),this._decorationElements.clear()}))}_queueRefresh(){this._animationFrame===void 0&&(this._animationFrame=this._renderService.addRefreshCallback(()=>{this._doRefreshDecorations(),this._animationFrame=void 0}))}_doRefreshDecorations(){for(const e of this._decorationService.decorations)this._renderDecoration(e);this._dimensionsChanged=!1}_renderDecoration(e){this._refreshStyle(e),this._dimensionsChanged&&this._refreshXPosition(e)}_createElement(e){var s,t;const i=document.createElement("div");i.classList.add("xterm-decoration"),i.classList.toggle("xterm-decoration-top-layer",((s=e==null?void 0:e.options)===null||s===void 0?void 0:s.layer)==="top"),i.style.width=`${Math.round((e.options.width||1)*this._renderService.dimensions.css.cell.width)}px`,i.style.height=(e.options.height||1)*this._renderService.dimensions.css.cell.height+"px",i.style.top=(e.marker.line-this._bufferService.buffers.active.ydisp)*this._renderService.dimensions.css.cell.height+"px",i.style.lineHeight=`${this._renderService.dimensions.css.cell.height}px`;const o=(t=e.options.x)!==null&&t!==void 0?t:0;return o&&o>this._bufferService.cols&&(i.style.display="none"),this._refreshXPosition(e,i),i}_refreshStyle(e){const s=e.marker.line-this._bufferService.buffers.active.ydisp;if(s<0||s>=this._bufferService.rows)e.element&&(e.element.style.display="none",e.onRenderEmitter.fire(e.element));else{let t=this._decorationElements.get(e);t||(t=this._createElement(e),e.element=t,this._decorationElements.set(e,t),this._container.appendChild(t),e.onDispose(()=>{this._decorationElements.delete(e),t.remove()})),t.style.top=s*this._renderService.dimensions.css.cell.height+"px",t.style.display=this._altBufferIsActive?"none":"block",e.onRenderEmitter.fire(t)}}_refreshXPosition(e,s=e.element){var t;if(!s)return;const i=(t=e.options.x)!==null&&t!==void 0?t:0;(e.options.anchor||"left")==="right"?s.style.right=i?i*this._renderService.dimensions.css.cell.width+"px":"":s.style.left=i?i*this._renderService.dimensions.css.cell.width+"px":""}_removeDecoration(e){var s;(s=this._decorationElements.get(e))===null||s===void 0||s.remove(),this._decorationElements.delete(e),e.dispose()}};r.BufferDecorationRenderer=u=c([_(1,g.IBufferService),_(2,g.IDecorationService),_(3,d.IRenderService)],u)},5871:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.ColorZoneStore=void 0,r.ColorZoneStore=class{constructor(){this._zones=[],this._zonePool=[],this._zonePoolIndex=0,this._linePadding={full:0,left:0,center:0,right:0}}get zones(){return this._zonePool.length=Math.min(this._zonePool.length,this._zones.length),this._zones}clear(){this._zones.length=0,this._zonePoolIndex=0}addDecoration(a){if(a.options.overviewRulerOptions){for(const c of this._zones)if(c.color===a.options.overviewRulerOptions.color&&c.position===a.options.overviewRulerOptions.position){if(this._lineIntersectsZone(c,a.marker.line))return;if(this._lineAdjacentToZone(c,a.marker.line,a.options.overviewRulerOptions.position))return void this._addLineToZone(c,a.marker.line)}if(this._zonePoolIndex<this._zonePool.length)return this._zonePool[this._zonePoolIndex].color=a.options.overviewRulerOptions.color,this._zonePool[this._zonePoolIndex].position=a.options.overviewRulerOptions.position,this._zonePool[this._zonePoolIndex].startBufferLine=a.marker.line,this._zonePool[this._zonePoolIndex].endBufferLine=a.marker.line,void this._zones.push(this._zonePool[this._zonePoolIndex++]);this._zones.push({color:a.options.overviewRulerOptions.color,position:a.options.overviewRulerOptions.position,startBufferLine:a.marker.line,endBufferLine:a.marker.line}),this._zonePool.push(this._zones[this._zones.length-1]),this._zonePoolIndex++}}setPadding(a){this._linePadding=a}_lineIntersectsZone(a,c){return c>=a.startBufferLine&&c<=a.endBufferLine}_lineAdjacentToZone(a,c,_){return c>=a.startBufferLine-this._linePadding[_||"full"]&&c<=a.endBufferLine+this._linePadding[_||"full"]}_addLineToZone(a,c){a.startBufferLine=Math.min(a.startBufferLine,c),a.endBufferLine=Math.max(a.endBufferLine,c)}}},5744:function(I,r,a){var c=this&&this.__decorate||function(o,l,v,m){var h,p=arguments.length,b=p<3?l:m===null?m=Object.getOwnPropertyDescriptor(l,v):m;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")b=Reflect.decorate(o,l,v,m);else for(var L=o.length-1;L>=0;L--)(h=o[L])&&(b=(p<3?h(b):p>3?h(l,v,b):h(l,v))||b);return p>3&&b&&Object.defineProperty(l,v,b),b},_=this&&this.__param||function(o,l){return function(v,m){l(v,m,o)}};Object.defineProperty(r,"__esModule",{value:!0}),r.OverviewRulerRenderer=void 0;const n=a(5871),d=a(3656),f=a(4725),g=a(844),u=a(2585),e={full:0,left:0,center:0,right:0},s={full:0,left:0,center:0,right:0},t={full:0,left:0,center:0,right:0};let i=r.OverviewRulerRenderer=class extends g.Disposable{get _width(){return this._optionsService.options.overviewRulerWidth||0}constructor(o,l,v,m,h,p,b){var L;super(),this._viewportElement=o,this._screenElement=l,this._bufferService=v,this._decorationService=m,this._renderService=h,this._optionsService=p,this._coreBrowseService=b,this._colorZoneStore=new n.ColorZoneStore,this._shouldUpdateDimensions=!0,this._shouldUpdateAnchor=!0,this._lastKnownBufferLength=0,this._canvas=document.createElement("canvas"),this._canvas.classList.add("xterm-decoration-overview-ruler"),this._refreshCanvasDimensions(),(L=this._viewportElement.parentElement)===null||L===void 0||L.insertBefore(this._canvas,this._viewportElement);const y=this._canvas.getContext("2d");if(!y)throw new Error("Ctx cannot be null");this._ctx=y,this._registerDecorationListeners(),this._registerBufferChangeListeners(),this._registerDimensionChangeListeners(),this.register((0,g.toDisposable)(()=>{var k;(k=this._canvas)===null||k===void 0||k.remove()}))}_registerDecorationListeners(){this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh(void 0,!0))),this.register(this._decorationService.onDecorationRemoved(()=>this._queueRefresh(void 0,!0)))}_registerBufferChangeListeners(){this.register(this._renderService.onRenderedViewportChange(()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._canvas.style.display=this._bufferService.buffer===this._bufferService.buffers.alt?"none":"block"})),this.register(this._bufferService.onScroll(()=>{this._lastKnownBufferLength!==this._bufferService.buffers.normal.lines.length&&(this._refreshDrawHeightConstants(),this._refreshColorZonePadding())}))}_registerDimensionChangeListeners(){this.register(this._renderService.onRender(()=>{this._containerHeight&&this._containerHeight===this._screenElement.clientHeight||(this._queueRefresh(!0),this._containerHeight=this._screenElement.clientHeight)})),this.register(this._optionsService.onSpecificOptionChange("overviewRulerWidth",()=>this._queueRefresh(!0))),this.register((0,d.addDisposableDomListener)(this._coreBrowseService.window,"resize",()=>this._queueRefresh(!0))),this._queueRefresh(!0)}_refreshDrawConstants(){const o=Math.floor(this._canvas.width/3),l=Math.ceil(this._canvas.width/3);s.full=this._canvas.width,s.left=o,s.center=l,s.right=o,this._refreshDrawHeightConstants(),t.full=0,t.left=0,t.center=s.left,t.right=s.left+s.center}_refreshDrawHeightConstants(){e.full=Math.round(2*this._coreBrowseService.dpr);const o=this._canvas.height/this._bufferService.buffer.lines.length,l=Math.round(Math.max(Math.min(o,12),6)*this._coreBrowseService.dpr);e.left=l,e.center=l,e.right=l}_refreshColorZonePadding(){this._colorZoneStore.setPadding({full:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.full),left:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.left),center:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.center),right:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.right)}),this._lastKnownBufferLength=this._bufferService.buffers.normal.lines.length}_refreshCanvasDimensions(){this._canvas.style.width=`${this._width}px`,this._canvas.width=Math.round(this._width*this._coreBrowseService.dpr),this._canvas.style.height=`${this._screenElement.clientHeight}px`,this._canvas.height=Math.round(this._screenElement.clientHeight*this._coreBrowseService.dpr),this._refreshDrawConstants(),this._refreshColorZonePadding()}_refreshDecorations(){this._shouldUpdateDimensions&&this._refreshCanvasDimensions(),this._ctx.clearRect(0,0,this._canvas.width,this._canvas.height),this._colorZoneStore.clear();for(const l of this._decorationService.decorations)this._colorZoneStore.addDecoration(l);this._ctx.lineWidth=1;const o=this._colorZoneStore.zones;for(const l of o)l.position!=="full"&&this._renderColorZone(l);for(const l of o)l.position==="full"&&this._renderColorZone(l);this._shouldUpdateDimensions=!1,this._shouldUpdateAnchor=!1}_renderColorZone(o){this._ctx.fillStyle=o.color,this._ctx.fillRect(t[o.position||"full"],Math.round((this._canvas.height-1)*(o.startBufferLine/this._bufferService.buffers.active.lines.length)-e[o.position||"full"]/2),s[o.position||"full"],Math.round((this._canvas.height-1)*((o.endBufferLine-o.startBufferLine)/this._bufferService.buffers.active.lines.length)+e[o.position||"full"]))}_queueRefresh(o,l){this._shouldUpdateDimensions=o||this._shouldUpdateDimensions,this._shouldUpdateAnchor=l||this._shouldUpdateAnchor,this._animationFrame===void 0&&(this._animationFrame=this._coreBrowseService.window.requestAnimationFrame(()=>{this._refreshDecorations(),this._animationFrame=void 0}))}};r.OverviewRulerRenderer=i=c([_(2,u.IBufferService),_(3,u.IDecorationService),_(4,f.IRenderService),_(5,u.IOptionsService),_(6,f.ICoreBrowserService)],i)},2950:function(I,r,a){var c=this&&this.__decorate||function(u,e,s,t){var i,o=arguments.length,l=o<3?e:t===null?t=Object.getOwnPropertyDescriptor(e,s):t;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")l=Reflect.decorate(u,e,s,t);else for(var v=u.length-1;v>=0;v--)(i=u[v])&&(l=(o<3?i(l):o>3?i(e,s,l):i(e,s))||l);return o>3&&l&&Object.defineProperty(e,s,l),l},_=this&&this.__param||function(u,e){return function(s,t){e(s,t,u)}};Object.defineProperty(r,"__esModule",{value:!0}),r.CompositionHelper=void 0;const n=a(4725),d=a(2585),f=a(2584);let g=r.CompositionHelper=class{get isComposing(){return this._isComposing}constructor(u,e,s,t,i,o){this._textarea=u,this._compositionView=e,this._bufferService=s,this._optionsService=t,this._coreService=i,this._renderService=o,this._isComposing=!1,this._isSendingComposition=!1,this._compositionPosition={start:0,end:0},this._dataAlreadySent=""}compositionstart(){this._isComposing=!0,this._compositionPosition.start=this._textarea.value.length,this._compositionView.textContent="",this._dataAlreadySent="",this._compositionView.classList.add("active")}compositionupdate(u){this._compositionView.textContent=u.data,this.updateCompositionElements(),setTimeout(()=>{this._compositionPosition.end=this._textarea.value.length},0)}compositionend(){this._finalizeComposition(!0)}keydown(u){if(this._isComposing||this._isSendingComposition){if(u.keyCode===229||u.keyCode===16||u.keyCode===17||u.keyCode===18)return!1;this._finalizeComposition(!1)}return u.keyCode!==229||(this._handleAnyTextareaChanges(),!1)}_finalizeComposition(u){if(this._compositionView.classList.remove("active"),this._isComposing=!1,u){const e={start:this._compositionPosition.start,end:this._compositionPosition.end};this._isSendingComposition=!0,setTimeout(()=>{if(this._isSendingComposition){let s;this._isSendingComposition=!1,e.start+=this._dataAlreadySent.length,s=this._isComposing?this._textarea.value.substring(e.start,e.end):this._textarea.value.substring(e.start),s.length>0&&this._coreService.triggerDataEvent(s,!0)}},0)}else{this._isSendingComposition=!1;const e=this._textarea.value.substring(this._compositionPosition.start,this._compositionPosition.end);this._coreService.triggerDataEvent(e,!0)}}_handleAnyTextareaChanges(){const u=this._textarea.value;setTimeout(()=>{if(!this._isComposing){const e=this._textarea.value,s=e.replace(u,"");this._dataAlreadySent=s,e.length>u.length?this._coreService.triggerDataEvent(s,!0):e.length<u.length?this._coreService.triggerDataEvent(`${f.C0.DEL}`,!0):e.length===u.length&&e!==u&&this._coreService.triggerDataEvent(e,!0)}},0)}updateCompositionElements(u){if(this._isComposing){if(this._bufferService.buffer.isCursorInViewport){const e=Math.min(this._bufferService.buffer.x,this._bufferService.cols-1),s=this._renderService.dimensions.css.cell.height,t=this._bufferService.buffer.y*this._renderService.dimensions.css.cell.height,i=e*this._renderService.dimensions.css.cell.width;this._compositionView.style.left=i+"px",this._compositionView.style.top=t+"px",this._compositionView.style.height=s+"px",this._compositionView.style.lineHeight=s+"px",this._compositionView.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._compositionView.style.fontSize=this._optionsService.rawOptions.fontSize+"px";const o=this._compositionView.getBoundingClientRect();this._textarea.style.left=i+"px",this._textarea.style.top=t+"px",this._textarea.style.width=Math.max(o.width,1)+"px",this._textarea.style.height=Math.max(o.height,1)+"px",this._textarea.style.lineHeight=o.height+"px"}u||setTimeout(()=>this.updateCompositionElements(!0),0)}}};r.CompositionHelper=g=c([_(2,d.IBufferService),_(3,d.IOptionsService),_(4,d.ICoreService),_(5,n.IRenderService)],g)},9806:(I,r)=>{function a(c,_,n){const d=n.getBoundingClientRect(),f=c.getComputedStyle(n),g=parseInt(f.getPropertyValue("padding-left")),u=parseInt(f.getPropertyValue("padding-top"));return[_.clientX-d.left-g,_.clientY-d.top-u]}Object.defineProperty(r,"__esModule",{value:!0}),r.getCoords=r.getCoordsRelativeToElement=void 0,r.getCoordsRelativeToElement=a,r.getCoords=function(c,_,n,d,f,g,u,e,s){if(!g)return;const t=a(c,_,n);return t?(t[0]=Math.ceil((t[0]+(s?u/2:0))/u),t[1]=Math.ceil(t[1]/e),t[0]=Math.min(Math.max(t[0],1),d+(s?1:0)),t[1]=Math.min(Math.max(t[1],1),f),t):void 0}},9504:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.moveToCellSequence=void 0;const c=a(2584);function _(e,s,t,i){const o=e-n(e,t),l=s-n(s,t),v=Math.abs(o-l)-function(m,h,p){let b=0;const L=m-n(m,p),y=h-n(h,p);for(let k=0;k<Math.abs(L-y);k++){const x=d(m,h)==="A"?-1:1,T=p.buffer.lines.get(L+x*k);T!=null&&T.isWrapped&&b++}return b}(e,s,t);return u(v,g(d(e,s),i))}function n(e,s){let t=0,i=s.buffer.lines.get(e),o=i==null?void 0:i.isWrapped;for(;o&&e>=0&&e<s.rows;)t++,i=s.buffer.lines.get(--e),o=i==null?void 0:i.isWrapped;return t}function d(e,s){return e>s?"A":"B"}function f(e,s,t,i,o,l){let v=e,m=s,h="";for(;v!==t||m!==i;)v+=o?1:-1,o&&v>l.cols-1?(h+=l.buffer.translateBufferLineToString(m,!1,e,v),v=0,e=0,m++):!o&&v<0&&(h+=l.buffer.translateBufferLineToString(m,!1,0,e+1),v=l.cols-1,e=v,m--);return h+l.buffer.translateBufferLineToString(m,!1,e,v)}function g(e,s){const t=s?"O":"[";return c.C0.ESC+t+e}function u(e,s){e=Math.floor(e);let t="";for(let i=0;i<e;i++)t+=s;return t}r.moveToCellSequence=function(e,s,t,i){const o=t.buffer.x,l=t.buffer.y;if(!t.buffer.hasScrollback)return function(h,p,b,L,y,k){return _(p,L,y,k).length===0?"":u(f(h,p,h,p-n(p,y),!1,y).length,g("D",k))}(o,l,0,s,t,i)+_(l,s,t,i)+function(h,p,b,L,y,k){let x;x=_(p,L,y,k).length>0?L-n(L,y):p;const T=L,O=function(M,C,w,E,D,P){let H;return H=_(w,E,D,P).length>0?E-n(E,D):C,M<w&&H<=E||M>=w&&H<E?"C":"D"}(h,p,b,L,y,k);return u(f(h,x,b,T,O==="C",y).length,g(O,k))}(o,l,e,s,t,i);let v;if(l===s)return v=o>e?"D":"C",u(Math.abs(o-e),g(v,i));v=l>s?"D":"C";const m=Math.abs(l-s);return u(function(h,p){return p.cols-h}(l>s?e:o,t)+(m-1)*t.cols+1+((l>s?o:e)-1),g(v,i))}},1296:function(I,r,a){var c=this&&this.__decorate||function(y,k,x,T){var O,M=arguments.length,C=M<3?k:T===null?T=Object.getOwnPropertyDescriptor(k,x):T;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")C=Reflect.decorate(y,k,x,T);else for(var w=y.length-1;w>=0;w--)(O=y[w])&&(C=(M<3?O(C):M>3?O(k,x,C):O(k,x))||C);return M>3&&C&&Object.defineProperty(k,x,C),C},_=this&&this.__param||function(y,k){return function(x,T){k(x,T,y)}};Object.defineProperty(r,"__esModule",{value:!0}),r.DomRenderer=void 0;const n=a(3787),d=a(2550),f=a(2223),g=a(6171),u=a(4725),e=a(8055),s=a(8460),t=a(844),i=a(2585),o="xterm-dom-renderer-owner-",l="xterm-rows",v="xterm-fg-",m="xterm-bg-",h="xterm-focus",p="xterm-selection";let b=1,L=r.DomRenderer=class extends t.Disposable{constructor(y,k,x,T,O,M,C,w,E,D){super(),this._element=y,this._screenElement=k,this._viewportElement=x,this._linkifier2=T,this._charSizeService=M,this._optionsService=C,this._bufferService=w,this._coreBrowserService=E,this._themeService=D,this._terminalClass=b++,this._rowElements=[],this.onRequestRedraw=this.register(new s.EventEmitter).event,this._rowContainer=document.createElement("div"),this._rowContainer.classList.add(l),this._rowContainer.style.lineHeight="normal",this._rowContainer.setAttribute("aria-hidden","true"),this._refreshRowElements(this._bufferService.cols,this._bufferService.rows),this._selectionContainer=document.createElement("div"),this._selectionContainer.classList.add(p),this._selectionContainer.setAttribute("aria-hidden","true"),this.dimensions=(0,g.createRenderDimensions)(),this._updateDimensions(),this.register(this._optionsService.onOptionChange(()=>this._handleOptionsChanged())),this.register(this._themeService.onChangeColors(P=>this._injectCss(P))),this._injectCss(this._themeService.colors),this._rowFactory=O.createInstance(n.DomRendererRowFactory,document),this._element.classList.add(o+this._terminalClass),this._screenElement.appendChild(this._rowContainer),this._screenElement.appendChild(this._selectionContainer),this.register(this._linkifier2.onShowLinkUnderline(P=>this._handleLinkHover(P))),this.register(this._linkifier2.onHideLinkUnderline(P=>this._handleLinkLeave(P))),this.register((0,t.toDisposable)(()=>{this._element.classList.remove(o+this._terminalClass),this._rowContainer.remove(),this._selectionContainer.remove(),this._widthCache.dispose(),this._themeStyleElement.remove(),this._dimensionsStyleElement.remove()})),this._widthCache=new d.WidthCache(document),this._widthCache.setFont(this._optionsService.rawOptions.fontFamily,this._optionsService.rawOptions.fontSize,this._optionsService.rawOptions.fontWeight,this._optionsService.rawOptions.fontWeightBold),this._setDefaultSpacing()}_updateDimensions(){const y=this._coreBrowserService.dpr;this.dimensions.device.char.width=this._charSizeService.width*y,this.dimensions.device.char.height=Math.ceil(this._charSizeService.height*y),this.dimensions.device.cell.width=this.dimensions.device.char.width+Math.round(this._optionsService.rawOptions.letterSpacing),this.dimensions.device.cell.height=Math.floor(this.dimensions.device.char.height*this._optionsService.rawOptions.lineHeight),this.dimensions.device.char.left=0,this.dimensions.device.char.top=0,this.dimensions.device.canvas.width=this.dimensions.device.cell.width*this._bufferService.cols,this.dimensions.device.canvas.height=this.dimensions.device.cell.height*this._bufferService.rows,this.dimensions.css.canvas.width=Math.round(this.dimensions.device.canvas.width/y),this.dimensions.css.canvas.height=Math.round(this.dimensions.device.canvas.height/y),this.dimensions.css.cell.width=this.dimensions.css.canvas.width/this._bufferService.cols,this.dimensions.css.cell.height=this.dimensions.css.canvas.height/this._bufferService.rows;for(const x of this._rowElements)x.style.width=`${this.dimensions.css.canvas.width}px`,x.style.height=`${this.dimensions.css.cell.height}px`,x.style.lineHeight=`${this.dimensions.css.cell.height}px`,x.style.overflow="hidden";this._dimensionsStyleElement||(this._dimensionsStyleElement=document.createElement("style"),this._screenElement.appendChild(this._dimensionsStyleElement));const k=`${this._terminalSelector} .${l} span { display: inline-block; height: 100%; vertical-align: top;}`;this._dimensionsStyleElement.textContent=k,this._selectionContainer.style.height=this._viewportElement.style.height,this._screenElement.style.width=`${this.dimensions.css.canvas.width}px`,this._screenElement.style.height=`${this.dimensions.css.canvas.height}px`}_injectCss(y){this._themeStyleElement||(this._themeStyleElement=document.createElement("style"),this._screenElement.appendChild(this._themeStyleElement));let k=`${this._terminalSelector} .${l} { color: ${y.foreground.css}; font-family: ${this._optionsService.rawOptions.fontFamily}; font-size: ${this._optionsService.rawOptions.fontSize}px; font-kerning: none; white-space: pre}`;k+=`${this._terminalSelector} .${l} .xterm-dim { color: ${e.color.multiplyOpacity(y.foreground,.5).css};}`,k+=`${this._terminalSelector} span:not(.xterm-bold) { font-weight: ${this._optionsService.rawOptions.fontWeight};}${this._terminalSelector} span.xterm-bold { font-weight: ${this._optionsService.rawOptions.fontWeightBold};}${this._terminalSelector} span.xterm-italic { font-style: italic;}`,k+="@keyframes blink_box_shadow_"+this._terminalClass+" { 50% {  border-bottom-style: hidden; }}",k+="@keyframes blink_block_"+this._terminalClass+` { 0% {  background-color: ${y.cursor.css};  color: ${y.cursorAccent.css}; } 50% {  background-color: inherit;  color: ${y.cursor.css}; }}`,k+=`${this._terminalSelector} .${l}.${h} .xterm-cursor.xterm-cursor-blink:not(.xterm-cursor-block) { animation: blink_box_shadow_`+this._terminalClass+` 1s step-end infinite;}${this._terminalSelector} .${l}.${h} .xterm-cursor.xterm-cursor-blink.xterm-cursor-block { animation: blink_block_`+this._terminalClass+` 1s step-end infinite;}${this._terminalSelector} .${l} .xterm-cursor.xterm-cursor-block { background-color: ${y.cursor.css}; color: ${y.cursorAccent.css};}${this._terminalSelector} .${l} .xterm-cursor.xterm-cursor-outline { outline: 1px solid ${y.cursor.css}; outline-offset: -1px;}${this._terminalSelector} .${l} .xterm-cursor.xterm-cursor-bar { box-shadow: ${this._optionsService.rawOptions.cursorWidth}px 0 0 ${y.cursor.css} inset;}${this._terminalSelector} .${l} .xterm-cursor.xterm-cursor-underline { border-bottom: 1px ${y.cursor.css}; border-bottom-style: solid; height: calc(100% - 1px);}`,k+=`${this._terminalSelector} .${p} { position: absolute; top: 0; left: 0; z-index: 1; pointer-events: none;}${this._terminalSelector}.focus .${p} div { position: absolute; background-color: ${y.selectionBackgroundOpaque.css};}${this._terminalSelector} .${p} div { position: absolute; background-color: ${y.selectionInactiveBackgroundOpaque.css};}`;for(const[x,T]of y.ansi.entries())k+=`${this._terminalSelector} .${v}${x} { color: ${T.css}; }${this._terminalSelector} .${v}${x}.xterm-dim { color: ${e.color.multiplyOpacity(T,.5).css}; }${this._terminalSelector} .${m}${x} { background-color: ${T.css}; }`;k+=`${this._terminalSelector} .${v}${f.INVERTED_DEFAULT_COLOR} { color: ${e.color.opaque(y.background).css}; }${this._terminalSelector} .${v}${f.INVERTED_DEFAULT_COLOR}.xterm-dim { color: ${e.color.multiplyOpacity(e.color.opaque(y.background),.5).css}; }${this._terminalSelector} .${m}${f.INVERTED_DEFAULT_COLOR} { background-color: ${y.foreground.css}; }`,this._themeStyleElement.textContent=k}_setDefaultSpacing(){const y=this.dimensions.css.cell.width-this._widthCache.get("W",!1,!1);this._rowContainer.style.letterSpacing=`${y}px`,this._rowFactory.defaultSpacing=y}handleDevicePixelRatioChange(){this._updateDimensions(),this._widthCache.clear(),this._setDefaultSpacing()}_refreshRowElements(y,k){for(let x=this._rowElements.length;x<=k;x++){const T=document.createElement("div");this._rowContainer.appendChild(T),this._rowElements.push(T)}for(;this._rowElements.length>k;)this._rowContainer.removeChild(this._rowElements.pop())}handleResize(y,k){this._refreshRowElements(y,k),this._updateDimensions()}handleCharSizeChanged(){this._updateDimensions(),this._widthCache.clear(),this._setDefaultSpacing()}handleBlur(){this._rowContainer.classList.remove(h)}handleFocus(){this._rowContainer.classList.add(h),this.renderRows(this._bufferService.buffer.y,this._bufferService.buffer.y)}handleSelectionChanged(y,k,x){if(this._selectionContainer.replaceChildren(),this._rowFactory.handleSelectionChanged(y,k,x),this.renderRows(0,this._bufferService.rows-1),!y||!k)return;const T=y[1]-this._bufferService.buffer.ydisp,O=k[1]-this._bufferService.buffer.ydisp,M=Math.max(T,0),C=Math.min(O,this._bufferService.rows-1);if(M>=this._bufferService.rows||C<0)return;const w=document.createDocumentFragment();if(x){const E=y[0]>k[0];w.appendChild(this._createSelectionElement(M,E?k[0]:y[0],E?y[0]:k[0],C-M+1))}else{const E=T===M?y[0]:0,D=M===O?k[0]:this._bufferService.cols;w.appendChild(this._createSelectionElement(M,E,D));const P=C-M-1;if(w.appendChild(this._createSelectionElement(M+1,0,this._bufferService.cols,P)),M!==C){const H=O===C?k[0]:this._bufferService.cols;w.appendChild(this._createSelectionElement(C,0,H))}}this._selectionContainer.appendChild(w)}_createSelectionElement(y,k,x,T=1){const O=document.createElement("div");return O.style.height=T*this.dimensions.css.cell.height+"px",O.style.top=y*this.dimensions.css.cell.height+"px",O.style.left=k*this.dimensions.css.cell.width+"px",O.style.width=this.dimensions.css.cell.width*(x-k)+"px",O}handleCursorMove(){}_handleOptionsChanged(){this._updateDimensions(),this._injectCss(this._themeService.colors),this._widthCache.setFont(this._optionsService.rawOptions.fontFamily,this._optionsService.rawOptions.fontSize,this._optionsService.rawOptions.fontWeight,this._optionsService.rawOptions.fontWeightBold),this._setDefaultSpacing()}clear(){for(const y of this._rowElements)y.replaceChildren()}renderRows(y,k){const x=this._bufferService.buffer,T=x.ybase+x.y,O=Math.min(x.x,this._bufferService.cols-1),M=this._optionsService.rawOptions.cursorBlink,C=this._optionsService.rawOptions.cursorStyle,w=this._optionsService.rawOptions.cursorInactiveStyle;for(let E=y;E<=k;E++){const D=E+x.ydisp,P=this._rowElements[E],H=x.lines.get(D);if(!P||!H)break;P.replaceChildren(...this._rowFactory.createRow(H,D,D===T,C,w,O,M,this.dimensions.css.cell.width,this._widthCache,-1,-1))}}get _terminalSelector(){return`.${o}${this._terminalClass}`}_handleLinkHover(y){this._setCellUnderline(y.x1,y.x2,y.y1,y.y2,y.cols,!0)}_handleLinkLeave(y){this._setCellUnderline(y.x1,y.x2,y.y1,y.y2,y.cols,!1)}_setCellUnderline(y,k,x,T,O,M){x<0&&(y=0),T<0&&(k=0);const C=this._bufferService.rows-1;x=Math.max(Math.min(x,C),0),T=Math.max(Math.min(T,C),0),O=Math.min(O,this._bufferService.cols);const w=this._bufferService.buffer,E=w.ybase+w.y,D=Math.min(w.x,O-1),P=this._optionsService.rawOptions.cursorBlink,H=this._optionsService.rawOptions.cursorStyle,U=this._optionsService.rawOptions.cursorInactiveStyle;for(let W=x;W<=T;++W){const z=W+w.ydisp,S=this._rowElements[W],R=w.lines.get(z);if(!S||!R)break;S.replaceChildren(...this._rowFactory.createRow(R,z,z===E,H,U,D,P,this.dimensions.css.cell.width,this._widthCache,M?W===x?y:0:-1,M?(W===T?k:O)-1:-1))}}};r.DomRenderer=L=c([_(4,i.IInstantiationService),_(5,u.ICharSizeService),_(6,i.IOptionsService),_(7,i.IBufferService),_(8,u.ICoreBrowserService),_(9,u.IThemeService)],L)},3787:function(I,r,a){var c=this&&this.__decorate||function(v,m,h,p){var b,L=arguments.length,y=L<3?m:p===null?p=Object.getOwnPropertyDescriptor(m,h):p;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")y=Reflect.decorate(v,m,h,p);else for(var k=v.length-1;k>=0;k--)(b=v[k])&&(y=(L<3?b(y):L>3?b(m,h,y):b(m,h))||y);return L>3&&y&&Object.defineProperty(m,h,y),y},_=this&&this.__param||function(v,m){return function(h,p){m(h,p,v)}};Object.defineProperty(r,"__esModule",{value:!0}),r.DomRendererRowFactory=void 0;const n=a(2223),d=a(643),f=a(511),g=a(2585),u=a(8055),e=a(4725),s=a(4269),t=a(6171),i=a(3734);let o=r.DomRendererRowFactory=class{constructor(v,m,h,p,b,L,y){this._document=v,this._characterJoinerService=m,this._optionsService=h,this._coreBrowserService=p,this._coreService=b,this._decorationService=L,this._themeService=y,this._workCell=new f.CellData,this._columnSelectMode=!1,this.defaultSpacing=0}handleSelectionChanged(v,m,h){this._selectionStart=v,this._selectionEnd=m,this._columnSelectMode=h}createRow(v,m,h,p,b,L,y,k,x,T,O){const M=[],C=this._characterJoinerService.getJoinedCharacters(m),w=this._themeService.colors;let E,D=v.getNoBgTrimmedLength();h&&D<L+1&&(D=L+1);let P=0,H="",U=0,W=0,z=0,S=!1,R=0,B=!1,A=0;const N=[],F=T!==-1&&O!==-1;for(let j=0;j<D;j++){v.loadCell(j,this._workCell);let V=this._workCell.getWidth();if(V===0)continue;let G=!1,ie=j,$=this._workCell;if(C.length>0&&j===C[0][0]){G=!0;const K=C.shift();$=new s.JoinedCellData(this._workCell,v.translateToString(!0,K[0],K[1]),K[1]-K[0]),ie=K[1]-1,V=$.getWidth()}const se=this._isCellInSelection(j,m),le=h&&j===L,ce=F&&j>=T&&j<=O;let de=!1;this._decorationService.forEachDecorationAtCell(j,m,void 0,K=>{de=!0});let ae=$.getChars()||d.WHITESPACE_CELL_CHAR;if(ae===" "&&($.isUnderline()||$.isOverline())&&(ae=" "),A=V*k-x.get(ae,$.isBold(),$.isItalic()),E){if(P&&(se&&B||!se&&!B&&$.bg===U)&&(se&&B&&w.selectionForeground||$.fg===W)&&$.extended.ext===z&&ce===S&&A===R&&!le&&!G&&!de){H+=ae,P++;continue}P&&(E.textContent=H),E=this._document.createElement("span"),P=0,H=""}else E=this._document.createElement("span");if(U=$.bg,W=$.fg,z=$.extended.ext,S=ce,R=A,B=se,G&&L>=j&&L<=ie&&(L=j),!this._coreService.isCursorHidden&&le){if(N.push("xterm-cursor"),this._coreBrowserService.isFocused)y&&N.push("xterm-cursor-blink"),N.push(p==="bar"?"xterm-cursor-bar":p==="underline"?"xterm-cursor-underline":"xterm-cursor-block");else if(b)switch(b){case"outline":N.push("xterm-cursor-outline");break;case"block":N.push("xterm-cursor-block");break;case"bar":N.push("xterm-cursor-bar");break;case"underline":N.push("xterm-cursor-underline")}}if($.isBold()&&N.push("xterm-bold"),$.isItalic()&&N.push("xterm-italic"),$.isDim()&&N.push("xterm-dim"),H=$.isInvisible()?d.WHITESPACE_CELL_CHAR:$.getChars()||d.WHITESPACE_CELL_CHAR,$.isUnderline()&&(N.push(`xterm-underline-${$.extended.underlineStyle}`),H===" "&&(H=" "),!$.isUnderlineColorDefault()))if($.isUnderlineColorRGB())E.style.textDecorationColor=`rgb(${i.AttributeData.toColorRGB($.getUnderlineColor()).join(",")})`;else{let K=$.getUnderlineColor();this._optionsService.rawOptions.drawBoldTextInBrightColors&&$.isBold()&&K<8&&(K+=8),E.style.textDecorationColor=w.ansi[K].css}$.isOverline()&&(N.push("xterm-overline"),H===" "&&(H=" ")),$.isStrikethrough()&&N.push("xterm-strikethrough"),ce&&(E.style.textDecoration="underline");let X=$.getFgColor(),re=$.getFgColorMode(),Y=$.getBgColor(),ne=$.getBgColorMode();const ue=!!$.isInverse();if(ue){const K=X;X=Y,Y=K;const Ce=re;re=ne,ne=Ce}let Q,_e,ee,oe=!1;switch(this._decorationService.forEachDecorationAtCell(j,m,void 0,K=>{K.options.layer!=="top"&&oe||(K.backgroundColorRGB&&(ne=50331648,Y=K.backgroundColorRGB.rgba>>8&16777215,Q=K.backgroundColorRGB),K.foregroundColorRGB&&(re=50331648,X=K.foregroundColorRGB.rgba>>8&16777215,_e=K.foregroundColorRGB),oe=K.options.layer==="top")}),!oe&&se&&(Q=this._coreBrowserService.isFocused?w.selectionBackgroundOpaque:w.selectionInactiveBackgroundOpaque,Y=Q.rgba>>8&16777215,ne=50331648,oe=!0,w.selectionForeground&&(re=50331648,X=w.selectionForeground.rgba>>8&16777215,_e=w.selectionForeground)),oe&&N.push("xterm-decoration-top"),ne){case 16777216:case 33554432:ee=w.ansi[Y],N.push(`xterm-bg-${Y}`);break;case 50331648:ee=u.rgba.toColor(Y>>16,Y>>8&255,255&Y),this._addStyle(E,`background-color:#${l((Y>>>0).toString(16),"0",6)}`);break;default:ue?(ee=w.foreground,N.push(`xterm-bg-${n.INVERTED_DEFAULT_COLOR}`)):ee=w.background}switch(Q||$.isDim()&&(Q=u.color.multiplyOpacity(ee,.5)),re){case 16777216:case 33554432:$.isBold()&&X<8&&this._optionsService.rawOptions.drawBoldTextInBrightColors&&(X+=8),this._applyMinimumContrast(E,ee,w.ansi[X],$,Q,void 0)||N.push(`xterm-fg-${X}`);break;case 50331648:const K=u.rgba.toColor(X>>16&255,X>>8&255,255&X);this._applyMinimumContrast(E,ee,K,$,Q,_e)||this._addStyle(E,`color:#${l(X.toString(16),"0",6)}`);break;default:this._applyMinimumContrast(E,ee,w.foreground,$,Q,void 0)||ue&&N.push(`xterm-fg-${n.INVERTED_DEFAULT_COLOR}`)}N.length&&(E.className=N.join(" "),N.length=0),le||G||de?E.textContent=H:P++,A!==this.defaultSpacing&&(E.style.letterSpacing=`${A}px`),M.push(E),j=ie}return E&&P&&(E.textContent=H),M}_applyMinimumContrast(v,m,h,p,b,L){if(this._optionsService.rawOptions.minimumContrastRatio===1||(0,t.excludeFromContrastRatioDemands)(p.getCode()))return!1;const y=this._getContrastCache(p);let k;if(b||L||(k=y.getColor(m.rgba,h.rgba)),k===void 0){const x=this._optionsService.rawOptions.minimumContrastRatio/(p.isDim()?2:1);k=u.color.ensureContrastRatio(b||m,L||h,x),y.setColor((b||m).rgba,(L||h).rgba,k??null)}return!!k&&(this._addStyle(v,`color:${k.css}`),!0)}_getContrastCache(v){return v.isDim()?this._themeService.colors.halfContrastCache:this._themeService.colors.contrastCache}_addStyle(v,m){v.setAttribute("style",`${v.getAttribute("style")||""}${m};`)}_isCellInSelection(v,m){const h=this._selectionStart,p=this._selectionEnd;return!(!h||!p)&&(this._columnSelectMode?h[0]<=p[0]?v>=h[0]&&m>=h[1]&&v<p[0]&&m<=p[1]:v<h[0]&&m>=h[1]&&v>=p[0]&&m<=p[1]:m>h[1]&&m<p[1]||h[1]===p[1]&&m===h[1]&&v>=h[0]&&v<p[0]||h[1]<p[1]&&m===p[1]&&v<p[0]||h[1]<p[1]&&m===h[1]&&v>=h[0])}};function l(v,m,h){for(;v.length<h;)v=m+v;return v}r.DomRendererRowFactory=o=c([_(1,e.ICharacterJoinerService),_(2,g.IOptionsService),_(3,e.ICoreBrowserService),_(4,g.ICoreService),_(5,g.IDecorationService),_(6,e.IThemeService)],o)},2550:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.WidthCache=void 0,r.WidthCache=class{constructor(a){this._flat=new Float32Array(256),this._font="",this._fontSize=0,this._weight="normal",this._weightBold="bold",this._measureElements=[],this._container=a.createElement("div"),this._container.style.position="absolute",this._container.style.top="-50000px",this._container.style.width="50000px",this._container.style.whiteSpace="pre",this._container.style.fontKerning="none";const c=a.createElement("span"),_=a.createElement("span");_.style.fontWeight="bold";const n=a.createElement("span");n.style.fontStyle="italic";const d=a.createElement("span");d.style.fontWeight="bold",d.style.fontStyle="italic",this._measureElements=[c,_,n,d],this._container.appendChild(c),this._container.appendChild(_),this._container.appendChild(n),this._container.appendChild(d),a.body.appendChild(this._container),this.clear()}dispose(){this._container.remove(),this._measureElements.length=0,this._holey=void 0}clear(){this._flat.fill(-9999),this._holey=new Map}setFont(a,c,_,n){a===this._font&&c===this._fontSize&&_===this._weight&&n===this._weightBold||(this._font=a,this._fontSize=c,this._weight=_,this._weightBold=n,this._container.style.fontFamily=this._font,this._container.style.fontSize=`${this._fontSize}px`,this._measureElements[0].style.fontWeight=`${_}`,this._measureElements[1].style.fontWeight=`${n}`,this._measureElements[2].style.fontWeight=`${_}`,this._measureElements[3].style.fontWeight=`${n}`,this.clear())}get(a,c,_){let n=0;if(!c&&!_&&a.length===1&&(n=a.charCodeAt(0))<256)return this._flat[n]!==-9999?this._flat[n]:this._flat[n]=this._measure(a,0);let d=a;c&&(d+="B"),_&&(d+="I");let f=this._holey.get(d);if(f===void 0){let g=0;c&&(g|=1),_&&(g|=2),f=this._measure(a,g),this._holey.set(d,f)}return f}_measure(a,c){const _=this._measureElements[c];return _.textContent=a.repeat(32),_.offsetWidth/32}}},2223:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.TEXT_BASELINE=r.DIM_OPACITY=r.INVERTED_DEFAULT_COLOR=void 0;const c=a(6114);r.INVERTED_DEFAULT_COLOR=257,r.DIM_OPACITY=.5,r.TEXT_BASELINE=c.isFirefox||c.isLegacyEdge?"bottom":"ideographic"},6171:(I,r)=>{function a(c){return 57508<=c&&c<=57558}Object.defineProperty(r,"__esModule",{value:!0}),r.createRenderDimensions=r.excludeFromContrastRatioDemands=r.isRestrictedPowerlineGlyph=r.isPowerlineGlyph=r.throwIfFalsy=void 0,r.throwIfFalsy=function(c){if(!c)throw new Error("value must not be falsy");return c},r.isPowerlineGlyph=a,r.isRestrictedPowerlineGlyph=function(c){return 57520<=c&&c<=57527},r.excludeFromContrastRatioDemands=function(c){return a(c)||function(_){return 9472<=_&&_<=9631}(c)},r.createRenderDimensions=function(){return{css:{canvas:{width:0,height:0},cell:{width:0,height:0}},device:{canvas:{width:0,height:0},cell:{width:0,height:0},char:{width:0,height:0,left:0,top:0}}}}},456:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.SelectionModel=void 0,r.SelectionModel=class{constructor(a){this._bufferService=a,this.isSelectAllActive=!1,this.selectionStartLength=0}clearSelection(){this.selectionStart=void 0,this.selectionEnd=void 0,this.isSelectAllActive=!1,this.selectionStartLength=0}get finalSelectionStart(){return this.isSelectAllActive?[0,0]:this.selectionEnd&&this.selectionStart&&this.areSelectionValuesReversed()?this.selectionEnd:this.selectionStart}get finalSelectionEnd(){if(this.isSelectAllActive)return[this._bufferService.cols,this._bufferService.buffer.ybase+this._bufferService.rows-1];if(this.selectionStart){if(!this.selectionEnd||this.areSelectionValuesReversed()){const a=this.selectionStart[0]+this.selectionStartLength;return a>this._bufferService.cols?a%this._bufferService.cols==0?[this._bufferService.cols,this.selectionStart[1]+Math.floor(a/this._bufferService.cols)-1]:[a%this._bufferService.cols,this.selectionStart[1]+Math.floor(a/this._bufferService.cols)]:[a,this.selectionStart[1]]}if(this.selectionStartLength&&this.selectionEnd[1]===this.selectionStart[1]){const a=this.selectionStart[0]+this.selectionStartLength;return a>this._bufferService.cols?[a%this._bufferService.cols,this.selectionStart[1]+Math.floor(a/this._bufferService.cols)]:[Math.max(a,this.selectionEnd[0]),this.selectionEnd[1]]}return this.selectionEnd}}areSelectionValuesReversed(){const a=this.selectionStart,c=this.selectionEnd;return!(!a||!c)&&(a[1]>c[1]||a[1]===c[1]&&a[0]>c[0])}handleTrim(a){return this.selectionStart&&(this.selectionStart[1]-=a),this.selectionEnd&&(this.selectionEnd[1]-=a),this.selectionEnd&&this.selectionEnd[1]<0?(this.clearSelection(),!0):(this.selectionStart&&this.selectionStart[1]<0&&(this.selectionStart[1]=0),!1)}}},428:function(I,r,a){var c=this&&this.__decorate||function(e,s,t,i){var o,l=arguments.length,v=l<3?s:i===null?i=Object.getOwnPropertyDescriptor(s,t):i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(e,s,t,i);else for(var m=e.length-1;m>=0;m--)(o=e[m])&&(v=(l<3?o(v):l>3?o(s,t,v):o(s,t))||v);return l>3&&v&&Object.defineProperty(s,t,v),v},_=this&&this.__param||function(e,s){return function(t,i){s(t,i,e)}};Object.defineProperty(r,"__esModule",{value:!0}),r.CharSizeService=void 0;const n=a(2585),d=a(8460),f=a(844);let g=r.CharSizeService=class extends f.Disposable{get hasValidSize(){return this.width>0&&this.height>0}constructor(e,s,t){super(),this._optionsService=t,this.width=0,this.height=0,this._onCharSizeChange=this.register(new d.EventEmitter),this.onCharSizeChange=this._onCharSizeChange.event,this._measureStrategy=new u(e,s,this._optionsService),this.register(this._optionsService.onMultipleOptionChange(["fontFamily","fontSize"],()=>this.measure()))}measure(){const e=this._measureStrategy.measure();e.width===this.width&&e.height===this.height||(this.width=e.width,this.height=e.height,this._onCharSizeChange.fire())}};r.CharSizeService=g=c([_(2,n.IOptionsService)],g);class u{constructor(s,t,i){this._document=s,this._parentElement=t,this._optionsService=i,this._result={width:0,height:0},this._measureElement=this._document.createElement("span"),this._measureElement.classList.add("xterm-char-measure-element"),this._measureElement.textContent="W".repeat(32),this._measureElement.setAttribute("aria-hidden","true"),this._measureElement.style.whiteSpace="pre",this._measureElement.style.fontKerning="none",this._parentElement.appendChild(this._measureElement)}measure(){this._measureElement.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._measureElement.style.fontSize=`${this._optionsService.rawOptions.fontSize}px`;const s={height:Number(this._measureElement.offsetHeight),width:Number(this._measureElement.offsetWidth)};return s.width!==0&&s.height!==0&&(this._result.width=s.width/32,this._result.height=Math.ceil(s.height)),this._result}}},4269:function(I,r,a){var c=this&&this.__decorate||function(s,t,i,o){var l,v=arguments.length,m=v<3?t:o===null?o=Object.getOwnPropertyDescriptor(t,i):o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")m=Reflect.decorate(s,t,i,o);else for(var h=s.length-1;h>=0;h--)(l=s[h])&&(m=(v<3?l(m):v>3?l(t,i,m):l(t,i))||m);return v>3&&m&&Object.defineProperty(t,i,m),m},_=this&&this.__param||function(s,t){return function(i,o){t(i,o,s)}};Object.defineProperty(r,"__esModule",{value:!0}),r.CharacterJoinerService=r.JoinedCellData=void 0;const n=a(3734),d=a(643),f=a(511),g=a(2585);class u extends n.AttributeData{constructor(t,i,o){super(),this.content=0,this.combinedData="",this.fg=t.fg,this.bg=t.bg,this.combinedData=i,this._width=o}isCombined(){return 2097152}getWidth(){return this._width}getChars(){return this.combinedData}getCode(){return 2097151}setFromCharData(t){throw new Error("not implemented")}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}r.JoinedCellData=u;let e=r.CharacterJoinerService=class me{constructor(t){this._bufferService=t,this._characterJoiners=[],this._nextCharacterJoinerId=0,this._workCell=new f.CellData}register(t){const i={id:this._nextCharacterJoinerId++,handler:t};return this._characterJoiners.push(i),i.id}deregister(t){for(let i=0;i<this._characterJoiners.length;i++)if(this._characterJoiners[i].id===t)return this._characterJoiners.splice(i,1),!0;return!1}getJoinedCharacters(t){if(this._characterJoiners.length===0)return[];const i=this._bufferService.buffer.lines.get(t);if(!i||i.length===0)return[];const o=[],l=i.translateToString(!0);let v=0,m=0,h=0,p=i.getFg(0),b=i.getBg(0);for(let L=0;L<i.getTrimmedLength();L++)if(i.loadCell(L,this._workCell),this._workCell.getWidth()!==0){if(this._workCell.fg!==p||this._workCell.bg!==b){if(L-v>1){const y=this._getJoinedRanges(l,h,m,i,v);for(let k=0;k<y.length;k++)o.push(y[k])}v=L,h=m,p=this._workCell.fg,b=this._workCell.bg}m+=this._workCell.getChars().length||d.WHITESPACE_CELL_CHAR.length}if(this._bufferService.cols-v>1){const L=this._getJoinedRanges(l,h,m,i,v);for(let y=0;y<L.length;y++)o.push(L[y])}return o}_getJoinedRanges(t,i,o,l,v){const m=t.substring(i,o);let h=[];try{h=this._characterJoiners[0].handler(m)}catch(p){console.error(p)}for(let p=1;p<this._characterJoiners.length;p++)try{const b=this._characterJoiners[p].handler(m);for(let L=0;L<b.length;L++)me._mergeRanges(h,b[L])}catch(b){console.error(b)}return this._stringRangesToCellRanges(h,l,v),h}_stringRangesToCellRanges(t,i,o){let l=0,v=!1,m=0,h=t[l];if(h){for(let p=o;p<this._bufferService.cols;p++){const b=i.getWidth(p),L=i.getString(p).length||d.WHITESPACE_CELL_CHAR.length;if(b!==0){if(!v&&h[0]<=m&&(h[0]=p,v=!0),h[1]<=m){if(h[1]=p,h=t[++l],!h)break;h[0]<=m?(h[0]=p,v=!0):v=!1}m+=L}}h&&(h[1]=this._bufferService.cols)}}static _mergeRanges(t,i){let o=!1;for(let l=0;l<t.length;l++){const v=t[l];if(o){if(i[1]<=v[0])return t[l-1][1]=i[1],t;if(i[1]<=v[1])return t[l-1][1]=Math.max(i[1],v[1]),t.splice(l,1),t;t.splice(l,1),l--}else{if(i[1]<=v[0])return t.splice(l,0,i),t;if(i[1]<=v[1])return v[0]=Math.min(i[0],v[0]),t;i[0]<v[1]&&(v[0]=Math.min(i[0],v[0]),o=!0)}}return o?t[t.length-1][1]=i[1]:t.push(i),t}};r.CharacterJoinerService=e=c([_(0,g.IBufferService)],e)},5114:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.CoreBrowserService=void 0,r.CoreBrowserService=class{constructor(a,c){this._textarea=a,this.window=c,this._isFocused=!1,this._cachedIsFocused=void 0,this._textarea.addEventListener("focus",()=>this._isFocused=!0),this._textarea.addEventListener("blur",()=>this._isFocused=!1)}get dpr(){return this.window.devicePixelRatio}get isFocused(){return this._cachedIsFocused===void 0&&(this._cachedIsFocused=this._isFocused&&this._textarea.ownerDocument.hasFocus(),queueMicrotask(()=>this._cachedIsFocused=void 0)),this._cachedIsFocused}}},8934:function(I,r,a){var c=this&&this.__decorate||function(g,u,e,s){var t,i=arguments.length,o=i<3?u:s===null?s=Object.getOwnPropertyDescriptor(u,e):s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")o=Reflect.decorate(g,u,e,s);else for(var l=g.length-1;l>=0;l--)(t=g[l])&&(o=(i<3?t(o):i>3?t(u,e,o):t(u,e))||o);return i>3&&o&&Object.defineProperty(u,e,o),o},_=this&&this.__param||function(g,u){return function(e,s){u(e,s,g)}};Object.defineProperty(r,"__esModule",{value:!0}),r.MouseService=void 0;const n=a(4725),d=a(9806);let f=r.MouseService=class{constructor(g,u){this._renderService=g,this._charSizeService=u}getCoords(g,u,e,s,t){return(0,d.getCoords)(window,g,u,e,s,this._charSizeService.hasValidSize,this._renderService.dimensions.css.cell.width,this._renderService.dimensions.css.cell.height,t)}getMouseReportCoords(g,u){const e=(0,d.getCoordsRelativeToElement)(window,g,u);if(this._charSizeService.hasValidSize)return e[0]=Math.min(Math.max(e[0],0),this._renderService.dimensions.css.canvas.width-1),e[1]=Math.min(Math.max(e[1],0),this._renderService.dimensions.css.canvas.height-1),{col:Math.floor(e[0]/this._renderService.dimensions.css.cell.width),row:Math.floor(e[1]/this._renderService.dimensions.css.cell.height),x:Math.floor(e[0]),y:Math.floor(e[1])}}};r.MouseService=f=c([_(0,n.IRenderService),_(1,n.ICharSizeService)],f)},3230:function(I,r,a){var c=this&&this.__decorate||function(o,l,v,m){var h,p=arguments.length,b=p<3?l:m===null?m=Object.getOwnPropertyDescriptor(l,v):m;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")b=Reflect.decorate(o,l,v,m);else for(var L=o.length-1;L>=0;L--)(h=o[L])&&(b=(p<3?h(b):p>3?h(l,v,b):h(l,v))||b);return p>3&&b&&Object.defineProperty(l,v,b),b},_=this&&this.__param||function(o,l){return function(v,m){l(v,m,o)}};Object.defineProperty(r,"__esModule",{value:!0}),r.RenderService=void 0;const n=a(3656),d=a(6193),f=a(5596),g=a(4725),u=a(8460),e=a(844),s=a(7226),t=a(2585);let i=r.RenderService=class extends e.Disposable{get dimensions(){return this._renderer.value.dimensions}constructor(o,l,v,m,h,p,b,L){if(super(),this._rowCount=o,this._charSizeService=m,this._renderer=this.register(new e.MutableDisposable),this._pausedResizeTask=new s.DebouncedIdleTask,this._isPaused=!1,this._needsFullRefresh=!1,this._isNextRenderRedrawOnly=!0,this._needsSelectionRefresh=!1,this._canvasWidth=0,this._canvasHeight=0,this._selectionState={start:void 0,end:void 0,columnSelectMode:!1},this._onDimensionsChange=this.register(new u.EventEmitter),this.onDimensionsChange=this._onDimensionsChange.event,this._onRenderedViewportChange=this.register(new u.EventEmitter),this.onRenderedViewportChange=this._onRenderedViewportChange.event,this._onRender=this.register(new u.EventEmitter),this.onRender=this._onRender.event,this._onRefreshRequest=this.register(new u.EventEmitter),this.onRefreshRequest=this._onRefreshRequest.event,this._renderDebouncer=new d.RenderDebouncer(b.window,(y,k)=>this._renderRows(y,k)),this.register(this._renderDebouncer),this._screenDprMonitor=new f.ScreenDprMonitor(b.window),this._screenDprMonitor.setListener(()=>this.handleDevicePixelRatioChange()),this.register(this._screenDprMonitor),this.register(p.onResize(()=>this._fullRefresh())),this.register(p.buffers.onBufferActivate(()=>{var y;return(y=this._renderer.value)===null||y===void 0?void 0:y.clear()})),this.register(v.onOptionChange(()=>this._handleOptionsChanged())),this.register(this._charSizeService.onCharSizeChange(()=>this.handleCharSizeChanged())),this.register(h.onDecorationRegistered(()=>this._fullRefresh())),this.register(h.onDecorationRemoved(()=>this._fullRefresh())),this.register(v.onMultipleOptionChange(["customGlyphs","drawBoldTextInBrightColors","letterSpacing","lineHeight","fontFamily","fontSize","fontWeight","fontWeightBold","minimumContrastRatio"],()=>{this.clear(),this.handleResize(p.cols,p.rows),this._fullRefresh()})),this.register(v.onMultipleOptionChange(["cursorBlink","cursorStyle"],()=>this.refreshRows(p.buffer.y,p.buffer.y,!0))),this.register((0,n.addDisposableDomListener)(b.window,"resize",()=>this.handleDevicePixelRatioChange())),this.register(L.onChangeColors(()=>this._fullRefresh())),"IntersectionObserver"in b.window){const y=new b.window.IntersectionObserver(k=>this._handleIntersectionChange(k[k.length-1]),{threshold:0});y.observe(l),this.register({dispose:()=>y.disconnect()})}}_handleIntersectionChange(o){this._isPaused=o.isIntersecting===void 0?o.intersectionRatio===0:!o.isIntersecting,this._isPaused||this._charSizeService.hasValidSize||this._charSizeService.measure(),!this._isPaused&&this._needsFullRefresh&&(this._pausedResizeTask.flush(),this.refreshRows(0,this._rowCount-1),this._needsFullRefresh=!1)}refreshRows(o,l,v=!1){this._isPaused?this._needsFullRefresh=!0:(v||(this._isNextRenderRedrawOnly=!1),this._renderDebouncer.refresh(o,l,this._rowCount))}_renderRows(o,l){this._renderer.value&&(o=Math.min(o,this._rowCount-1),l=Math.min(l,this._rowCount-1),this._renderer.value.renderRows(o,l),this._needsSelectionRefresh&&(this._renderer.value.handleSelectionChanged(this._selectionState.start,this._selectionState.end,this._selectionState.columnSelectMode),this._needsSelectionRefresh=!1),this._isNextRenderRedrawOnly||this._onRenderedViewportChange.fire({start:o,end:l}),this._onRender.fire({start:o,end:l}),this._isNextRenderRedrawOnly=!0)}resize(o,l){this._rowCount=l,this._fireOnCanvasResize()}_handleOptionsChanged(){this._renderer.value&&(this.refreshRows(0,this._rowCount-1),this._fireOnCanvasResize())}_fireOnCanvasResize(){this._renderer.value&&(this._renderer.value.dimensions.css.canvas.width===this._canvasWidth&&this._renderer.value.dimensions.css.canvas.height===this._canvasHeight||this._onDimensionsChange.fire(this._renderer.value.dimensions))}hasRenderer(){return!!this._renderer.value}setRenderer(o){this._renderer.value=o,this._renderer.value.onRequestRedraw(l=>this.refreshRows(l.start,l.end,!0)),this._needsSelectionRefresh=!0,this._fullRefresh()}addRefreshCallback(o){return this._renderDebouncer.addRefreshCallback(o)}_fullRefresh(){this._isPaused?this._needsFullRefresh=!0:this.refreshRows(0,this._rowCount-1)}clearTextureAtlas(){var o,l;this._renderer.value&&((l=(o=this._renderer.value).clearTextureAtlas)===null||l===void 0||l.call(o),this._fullRefresh())}handleDevicePixelRatioChange(){this._charSizeService.measure(),this._renderer.value&&(this._renderer.value.handleDevicePixelRatioChange(),this.refreshRows(0,this._rowCount-1))}handleResize(o,l){this._renderer.value&&(this._isPaused?this._pausedResizeTask.set(()=>this._renderer.value.handleResize(o,l)):this._renderer.value.handleResize(o,l),this._fullRefresh())}handleCharSizeChanged(){var o;(o=this._renderer.value)===null||o===void 0||o.handleCharSizeChanged()}handleBlur(){var o;(o=this._renderer.value)===null||o===void 0||o.handleBlur()}handleFocus(){var o;(o=this._renderer.value)===null||o===void 0||o.handleFocus()}handleSelectionChanged(o,l,v){var m;this._selectionState.start=o,this._selectionState.end=l,this._selectionState.columnSelectMode=v,(m=this._renderer.value)===null||m===void 0||m.handleSelectionChanged(o,l,v)}handleCursorMove(){var o;(o=this._renderer.value)===null||o===void 0||o.handleCursorMove()}clear(){var o;(o=this._renderer.value)===null||o===void 0||o.clear()}};r.RenderService=i=c([_(2,t.IOptionsService),_(3,g.ICharSizeService),_(4,t.IDecorationService),_(5,t.IBufferService),_(6,g.ICoreBrowserService),_(7,g.IThemeService)],i)},9312:function(I,r,a){var c=this&&this.__decorate||function(h,p,b,L){var y,k=arguments.length,x=k<3?p:L===null?L=Object.getOwnPropertyDescriptor(p,b):L;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")x=Reflect.decorate(h,p,b,L);else for(var T=h.length-1;T>=0;T--)(y=h[T])&&(x=(k<3?y(x):k>3?y(p,b,x):y(p,b))||x);return k>3&&x&&Object.defineProperty(p,b,x),x},_=this&&this.__param||function(h,p){return function(b,L){p(b,L,h)}};Object.defineProperty(r,"__esModule",{value:!0}),r.SelectionService=void 0;const n=a(9806),d=a(9504),f=a(456),g=a(4725),u=a(8460),e=a(844),s=a(6114),t=a(4841),i=a(511),o=a(2585),l=" ",v=new RegExp(l,"g");let m=r.SelectionService=class extends e.Disposable{constructor(h,p,b,L,y,k,x,T,O){super(),this._element=h,this._screenElement=p,this._linkifier=b,this._bufferService=L,this._coreService=y,this._mouseService=k,this._optionsService=x,this._renderService=T,this._coreBrowserService=O,this._dragScrollAmount=0,this._enabled=!0,this._workCell=new i.CellData,this._mouseDownTimeStamp=0,this._oldHasSelection=!1,this._oldSelectionStart=void 0,this._oldSelectionEnd=void 0,this._onLinuxMouseSelection=this.register(new u.EventEmitter),this.onLinuxMouseSelection=this._onLinuxMouseSelection.event,this._onRedrawRequest=this.register(new u.EventEmitter),this.onRequestRedraw=this._onRedrawRequest.event,this._onSelectionChange=this.register(new u.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onRequestScrollLines=this.register(new u.EventEmitter),this.onRequestScrollLines=this._onRequestScrollLines.event,this._mouseMoveListener=M=>this._handleMouseMove(M),this._mouseUpListener=M=>this._handleMouseUp(M),this._coreService.onUserInput(()=>{this.hasSelection&&this.clearSelection()}),this._trimListener=this._bufferService.buffer.lines.onTrim(M=>this._handleTrim(M)),this.register(this._bufferService.buffers.onBufferActivate(M=>this._handleBufferActivate(M))),this.enable(),this._model=new f.SelectionModel(this._bufferService),this._activeSelectionMode=0,this.register((0,e.toDisposable)(()=>{this._removeMouseDownListeners()}))}reset(){this.clearSelection()}disable(){this.clearSelection(),this._enabled=!1}enable(){this._enabled=!0}get selectionStart(){return this._model.finalSelectionStart}get selectionEnd(){return this._model.finalSelectionEnd}get hasSelection(){const h=this._model.finalSelectionStart,p=this._model.finalSelectionEnd;return!(!h||!p||h[0]===p[0]&&h[1]===p[1])}get selectionText(){const h=this._model.finalSelectionStart,p=this._model.finalSelectionEnd;if(!h||!p)return"";const b=this._bufferService.buffer,L=[];if(this._activeSelectionMode===3){if(h[0]===p[0])return"";const y=h[0]<p[0]?h[0]:p[0],k=h[0]<p[0]?p[0]:h[0];for(let x=h[1];x<=p[1];x++){const T=b.translateBufferLineToString(x,!0,y,k);L.push(T)}}else{const y=h[1]===p[1]?p[0]:void 0;L.push(b.translateBufferLineToString(h[1],!0,h[0],y));for(let k=h[1]+1;k<=p[1]-1;k++){const x=b.lines.get(k),T=b.translateBufferLineToString(k,!0);x!=null&&x.isWrapped?L[L.length-1]+=T:L.push(T)}if(h[1]!==p[1]){const k=b.lines.get(p[1]),x=b.translateBufferLineToString(p[1],!0,0,p[0]);k&&k.isWrapped?L[L.length-1]+=x:L.push(x)}}return L.map(y=>y.replace(v," ")).join(s.isWindows?`\r
`:`
`)}clearSelection(){this._model.clearSelection(),this._removeMouseDownListeners(),this.refresh(),this._onSelectionChange.fire()}refresh(h){this._refreshAnimationFrame||(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._refresh())),s.isLinux&&h&&this.selectionText.length&&this._onLinuxMouseSelection.fire(this.selectionText)}_refresh(){this._refreshAnimationFrame=void 0,this._onRedrawRequest.fire({start:this._model.finalSelectionStart,end:this._model.finalSelectionEnd,columnSelectMode:this._activeSelectionMode===3})}_isClickInSelection(h){const p=this._getMouseBufferCoords(h),b=this._model.finalSelectionStart,L=this._model.finalSelectionEnd;return!!(b&&L&&p)&&this._areCoordsInSelection(p,b,L)}isCellInSelection(h,p){const b=this._model.finalSelectionStart,L=this._model.finalSelectionEnd;return!(!b||!L)&&this._areCoordsInSelection([h,p],b,L)}_areCoordsInSelection(h,p,b){return h[1]>p[1]&&h[1]<b[1]||p[1]===b[1]&&h[1]===p[1]&&h[0]>=p[0]&&h[0]<b[0]||p[1]<b[1]&&h[1]===b[1]&&h[0]<b[0]||p[1]<b[1]&&h[1]===p[1]&&h[0]>=p[0]}_selectWordAtCursor(h,p){var b,L;const y=(L=(b=this._linkifier.currentLink)===null||b===void 0?void 0:b.link)===null||L===void 0?void 0:L.range;if(y)return this._model.selectionStart=[y.start.x-1,y.start.y-1],this._model.selectionStartLength=(0,t.getRangeLength)(y,this._bufferService.cols),this._model.selectionEnd=void 0,!0;const k=this._getMouseBufferCoords(h);return!!k&&(this._selectWordAt(k,p),this._model.selectionEnd=void 0,!0)}selectAll(){this._model.isSelectAllActive=!0,this.refresh(),this._onSelectionChange.fire()}selectLines(h,p){this._model.clearSelection(),h=Math.max(h,0),p=Math.min(p,this._bufferService.buffer.lines.length-1),this._model.selectionStart=[0,h],this._model.selectionEnd=[this._bufferService.cols,p],this.refresh(),this._onSelectionChange.fire()}_handleTrim(h){this._model.handleTrim(h)&&this.refresh()}_getMouseBufferCoords(h){const p=this._mouseService.getCoords(h,this._screenElement,this._bufferService.cols,this._bufferService.rows,!0);if(p)return p[0]--,p[1]--,p[1]+=this._bufferService.buffer.ydisp,p}_getMouseEventScrollAmount(h){let p=(0,n.getCoordsRelativeToElement)(this._coreBrowserService.window,h,this._screenElement)[1];const b=this._renderService.dimensions.css.canvas.height;return p>=0&&p<=b?0:(p>b&&(p-=b),p=Math.min(Math.max(p,-50),50),p/=50,p/Math.abs(p)+Math.round(14*p))}shouldForceSelection(h){return s.isMac?h.altKey&&this._optionsService.rawOptions.macOptionClickForcesSelection:h.shiftKey}handleMouseDown(h){if(this._mouseDownTimeStamp=h.timeStamp,(h.button!==2||!this.hasSelection)&&h.button===0){if(!this._enabled){if(!this.shouldForceSelection(h))return;h.stopPropagation()}h.preventDefault(),this._dragScrollAmount=0,this._enabled&&h.shiftKey?this._handleIncrementalClick(h):h.detail===1?this._handleSingleClick(h):h.detail===2?this._handleDoubleClick(h):h.detail===3&&this._handleTripleClick(h),this._addMouseDownListeners(),this.refresh(!0)}}_addMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.addEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.addEventListener("mouseup",this._mouseUpListener)),this._dragScrollIntervalTimer=this._coreBrowserService.window.setInterval(()=>this._dragScroll(),50)}_removeMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.removeEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.removeEventListener("mouseup",this._mouseUpListener)),this._coreBrowserService.window.clearInterval(this._dragScrollIntervalTimer),this._dragScrollIntervalTimer=void 0}_handleIncrementalClick(h){this._model.selectionStart&&(this._model.selectionEnd=this._getMouseBufferCoords(h))}_handleSingleClick(h){if(this._model.selectionStartLength=0,this._model.isSelectAllActive=!1,this._activeSelectionMode=this.shouldColumnSelect(h)?3:0,this._model.selectionStart=this._getMouseBufferCoords(h),!this._model.selectionStart)return;this._model.selectionEnd=void 0;const p=this._bufferService.buffer.lines.get(this._model.selectionStart[1]);p&&p.length!==this._model.selectionStart[0]&&p.hasWidth(this._model.selectionStart[0])===0&&this._model.selectionStart[0]++}_handleDoubleClick(h){this._selectWordAtCursor(h,!0)&&(this._activeSelectionMode=1)}_handleTripleClick(h){const p=this._getMouseBufferCoords(h);p&&(this._activeSelectionMode=2,this._selectLineAt(p[1]))}shouldColumnSelect(h){return h.altKey&&!(s.isMac&&this._optionsService.rawOptions.macOptionClickForcesSelection)}_handleMouseMove(h){if(h.stopImmediatePropagation(),!this._model.selectionStart)return;const p=this._model.selectionEnd?[this._model.selectionEnd[0],this._model.selectionEnd[1]]:null;if(this._model.selectionEnd=this._getMouseBufferCoords(h),!this._model.selectionEnd)return void this.refresh(!0);this._activeSelectionMode===2?this._model.selectionEnd[1]<this._model.selectionStart[1]?this._model.selectionEnd[0]=0:this._model.selectionEnd[0]=this._bufferService.cols:this._activeSelectionMode===1&&this._selectToWordAt(this._model.selectionEnd),this._dragScrollAmount=this._getMouseEventScrollAmount(h),this._activeSelectionMode!==3&&(this._dragScrollAmount>0?this._model.selectionEnd[0]=this._bufferService.cols:this._dragScrollAmount<0&&(this._model.selectionEnd[0]=0));const b=this._bufferService.buffer;if(this._model.selectionEnd[1]<b.lines.length){const L=b.lines.get(this._model.selectionEnd[1]);L&&L.hasWidth(this._model.selectionEnd[0])===0&&this._model.selectionEnd[0]++}p&&p[0]===this._model.selectionEnd[0]&&p[1]===this._model.selectionEnd[1]||this.refresh(!0)}_dragScroll(){if(this._model.selectionEnd&&this._model.selectionStart&&this._dragScrollAmount){this._onRequestScrollLines.fire({amount:this._dragScrollAmount,suppressScrollEvent:!1});const h=this._bufferService.buffer;this._dragScrollAmount>0?(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=this._bufferService.cols),this._model.selectionEnd[1]=Math.min(h.ydisp+this._bufferService.rows,h.lines.length-1)):(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=0),this._model.selectionEnd[1]=h.ydisp),this.refresh()}}_handleMouseUp(h){const p=h.timeStamp-this._mouseDownTimeStamp;if(this._removeMouseDownListeners(),this.selectionText.length<=1&&p<500&&h.altKey&&this._optionsService.rawOptions.altClickMovesCursor){if(this._bufferService.buffer.ybase===this._bufferService.buffer.ydisp){const b=this._mouseService.getCoords(h,this._element,this._bufferService.cols,this._bufferService.rows,!1);if(b&&b[0]!==void 0&&b[1]!==void 0){const L=(0,d.moveToCellSequence)(b[0]-1,b[1]-1,this._bufferService,this._coreService.decPrivateModes.applicationCursorKeys);this._coreService.triggerDataEvent(L,!0)}}}else this._fireEventIfSelectionChanged()}_fireEventIfSelectionChanged(){const h=this._model.finalSelectionStart,p=this._model.finalSelectionEnd,b=!(!h||!p||h[0]===p[0]&&h[1]===p[1]);b?h&&p&&(this._oldSelectionStart&&this._oldSelectionEnd&&h[0]===this._oldSelectionStart[0]&&h[1]===this._oldSelectionStart[1]&&p[0]===this._oldSelectionEnd[0]&&p[1]===this._oldSelectionEnd[1]||this._fireOnSelectionChange(h,p,b)):this._oldHasSelection&&this._fireOnSelectionChange(h,p,b)}_fireOnSelectionChange(h,p,b){this._oldSelectionStart=h,this._oldSelectionEnd=p,this._oldHasSelection=b,this._onSelectionChange.fire()}_handleBufferActivate(h){this.clearSelection(),this._trimListener.dispose(),this._trimListener=h.activeBuffer.lines.onTrim(p=>this._handleTrim(p))}_convertViewportColToCharacterIndex(h,p){let b=p;for(let L=0;p>=L;L++){const y=h.loadCell(L,this._workCell).getChars().length;this._workCell.getWidth()===0?b--:y>1&&p!==L&&(b+=y-1)}return b}setSelection(h,p,b){this._model.clearSelection(),this._removeMouseDownListeners(),this._model.selectionStart=[h,p],this._model.selectionStartLength=b,this.refresh(),this._fireEventIfSelectionChanged()}rightClickSelect(h){this._isClickInSelection(h)||(this._selectWordAtCursor(h,!1)&&this.refresh(!0),this._fireEventIfSelectionChanged())}_getWordAt(h,p,b=!0,L=!0){if(h[0]>=this._bufferService.cols)return;const y=this._bufferService.buffer,k=y.lines.get(h[1]);if(!k)return;const x=y.translateBufferLineToString(h[1],!1);let T=this._convertViewportColToCharacterIndex(k,h[0]),O=T;const M=h[0]-T;let C=0,w=0,E=0,D=0;if(x.charAt(T)===" "){for(;T>0&&x.charAt(T-1)===" ";)T--;for(;O<x.length&&x.charAt(O+1)===" ";)O++}else{let U=h[0],W=h[0];k.getWidth(U)===0&&(C++,U--),k.getWidth(W)===2&&(w++,W++);const z=k.getString(W).length;for(z>1&&(D+=z-1,O+=z-1);U>0&&T>0&&!this._isCharWordSeparator(k.loadCell(U-1,this._workCell));){k.loadCell(U-1,this._workCell);const S=this._workCell.getChars().length;this._workCell.getWidth()===0?(C++,U--):S>1&&(E+=S-1,T-=S-1),T--,U--}for(;W<k.length&&O+1<x.length&&!this._isCharWordSeparator(k.loadCell(W+1,this._workCell));){k.loadCell(W+1,this._workCell);const S=this._workCell.getChars().length;this._workCell.getWidth()===2?(w++,W++):S>1&&(D+=S-1,O+=S-1),O++,W++}}O++;let P=T+M-C+E,H=Math.min(this._bufferService.cols,O-T+C+w-E-D);if(p||x.slice(T,O).trim()!==""){if(b&&P===0&&k.getCodePoint(0)!==32){const U=y.lines.get(h[1]-1);if(U&&k.isWrapped&&U.getCodePoint(this._bufferService.cols-1)!==32){const W=this._getWordAt([this._bufferService.cols-1,h[1]-1],!1,!0,!1);if(W){const z=this._bufferService.cols-W.start;P-=z,H+=z}}}if(L&&P+H===this._bufferService.cols&&k.getCodePoint(this._bufferService.cols-1)!==32){const U=y.lines.get(h[1]+1);if(U!=null&&U.isWrapped&&U.getCodePoint(0)!==32){const W=this._getWordAt([0,h[1]+1],!1,!1,!0);W&&(H+=W.length)}}return{start:P,length:H}}}_selectWordAt(h,p){const b=this._getWordAt(h,p);if(b){for(;b.start<0;)b.start+=this._bufferService.cols,h[1]--;this._model.selectionStart=[b.start,h[1]],this._model.selectionStartLength=b.length}}_selectToWordAt(h){const p=this._getWordAt(h,!0);if(p){let b=h[1];for(;p.start<0;)p.start+=this._bufferService.cols,b--;if(!this._model.areSelectionValuesReversed())for(;p.start+p.length>this._bufferService.cols;)p.length-=this._bufferService.cols,b++;this._model.selectionEnd=[this._model.areSelectionValuesReversed()?p.start:p.start+p.length,b]}}_isCharWordSeparator(h){return h.getWidth()!==0&&this._optionsService.rawOptions.wordSeparator.indexOf(h.getChars())>=0}_selectLineAt(h){const p=this._bufferService.buffer.getWrappedRangeForLine(h),b={start:{x:0,y:p.first},end:{x:this._bufferService.cols-1,y:p.last}};this._model.selectionStart=[0,p.first],this._model.selectionEnd=void 0,this._model.selectionStartLength=(0,t.getRangeLength)(b,this._bufferService.cols)}};r.SelectionService=m=c([_(3,o.IBufferService),_(4,o.ICoreService),_(5,g.IMouseService),_(6,o.IOptionsService),_(7,g.IRenderService),_(8,g.ICoreBrowserService)],m)},4725:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.IThemeService=r.ICharacterJoinerService=r.ISelectionService=r.IRenderService=r.IMouseService=r.ICoreBrowserService=r.ICharSizeService=void 0;const c=a(8343);r.ICharSizeService=(0,c.createDecorator)("CharSizeService"),r.ICoreBrowserService=(0,c.createDecorator)("CoreBrowserService"),r.IMouseService=(0,c.createDecorator)("MouseService"),r.IRenderService=(0,c.createDecorator)("RenderService"),r.ISelectionService=(0,c.createDecorator)("SelectionService"),r.ICharacterJoinerService=(0,c.createDecorator)("CharacterJoinerService"),r.IThemeService=(0,c.createDecorator)("ThemeService")},6731:function(I,r,a){var c=this&&this.__decorate||function(m,h,p,b){var L,y=arguments.length,k=y<3?h:b===null?b=Object.getOwnPropertyDescriptor(h,p):b;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")k=Reflect.decorate(m,h,p,b);else for(var x=m.length-1;x>=0;x--)(L=m[x])&&(k=(y<3?L(k):y>3?L(h,p,k):L(h,p))||k);return y>3&&k&&Object.defineProperty(h,p,k),k},_=this&&this.__param||function(m,h){return function(p,b){h(p,b,m)}};Object.defineProperty(r,"__esModule",{value:!0}),r.ThemeService=r.DEFAULT_ANSI_COLORS=void 0;const n=a(7239),d=a(8055),f=a(8460),g=a(844),u=a(2585),e=d.css.toColor("#ffffff"),s=d.css.toColor("#000000"),t=d.css.toColor("#ffffff"),i=d.css.toColor("#000000"),o={css:"rgba(255, 255, 255, 0.3)",rgba:4294967117};r.DEFAULT_ANSI_COLORS=Object.freeze((()=>{const m=[d.css.toColor("#2e3436"),d.css.toColor("#cc0000"),d.css.toColor("#4e9a06"),d.css.toColor("#c4a000"),d.css.toColor("#3465a4"),d.css.toColor("#75507b"),d.css.toColor("#06989a"),d.css.toColor("#d3d7cf"),d.css.toColor("#555753"),d.css.toColor("#ef2929"),d.css.toColor("#8ae234"),d.css.toColor("#fce94f"),d.css.toColor("#729fcf"),d.css.toColor("#ad7fa8"),d.css.toColor("#34e2e2"),d.css.toColor("#eeeeec")],h=[0,95,135,175,215,255];for(let p=0;p<216;p++){const b=h[p/36%6|0],L=h[p/6%6|0],y=h[p%6];m.push({css:d.channels.toCss(b,L,y),rgba:d.channels.toRgba(b,L,y)})}for(let p=0;p<24;p++){const b=8+10*p;m.push({css:d.channels.toCss(b,b,b),rgba:d.channels.toRgba(b,b,b)})}return m})());let l=r.ThemeService=class extends g.Disposable{get colors(){return this._colors}constructor(m){super(),this._optionsService=m,this._contrastCache=new n.ColorContrastCache,this._halfContrastCache=new n.ColorContrastCache,this._onChangeColors=this.register(new f.EventEmitter),this.onChangeColors=this._onChangeColors.event,this._colors={foreground:e,background:s,cursor:t,cursorAccent:i,selectionForeground:void 0,selectionBackgroundTransparent:o,selectionBackgroundOpaque:d.color.blend(s,o),selectionInactiveBackgroundTransparent:o,selectionInactiveBackgroundOpaque:d.color.blend(s,o),ansi:r.DEFAULT_ANSI_COLORS.slice(),contrastCache:this._contrastCache,halfContrastCache:this._halfContrastCache},this._updateRestoreColors(),this._setTheme(this._optionsService.rawOptions.theme),this.register(this._optionsService.onSpecificOptionChange("minimumContrastRatio",()=>this._contrastCache.clear())),this.register(this._optionsService.onSpecificOptionChange("theme",()=>this._setTheme(this._optionsService.rawOptions.theme)))}_setTheme(m={}){const h=this._colors;if(h.foreground=v(m.foreground,e),h.background=v(m.background,s),h.cursor=v(m.cursor,t),h.cursorAccent=v(m.cursorAccent,i),h.selectionBackgroundTransparent=v(m.selectionBackground,o),h.selectionBackgroundOpaque=d.color.blend(h.background,h.selectionBackgroundTransparent),h.selectionInactiveBackgroundTransparent=v(m.selectionInactiveBackground,h.selectionBackgroundTransparent),h.selectionInactiveBackgroundOpaque=d.color.blend(h.background,h.selectionInactiveBackgroundTransparent),h.selectionForeground=m.selectionForeground?v(m.selectionForeground,d.NULL_COLOR):void 0,h.selectionForeground===d.NULL_COLOR&&(h.selectionForeground=void 0),d.color.isOpaque(h.selectionBackgroundTransparent)&&(h.selectionBackgroundTransparent=d.color.opacity(h.selectionBackgroundTransparent,.3)),d.color.isOpaque(h.selectionInactiveBackgroundTransparent)&&(h.selectionInactiveBackgroundTransparent=d.color.opacity(h.selectionInactiveBackgroundTransparent,.3)),h.ansi=r.DEFAULT_ANSI_COLORS.slice(),h.ansi[0]=v(m.black,r.DEFAULT_ANSI_COLORS[0]),h.ansi[1]=v(m.red,r.DEFAULT_ANSI_COLORS[1]),h.ansi[2]=v(m.green,r.DEFAULT_ANSI_COLORS[2]),h.ansi[3]=v(m.yellow,r.DEFAULT_ANSI_COLORS[3]),h.ansi[4]=v(m.blue,r.DEFAULT_ANSI_COLORS[4]),h.ansi[5]=v(m.magenta,r.DEFAULT_ANSI_COLORS[5]),h.ansi[6]=v(m.cyan,r.DEFAULT_ANSI_COLORS[6]),h.ansi[7]=v(m.white,r.DEFAULT_ANSI_COLORS[7]),h.ansi[8]=v(m.brightBlack,r.DEFAULT_ANSI_COLORS[8]),h.ansi[9]=v(m.brightRed,r.DEFAULT_ANSI_COLORS[9]),h.ansi[10]=v(m.brightGreen,r.DEFAULT_ANSI_COLORS[10]),h.ansi[11]=v(m.brightYellow,r.DEFAULT_ANSI_COLORS[11]),h.ansi[12]=v(m.brightBlue,r.DEFAULT_ANSI_COLORS[12]),h.ansi[13]=v(m.brightMagenta,r.DEFAULT_ANSI_COLORS[13]),h.ansi[14]=v(m.brightCyan,r.DEFAULT_ANSI_COLORS[14]),h.ansi[15]=v(m.brightWhite,r.DEFAULT_ANSI_COLORS[15]),m.extendedAnsi){const p=Math.min(h.ansi.length-16,m.extendedAnsi.length);for(let b=0;b<p;b++)h.ansi[b+16]=v(m.extendedAnsi[b],r.DEFAULT_ANSI_COLORS[b+16])}this._contrastCache.clear(),this._halfContrastCache.clear(),this._updateRestoreColors(),this._onChangeColors.fire(this.colors)}restoreColor(m){this._restoreColor(m),this._onChangeColors.fire(this.colors)}_restoreColor(m){if(m!==void 0)switch(m){case 256:this._colors.foreground=this._restoreColors.foreground;break;case 257:this._colors.background=this._restoreColors.background;break;case 258:this._colors.cursor=this._restoreColors.cursor;break;default:this._colors.ansi[m]=this._restoreColors.ansi[m]}else for(let h=0;h<this._restoreColors.ansi.length;++h)this._colors.ansi[h]=this._restoreColors.ansi[h]}modifyColors(m){m(this._colors),this._onChangeColors.fire(this.colors)}_updateRestoreColors(){this._restoreColors={foreground:this._colors.foreground,background:this._colors.background,cursor:this._colors.cursor,ansi:this._colors.ansi.slice()}}};function v(m,h){if(m!==void 0)try{return d.css.toColor(m)}catch{}return h}r.ThemeService=l=c([_(0,u.IOptionsService)],l)},6349:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.CircularList=void 0;const c=a(8460),_=a(844);class n extends _.Disposable{constructor(f){super(),this._maxLength=f,this.onDeleteEmitter=this.register(new c.EventEmitter),this.onDelete=this.onDeleteEmitter.event,this.onInsertEmitter=this.register(new c.EventEmitter),this.onInsert=this.onInsertEmitter.event,this.onTrimEmitter=this.register(new c.EventEmitter),this.onTrim=this.onTrimEmitter.event,this._array=new Array(this._maxLength),this._startIndex=0,this._length=0}get maxLength(){return this._maxLength}set maxLength(f){if(this._maxLength===f)return;const g=new Array(f);for(let u=0;u<Math.min(f,this.length);u++)g[u]=this._array[this._getCyclicIndex(u)];this._array=g,this._maxLength=f,this._startIndex=0}get length(){return this._length}set length(f){if(f>this._length)for(let g=this._length;g<f;g++)this._array[g]=void 0;this._length=f}get(f){return this._array[this._getCyclicIndex(f)]}set(f,g){this._array[this._getCyclicIndex(f)]=g}push(f){this._array[this._getCyclicIndex(this._length)]=f,this._length===this._maxLength?(this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1)):this._length++}recycle(){if(this._length!==this._maxLength)throw new Error("Can only recycle when the buffer is full");return this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1),this._array[this._getCyclicIndex(this._length-1)]}get isFull(){return this._length===this._maxLength}pop(){return this._array[this._getCyclicIndex(this._length---1)]}splice(f,g,...u){if(g){for(let e=f;e<this._length-g;e++)this._array[this._getCyclicIndex(e)]=this._array[this._getCyclicIndex(e+g)];this._length-=g,this.onDeleteEmitter.fire({index:f,amount:g})}for(let e=this._length-1;e>=f;e--)this._array[this._getCyclicIndex(e+u.length)]=this._array[this._getCyclicIndex(e)];for(let e=0;e<u.length;e++)this._array[this._getCyclicIndex(f+e)]=u[e];if(u.length&&this.onInsertEmitter.fire({index:f,amount:u.length}),this._length+u.length>this._maxLength){const e=this._length+u.length-this._maxLength;this._startIndex+=e,this._length=this._maxLength,this.onTrimEmitter.fire(e)}else this._length+=u.length}trimStart(f){f>this._length&&(f=this._length),this._startIndex+=f,this._length-=f,this.onTrimEmitter.fire(f)}shiftElements(f,g,u){if(!(g<=0)){if(f<0||f>=this._length)throw new Error("start argument out of range");if(f+u<0)throw new Error("Cannot shift elements in list beyond index 0");if(u>0){for(let s=g-1;s>=0;s--)this.set(f+s+u,this.get(f+s));const e=f+g+u-this._length;if(e>0)for(this._length+=e;this._length>this._maxLength;)this._length--,this._startIndex++,this.onTrimEmitter.fire(1)}else for(let e=0;e<g;e++)this.set(f+e+u,this.get(f+e))}}_getCyclicIndex(f){return(this._startIndex+f)%this._maxLength}}r.CircularList=n},1439:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.clone=void 0,r.clone=function a(c,_=5){if(typeof c!="object")return c;const n=Array.isArray(c)?[]:{};for(const d in c)n[d]=_<=1?c[d]:c[d]&&a(c[d],_-1);return n}},8055:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.contrastRatio=r.toPaddedHex=r.rgba=r.rgb=r.css=r.color=r.channels=r.NULL_COLOR=void 0;const c=a(6114);let _=0,n=0,d=0,f=0;var g,u,e,s,t;function i(l){const v=l.toString(16);return v.length<2?"0"+v:v}function o(l,v){return l<v?(v+.05)/(l+.05):(l+.05)/(v+.05)}r.NULL_COLOR={css:"#00000000",rgba:0},function(l){l.toCss=function(v,m,h,p){return p!==void 0?`#${i(v)}${i(m)}${i(h)}${i(p)}`:`#${i(v)}${i(m)}${i(h)}`},l.toRgba=function(v,m,h,p=255){return(v<<24|m<<16|h<<8|p)>>>0}}(g||(r.channels=g={})),function(l){function v(m,h){return f=Math.round(255*h),[_,n,d]=t.toChannels(m.rgba),{css:g.toCss(_,n,d,f),rgba:g.toRgba(_,n,d,f)}}l.blend=function(m,h){if(f=(255&h.rgba)/255,f===1)return{css:h.css,rgba:h.rgba};const p=h.rgba>>24&255,b=h.rgba>>16&255,L=h.rgba>>8&255,y=m.rgba>>24&255,k=m.rgba>>16&255,x=m.rgba>>8&255;return _=y+Math.round((p-y)*f),n=k+Math.round((b-k)*f),d=x+Math.round((L-x)*f),{css:g.toCss(_,n,d),rgba:g.toRgba(_,n,d)}},l.isOpaque=function(m){return(255&m.rgba)==255},l.ensureContrastRatio=function(m,h,p){const b=t.ensureContrastRatio(m.rgba,h.rgba,p);if(b)return t.toColor(b>>24&255,b>>16&255,b>>8&255)},l.opaque=function(m){const h=(255|m.rgba)>>>0;return[_,n,d]=t.toChannels(h),{css:g.toCss(_,n,d),rgba:h}},l.opacity=v,l.multiplyOpacity=function(m,h){return f=255&m.rgba,v(m,f*h/255)},l.toColorRGB=function(m){return[m.rgba>>24&255,m.rgba>>16&255,m.rgba>>8&255]}}(u||(r.color=u={})),function(l){let v,m;if(!c.isNode){const h=document.createElement("canvas");h.width=1,h.height=1;const p=h.getContext("2d",{willReadFrequently:!0});p&&(v=p,v.globalCompositeOperation="copy",m=v.createLinearGradient(0,0,1,1))}l.toColor=function(h){if(h.match(/#[\da-f]{3,8}/i))switch(h.length){case 4:return _=parseInt(h.slice(1,2).repeat(2),16),n=parseInt(h.slice(2,3).repeat(2),16),d=parseInt(h.slice(3,4).repeat(2),16),t.toColor(_,n,d);case 5:return _=parseInt(h.slice(1,2).repeat(2),16),n=parseInt(h.slice(2,3).repeat(2),16),d=parseInt(h.slice(3,4).repeat(2),16),f=parseInt(h.slice(4,5).repeat(2),16),t.toColor(_,n,d,f);case 7:return{css:h,rgba:(parseInt(h.slice(1),16)<<8|255)>>>0};case 9:return{css:h,rgba:parseInt(h.slice(1),16)>>>0}}const p=h.match(/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(,\s*(0|1|\d?\.(\d+))\s*)?\)/);if(p)return _=parseInt(p[1]),n=parseInt(p[2]),d=parseInt(p[3]),f=Math.round(255*(p[5]===void 0?1:parseFloat(p[5]))),t.toColor(_,n,d,f);if(!v||!m)throw new Error("css.toColor: Unsupported css format");if(v.fillStyle=m,v.fillStyle=h,typeof v.fillStyle!="string")throw new Error("css.toColor: Unsupported css format");if(v.fillRect(0,0,1,1),[_,n,d,f]=v.getImageData(0,0,1,1).data,f!==255)throw new Error("css.toColor: Unsupported css format");return{rgba:g.toRgba(_,n,d,f),css:h}}}(e||(r.css=e={})),function(l){function v(m,h,p){const b=m/255,L=h/255,y=p/255;return .2126*(b<=.03928?b/12.92:Math.pow((b+.055)/1.055,2.4))+.7152*(L<=.03928?L/12.92:Math.pow((L+.055)/1.055,2.4))+.0722*(y<=.03928?y/12.92:Math.pow((y+.055)/1.055,2.4))}l.relativeLuminance=function(m){return v(m>>16&255,m>>8&255,255&m)},l.relativeLuminance2=v}(s||(r.rgb=s={})),function(l){function v(h,p,b){const L=h>>24&255,y=h>>16&255,k=h>>8&255;let x=p>>24&255,T=p>>16&255,O=p>>8&255,M=o(s.relativeLuminance2(x,T,O),s.relativeLuminance2(L,y,k));for(;M<b&&(x>0||T>0||O>0);)x-=Math.max(0,Math.ceil(.1*x)),T-=Math.max(0,Math.ceil(.1*T)),O-=Math.max(0,Math.ceil(.1*O)),M=o(s.relativeLuminance2(x,T,O),s.relativeLuminance2(L,y,k));return(x<<24|T<<16|O<<8|255)>>>0}function m(h,p,b){const L=h>>24&255,y=h>>16&255,k=h>>8&255;let x=p>>24&255,T=p>>16&255,O=p>>8&255,M=o(s.relativeLuminance2(x,T,O),s.relativeLuminance2(L,y,k));for(;M<b&&(x<255||T<255||O<255);)x=Math.min(255,x+Math.ceil(.1*(255-x))),T=Math.min(255,T+Math.ceil(.1*(255-T))),O=Math.min(255,O+Math.ceil(.1*(255-O))),M=o(s.relativeLuminance2(x,T,O),s.relativeLuminance2(L,y,k));return(x<<24|T<<16|O<<8|255)>>>0}l.ensureContrastRatio=function(h,p,b){const L=s.relativeLuminance(h>>8),y=s.relativeLuminance(p>>8);if(o(L,y)<b){if(y<L){const T=v(h,p,b),O=o(L,s.relativeLuminance(T>>8));if(O<b){const M=m(h,p,b);return O>o(L,s.relativeLuminance(M>>8))?T:M}return T}const k=m(h,p,b),x=o(L,s.relativeLuminance(k>>8));if(x<b){const T=v(h,p,b);return x>o(L,s.relativeLuminance(T>>8))?k:T}return k}},l.reduceLuminance=v,l.increaseLuminance=m,l.toChannels=function(h){return[h>>24&255,h>>16&255,h>>8&255,255&h]},l.toColor=function(h,p,b,L){return{css:g.toCss(h,p,b,L),rgba:g.toRgba(h,p,b,L)}}}(t||(r.rgba=t={})),r.toPaddedHex=i,r.contrastRatio=o},8969:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.CoreTerminal=void 0;const c=a(844),_=a(2585),n=a(4348),d=a(7866),f=a(744),g=a(7302),u=a(6975),e=a(8460),s=a(1753),t=a(1480),i=a(7994),o=a(9282),l=a(5435),v=a(5981),m=a(2660);let h=!1;class p extends c.Disposable{get onScroll(){return this._onScrollApi||(this._onScrollApi=this.register(new e.EventEmitter),this._onScroll.event(L=>{var y;(y=this._onScrollApi)===null||y===void 0||y.fire(L.position)})),this._onScrollApi.event}get cols(){return this._bufferService.cols}get rows(){return this._bufferService.rows}get buffers(){return this._bufferService.buffers}get options(){return this.optionsService.options}set options(L){for(const y in L)this.optionsService.options[y]=L[y]}constructor(L){super(),this._windowsWrappingHeuristics=this.register(new c.MutableDisposable),this._onBinary=this.register(new e.EventEmitter),this.onBinary=this._onBinary.event,this._onData=this.register(new e.EventEmitter),this.onData=this._onData.event,this._onLineFeed=this.register(new e.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onResize=this.register(new e.EventEmitter),this.onResize=this._onResize.event,this._onWriteParsed=this.register(new e.EventEmitter),this.onWriteParsed=this._onWriteParsed.event,this._onScroll=this.register(new e.EventEmitter),this._instantiationService=new n.InstantiationService,this.optionsService=this.register(new g.OptionsService(L)),this._instantiationService.setService(_.IOptionsService,this.optionsService),this._bufferService=this.register(this._instantiationService.createInstance(f.BufferService)),this._instantiationService.setService(_.IBufferService,this._bufferService),this._logService=this.register(this._instantiationService.createInstance(d.LogService)),this._instantiationService.setService(_.ILogService,this._logService),this.coreService=this.register(this._instantiationService.createInstance(u.CoreService)),this._instantiationService.setService(_.ICoreService,this.coreService),this.coreMouseService=this.register(this._instantiationService.createInstance(s.CoreMouseService)),this._instantiationService.setService(_.ICoreMouseService,this.coreMouseService),this.unicodeService=this.register(this._instantiationService.createInstance(t.UnicodeService)),this._instantiationService.setService(_.IUnicodeService,this.unicodeService),this._charsetService=this._instantiationService.createInstance(i.CharsetService),this._instantiationService.setService(_.ICharsetService,this._charsetService),this._oscLinkService=this._instantiationService.createInstance(m.OscLinkService),this._instantiationService.setService(_.IOscLinkService,this._oscLinkService),this._inputHandler=this.register(new l.InputHandler(this._bufferService,this._charsetService,this.coreService,this._logService,this.optionsService,this._oscLinkService,this.coreMouseService,this.unicodeService)),this.register((0,e.forwardEvent)(this._inputHandler.onLineFeed,this._onLineFeed)),this.register(this._inputHandler),this.register((0,e.forwardEvent)(this._bufferService.onResize,this._onResize)),this.register((0,e.forwardEvent)(this.coreService.onData,this._onData)),this.register((0,e.forwardEvent)(this.coreService.onBinary,this._onBinary)),this.register(this.coreService.onRequestScrollToBottom(()=>this.scrollToBottom())),this.register(this.coreService.onUserInput(()=>this._writeBuffer.handleUserInput())),this.register(this.optionsService.onMultipleOptionChange(["windowsMode","windowsPty"],()=>this._handleWindowsPtyOptionChange())),this.register(this._bufferService.onScroll(y=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this.register(this._inputHandler.onScroll(y=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this._writeBuffer=this.register(new v.WriteBuffer((y,k)=>this._inputHandler.parse(y,k))),this.register((0,e.forwardEvent)(this._writeBuffer.onWriteParsed,this._onWriteParsed))}write(L,y){this._writeBuffer.write(L,y)}writeSync(L,y){this._logService.logLevel<=_.LogLevelEnum.WARN&&!h&&(this._logService.warn("writeSync is unreliable and will be removed soon."),h=!0),this._writeBuffer.writeSync(L,y)}resize(L,y){isNaN(L)||isNaN(y)||(L=Math.max(L,f.MINIMUM_COLS),y=Math.max(y,f.MINIMUM_ROWS),this._bufferService.resize(L,y))}scroll(L,y=!1){this._bufferService.scroll(L,y)}scrollLines(L,y,k){this._bufferService.scrollLines(L,y,k)}scrollPages(L){this.scrollLines(L*(this.rows-1))}scrollToTop(){this.scrollLines(-this._bufferService.buffer.ydisp)}scrollToBottom(){this.scrollLines(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp)}scrollToLine(L){const y=L-this._bufferService.buffer.ydisp;y!==0&&this.scrollLines(y)}registerEscHandler(L,y){return this._inputHandler.registerEscHandler(L,y)}registerDcsHandler(L,y){return this._inputHandler.registerDcsHandler(L,y)}registerCsiHandler(L,y){return this._inputHandler.registerCsiHandler(L,y)}registerOscHandler(L,y){return this._inputHandler.registerOscHandler(L,y)}_setup(){this._handleWindowsPtyOptionChange()}reset(){this._inputHandler.reset(),this._bufferService.reset(),this._charsetService.reset(),this.coreService.reset(),this.coreMouseService.reset()}_handleWindowsPtyOptionChange(){let L=!1;const y=this.optionsService.rawOptions.windowsPty;y&&y.buildNumber!==void 0&&y.buildNumber!==void 0?L=y.backend==="conpty"&&y.buildNumber<21376:this.optionsService.rawOptions.windowsMode&&(L=!0),L?this._enableWindowsWrappingHeuristics():this._windowsWrappingHeuristics.clear()}_enableWindowsWrappingHeuristics(){if(!this._windowsWrappingHeuristics.value){const L=[];L.push(this.onLineFeed(o.updateWindowsModeWrappedState.bind(null,this._bufferService))),L.push(this.registerCsiHandler({final:"H"},()=>((0,o.updateWindowsModeWrappedState)(this._bufferService),!1))),this._windowsWrappingHeuristics.value=(0,c.toDisposable)(()=>{for(const y of L)y.dispose()})}}}r.CoreTerminal=p},8460:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.forwardEvent=r.EventEmitter=void 0,r.EventEmitter=class{constructor(){this._listeners=[],this._disposed=!1}get event(){return this._event||(this._event=a=>(this._listeners.push(a),{dispose:()=>{if(!this._disposed){for(let c=0;c<this._listeners.length;c++)if(this._listeners[c]===a)return void this._listeners.splice(c,1)}}})),this._event}fire(a,c){const _=[];for(let n=0;n<this._listeners.length;n++)_.push(this._listeners[n]);for(let n=0;n<_.length;n++)_[n].call(void 0,a,c)}dispose(){this.clearListeners(),this._disposed=!0}clearListeners(){this._listeners&&(this._listeners.length=0)}},r.forwardEvent=function(a,c){return a(_=>c.fire(_))}},5435:function(I,r,a){var c=this&&this.__decorate||function(M,C,w,E){var D,P=arguments.length,H=P<3?C:E===null?E=Object.getOwnPropertyDescriptor(C,w):E;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")H=Reflect.decorate(M,C,w,E);else for(var U=M.length-1;U>=0;U--)(D=M[U])&&(H=(P<3?D(H):P>3?D(C,w,H):D(C,w))||H);return P>3&&H&&Object.defineProperty(C,w,H),H},_=this&&this.__param||function(M,C){return function(w,E){C(w,E,M)}};Object.defineProperty(r,"__esModule",{value:!0}),r.InputHandler=r.WindowsOptionsReportType=void 0;const n=a(2584),d=a(7116),f=a(2015),g=a(844),u=a(482),e=a(8437),s=a(8460),t=a(643),i=a(511),o=a(3734),l=a(2585),v=a(6242),m=a(6351),h=a(5941),p={"(":0,")":1,"*":2,"+":3,"-":1,".":2},b=131072;function L(M,C){if(M>24)return C.setWinLines||!1;switch(M){case 1:return!!C.restoreWin;case 2:return!!C.minimizeWin;case 3:return!!C.setWinPosition;case 4:return!!C.setWinSizePixels;case 5:return!!C.raiseWin;case 6:return!!C.lowerWin;case 7:return!!C.refreshWin;case 8:return!!C.setWinSizeChars;case 9:return!!C.maximizeWin;case 10:return!!C.fullscreenWin;case 11:return!!C.getWinState;case 13:return!!C.getWinPosition;case 14:return!!C.getWinSizePixels;case 15:return!!C.getScreenSizePixels;case 16:return!!C.getCellSizePixels;case 18:return!!C.getWinSizeChars;case 19:return!!C.getScreenSizeChars;case 20:return!!C.getIconTitle;case 21:return!!C.getWinTitle;case 22:return!!C.pushTitle;case 23:return!!C.popTitle;case 24:return!!C.setWinLines}return!1}var y;(function(M){M[M.GET_WIN_SIZE_PIXELS=0]="GET_WIN_SIZE_PIXELS",M[M.GET_CELL_SIZE_PIXELS=1]="GET_CELL_SIZE_PIXELS"})(y||(r.WindowsOptionsReportType=y={}));let k=0;class x extends g.Disposable{getAttrData(){return this._curAttrData}constructor(C,w,E,D,P,H,U,W,z=new f.EscapeSequenceParser){super(),this._bufferService=C,this._charsetService=w,this._coreService=E,this._logService=D,this._optionsService=P,this._oscLinkService=H,this._coreMouseService=U,this._unicodeService=W,this._parser=z,this._parseBuffer=new Uint32Array(4096),this._stringDecoder=new u.StringToUtf32,this._utf8Decoder=new u.Utf8ToUtf32,this._workCell=new i.CellData,this._windowTitle="",this._iconName="",this._windowTitleStack=[],this._iconNameStack=[],this._curAttrData=e.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=e.DEFAULT_ATTR_DATA.clone(),this._onRequestBell=this.register(new s.EventEmitter),this.onRequestBell=this._onRequestBell.event,this._onRequestRefreshRows=this.register(new s.EventEmitter),this.onRequestRefreshRows=this._onRequestRefreshRows.event,this._onRequestReset=this.register(new s.EventEmitter),this.onRequestReset=this._onRequestReset.event,this._onRequestSendFocus=this.register(new s.EventEmitter),this.onRequestSendFocus=this._onRequestSendFocus.event,this._onRequestSyncScrollBar=this.register(new s.EventEmitter),this.onRequestSyncScrollBar=this._onRequestSyncScrollBar.event,this._onRequestWindowsOptionsReport=this.register(new s.EventEmitter),this.onRequestWindowsOptionsReport=this._onRequestWindowsOptionsReport.event,this._onA11yChar=this.register(new s.EventEmitter),this.onA11yChar=this._onA11yChar.event,this._onA11yTab=this.register(new s.EventEmitter),this.onA11yTab=this._onA11yTab.event,this._onCursorMove=this.register(new s.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onLineFeed=this.register(new s.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onScroll=this.register(new s.EventEmitter),this.onScroll=this._onScroll.event,this._onTitleChange=this.register(new s.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onColor=this.register(new s.EventEmitter),this.onColor=this._onColor.event,this._parseStack={paused:!1,cursorStartX:0,cursorStartY:0,decodedLength:0,position:0},this._specialColors=[256,257,258],this.register(this._parser),this._dirtyRowTracker=new T(this._bufferService),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(S=>this._activeBuffer=S.activeBuffer)),this._parser.setCsiHandlerFallback((S,R)=>{this._logService.debug("Unknown CSI code: ",{identifier:this._parser.identToString(S),params:R.toArray()})}),this._parser.setEscHandlerFallback(S=>{this._logService.debug("Unknown ESC code: ",{identifier:this._parser.identToString(S)})}),this._parser.setExecuteHandlerFallback(S=>{this._logService.debug("Unknown EXECUTE code: ",{code:S})}),this._parser.setOscHandlerFallback((S,R,B)=>{this._logService.debug("Unknown OSC code: ",{identifier:S,action:R,data:B})}),this._parser.setDcsHandlerFallback((S,R,B)=>{R==="HOOK"&&(B=B.toArray()),this._logService.debug("Unknown DCS code: ",{identifier:this._parser.identToString(S),action:R,payload:B})}),this._parser.setPrintHandler((S,R,B)=>this.print(S,R,B)),this._parser.registerCsiHandler({final:"@"},S=>this.insertChars(S)),this._parser.registerCsiHandler({intermediates:" ",final:"@"},S=>this.scrollLeft(S)),this._parser.registerCsiHandler({final:"A"},S=>this.cursorUp(S)),this._parser.registerCsiHandler({intermediates:" ",final:"A"},S=>this.scrollRight(S)),this._parser.registerCsiHandler({final:"B"},S=>this.cursorDown(S)),this._parser.registerCsiHandler({final:"C"},S=>this.cursorForward(S)),this._parser.registerCsiHandler({final:"D"},S=>this.cursorBackward(S)),this._parser.registerCsiHandler({final:"E"},S=>this.cursorNextLine(S)),this._parser.registerCsiHandler({final:"F"},S=>this.cursorPrecedingLine(S)),this._parser.registerCsiHandler({final:"G"},S=>this.cursorCharAbsolute(S)),this._parser.registerCsiHandler({final:"H"},S=>this.cursorPosition(S)),this._parser.registerCsiHandler({final:"I"},S=>this.cursorForwardTab(S)),this._parser.registerCsiHandler({final:"J"},S=>this.eraseInDisplay(S,!1)),this._parser.registerCsiHandler({prefix:"?",final:"J"},S=>this.eraseInDisplay(S,!0)),this._parser.registerCsiHandler({final:"K"},S=>this.eraseInLine(S,!1)),this._parser.registerCsiHandler({prefix:"?",final:"K"},S=>this.eraseInLine(S,!0)),this._parser.registerCsiHandler({final:"L"},S=>this.insertLines(S)),this._parser.registerCsiHandler({final:"M"},S=>this.deleteLines(S)),this._parser.registerCsiHandler({final:"P"},S=>this.deleteChars(S)),this._parser.registerCsiHandler({final:"S"},S=>this.scrollUp(S)),this._parser.registerCsiHandler({final:"T"},S=>this.scrollDown(S)),this._parser.registerCsiHandler({final:"X"},S=>this.eraseChars(S)),this._parser.registerCsiHandler({final:"Z"},S=>this.cursorBackwardTab(S)),this._parser.registerCsiHandler({final:"`"},S=>this.charPosAbsolute(S)),this._parser.registerCsiHandler({final:"a"},S=>this.hPositionRelative(S)),this._parser.registerCsiHandler({final:"b"},S=>this.repeatPrecedingCharacter(S)),this._parser.registerCsiHandler({final:"c"},S=>this.sendDeviceAttributesPrimary(S)),this._parser.registerCsiHandler({prefix:">",final:"c"},S=>this.sendDeviceAttributesSecondary(S)),this._parser.registerCsiHandler({final:"d"},S=>this.linePosAbsolute(S)),this._parser.registerCsiHandler({final:"e"},S=>this.vPositionRelative(S)),this._parser.registerCsiHandler({final:"f"},S=>this.hVPosition(S)),this._parser.registerCsiHandler({final:"g"},S=>this.tabClear(S)),this._parser.registerCsiHandler({final:"h"},S=>this.setMode(S)),this._parser.registerCsiHandler({prefix:"?",final:"h"},S=>this.setModePrivate(S)),this._parser.registerCsiHandler({final:"l"},S=>this.resetMode(S)),this._parser.registerCsiHandler({prefix:"?",final:"l"},S=>this.resetModePrivate(S)),this._parser.registerCsiHandler({final:"m"},S=>this.charAttributes(S)),this._parser.registerCsiHandler({final:"n"},S=>this.deviceStatus(S)),this._parser.registerCsiHandler({prefix:"?",final:"n"},S=>this.deviceStatusPrivate(S)),this._parser.registerCsiHandler({intermediates:"!",final:"p"},S=>this.softReset(S)),this._parser.registerCsiHandler({intermediates:" ",final:"q"},S=>this.setCursorStyle(S)),this._parser.registerCsiHandler({final:"r"},S=>this.setScrollRegion(S)),this._parser.registerCsiHandler({final:"s"},S=>this.saveCursor(S)),this._parser.registerCsiHandler({final:"t"},S=>this.windowOptions(S)),this._parser.registerCsiHandler({final:"u"},S=>this.restoreCursor(S)),this._parser.registerCsiHandler({intermediates:"'",final:"}"},S=>this.insertColumns(S)),this._parser.registerCsiHandler({intermediates:"'",final:"~"},S=>this.deleteColumns(S)),this._parser.registerCsiHandler({intermediates:'"',final:"q"},S=>this.selectProtected(S)),this._parser.registerCsiHandler({intermediates:"$",final:"p"},S=>this.requestMode(S,!0)),this._parser.registerCsiHandler({prefix:"?",intermediates:"$",final:"p"},S=>this.requestMode(S,!1)),this._parser.setExecuteHandler(n.C0.BEL,()=>this.bell()),this._parser.setExecuteHandler(n.C0.LF,()=>this.lineFeed()),this._parser.setExecuteHandler(n.C0.VT,()=>this.lineFeed()),this._parser.setExecuteHandler(n.C0.FF,()=>this.lineFeed()),this._parser.setExecuteHandler(n.C0.CR,()=>this.carriageReturn()),this._parser.setExecuteHandler(n.C0.BS,()=>this.backspace()),this._parser.setExecuteHandler(n.C0.HT,()=>this.tab()),this._parser.setExecuteHandler(n.C0.SO,()=>this.shiftOut()),this._parser.setExecuteHandler(n.C0.SI,()=>this.shiftIn()),this._parser.setExecuteHandler(n.C1.IND,()=>this.index()),this._parser.setExecuteHandler(n.C1.NEL,()=>this.nextLine()),this._parser.setExecuteHandler(n.C1.HTS,()=>this.tabSet()),this._parser.registerOscHandler(0,new v.OscHandler(S=>(this.setTitle(S),this.setIconName(S),!0))),this._parser.registerOscHandler(1,new v.OscHandler(S=>this.setIconName(S))),this._parser.registerOscHandler(2,new v.OscHandler(S=>this.setTitle(S))),this._parser.registerOscHandler(4,new v.OscHandler(S=>this.setOrReportIndexedColor(S))),this._parser.registerOscHandler(8,new v.OscHandler(S=>this.setHyperlink(S))),this._parser.registerOscHandler(10,new v.OscHandler(S=>this.setOrReportFgColor(S))),this._parser.registerOscHandler(11,new v.OscHandler(S=>this.setOrReportBgColor(S))),this._parser.registerOscHandler(12,new v.OscHandler(S=>this.setOrReportCursorColor(S))),this._parser.registerOscHandler(104,new v.OscHandler(S=>this.restoreIndexedColor(S))),this._parser.registerOscHandler(110,new v.OscHandler(S=>this.restoreFgColor(S))),this._parser.registerOscHandler(111,new v.OscHandler(S=>this.restoreBgColor(S))),this._parser.registerOscHandler(112,new v.OscHandler(S=>this.restoreCursorColor(S))),this._parser.registerEscHandler({final:"7"},()=>this.saveCursor()),this._parser.registerEscHandler({final:"8"},()=>this.restoreCursor()),this._parser.registerEscHandler({final:"D"},()=>this.index()),this._parser.registerEscHandler({final:"E"},()=>this.nextLine()),this._parser.registerEscHandler({final:"H"},()=>this.tabSet()),this._parser.registerEscHandler({final:"M"},()=>this.reverseIndex()),this._parser.registerEscHandler({final:"="},()=>this.keypadApplicationMode()),this._parser.registerEscHandler({final:">"},()=>this.keypadNumericMode()),this._parser.registerEscHandler({final:"c"},()=>this.fullReset()),this._parser.registerEscHandler({final:"n"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"o"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"|"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"}"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"~"},()=>this.setgLevel(1)),this._parser.registerEscHandler({intermediates:"%",final:"@"},()=>this.selectDefaultCharset()),this._parser.registerEscHandler({intermediates:"%",final:"G"},()=>this.selectDefaultCharset());for(const S in d.CHARSETS)this._parser.registerEscHandler({intermediates:"(",final:S},()=>this.selectCharset("("+S)),this._parser.registerEscHandler({intermediates:")",final:S},()=>this.selectCharset(")"+S)),this._parser.registerEscHandler({intermediates:"*",final:S},()=>this.selectCharset("*"+S)),this._parser.registerEscHandler({intermediates:"+",final:S},()=>this.selectCharset("+"+S)),this._parser.registerEscHandler({intermediates:"-",final:S},()=>this.selectCharset("-"+S)),this._parser.registerEscHandler({intermediates:".",final:S},()=>this.selectCharset("."+S)),this._parser.registerEscHandler({intermediates:"/",final:S},()=>this.selectCharset("/"+S));this._parser.registerEscHandler({intermediates:"#",final:"8"},()=>this.screenAlignmentPattern()),this._parser.setErrorHandler(S=>(this._logService.error("Parsing error: ",S),S)),this._parser.registerDcsHandler({intermediates:"$",final:"q"},new m.DcsHandler((S,R)=>this.requestStatusString(S,R)))}_preserveStack(C,w,E,D){this._parseStack.paused=!0,this._parseStack.cursorStartX=C,this._parseStack.cursorStartY=w,this._parseStack.decodedLength=E,this._parseStack.position=D}_logSlowResolvingAsync(C){this._logService.logLevel<=l.LogLevelEnum.WARN&&Promise.race([C,new Promise((w,E)=>setTimeout(()=>E("#SLOW_TIMEOUT"),5e3))]).catch(w=>{if(w!=="#SLOW_TIMEOUT")throw w;console.warn("async parser handler taking longer than 5000 ms")})}_getCurrentLinkId(){return this._curAttrData.extended.urlId}parse(C,w){let E,D=this._activeBuffer.x,P=this._activeBuffer.y,H=0;const U=this._parseStack.paused;if(U){if(E=this._parser.parse(this._parseBuffer,this._parseStack.decodedLength,w))return this._logSlowResolvingAsync(E),E;D=this._parseStack.cursorStartX,P=this._parseStack.cursorStartY,this._parseStack.paused=!1,C.length>b&&(H=this._parseStack.position+b)}if(this._logService.logLevel<=l.LogLevelEnum.DEBUG&&this._logService.debug("parsing data"+(typeof C=="string"?` "${C}"`:` "${Array.prototype.map.call(C,W=>String.fromCharCode(W)).join("")}"`),typeof C=="string"?C.split("").map(W=>W.charCodeAt(0)):C),this._parseBuffer.length<C.length&&this._parseBuffer.length<b&&(this._parseBuffer=new Uint32Array(Math.min(C.length,b))),U||this._dirtyRowTracker.clearRange(),C.length>b)for(let W=H;W<C.length;W+=b){const z=W+b<C.length?W+b:C.length,S=typeof C=="string"?this._stringDecoder.decode(C.substring(W,z),this._parseBuffer):this._utf8Decoder.decode(C.subarray(W,z),this._parseBuffer);if(E=this._parser.parse(this._parseBuffer,S))return this._preserveStack(D,P,S,W),this._logSlowResolvingAsync(E),E}else if(!U){const W=typeof C=="string"?this._stringDecoder.decode(C,this._parseBuffer):this._utf8Decoder.decode(C,this._parseBuffer);if(E=this._parser.parse(this._parseBuffer,W))return this._preserveStack(D,P,W,0),this._logSlowResolvingAsync(E),E}this._activeBuffer.x===D&&this._activeBuffer.y===P||this._onCursorMove.fire(),this._onRequestRefreshRows.fire(this._dirtyRowTracker.start,this._dirtyRowTracker.end)}print(C,w,E){let D,P;const H=this._charsetService.charset,U=this._optionsService.rawOptions.screenReaderMode,W=this._bufferService.cols,z=this._coreService.decPrivateModes.wraparound,S=this._coreService.modes.insertMode,R=this._curAttrData;let B=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._activeBuffer.x&&E-w>0&&B.getWidth(this._activeBuffer.x-1)===2&&B.setCellFromCodePoint(this._activeBuffer.x-1,0,1,R.fg,R.bg,R.extended);for(let A=w;A<E;++A){if(D=C[A],P=this._unicodeService.wcwidth(D),D<127&&H){const N=H[String.fromCharCode(D)];N&&(D=N.charCodeAt(0))}if(U&&this._onA11yChar.fire((0,u.stringFromCodePoint)(D)),this._getCurrentLinkId()&&this._oscLinkService.addLineToLink(this._getCurrentLinkId(),this._activeBuffer.ybase+this._activeBuffer.y),P||!this._activeBuffer.x){if(this._activeBuffer.x+P-1>=W){if(z){for(;this._activeBuffer.x<W;)B.setCellFromCodePoint(this._activeBuffer.x++,0,1,R.fg,R.bg,R.extended);this._activeBuffer.x=0,this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData(),!0)):(this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!0),B=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y)}else if(this._activeBuffer.x=W-1,P===2)continue}if(S&&(B.insertCells(this._activeBuffer.x,P,this._activeBuffer.getNullCell(R),R),B.getWidth(W-1)===2&&B.setCellFromCodePoint(W-1,t.NULL_CELL_CODE,t.NULL_CELL_WIDTH,R.fg,R.bg,R.extended)),B.setCellFromCodePoint(this._activeBuffer.x++,D,P,R.fg,R.bg,R.extended),P>0)for(;--P;)B.setCellFromCodePoint(this._activeBuffer.x++,0,0,R.fg,R.bg,R.extended)}else B.getWidth(this._activeBuffer.x-1)?B.addCodepointToCell(this._activeBuffer.x-1,D):B.addCodepointToCell(this._activeBuffer.x-2,D)}E-w>0&&(B.loadCell(this._activeBuffer.x-1,this._workCell),this._workCell.getWidth()===2||this._workCell.getCode()>65535?this._parser.precedingCodepoint=0:this._workCell.isCombined()?this._parser.precedingCodepoint=this._workCell.getChars().charCodeAt(0):this._parser.precedingCodepoint=this._workCell.content),this._activeBuffer.x<W&&E-w>0&&B.getWidth(this._activeBuffer.x)===0&&!B.hasContent(this._activeBuffer.x)&&B.setCellFromCodePoint(this._activeBuffer.x,0,1,R.fg,R.bg,R.extended),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}registerCsiHandler(C,w){return C.final!=="t"||C.prefix||C.intermediates?this._parser.registerCsiHandler(C,w):this._parser.registerCsiHandler(C,E=>!L(E.params[0],this._optionsService.rawOptions.windowOptions)||w(E))}registerDcsHandler(C,w){return this._parser.registerDcsHandler(C,new m.DcsHandler(w))}registerEscHandler(C,w){return this._parser.registerEscHandler(C,w)}registerOscHandler(C,w){return this._parser.registerOscHandler(C,new v.OscHandler(w))}bell(){return this._onRequestBell.fire(),!0}lineFeed(){return this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._optionsService.rawOptions.convertEol&&(this._activeBuffer.x=0),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows?this._activeBuffer.y=this._bufferService.rows-1:this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.x>=this._bufferService.cols&&this._activeBuffer.x--,this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._onLineFeed.fire(),!0}carriageReturn(){return this._activeBuffer.x=0,!0}backspace(){var C;if(!this._coreService.decPrivateModes.reverseWraparound)return this._restrictCursor(),this._activeBuffer.x>0&&this._activeBuffer.x--,!0;if(this._restrictCursor(this._bufferService.cols),this._activeBuffer.x>0)this._activeBuffer.x--;else if(this._activeBuffer.x===0&&this._activeBuffer.y>this._activeBuffer.scrollTop&&this._activeBuffer.y<=this._activeBuffer.scrollBottom&&(!((C=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y))===null||C===void 0)&&C.isWrapped)){this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.y--,this._activeBuffer.x=this._bufferService.cols-1;const w=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);w.hasWidth(this._activeBuffer.x)&&!w.hasContent(this._activeBuffer.x)&&this._activeBuffer.x--}return this._restrictCursor(),!0}tab(){if(this._activeBuffer.x>=this._bufferService.cols)return!0;const C=this._activeBuffer.x;return this._activeBuffer.x=this._activeBuffer.nextStop(),this._optionsService.rawOptions.screenReaderMode&&this._onA11yTab.fire(this._activeBuffer.x-C),!0}shiftOut(){return this._charsetService.setgLevel(1),!0}shiftIn(){return this._charsetService.setgLevel(0),!0}_restrictCursor(C=this._bufferService.cols-1){this._activeBuffer.x=Math.min(C,Math.max(0,this._activeBuffer.x)),this._activeBuffer.y=this._coreService.decPrivateModes.origin?Math.min(this._activeBuffer.scrollBottom,Math.max(this._activeBuffer.scrollTop,this._activeBuffer.y)):Math.min(this._bufferService.rows-1,Math.max(0,this._activeBuffer.y)),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_setCursor(C,w){this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._coreService.decPrivateModes.origin?(this._activeBuffer.x=C,this._activeBuffer.y=this._activeBuffer.scrollTop+w):(this._activeBuffer.x=C,this._activeBuffer.y=w),this._restrictCursor(),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_moveCursor(C,w){this._restrictCursor(),this._setCursor(this._activeBuffer.x+C,this._activeBuffer.y+w)}cursorUp(C){const w=this._activeBuffer.y-this._activeBuffer.scrollTop;return w>=0?this._moveCursor(0,-Math.min(w,C.params[0]||1)):this._moveCursor(0,-(C.params[0]||1)),!0}cursorDown(C){const w=this._activeBuffer.scrollBottom-this._activeBuffer.y;return w>=0?this._moveCursor(0,Math.min(w,C.params[0]||1)):this._moveCursor(0,C.params[0]||1),!0}cursorForward(C){return this._moveCursor(C.params[0]||1,0),!0}cursorBackward(C){return this._moveCursor(-(C.params[0]||1),0),!0}cursorNextLine(C){return this.cursorDown(C),this._activeBuffer.x=0,!0}cursorPrecedingLine(C){return this.cursorUp(C),this._activeBuffer.x=0,!0}cursorCharAbsolute(C){return this._setCursor((C.params[0]||1)-1,this._activeBuffer.y),!0}cursorPosition(C){return this._setCursor(C.length>=2?(C.params[1]||1)-1:0,(C.params[0]||1)-1),!0}charPosAbsolute(C){return this._setCursor((C.params[0]||1)-1,this._activeBuffer.y),!0}hPositionRelative(C){return this._moveCursor(C.params[0]||1,0),!0}linePosAbsolute(C){return this._setCursor(this._activeBuffer.x,(C.params[0]||1)-1),!0}vPositionRelative(C){return this._moveCursor(0,C.params[0]||1),!0}hVPosition(C){return this.cursorPosition(C),!0}tabClear(C){const w=C.params[0];return w===0?delete this._activeBuffer.tabs[this._activeBuffer.x]:w===3&&(this._activeBuffer.tabs={}),!0}cursorForwardTab(C){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let w=C.params[0]||1;for(;w--;)this._activeBuffer.x=this._activeBuffer.nextStop();return!0}cursorBackwardTab(C){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let w=C.params[0]||1;for(;w--;)this._activeBuffer.x=this._activeBuffer.prevStop();return!0}selectProtected(C){const w=C.params[0];return w===1&&(this._curAttrData.bg|=536870912),w!==2&&w!==0||(this._curAttrData.bg&=-536870913),!0}_eraseInBufferLine(C,w,E,D=!1,P=!1){const H=this._activeBuffer.lines.get(this._activeBuffer.ybase+C);H.replaceCells(w,E,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData(),P),D&&(H.isWrapped=!1)}_resetBufferLine(C,w=!1){const E=this._activeBuffer.lines.get(this._activeBuffer.ybase+C);E&&(E.fill(this._activeBuffer.getNullCell(this._eraseAttrData()),w),this._bufferService.buffer.clearMarkers(this._activeBuffer.ybase+C),E.isWrapped=!1)}eraseInDisplay(C,w=!1){let E;switch(this._restrictCursor(this._bufferService.cols),C.params[0]){case 0:for(E=this._activeBuffer.y,this._dirtyRowTracker.markDirty(E),this._eraseInBufferLine(E++,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,w);E<this._bufferService.rows;E++)this._resetBufferLine(E,w);this._dirtyRowTracker.markDirty(E);break;case 1:for(E=this._activeBuffer.y,this._dirtyRowTracker.markDirty(E),this._eraseInBufferLine(E,0,this._activeBuffer.x+1,!0,w),this._activeBuffer.x+1>=this._bufferService.cols&&(this._activeBuffer.lines.get(E+1).isWrapped=!1);E--;)this._resetBufferLine(E,w);this._dirtyRowTracker.markDirty(0);break;case 2:for(E=this._bufferService.rows,this._dirtyRowTracker.markDirty(E-1);E--;)this._resetBufferLine(E,w);this._dirtyRowTracker.markDirty(0);break;case 3:const D=this._activeBuffer.lines.length-this._bufferService.rows;D>0&&(this._activeBuffer.lines.trimStart(D),this._activeBuffer.ybase=Math.max(this._activeBuffer.ybase-D,0),this._activeBuffer.ydisp=Math.max(this._activeBuffer.ydisp-D,0),this._onScroll.fire(0))}return!0}eraseInLine(C,w=!1){switch(this._restrictCursor(this._bufferService.cols),C.params[0]){case 0:this._eraseInBufferLine(this._activeBuffer.y,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,w);break;case 1:this._eraseInBufferLine(this._activeBuffer.y,0,this._activeBuffer.x+1,!1,w);break;case 2:this._eraseInBufferLine(this._activeBuffer.y,0,this._bufferService.cols,!0,w)}return this._dirtyRowTracker.markDirty(this._activeBuffer.y),!0}insertLines(C){this._restrictCursor();let w=C.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const E=this._activeBuffer.ybase+this._activeBuffer.y,D=this._bufferService.rows-1-this._activeBuffer.scrollBottom,P=this._bufferService.rows-1+this._activeBuffer.ybase-D+1;for(;w--;)this._activeBuffer.lines.splice(P-1,1),this._activeBuffer.lines.splice(E,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}deleteLines(C){this._restrictCursor();let w=C.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const E=this._activeBuffer.ybase+this._activeBuffer.y;let D;for(D=this._bufferService.rows-1-this._activeBuffer.scrollBottom,D=this._bufferService.rows-1+this._activeBuffer.ybase-D;w--;)this._activeBuffer.lines.splice(E,1),this._activeBuffer.lines.splice(D,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}insertChars(C){this._restrictCursor();const w=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return w&&(w.insertCells(this._activeBuffer.x,C.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}deleteChars(C){this._restrictCursor();const w=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return w&&(w.deleteCells(this._activeBuffer.x,C.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}scrollUp(C){let w=C.params[0]||1;for(;w--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollDown(C){let w=C.params[0]||1;for(;w--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,0,this._activeBuffer.getBlankLine(e.DEFAULT_ATTR_DATA));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollLeft(C){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const w=C.params[0]||1;for(let E=this._activeBuffer.scrollTop;E<=this._activeBuffer.scrollBottom;++E){const D=this._activeBuffer.lines.get(this._activeBuffer.ybase+E);D.deleteCells(0,w,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),D.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollRight(C){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const w=C.params[0]||1;for(let E=this._activeBuffer.scrollTop;E<=this._activeBuffer.scrollBottom;++E){const D=this._activeBuffer.lines.get(this._activeBuffer.ybase+E);D.insertCells(0,w,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),D.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}insertColumns(C){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const w=C.params[0]||1;for(let E=this._activeBuffer.scrollTop;E<=this._activeBuffer.scrollBottom;++E){const D=this._activeBuffer.lines.get(this._activeBuffer.ybase+E);D.insertCells(this._activeBuffer.x,w,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),D.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}deleteColumns(C){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const w=C.params[0]||1;for(let E=this._activeBuffer.scrollTop;E<=this._activeBuffer.scrollBottom;++E){const D=this._activeBuffer.lines.get(this._activeBuffer.ybase+E);D.deleteCells(this._activeBuffer.x,w,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),D.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}eraseChars(C){this._restrictCursor();const w=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return w&&(w.replaceCells(this._activeBuffer.x,this._activeBuffer.x+(C.params[0]||1),this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}repeatPrecedingCharacter(C){if(!this._parser.precedingCodepoint)return!0;const w=C.params[0]||1,E=new Uint32Array(w);for(let D=0;D<w;++D)E[D]=this._parser.precedingCodepoint;return this.print(E,0,E.length),!0}sendDeviceAttributesPrimary(C){return C.params[0]>0||(this._is("xterm")||this._is("rxvt-unicode")||this._is("screen")?this._coreService.triggerDataEvent(n.C0.ESC+"[?1;2c"):this._is("linux")&&this._coreService.triggerDataEvent(n.C0.ESC+"[?6c")),!0}sendDeviceAttributesSecondary(C){return C.params[0]>0||(this._is("xterm")?this._coreService.triggerDataEvent(n.C0.ESC+"[>0;276;0c"):this._is("rxvt-unicode")?this._coreService.triggerDataEvent(n.C0.ESC+"[>85;95;0c"):this._is("linux")?this._coreService.triggerDataEvent(C.params[0]+"c"):this._is("screen")&&this._coreService.triggerDataEvent(n.C0.ESC+"[>83;40003;0c")),!0}_is(C){return(this._optionsService.rawOptions.termName+"").indexOf(C)===0}setMode(C){for(let w=0;w<C.length;w++)switch(C.params[w]){case 4:this._coreService.modes.insertMode=!0;break;case 20:this._optionsService.options.convertEol=!0}return!0}setModePrivate(C){for(let w=0;w<C.length;w++)switch(C.params[w]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!0;break;case 2:this._charsetService.setgCharset(0,d.DEFAULT_CHARSET),this._charsetService.setgCharset(1,d.DEFAULT_CHARSET),this._charsetService.setgCharset(2,d.DEFAULT_CHARSET),this._charsetService.setgCharset(3,d.DEFAULT_CHARSET);break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(132,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!0,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!0;break;case 12:this._optionsService.options.cursorBlink=!0;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!0;break;case 66:this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire();break;case 9:this._coreMouseService.activeProtocol="X10";break;case 1e3:this._coreMouseService.activeProtocol="VT200";break;case 1002:this._coreMouseService.activeProtocol="DRAG";break;case 1003:this._coreMouseService.activeProtocol="ANY";break;case 1004:this._coreService.decPrivateModes.sendFocus=!0,this._onRequestSendFocus.fire();break;case 1005:this._logService.debug("DECSET 1005 not supported (see #2507)");break;case 1006:this._coreMouseService.activeEncoding="SGR";break;case 1015:this._logService.debug("DECSET 1015 not supported (see #2507)");break;case 1016:this._coreMouseService.activeEncoding="SGR_PIXELS";break;case 25:this._coreService.isCursorHidden=!1;break;case 1048:this.saveCursor();break;case 1049:this.saveCursor();case 47:case 1047:this._bufferService.buffers.activateAltBuffer(this._eraseAttrData()),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!0}return!0}resetMode(C){for(let w=0;w<C.length;w++)switch(C.params[w]){case 4:this._coreService.modes.insertMode=!1;break;case 20:this._optionsService.options.convertEol=!1}return!0}resetModePrivate(C){for(let w=0;w<C.length;w++)switch(C.params[w]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!1;break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(80,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!1,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!1;break;case 12:this._optionsService.options.cursorBlink=!1;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!1;break;case 66:this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire();break;case 9:case 1e3:case 1002:case 1003:this._coreMouseService.activeProtocol="NONE";break;case 1004:this._coreService.decPrivateModes.sendFocus=!1;break;case 1005:this._logService.debug("DECRST 1005 not supported (see #2507)");break;case 1006:case 1016:this._coreMouseService.activeEncoding="DEFAULT";break;case 1015:this._logService.debug("DECRST 1015 not supported (see #2507)");break;case 25:this._coreService.isCursorHidden=!0;break;case 1048:this.restoreCursor();break;case 1049:case 47:case 1047:this._bufferService.buffers.activateNormalBuffer(),C.params[w]===1049&&this.restoreCursor(),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!1}return!0}requestMode(C,w){const E=this._coreService.decPrivateModes,{activeProtocol:D,activeEncoding:P}=this._coreMouseService,H=this._coreService,{buffers:U,cols:W}=this._bufferService,{active:z,alt:S}=U,R=this._optionsService.rawOptions,B=j=>j?1:2,A=C.params[0];return N=A,F=w?A===2?4:A===4?B(H.modes.insertMode):A===12?3:A===20?B(R.convertEol):0:A===1?B(E.applicationCursorKeys):A===3?R.windowOptions.setWinLines?W===80?2:W===132?1:0:0:A===6?B(E.origin):A===7?B(E.wraparound):A===8?3:A===9?B(D==="X10"):A===12?B(R.cursorBlink):A===25?B(!H.isCursorHidden):A===45?B(E.reverseWraparound):A===66?B(E.applicationKeypad):A===67?4:A===1e3?B(D==="VT200"):A===1002?B(D==="DRAG"):A===1003?B(D==="ANY"):A===1004?B(E.sendFocus):A===1005?4:A===1006?B(P==="SGR"):A===1015?4:A===1016?B(P==="SGR_PIXELS"):A===1048?1:A===47||A===1047||A===1049?B(z===S):A===2004?B(E.bracketedPasteMode):0,H.triggerDataEvent(`${n.C0.ESC}[${w?"":"?"}${N};${F}$y`),!0;var N,F}_updateAttrColor(C,w,E,D,P){return w===2?(C|=50331648,C&=-16777216,C|=o.AttributeData.fromColorRGB([E,D,P])):w===5&&(C&=-50331904,C|=33554432|255&E),C}_extractColor(C,w,E){const D=[0,0,-1,0,0,0];let P=0,H=0;do{if(D[H+P]=C.params[w+H],C.hasSubParams(w+H)){const U=C.getSubParams(w+H);let W=0;do D[1]===5&&(P=1),D[H+W+1+P]=U[W];while(++W<U.length&&W+H+1+P<D.length);break}if(D[1]===5&&H+P>=2||D[1]===2&&H+P>=5)break;D[1]&&(P=1)}while(++H+w<C.length&&H+P<D.length);for(let U=2;U<D.length;++U)D[U]===-1&&(D[U]=0);switch(D[0]){case 38:E.fg=this._updateAttrColor(E.fg,D[1],D[3],D[4],D[5]);break;case 48:E.bg=this._updateAttrColor(E.bg,D[1],D[3],D[4],D[5]);break;case 58:E.extended=E.extended.clone(),E.extended.underlineColor=this._updateAttrColor(E.extended.underlineColor,D[1],D[3],D[4],D[5])}return H}_processUnderline(C,w){w.extended=w.extended.clone(),(!~C||C>5)&&(C=1),w.extended.underlineStyle=C,w.fg|=268435456,C===0&&(w.fg&=-268435457),w.updateExtended()}_processSGR0(C){C.fg=e.DEFAULT_ATTR_DATA.fg,C.bg=e.DEFAULT_ATTR_DATA.bg,C.extended=C.extended.clone(),C.extended.underlineStyle=0,C.extended.underlineColor&=-67108864,C.updateExtended()}charAttributes(C){if(C.length===1&&C.params[0]===0)return this._processSGR0(this._curAttrData),!0;const w=C.length;let E;const D=this._curAttrData;for(let P=0;P<w;P++)E=C.params[P],E>=30&&E<=37?(D.fg&=-50331904,D.fg|=16777216|E-30):E>=40&&E<=47?(D.bg&=-50331904,D.bg|=16777216|E-40):E>=90&&E<=97?(D.fg&=-50331904,D.fg|=16777224|E-90):E>=100&&E<=107?(D.bg&=-50331904,D.bg|=16777224|E-100):E===0?this._processSGR0(D):E===1?D.fg|=134217728:E===3?D.bg|=67108864:E===4?(D.fg|=268435456,this._processUnderline(C.hasSubParams(P)?C.getSubParams(P)[0]:1,D)):E===5?D.fg|=536870912:E===7?D.fg|=67108864:E===8?D.fg|=1073741824:E===9?D.fg|=2147483648:E===2?D.bg|=134217728:E===21?this._processUnderline(2,D):E===22?(D.fg&=-134217729,D.bg&=-134217729):E===23?D.bg&=-67108865:E===24?(D.fg&=-268435457,this._processUnderline(0,D)):E===25?D.fg&=-536870913:E===27?D.fg&=-67108865:E===28?D.fg&=-1073741825:E===29?D.fg&=2147483647:E===39?(D.fg&=-67108864,D.fg|=16777215&e.DEFAULT_ATTR_DATA.fg):E===49?(D.bg&=-67108864,D.bg|=16777215&e.DEFAULT_ATTR_DATA.bg):E===38||E===48||E===58?P+=this._extractColor(C,P,D):E===53?D.bg|=1073741824:E===55?D.bg&=-1073741825:E===59?(D.extended=D.extended.clone(),D.extended.underlineColor=-1,D.updateExtended()):E===100?(D.fg&=-67108864,D.fg|=16777215&e.DEFAULT_ATTR_DATA.fg,D.bg&=-67108864,D.bg|=16777215&e.DEFAULT_ATTR_DATA.bg):this._logService.debug("Unknown SGR attribute: %d.",E);return!0}deviceStatus(C){switch(C.params[0]){case 5:this._coreService.triggerDataEvent(`${n.C0.ESC}[0n`);break;case 6:const w=this._activeBuffer.y+1,E=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${n.C0.ESC}[${w};${E}R`)}return!0}deviceStatusPrivate(C){if(C.params[0]===6){const w=this._activeBuffer.y+1,E=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${n.C0.ESC}[?${w};${E}R`)}return!0}softReset(C){return this._coreService.isCursorHidden=!1,this._onRequestSyncScrollBar.fire(),this._activeBuffer.scrollTop=0,this._activeBuffer.scrollBottom=this._bufferService.rows-1,this._curAttrData=e.DEFAULT_ATTR_DATA.clone(),this._coreService.reset(),this._charsetService.reset(),this._activeBuffer.savedX=0,this._activeBuffer.savedY=this._activeBuffer.ybase,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,this._coreService.decPrivateModes.origin=!1,!0}setCursorStyle(C){const w=C.params[0]||1;switch(w){case 1:case 2:this._optionsService.options.cursorStyle="block";break;case 3:case 4:this._optionsService.options.cursorStyle="underline";break;case 5:case 6:this._optionsService.options.cursorStyle="bar"}const E=w%2==1;return this._optionsService.options.cursorBlink=E,!0}setScrollRegion(C){const w=C.params[0]||1;let E;return(C.length<2||(E=C.params[1])>this._bufferService.rows||E===0)&&(E=this._bufferService.rows),E>w&&(this._activeBuffer.scrollTop=w-1,this._activeBuffer.scrollBottom=E-1,this._setCursor(0,0)),!0}windowOptions(C){if(!L(C.params[0],this._optionsService.rawOptions.windowOptions))return!0;const w=C.length>1?C.params[1]:0;switch(C.params[0]){case 14:w!==2&&this._onRequestWindowsOptionsReport.fire(y.GET_WIN_SIZE_PIXELS);break;case 16:this._onRequestWindowsOptionsReport.fire(y.GET_CELL_SIZE_PIXELS);break;case 18:this._bufferService&&this._coreService.triggerDataEvent(`${n.C0.ESC}[8;${this._bufferService.rows};${this._bufferService.cols}t`);break;case 22:w!==0&&w!==2||(this._windowTitleStack.push(this._windowTitle),this._windowTitleStack.length>10&&this._windowTitleStack.shift()),w!==0&&w!==1||(this._iconNameStack.push(this._iconName),this._iconNameStack.length>10&&this._iconNameStack.shift());break;case 23:w!==0&&w!==2||this._windowTitleStack.length&&this.setTitle(this._windowTitleStack.pop()),w!==0&&w!==1||this._iconNameStack.length&&this.setIconName(this._iconNameStack.pop())}return!0}saveCursor(C){return this._activeBuffer.savedX=this._activeBuffer.x,this._activeBuffer.savedY=this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,!0}restoreCursor(C){return this._activeBuffer.x=this._activeBuffer.savedX||0,this._activeBuffer.y=Math.max(this._activeBuffer.savedY-this._activeBuffer.ybase,0),this._curAttrData.fg=this._activeBuffer.savedCurAttrData.fg,this._curAttrData.bg=this._activeBuffer.savedCurAttrData.bg,this._charsetService.charset=this._savedCharset,this._activeBuffer.savedCharset&&(this._charsetService.charset=this._activeBuffer.savedCharset),this._restrictCursor(),!0}setTitle(C){return this._windowTitle=C,this._onTitleChange.fire(C),!0}setIconName(C){return this._iconName=C,!0}setOrReportIndexedColor(C){const w=[],E=C.split(";");for(;E.length>1;){const D=E.shift(),P=E.shift();if(/^\d+$/.exec(D)){const H=parseInt(D);if(O(H))if(P==="?")w.push({type:0,index:H});else{const U=(0,h.parseColor)(P);U&&w.push({type:1,index:H,color:U})}}}return w.length&&this._onColor.fire(w),!0}setHyperlink(C){const w=C.split(";");return!(w.length<2)&&(w[1]?this._createHyperlink(w[0],w[1]):!w[0]&&this._finishHyperlink())}_createHyperlink(C,w){this._getCurrentLinkId()&&this._finishHyperlink();const E=C.split(":");let D;const P=E.findIndex(H=>H.startsWith("id="));return P!==-1&&(D=E[P].slice(3)||void 0),this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=this._oscLinkService.registerLink({id:D,uri:w}),this._curAttrData.updateExtended(),!0}_finishHyperlink(){return this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=0,this._curAttrData.updateExtended(),!0}_setOrReportSpecialColor(C,w){const E=C.split(";");for(let D=0;D<E.length&&!(w>=this._specialColors.length);++D,++w)if(E[D]==="?")this._onColor.fire([{type:0,index:this._specialColors[w]}]);else{const P=(0,h.parseColor)(E[D]);P&&this._onColor.fire([{type:1,index:this._specialColors[w],color:P}])}return!0}setOrReportFgColor(C){return this._setOrReportSpecialColor(C,0)}setOrReportBgColor(C){return this._setOrReportSpecialColor(C,1)}setOrReportCursorColor(C){return this._setOrReportSpecialColor(C,2)}restoreIndexedColor(C){if(!C)return this._onColor.fire([{type:2}]),!0;const w=[],E=C.split(";");for(let D=0;D<E.length;++D)if(/^\d+$/.exec(E[D])){const P=parseInt(E[D]);O(P)&&w.push({type:2,index:P})}return w.length&&this._onColor.fire(w),!0}restoreFgColor(C){return this._onColor.fire([{type:2,index:256}]),!0}restoreBgColor(C){return this._onColor.fire([{type:2,index:257}]),!0}restoreCursorColor(C){return this._onColor.fire([{type:2,index:258}]),!0}nextLine(){return this._activeBuffer.x=0,this.index(),!0}keypadApplicationMode(){return this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire(),!0}keypadNumericMode(){return this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire(),!0}selectDefaultCharset(){return this._charsetService.setgLevel(0),this._charsetService.setgCharset(0,d.DEFAULT_CHARSET),!0}selectCharset(C){return C.length!==2?(this.selectDefaultCharset(),!0):(C[0]==="/"||this._charsetService.setgCharset(p[C[0]],d.CHARSETS[C[1]]||d.DEFAULT_CHARSET),!0)}index(){return this._restrictCursor(),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._restrictCursor(),!0}tabSet(){return this._activeBuffer.tabs[this._activeBuffer.x]=!0,!0}reverseIndex(){if(this._restrictCursor(),this._activeBuffer.y===this._activeBuffer.scrollTop){const C=this._activeBuffer.scrollBottom-this._activeBuffer.scrollTop;this._activeBuffer.lines.shiftElements(this._activeBuffer.ybase+this._activeBuffer.y,C,1),this._activeBuffer.lines.set(this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.getBlankLine(this._eraseAttrData())),this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom)}else this._activeBuffer.y--,this._restrictCursor();return!0}fullReset(){return this._parser.reset(),this._onRequestReset.fire(),!0}reset(){this._curAttrData=e.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=e.DEFAULT_ATTR_DATA.clone()}_eraseAttrData(){return this._eraseAttrDataInternal.bg&=-67108864,this._eraseAttrDataInternal.bg|=67108863&this._curAttrData.bg,this._eraseAttrDataInternal}setgLevel(C){return this._charsetService.setgLevel(C),!0}screenAlignmentPattern(){const C=new i.CellData;C.content=4194373,C.fg=this._curAttrData.fg,C.bg=this._curAttrData.bg,this._setCursor(0,0);for(let w=0;w<this._bufferService.rows;++w){const E=this._activeBuffer.ybase+this._activeBuffer.y+w,D=this._activeBuffer.lines.get(E);D&&(D.fill(C),D.isWrapped=!1)}return this._dirtyRowTracker.markAllDirty(),this._setCursor(0,0),!0}requestStatusString(C,w){const E=this._bufferService.buffer,D=this._optionsService.rawOptions;return(P=>(this._coreService.triggerDataEvent(`${n.C0.ESC}${P}${n.C0.ESC}\\`),!0))(C==='"q'?`P1$r${this._curAttrData.isProtected()?1:0}"q`:C==='"p'?'P1$r61;1"p':C==="r"?`P1$r${E.scrollTop+1};${E.scrollBottom+1}r`:C==="m"?"P1$r0m":C===" q"?`P1$r${{block:2,underline:4,bar:6}[D.cursorStyle]-(D.cursorBlink?1:0)} q`:"P0$r")}markRangeDirty(C,w){this._dirtyRowTracker.markRangeDirty(C,w)}}r.InputHandler=x;let T=class{constructor(M){this._bufferService=M,this.clearRange()}clearRange(){this.start=this._bufferService.buffer.y,this.end=this._bufferService.buffer.y}markDirty(M){M<this.start?this.start=M:M>this.end&&(this.end=M)}markRangeDirty(M,C){M>C&&(k=M,M=C,C=k),M<this.start&&(this.start=M),C>this.end&&(this.end=C)}markAllDirty(){this.markRangeDirty(0,this._bufferService.rows-1)}};function O(M){return 0<=M&&M<256}T=c([_(0,l.IBufferService)],T)},844:(I,r)=>{function a(c){for(const _ of c)_.dispose();c.length=0}Object.defineProperty(r,"__esModule",{value:!0}),r.getDisposeArrayDisposable=r.disposeArray=r.toDisposable=r.MutableDisposable=r.Disposable=void 0,r.Disposable=class{constructor(){this._disposables=[],this._isDisposed=!1}dispose(){this._isDisposed=!0;for(const c of this._disposables)c.dispose();this._disposables.length=0}register(c){return this._disposables.push(c),c}unregister(c){const _=this._disposables.indexOf(c);_!==-1&&this._disposables.splice(_,1)}},r.MutableDisposable=class{constructor(){this._isDisposed=!1}get value(){return this._isDisposed?void 0:this._value}set value(c){var _;this._isDisposed||c===this._value||((_=this._value)===null||_===void 0||_.dispose(),this._value=c)}clear(){this.value=void 0}dispose(){var c;this._isDisposed=!0,(c=this._value)===null||c===void 0||c.dispose(),this._value=void 0}},r.toDisposable=function(c){return{dispose:c}},r.disposeArray=a,r.getDisposeArrayDisposable=function(c){return{dispose:()=>a(c)}}},1505:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.FourKeyMap=r.TwoKeyMap=void 0;class a{constructor(){this._data={}}set(_,n,d){this._data[_]||(this._data[_]={}),this._data[_][n]=d}get(_,n){return this._data[_]?this._data[_][n]:void 0}clear(){this._data={}}}r.TwoKeyMap=a,r.FourKeyMap=class{constructor(){this._data=new a}set(c,_,n,d,f){this._data.get(c,_)||this._data.set(c,_,new a),this._data.get(c,_).set(n,d,f)}get(c,_,n,d){var f;return(f=this._data.get(c,_))===null||f===void 0?void 0:f.get(n,d)}clear(){this._data.clear()}}},6114:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.isChromeOS=r.isLinux=r.isWindows=r.isIphone=r.isIpad=r.isMac=r.getSafariVersion=r.isSafari=r.isLegacyEdge=r.isFirefox=r.isNode=void 0,r.isNode=typeof navigator>"u";const a=r.isNode?"node":navigator.userAgent,c=r.isNode?"node":navigator.platform;r.isFirefox=a.includes("Firefox"),r.isLegacyEdge=a.includes("Edge"),r.isSafari=/^((?!chrome|android).)*safari/i.test(a),r.getSafariVersion=function(){if(!r.isSafari)return 0;const _=a.match(/Version\/(\d+)/);return _===null||_.length<2?0:parseInt(_[1])},r.isMac=["Macintosh","MacIntel","MacPPC","Mac68K"].includes(c),r.isIpad=c==="iPad",r.isIphone=c==="iPhone",r.isWindows=["Windows","Win16","Win32","WinCE"].includes(c),r.isLinux=c.indexOf("Linux")>=0,r.isChromeOS=/\bCrOS\b/.test(a)},6106:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.SortedList=void 0;let a=0;r.SortedList=class{constructor(c){this._getKey=c,this._array=[]}clear(){this._array.length=0}insert(c){this._array.length!==0?(a=this._search(this._getKey(c)),this._array.splice(a,0,c)):this._array.push(c)}delete(c){if(this._array.length===0)return!1;const _=this._getKey(c);if(_===void 0||(a=this._search(_),a===-1)||this._getKey(this._array[a])!==_)return!1;do if(this._array[a]===c)return this._array.splice(a,1),!0;while(++a<this._array.length&&this._getKey(this._array[a])===_);return!1}*getKeyIterator(c){if(this._array.length!==0&&(a=this._search(c),!(a<0||a>=this._array.length)&&this._getKey(this._array[a])===c))do yield this._array[a];while(++a<this._array.length&&this._getKey(this._array[a])===c)}forEachByKey(c,_){if(this._array.length!==0&&(a=this._search(c),!(a<0||a>=this._array.length)&&this._getKey(this._array[a])===c))do _(this._array[a]);while(++a<this._array.length&&this._getKey(this._array[a])===c)}values(){return[...this._array].values()}_search(c){let _=0,n=this._array.length-1;for(;n>=_;){let d=_+n>>1;const f=this._getKey(this._array[d]);if(f>c)n=d-1;else{if(!(f<c)){for(;d>0&&this._getKey(this._array[d-1])===c;)d--;return d}_=d+1}}return _}}},7226:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.DebouncedIdleTask=r.IdleTaskQueue=r.PriorityTaskQueue=void 0;const c=a(6114);class _{constructor(){this._tasks=[],this._i=0}enqueue(f){this._tasks.push(f),this._start()}flush(){for(;this._i<this._tasks.length;)this._tasks[this._i]()||this._i++;this.clear()}clear(){this._idleCallback&&(this._cancelCallback(this._idleCallback),this._idleCallback=void 0),this._i=0,this._tasks.length=0}_start(){this._idleCallback||(this._idleCallback=this._requestCallback(this._process.bind(this)))}_process(f){this._idleCallback=void 0;let g=0,u=0,e=f.timeRemaining(),s=0;for(;this._i<this._tasks.length;){if(g=Date.now(),this._tasks[this._i]()||this._i++,g=Math.max(1,Date.now()-g),u=Math.max(g,u),s=f.timeRemaining(),1.5*u>s)return e-g<-20&&console.warn(`task queue exceeded allotted deadline by ${Math.abs(Math.round(e-g))}ms`),void this._start();e=s}this.clear()}}class n extends _{_requestCallback(f){return setTimeout(()=>f(this._createDeadline(16)))}_cancelCallback(f){clearTimeout(f)}_createDeadline(f){const g=Date.now()+f;return{timeRemaining:()=>Math.max(0,g-Date.now())}}}r.PriorityTaskQueue=n,r.IdleTaskQueue=!c.isNode&&"requestIdleCallback"in window?class extends _{_requestCallback(d){return requestIdleCallback(d)}_cancelCallback(d){cancelIdleCallback(d)}}:n,r.DebouncedIdleTask=class{constructor(){this._queue=new r.IdleTaskQueue}set(d){this._queue.clear(),this._queue.enqueue(d)}flush(){this._queue.flush()}}},9282:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.updateWindowsModeWrappedState=void 0;const c=a(643);r.updateWindowsModeWrappedState=function(_){const n=_.buffer.lines.get(_.buffer.ybase+_.buffer.y-1),d=n==null?void 0:n.get(_.cols-1),f=_.buffer.lines.get(_.buffer.ybase+_.buffer.y);f&&d&&(f.isWrapped=d[c.CHAR_DATA_CODE_INDEX]!==c.NULL_CELL_CODE&&d[c.CHAR_DATA_CODE_INDEX]!==c.WHITESPACE_CELL_CODE)}},3734:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.ExtendedAttrs=r.AttributeData=void 0;class a{constructor(){this.fg=0,this.bg=0,this.extended=new c}static toColorRGB(n){return[n>>>16&255,n>>>8&255,255&n]}static fromColorRGB(n){return(255&n[0])<<16|(255&n[1])<<8|255&n[2]}clone(){const n=new a;return n.fg=this.fg,n.bg=this.bg,n.extended=this.extended.clone(),n}isInverse(){return 67108864&this.fg}isBold(){return 134217728&this.fg}isUnderline(){return this.hasExtendedAttrs()&&this.extended.underlineStyle!==0?1:268435456&this.fg}isBlink(){return 536870912&this.fg}isInvisible(){return 1073741824&this.fg}isItalic(){return 67108864&this.bg}isDim(){return 134217728&this.bg}isStrikethrough(){return 2147483648&this.fg}isProtected(){return 536870912&this.bg}isOverline(){return 1073741824&this.bg}getFgColorMode(){return 50331648&this.fg}getBgColorMode(){return 50331648&this.bg}isFgRGB(){return(50331648&this.fg)==50331648}isBgRGB(){return(50331648&this.bg)==50331648}isFgPalette(){return(50331648&this.fg)==16777216||(50331648&this.fg)==33554432}isBgPalette(){return(50331648&this.bg)==16777216||(50331648&this.bg)==33554432}isFgDefault(){return(50331648&this.fg)==0}isBgDefault(){return(50331648&this.bg)==0}isAttributeDefault(){return this.fg===0&&this.bg===0}getFgColor(){switch(50331648&this.fg){case 16777216:case 33554432:return 255&this.fg;case 50331648:return 16777215&this.fg;default:return-1}}getBgColor(){switch(50331648&this.bg){case 16777216:case 33554432:return 255&this.bg;case 50331648:return 16777215&this.bg;default:return-1}}hasExtendedAttrs(){return 268435456&this.bg}updateExtended(){this.extended.isEmpty()?this.bg&=-268435457:this.bg|=268435456}getUnderlineColor(){if(268435456&this.bg&&~this.extended.underlineColor)switch(50331648&this.extended.underlineColor){case 16777216:case 33554432:return 255&this.extended.underlineColor;case 50331648:return 16777215&this.extended.underlineColor;default:return this.getFgColor()}return this.getFgColor()}getUnderlineColorMode(){return 268435456&this.bg&&~this.extended.underlineColor?50331648&this.extended.underlineColor:this.getFgColorMode()}isUnderlineColorRGB(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==50331648:this.isFgRGB()}isUnderlineColorPalette(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==16777216||(50331648&this.extended.underlineColor)==33554432:this.isFgPalette()}isUnderlineColorDefault(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==0:this.isFgDefault()}getUnderlineStyle(){return 268435456&this.fg?268435456&this.bg?this.extended.underlineStyle:1:0}}r.AttributeData=a;class c{get ext(){return this._urlId?-469762049&this._ext|this.underlineStyle<<26:this._ext}set ext(n){this._ext=n}get underlineStyle(){return this._urlId?5:(469762048&this._ext)>>26}set underlineStyle(n){this._ext&=-469762049,this._ext|=n<<26&469762048}get underlineColor(){return 67108863&this._ext}set underlineColor(n){this._ext&=-67108864,this._ext|=67108863&n}get urlId(){return this._urlId}set urlId(n){this._urlId=n}constructor(n=0,d=0){this._ext=0,this._urlId=0,this._ext=n,this._urlId=d}clone(){return new c(this._ext,this._urlId)}isEmpty(){return this.underlineStyle===0&&this._urlId===0}}r.ExtendedAttrs=c},9092:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.Buffer=r.MAX_BUFFER_SIZE=void 0;const c=a(6349),_=a(7226),n=a(3734),d=a(8437),f=a(4634),g=a(511),u=a(643),e=a(4863),s=a(7116);r.MAX_BUFFER_SIZE=4294967295,r.Buffer=class{constructor(t,i,o){this._hasScrollback=t,this._optionsService=i,this._bufferService=o,this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.tabs={},this.savedY=0,this.savedX=0,this.savedCurAttrData=d.DEFAULT_ATTR_DATA.clone(),this.savedCharset=s.DEFAULT_CHARSET,this.markers=[],this._nullCell=g.CellData.fromCharData([0,u.NULL_CELL_CHAR,u.NULL_CELL_WIDTH,u.NULL_CELL_CODE]),this._whitespaceCell=g.CellData.fromCharData([0,u.WHITESPACE_CELL_CHAR,u.WHITESPACE_CELL_WIDTH,u.WHITESPACE_CELL_CODE]),this._isClearing=!1,this._memoryCleanupQueue=new _.IdleTaskQueue,this._memoryCleanupPosition=0,this._cols=this._bufferService.cols,this._rows=this._bufferService.rows,this.lines=new c.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}getNullCell(t){return t?(this._nullCell.fg=t.fg,this._nullCell.bg=t.bg,this._nullCell.extended=t.extended):(this._nullCell.fg=0,this._nullCell.bg=0,this._nullCell.extended=new n.ExtendedAttrs),this._nullCell}getWhitespaceCell(t){return t?(this._whitespaceCell.fg=t.fg,this._whitespaceCell.bg=t.bg,this._whitespaceCell.extended=t.extended):(this._whitespaceCell.fg=0,this._whitespaceCell.bg=0,this._whitespaceCell.extended=new n.ExtendedAttrs),this._whitespaceCell}getBlankLine(t,i){return new d.BufferLine(this._bufferService.cols,this.getNullCell(t),i)}get hasScrollback(){return this._hasScrollback&&this.lines.maxLength>this._rows}get isCursorInViewport(){const t=this.ybase+this.y-this.ydisp;return t>=0&&t<this._rows}_getCorrectBufferLength(t){if(!this._hasScrollback)return t;const i=t+this._optionsService.rawOptions.scrollback;return i>r.MAX_BUFFER_SIZE?r.MAX_BUFFER_SIZE:i}fillViewportRows(t){if(this.lines.length===0){t===void 0&&(t=d.DEFAULT_ATTR_DATA);let i=this._rows;for(;i--;)this.lines.push(this.getBlankLine(t))}}clear(){this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.lines=new c.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}resize(t,i){const o=this.getNullCell(d.DEFAULT_ATTR_DATA);let l=0;const v=this._getCorrectBufferLength(i);if(v>this.lines.maxLength&&(this.lines.maxLength=v),this.lines.length>0){if(this._cols<t)for(let h=0;h<this.lines.length;h++)l+=+this.lines.get(h).resize(t,o);let m=0;if(this._rows<i)for(let h=this._rows;h<i;h++)this.lines.length<i+this.ybase&&(this._optionsService.rawOptions.windowsMode||this._optionsService.rawOptions.windowsPty.backend!==void 0||this._optionsService.rawOptions.windowsPty.buildNumber!==void 0?this.lines.push(new d.BufferLine(t,o)):this.ybase>0&&this.lines.length<=this.ybase+this.y+m+1?(this.ybase--,m++,this.ydisp>0&&this.ydisp--):this.lines.push(new d.BufferLine(t,o)));else for(let h=this._rows;h>i;h--)this.lines.length>i+this.ybase&&(this.lines.length>this.ybase+this.y+1?this.lines.pop():(this.ybase++,this.ydisp++));if(v<this.lines.maxLength){const h=this.lines.length-v;h>0&&(this.lines.trimStart(h),this.ybase=Math.max(this.ybase-h,0),this.ydisp=Math.max(this.ydisp-h,0),this.savedY=Math.max(this.savedY-h,0)),this.lines.maxLength=v}this.x=Math.min(this.x,t-1),this.y=Math.min(this.y,i-1),m&&(this.y+=m),this.savedX=Math.min(this.savedX,t-1),this.scrollTop=0}if(this.scrollBottom=i-1,this._isReflowEnabled&&(this._reflow(t,i),this._cols>t))for(let m=0;m<this.lines.length;m++)l+=+this.lines.get(m).resize(t,o);this._cols=t,this._rows=i,this._memoryCleanupQueue.clear(),l>.1*this.lines.length&&(this._memoryCleanupPosition=0,this._memoryCleanupQueue.enqueue(()=>this._batchedMemoryCleanup()))}_batchedMemoryCleanup(){let t=!0;this._memoryCleanupPosition>=this.lines.length&&(this._memoryCleanupPosition=0,t=!1);let i=0;for(;this._memoryCleanupPosition<this.lines.length;)if(i+=this.lines.get(this._memoryCleanupPosition++).cleanupMemory(),i>100)return!0;return t}get _isReflowEnabled(){const t=this._optionsService.rawOptions.windowsPty;return t&&t.buildNumber?this._hasScrollback&&t.backend==="conpty"&&t.buildNumber>=21376:this._hasScrollback&&!this._optionsService.rawOptions.windowsMode}_reflow(t,i){this._cols!==t&&(t>this._cols?this._reflowLarger(t,i):this._reflowSmaller(t,i))}_reflowLarger(t,i){const o=(0,f.reflowLargerGetLinesToRemove)(this.lines,this._cols,t,this.ybase+this.y,this.getNullCell(d.DEFAULT_ATTR_DATA));if(o.length>0){const l=(0,f.reflowLargerCreateNewLayout)(this.lines,o);(0,f.reflowLargerApplyNewLayout)(this.lines,l.layout),this._reflowLargerAdjustViewport(t,i,l.countRemoved)}}_reflowLargerAdjustViewport(t,i,o){const l=this.getNullCell(d.DEFAULT_ATTR_DATA);let v=o;for(;v-- >0;)this.ybase===0?(this.y>0&&this.y--,this.lines.length<i&&this.lines.push(new d.BufferLine(t,l))):(this.ydisp===this.ybase&&this.ydisp--,this.ybase--);this.savedY=Math.max(this.savedY-o,0)}_reflowSmaller(t,i){const o=this.getNullCell(d.DEFAULT_ATTR_DATA),l=[];let v=0;for(let m=this.lines.length-1;m>=0;m--){let h=this.lines.get(m);if(!h||!h.isWrapped&&h.getTrimmedLength()<=t)continue;const p=[h];for(;h.isWrapped&&m>0;)h=this.lines.get(--m),p.unshift(h);const b=this.ybase+this.y;if(b>=m&&b<m+p.length)continue;const L=p[p.length-1].getTrimmedLength(),y=(0,f.reflowSmallerGetNewLineLengths)(p,this._cols,t),k=y.length-p.length;let x;x=this.ybase===0&&this.y!==this.lines.length-1?Math.max(0,this.y-this.lines.maxLength+k):Math.max(0,this.lines.length-this.lines.maxLength+k);const T=[];for(let D=0;D<k;D++){const P=this.getBlankLine(d.DEFAULT_ATTR_DATA,!0);T.push(P)}T.length>0&&(l.push({start:m+p.length+v,newLines:T}),v+=T.length),p.push(...T);let O=y.length-1,M=y[O];M===0&&(O--,M=y[O]);let C=p.length-k-1,w=L;for(;C>=0;){const D=Math.min(w,M);if(p[O]===void 0)break;if(p[O].copyCellsFrom(p[C],w-D,M-D,D,!0),M-=D,M===0&&(O--,M=y[O]),w-=D,w===0){C--;const P=Math.max(C,0);w=(0,f.getWrappedLineTrimmedLength)(p,P,this._cols)}}for(let D=0;D<p.length;D++)y[D]<t&&p[D].setCell(y[D],o);let E=k-x;for(;E-- >0;)this.ybase===0?this.y<i-1?(this.y++,this.lines.pop()):(this.ybase++,this.ydisp++):this.ybase<Math.min(this.lines.maxLength,this.lines.length+v)-i&&(this.ybase===this.ydisp&&this.ydisp++,this.ybase++);this.savedY=Math.min(this.savedY+k,this.ybase+i-1)}if(l.length>0){const m=[],h=[];for(let O=0;O<this.lines.length;O++)h.push(this.lines.get(O));const p=this.lines.length;let b=p-1,L=0,y=l[L];this.lines.length=Math.min(this.lines.maxLength,this.lines.length+v);let k=0;for(let O=Math.min(this.lines.maxLength-1,p+v-1);O>=0;O--)if(y&&y.start>b+k){for(let M=y.newLines.length-1;M>=0;M--)this.lines.set(O--,y.newLines[M]);O++,m.push({index:b+1,amount:y.newLines.length}),k+=y.newLines.length,y=l[++L]}else this.lines.set(O,h[b--]);let x=0;for(let O=m.length-1;O>=0;O--)m[O].index+=x,this.lines.onInsertEmitter.fire(m[O]),x+=m[O].amount;const T=Math.max(0,p+v-this.lines.maxLength);T>0&&this.lines.onTrimEmitter.fire(T)}}translateBufferLineToString(t,i,o=0,l){const v=this.lines.get(t);return v?v.translateToString(i,o,l):""}getWrappedRangeForLine(t){let i=t,o=t;for(;i>0&&this.lines.get(i).isWrapped;)i--;for(;o+1<this.lines.length&&this.lines.get(o+1).isWrapped;)o++;return{first:i,last:o}}setupTabStops(t){for(t!=null?this.tabs[t]||(t=this.prevStop(t)):(this.tabs={},t=0);t<this._cols;t+=this._optionsService.rawOptions.tabStopWidth)this.tabs[t]=!0}prevStop(t){for(t==null&&(t=this.x);!this.tabs[--t]&&t>0;);return t>=this._cols?this._cols-1:t<0?0:t}nextStop(t){for(t==null&&(t=this.x);!this.tabs[++t]&&t<this._cols;);return t>=this._cols?this._cols-1:t<0?0:t}clearMarkers(t){this._isClearing=!0;for(let i=0;i<this.markers.length;i++)this.markers[i].line===t&&(this.markers[i].dispose(),this.markers.splice(i--,1));this._isClearing=!1}clearAllMarkers(){this._isClearing=!0;for(let t=0;t<this.markers.length;t++)this.markers[t].dispose(),this.markers.splice(t--,1);this._isClearing=!1}addMarker(t){const i=new e.Marker(t);return this.markers.push(i),i.register(this.lines.onTrim(o=>{i.line-=o,i.line<0&&i.dispose()})),i.register(this.lines.onInsert(o=>{i.line>=o.index&&(i.line+=o.amount)})),i.register(this.lines.onDelete(o=>{i.line>=o.index&&i.line<o.index+o.amount&&i.dispose(),i.line>o.index&&(i.line-=o.amount)})),i.register(i.onDispose(()=>this._removeMarker(i))),i}_removeMarker(t){this._isClearing||this.markers.splice(this.markers.indexOf(t),1)}}},8437:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.BufferLine=r.DEFAULT_ATTR_DATA=void 0;const c=a(3734),_=a(511),n=a(643),d=a(482);r.DEFAULT_ATTR_DATA=Object.freeze(new c.AttributeData);let f=0;class g{constructor(e,s,t=!1){this.isWrapped=t,this._combined={},this._extendedAttrs={},this._data=new Uint32Array(3*e);const i=s||_.CellData.fromCharData([0,n.NULL_CELL_CHAR,n.NULL_CELL_WIDTH,n.NULL_CELL_CODE]);for(let o=0;o<e;++o)this.setCell(o,i);this.length=e}get(e){const s=this._data[3*e+0],t=2097151&s;return[this._data[3*e+1],2097152&s?this._combined[e]:t?(0,d.stringFromCodePoint)(t):"",s>>22,2097152&s?this._combined[e].charCodeAt(this._combined[e].length-1):t]}set(e,s){this._data[3*e+1]=s[n.CHAR_DATA_ATTR_INDEX],s[n.CHAR_DATA_CHAR_INDEX].length>1?(this._combined[e]=s[1],this._data[3*e+0]=2097152|e|s[n.CHAR_DATA_WIDTH_INDEX]<<22):this._data[3*e+0]=s[n.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|s[n.CHAR_DATA_WIDTH_INDEX]<<22}getWidth(e){return this._data[3*e+0]>>22}hasWidth(e){return 12582912&this._data[3*e+0]}getFg(e){return this._data[3*e+1]}getBg(e){return this._data[3*e+2]}hasContent(e){return 4194303&this._data[3*e+0]}getCodePoint(e){const s=this._data[3*e+0];return 2097152&s?this._combined[e].charCodeAt(this._combined[e].length-1):2097151&s}isCombined(e){return 2097152&this._data[3*e+0]}getString(e){const s=this._data[3*e+0];return 2097152&s?this._combined[e]:2097151&s?(0,d.stringFromCodePoint)(2097151&s):""}isProtected(e){return 536870912&this._data[3*e+2]}loadCell(e,s){return f=3*e,s.content=this._data[f+0],s.fg=this._data[f+1],s.bg=this._data[f+2],2097152&s.content&&(s.combinedData=this._combined[e]),268435456&s.bg&&(s.extended=this._extendedAttrs[e]),s}setCell(e,s){2097152&s.content&&(this._combined[e]=s.combinedData),268435456&s.bg&&(this._extendedAttrs[e]=s.extended),this._data[3*e+0]=s.content,this._data[3*e+1]=s.fg,this._data[3*e+2]=s.bg}setCellFromCodePoint(e,s,t,i,o,l){268435456&o&&(this._extendedAttrs[e]=l),this._data[3*e+0]=s|t<<22,this._data[3*e+1]=i,this._data[3*e+2]=o}addCodepointToCell(e,s){let t=this._data[3*e+0];2097152&t?this._combined[e]+=(0,d.stringFromCodePoint)(s):(2097151&t?(this._combined[e]=(0,d.stringFromCodePoint)(2097151&t)+(0,d.stringFromCodePoint)(s),t&=-2097152,t|=2097152):t=s|4194304,this._data[3*e+0]=t)}insertCells(e,s,t,i){if((e%=this.length)&&this.getWidth(e-1)===2&&this.setCellFromCodePoint(e-1,0,1,(i==null?void 0:i.fg)||0,(i==null?void 0:i.bg)||0,(i==null?void 0:i.extended)||new c.ExtendedAttrs),s<this.length-e){const o=new _.CellData;for(let l=this.length-e-s-1;l>=0;--l)this.setCell(e+s+l,this.loadCell(e+l,o));for(let l=0;l<s;++l)this.setCell(e+l,t)}else for(let o=e;o<this.length;++o)this.setCell(o,t);this.getWidth(this.length-1)===2&&this.setCellFromCodePoint(this.length-1,0,1,(i==null?void 0:i.fg)||0,(i==null?void 0:i.bg)||0,(i==null?void 0:i.extended)||new c.ExtendedAttrs)}deleteCells(e,s,t,i){if(e%=this.length,s<this.length-e){const o=new _.CellData;for(let l=0;l<this.length-e-s;++l)this.setCell(e+l,this.loadCell(e+s+l,o));for(let l=this.length-s;l<this.length;++l)this.setCell(l,t)}else for(let o=e;o<this.length;++o)this.setCell(o,t);e&&this.getWidth(e-1)===2&&this.setCellFromCodePoint(e-1,0,1,(i==null?void 0:i.fg)||0,(i==null?void 0:i.bg)||0,(i==null?void 0:i.extended)||new c.ExtendedAttrs),this.getWidth(e)!==0||this.hasContent(e)||this.setCellFromCodePoint(e,0,1,(i==null?void 0:i.fg)||0,(i==null?void 0:i.bg)||0,(i==null?void 0:i.extended)||new c.ExtendedAttrs)}replaceCells(e,s,t,i,o=!1){if(o)for(e&&this.getWidth(e-1)===2&&!this.isProtected(e-1)&&this.setCellFromCodePoint(e-1,0,1,(i==null?void 0:i.fg)||0,(i==null?void 0:i.bg)||0,(i==null?void 0:i.extended)||new c.ExtendedAttrs),s<this.length&&this.getWidth(s-1)===2&&!this.isProtected(s)&&this.setCellFromCodePoint(s,0,1,(i==null?void 0:i.fg)||0,(i==null?void 0:i.bg)||0,(i==null?void 0:i.extended)||new c.ExtendedAttrs);e<s&&e<this.length;)this.isProtected(e)||this.setCell(e,t),e++;else for(e&&this.getWidth(e-1)===2&&this.setCellFromCodePoint(e-1,0,1,(i==null?void 0:i.fg)||0,(i==null?void 0:i.bg)||0,(i==null?void 0:i.extended)||new c.ExtendedAttrs),s<this.length&&this.getWidth(s-1)===2&&this.setCellFromCodePoint(s,0,1,(i==null?void 0:i.fg)||0,(i==null?void 0:i.bg)||0,(i==null?void 0:i.extended)||new c.ExtendedAttrs);e<s&&e<this.length;)this.setCell(e++,t)}resize(e,s){if(e===this.length)return 4*this._data.length*2<this._data.buffer.byteLength;const t=3*e;if(e>this.length){if(this._data.buffer.byteLength>=4*t)this._data=new Uint32Array(this._data.buffer,0,t);else{const i=new Uint32Array(t);i.set(this._data),this._data=i}for(let i=this.length;i<e;++i)this.setCell(i,s)}else{this._data=this._data.subarray(0,t);const i=Object.keys(this._combined);for(let l=0;l<i.length;l++){const v=parseInt(i[l],10);v>=e&&delete this._combined[v]}const o=Object.keys(this._extendedAttrs);for(let l=0;l<o.length;l++){const v=parseInt(o[l],10);v>=e&&delete this._extendedAttrs[v]}}return this.length=e,4*t*2<this._data.buffer.byteLength}cleanupMemory(){if(4*this._data.length*2<this._data.buffer.byteLength){const e=new Uint32Array(this._data.length);return e.set(this._data),this._data=e,1}return 0}fill(e,s=!1){if(s)for(let t=0;t<this.length;++t)this.isProtected(t)||this.setCell(t,e);else{this._combined={},this._extendedAttrs={};for(let t=0;t<this.length;++t)this.setCell(t,e)}}copyFrom(e){this.length!==e.length?this._data=new Uint32Array(e._data):this._data.set(e._data),this.length=e.length,this._combined={};for(const s in e._combined)this._combined[s]=e._combined[s];this._extendedAttrs={};for(const s in e._extendedAttrs)this._extendedAttrs[s]=e._extendedAttrs[s];this.isWrapped=e.isWrapped}clone(){const e=new g(0);e._data=new Uint32Array(this._data),e.length=this.length;for(const s in this._combined)e._combined[s]=this._combined[s];for(const s in this._extendedAttrs)e._extendedAttrs[s]=this._extendedAttrs[s];return e.isWrapped=this.isWrapped,e}getTrimmedLength(){for(let e=this.length-1;e>=0;--e)if(4194303&this._data[3*e+0])return e+(this._data[3*e+0]>>22);return 0}getNoBgTrimmedLength(){for(let e=this.length-1;e>=0;--e)if(4194303&this._data[3*e+0]||50331648&this._data[3*e+2])return e+(this._data[3*e+0]>>22);return 0}copyCellsFrom(e,s,t,i,o){const l=e._data;if(o)for(let m=i-1;m>=0;m--){for(let h=0;h<3;h++)this._data[3*(t+m)+h]=l[3*(s+m)+h];268435456&l[3*(s+m)+2]&&(this._extendedAttrs[t+m]=e._extendedAttrs[s+m])}else for(let m=0;m<i;m++){for(let h=0;h<3;h++)this._data[3*(t+m)+h]=l[3*(s+m)+h];268435456&l[3*(s+m)+2]&&(this._extendedAttrs[t+m]=e._extendedAttrs[s+m])}const v=Object.keys(e._combined);for(let m=0;m<v.length;m++){const h=parseInt(v[m],10);h>=s&&(this._combined[h-s+t]=e._combined[h])}}translateToString(e=!1,s=0,t=this.length){e&&(t=Math.min(t,this.getTrimmedLength()));let i="";for(;s<t;){const o=this._data[3*s+0],l=2097151&o;i+=2097152&o?this._combined[s]:l?(0,d.stringFromCodePoint)(l):n.WHITESPACE_CELL_CHAR,s+=o>>22||1}return i}}r.BufferLine=g},4841:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.getRangeLength=void 0,r.getRangeLength=function(a,c){if(a.start.y>a.end.y)throw new Error(`Buffer range end (${a.end.x}, ${a.end.y}) cannot be before start (${a.start.x}, ${a.start.y})`);return c*(a.end.y-a.start.y)+(a.end.x-a.start.x+1)}},4634:(I,r)=>{function a(c,_,n){if(_===c.length-1)return c[_].getTrimmedLength();const d=!c[_].hasContent(n-1)&&c[_].getWidth(n-1)===1,f=c[_+1].getWidth(0)===2;return d&&f?n-1:n}Object.defineProperty(r,"__esModule",{value:!0}),r.getWrappedLineTrimmedLength=r.reflowSmallerGetNewLineLengths=r.reflowLargerApplyNewLayout=r.reflowLargerCreateNewLayout=r.reflowLargerGetLinesToRemove=void 0,r.reflowLargerGetLinesToRemove=function(c,_,n,d,f){const g=[];for(let u=0;u<c.length-1;u++){let e=u,s=c.get(++e);if(!s.isWrapped)continue;const t=[c.get(u)];for(;e<c.length&&s.isWrapped;)t.push(s),s=c.get(++e);if(d>=u&&d<e){u+=t.length-1;continue}let i=0,o=a(t,i,_),l=1,v=0;for(;l<t.length;){const h=a(t,l,_),p=h-v,b=n-o,L=Math.min(p,b);t[i].copyCellsFrom(t[l],v,o,L,!1),o+=L,o===n&&(i++,o=0),v+=L,v===h&&(l++,v=0),o===0&&i!==0&&t[i-1].getWidth(n-1)===2&&(t[i].copyCellsFrom(t[i-1],n-1,o++,1,!1),t[i-1].setCell(n-1,f))}t[i].replaceCells(o,n,f);let m=0;for(let h=t.length-1;h>0&&(h>i||t[h].getTrimmedLength()===0);h--)m++;m>0&&(g.push(u+t.length-m),g.push(m)),u+=t.length-1}return g},r.reflowLargerCreateNewLayout=function(c,_){const n=[];let d=0,f=_[d],g=0;for(let u=0;u<c.length;u++)if(f===u){const e=_[++d];c.onDeleteEmitter.fire({index:u-g,amount:e}),u+=e-1,g+=e,f=_[++d]}else n.push(u);return{layout:n,countRemoved:g}},r.reflowLargerApplyNewLayout=function(c,_){const n=[];for(let d=0;d<_.length;d++)n.push(c.get(_[d]));for(let d=0;d<n.length;d++)c.set(d,n[d]);c.length=_.length},r.reflowSmallerGetNewLineLengths=function(c,_,n){const d=[],f=c.map((s,t)=>a(c,t,_)).reduce((s,t)=>s+t);let g=0,u=0,e=0;for(;e<f;){if(f-e<n){d.push(f-e);break}g+=n;const s=a(c,u,_);g>s&&(g-=s,u++);const t=c[u].getWidth(g-1)===2;t&&g--;const i=t?n-1:n;d.push(i),e+=i}return d},r.getWrappedLineTrimmedLength=a},5295:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.BufferSet=void 0;const c=a(8460),_=a(844),n=a(9092);class d extends _.Disposable{constructor(g,u){super(),this._optionsService=g,this._bufferService=u,this._onBufferActivate=this.register(new c.EventEmitter),this.onBufferActivate=this._onBufferActivate.event,this.reset(),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.resize(this._bufferService.cols,this._bufferService.rows))),this.register(this._optionsService.onSpecificOptionChange("tabStopWidth",()=>this.setupTabStops()))}reset(){this._normal=new n.Buffer(!0,this._optionsService,this._bufferService),this._normal.fillViewportRows(),this._alt=new n.Buffer(!1,this._optionsService,this._bufferService),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}),this.setupTabStops()}get alt(){return this._alt}get active(){return this._activeBuffer}get normal(){return this._normal}activateNormalBuffer(){this._activeBuffer!==this._normal&&(this._normal.x=this._alt.x,this._normal.y=this._alt.y,this._alt.clearAllMarkers(),this._alt.clear(),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}))}activateAltBuffer(g){this._activeBuffer!==this._alt&&(this._alt.fillViewportRows(g),this._alt.x=this._normal.x,this._alt.y=this._normal.y,this._activeBuffer=this._alt,this._onBufferActivate.fire({activeBuffer:this._alt,inactiveBuffer:this._normal}))}resize(g,u){this._normal.resize(g,u),this._alt.resize(g,u),this.setupTabStops(g)}setupTabStops(g){this._normal.setupTabStops(g),this._alt.setupTabStops(g)}}r.BufferSet=d},511:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.CellData=void 0;const c=a(482),_=a(643),n=a(3734);class d extends n.AttributeData{constructor(){super(...arguments),this.content=0,this.fg=0,this.bg=0,this.extended=new n.ExtendedAttrs,this.combinedData=""}static fromCharData(g){const u=new d;return u.setFromCharData(g),u}isCombined(){return 2097152&this.content}getWidth(){return this.content>>22}getChars(){return 2097152&this.content?this.combinedData:2097151&this.content?(0,c.stringFromCodePoint)(2097151&this.content):""}getCode(){return this.isCombined()?this.combinedData.charCodeAt(this.combinedData.length-1):2097151&this.content}setFromCharData(g){this.fg=g[_.CHAR_DATA_ATTR_INDEX],this.bg=0;let u=!1;if(g[_.CHAR_DATA_CHAR_INDEX].length>2)u=!0;else if(g[_.CHAR_DATA_CHAR_INDEX].length===2){const e=g[_.CHAR_DATA_CHAR_INDEX].charCodeAt(0);if(55296<=e&&e<=56319){const s=g[_.CHAR_DATA_CHAR_INDEX].charCodeAt(1);56320<=s&&s<=57343?this.content=1024*(e-55296)+s-56320+65536|g[_.CHAR_DATA_WIDTH_INDEX]<<22:u=!0}else u=!0}else this.content=g[_.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|g[_.CHAR_DATA_WIDTH_INDEX]<<22;u&&(this.combinedData=g[_.CHAR_DATA_CHAR_INDEX],this.content=2097152|g[_.CHAR_DATA_WIDTH_INDEX]<<22)}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}r.CellData=d},643:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.WHITESPACE_CELL_CODE=r.WHITESPACE_CELL_WIDTH=r.WHITESPACE_CELL_CHAR=r.NULL_CELL_CODE=r.NULL_CELL_WIDTH=r.NULL_CELL_CHAR=r.CHAR_DATA_CODE_INDEX=r.CHAR_DATA_WIDTH_INDEX=r.CHAR_DATA_CHAR_INDEX=r.CHAR_DATA_ATTR_INDEX=r.DEFAULT_EXT=r.DEFAULT_ATTR=r.DEFAULT_COLOR=void 0,r.DEFAULT_COLOR=0,r.DEFAULT_ATTR=256|r.DEFAULT_COLOR<<9,r.DEFAULT_EXT=0,r.CHAR_DATA_ATTR_INDEX=0,r.CHAR_DATA_CHAR_INDEX=1,r.CHAR_DATA_WIDTH_INDEX=2,r.CHAR_DATA_CODE_INDEX=3,r.NULL_CELL_CHAR="",r.NULL_CELL_WIDTH=1,r.NULL_CELL_CODE=0,r.WHITESPACE_CELL_CHAR=" ",r.WHITESPACE_CELL_WIDTH=1,r.WHITESPACE_CELL_CODE=32},4863:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.Marker=void 0;const c=a(8460),_=a(844);class n{get id(){return this._id}constructor(f){this.line=f,this.isDisposed=!1,this._disposables=[],this._id=n._nextId++,this._onDispose=this.register(new c.EventEmitter),this.onDispose=this._onDispose.event}dispose(){this.isDisposed||(this.isDisposed=!0,this.line=-1,this._onDispose.fire(),(0,_.disposeArray)(this._disposables),this._disposables.length=0)}register(f){return this._disposables.push(f),f}}r.Marker=n,n._nextId=1},7116:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.DEFAULT_CHARSET=r.CHARSETS=void 0,r.CHARSETS={},r.DEFAULT_CHARSET=r.CHARSETS.B,r.CHARSETS[0]={"`":"◆",a:"▒",b:"␉",c:"␌",d:"␍",e:"␊",f:"°",g:"±",h:"␤",i:"␋",j:"┘",k:"┐",l:"┌",m:"└",n:"┼",o:"⎺",p:"⎻",q:"─",r:"⎼",s:"⎽",t:"├",u:"┤",v:"┴",w:"┬",x:"│",y:"≤",z:"≥","{":"π","|":"≠","}":"£","~":"·"},r.CHARSETS.A={"#":"£"},r.CHARSETS.B=void 0,r.CHARSETS[4]={"#":"£","@":"¾","[":"ij","\\":"½","]":"|","{":"¨","|":"f","}":"¼","~":"´"},r.CHARSETS.C=r.CHARSETS[5]={"[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},r.CHARSETS.R={"#":"£","@":"à","[":"°","\\":"ç","]":"§","{":"é","|":"ù","}":"è","~":"¨"},r.CHARSETS.Q={"@":"à","[":"â","\\":"ç","]":"ê","^":"î","`":"ô","{":"é","|":"ù","}":"è","~":"û"},r.CHARSETS.K={"@":"§","[":"Ä","\\":"Ö","]":"Ü","{":"ä","|":"ö","}":"ü","~":"ß"},r.CHARSETS.Y={"#":"£","@":"§","[":"°","\\":"ç","]":"é","`":"ù","{":"à","|":"ò","}":"è","~":"ì"},r.CHARSETS.E=r.CHARSETS[6]={"@":"Ä","[":"Æ","\\":"Ø","]":"Å","^":"Ü","`":"ä","{":"æ","|":"ø","}":"å","~":"ü"},r.CHARSETS.Z={"#":"£","@":"§","[":"¡","\\":"Ñ","]":"¿","{":"°","|":"ñ","}":"ç"},r.CHARSETS.H=r.CHARSETS[7]={"@":"É","[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},r.CHARSETS["="]={"#":"ù","@":"à","[":"é","\\":"ç","]":"ê","^":"î",_:"è","`":"ô","{":"ä","|":"ö","}":"ü","~":"û"}},2584:(I,r)=>{var a,c,_;Object.defineProperty(r,"__esModule",{value:!0}),r.C1_ESCAPED=r.C1=r.C0=void 0,function(n){n.NUL="\0",n.SOH="",n.STX="",n.ETX="",n.EOT="",n.ENQ="",n.ACK="",n.BEL="\x07",n.BS="\b",n.HT="	",n.LF=`
`,n.VT="\v",n.FF="\f",n.CR="\r",n.SO="",n.SI="",n.DLE="",n.DC1="",n.DC2="",n.DC3="",n.DC4="",n.NAK="",n.SYN="",n.ETB="",n.CAN="",n.EM="",n.SUB="",n.ESC="\x1B",n.FS="",n.GS="",n.RS="",n.US="",n.SP=" ",n.DEL=""}(a||(r.C0=a={})),function(n){n.PAD="",n.HOP="",n.BPH="",n.NBH="",n.IND="",n.NEL="",n.SSA="",n.ESA="",n.HTS="",n.HTJ="",n.VTS="",n.PLD="",n.PLU="",n.RI="",n.SS2="",n.SS3="",n.DCS="",n.PU1="",n.PU2="",n.STS="",n.CCH="",n.MW="",n.SPA="",n.EPA="",n.SOS="",n.SGCI="",n.SCI="",n.CSI="",n.ST="",n.OSC="",n.PM="",n.APC=""}(c||(r.C1=c={})),function(n){n.ST=`${a.ESC}\\`}(_||(r.C1_ESCAPED=_={}))},7399:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.evaluateKeyboardEvent=void 0;const c=a(2584),_={48:["0",")"],49:["1","!"],50:["2","@"],51:["3","#"],52:["4","$"],53:["5","%"],54:["6","^"],55:["7","&"],56:["8","*"],57:["9","("],186:[";",":"],187:["=","+"],188:[",","<"],189:["-","_"],190:[".",">"],191:["/","?"],192:["`","~"],219:["[","{"],220:["\\","|"],221:["]","}"],222:["'",'"']};r.evaluateKeyboardEvent=function(n,d,f,g){const u={type:0,cancel:!1,key:void 0},e=(n.shiftKey?1:0)|(n.altKey?2:0)|(n.ctrlKey?4:0)|(n.metaKey?8:0);switch(n.keyCode){case 0:n.key==="UIKeyInputUpArrow"?u.key=d?c.C0.ESC+"OA":c.C0.ESC+"[A":n.key==="UIKeyInputLeftArrow"?u.key=d?c.C0.ESC+"OD":c.C0.ESC+"[D":n.key==="UIKeyInputRightArrow"?u.key=d?c.C0.ESC+"OC":c.C0.ESC+"[C":n.key==="UIKeyInputDownArrow"&&(u.key=d?c.C0.ESC+"OB":c.C0.ESC+"[B");break;case 8:if(n.altKey){u.key=c.C0.ESC+c.C0.DEL;break}u.key=c.C0.DEL;break;case 9:if(n.shiftKey){u.key=c.C0.ESC+"[Z";break}u.key=c.C0.HT,u.cancel=!0;break;case 13:u.key=n.altKey?c.C0.ESC+c.C0.CR:c.C0.CR,u.cancel=!0;break;case 27:u.key=c.C0.ESC,n.altKey&&(u.key=c.C0.ESC+c.C0.ESC),u.cancel=!0;break;case 37:if(n.metaKey)break;e?(u.key=c.C0.ESC+"[1;"+(e+1)+"D",u.key===c.C0.ESC+"[1;3D"&&(u.key=c.C0.ESC+(f?"b":"[1;5D"))):u.key=d?c.C0.ESC+"OD":c.C0.ESC+"[D";break;case 39:if(n.metaKey)break;e?(u.key=c.C0.ESC+"[1;"+(e+1)+"C",u.key===c.C0.ESC+"[1;3C"&&(u.key=c.C0.ESC+(f?"f":"[1;5C"))):u.key=d?c.C0.ESC+"OC":c.C0.ESC+"[C";break;case 38:if(n.metaKey)break;e?(u.key=c.C0.ESC+"[1;"+(e+1)+"A",f||u.key!==c.C0.ESC+"[1;3A"||(u.key=c.C0.ESC+"[1;5A")):u.key=d?c.C0.ESC+"OA":c.C0.ESC+"[A";break;case 40:if(n.metaKey)break;e?(u.key=c.C0.ESC+"[1;"+(e+1)+"B",f||u.key!==c.C0.ESC+"[1;3B"||(u.key=c.C0.ESC+"[1;5B")):u.key=d?c.C0.ESC+"OB":c.C0.ESC+"[B";break;case 45:n.shiftKey||n.ctrlKey||(u.key=c.C0.ESC+"[2~");break;case 46:u.key=e?c.C0.ESC+"[3;"+(e+1)+"~":c.C0.ESC+"[3~";break;case 36:u.key=e?c.C0.ESC+"[1;"+(e+1)+"H":d?c.C0.ESC+"OH":c.C0.ESC+"[H";break;case 35:u.key=e?c.C0.ESC+"[1;"+(e+1)+"F":d?c.C0.ESC+"OF":c.C0.ESC+"[F";break;case 33:n.shiftKey?u.type=2:n.ctrlKey?u.key=c.C0.ESC+"[5;"+(e+1)+"~":u.key=c.C0.ESC+"[5~";break;case 34:n.shiftKey?u.type=3:n.ctrlKey?u.key=c.C0.ESC+"[6;"+(e+1)+"~":u.key=c.C0.ESC+"[6~";break;case 112:u.key=e?c.C0.ESC+"[1;"+(e+1)+"P":c.C0.ESC+"OP";break;case 113:u.key=e?c.C0.ESC+"[1;"+(e+1)+"Q":c.C0.ESC+"OQ";break;case 114:u.key=e?c.C0.ESC+"[1;"+(e+1)+"R":c.C0.ESC+"OR";break;case 115:u.key=e?c.C0.ESC+"[1;"+(e+1)+"S":c.C0.ESC+"OS";break;case 116:u.key=e?c.C0.ESC+"[15;"+(e+1)+"~":c.C0.ESC+"[15~";break;case 117:u.key=e?c.C0.ESC+"[17;"+(e+1)+"~":c.C0.ESC+"[17~";break;case 118:u.key=e?c.C0.ESC+"[18;"+(e+1)+"~":c.C0.ESC+"[18~";break;case 119:u.key=e?c.C0.ESC+"[19;"+(e+1)+"~":c.C0.ESC+"[19~";break;case 120:u.key=e?c.C0.ESC+"[20;"+(e+1)+"~":c.C0.ESC+"[20~";break;case 121:u.key=e?c.C0.ESC+"[21;"+(e+1)+"~":c.C0.ESC+"[21~";break;case 122:u.key=e?c.C0.ESC+"[23;"+(e+1)+"~":c.C0.ESC+"[23~";break;case 123:u.key=e?c.C0.ESC+"[24;"+(e+1)+"~":c.C0.ESC+"[24~";break;default:if(!n.ctrlKey||n.shiftKey||n.altKey||n.metaKey)if(f&&!g||!n.altKey||n.metaKey)!f||n.altKey||n.ctrlKey||n.shiftKey||!n.metaKey?n.key&&!n.ctrlKey&&!n.altKey&&!n.metaKey&&n.keyCode>=48&&n.key.length===1?u.key=n.key:n.key&&n.ctrlKey&&(n.key==="_"&&(u.key=c.C0.US),n.key==="@"&&(u.key=c.C0.NUL)):n.keyCode===65&&(u.type=1);else{const s=_[n.keyCode],t=s==null?void 0:s[n.shiftKey?1:0];if(t)u.key=c.C0.ESC+t;else if(n.keyCode>=65&&n.keyCode<=90){const i=n.ctrlKey?n.keyCode-64:n.keyCode+32;let o=String.fromCharCode(i);n.shiftKey&&(o=o.toUpperCase()),u.key=c.C0.ESC+o}else if(n.keyCode===32)u.key=c.C0.ESC+(n.ctrlKey?c.C0.NUL:" ");else if(n.key==="Dead"&&n.code.startsWith("Key")){let i=n.code.slice(3,4);n.shiftKey||(i=i.toLowerCase()),u.key=c.C0.ESC+i,u.cancel=!0}}else n.keyCode>=65&&n.keyCode<=90?u.key=String.fromCharCode(n.keyCode-64):n.keyCode===32?u.key=c.C0.NUL:n.keyCode>=51&&n.keyCode<=55?u.key=String.fromCharCode(n.keyCode-51+27):n.keyCode===56?u.key=c.C0.DEL:n.keyCode===219?u.key=c.C0.ESC:n.keyCode===220?u.key=c.C0.FS:n.keyCode===221&&(u.key=c.C0.GS)}return u}},482:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.Utf8ToUtf32=r.StringToUtf32=r.utf32ToString=r.stringFromCodePoint=void 0,r.stringFromCodePoint=function(a){return a>65535?(a-=65536,String.fromCharCode(55296+(a>>10))+String.fromCharCode(a%1024+56320)):String.fromCharCode(a)},r.utf32ToString=function(a,c=0,_=a.length){let n="";for(let d=c;d<_;++d){let f=a[d];f>65535?(f-=65536,n+=String.fromCharCode(55296+(f>>10))+String.fromCharCode(f%1024+56320)):n+=String.fromCharCode(f)}return n},r.StringToUtf32=class{constructor(){this._interim=0}clear(){this._interim=0}decode(a,c){const _=a.length;if(!_)return 0;let n=0,d=0;if(this._interim){const f=a.charCodeAt(d++);56320<=f&&f<=57343?c[n++]=1024*(this._interim-55296)+f-56320+65536:(c[n++]=this._interim,c[n++]=f),this._interim=0}for(let f=d;f<_;++f){const g=a.charCodeAt(f);if(55296<=g&&g<=56319){if(++f>=_)return this._interim=g,n;const u=a.charCodeAt(f);56320<=u&&u<=57343?c[n++]=1024*(g-55296)+u-56320+65536:(c[n++]=g,c[n++]=u)}else g!==65279&&(c[n++]=g)}return n}},r.Utf8ToUtf32=class{constructor(){this.interim=new Uint8Array(3)}clear(){this.interim.fill(0)}decode(a,c){const _=a.length;if(!_)return 0;let n,d,f,g,u=0,e=0,s=0;if(this.interim[0]){let o=!1,l=this.interim[0];l&=(224&l)==192?31:(240&l)==224?15:7;let v,m=0;for(;(v=63&this.interim[++m])&&m<4;)l<<=6,l|=v;const h=(224&this.interim[0])==192?2:(240&this.interim[0])==224?3:4,p=h-m;for(;s<p;){if(s>=_)return 0;if(v=a[s++],(192&v)!=128){s--,o=!0;break}this.interim[m++]=v,l<<=6,l|=63&v}o||(h===2?l<128?s--:c[u++]=l:h===3?l<2048||l>=55296&&l<=57343||l===65279||(c[u++]=l):l<65536||l>1114111||(c[u++]=l)),this.interim.fill(0)}const t=_-4;let i=s;for(;i<_;){for(;!(!(i<t)||128&(n=a[i])||128&(d=a[i+1])||128&(f=a[i+2])||128&(g=a[i+3]));)c[u++]=n,c[u++]=d,c[u++]=f,c[u++]=g,i+=4;if(n=a[i++],n<128)c[u++]=n;else if((224&n)==192){if(i>=_)return this.interim[0]=n,u;if(d=a[i++],(192&d)!=128){i--;continue}if(e=(31&n)<<6|63&d,e<128){i--;continue}c[u++]=e}else if((240&n)==224){if(i>=_)return this.interim[0]=n,u;if(d=a[i++],(192&d)!=128){i--;continue}if(i>=_)return this.interim[0]=n,this.interim[1]=d,u;if(f=a[i++],(192&f)!=128){i--;continue}if(e=(15&n)<<12|(63&d)<<6|63&f,e<2048||e>=55296&&e<=57343||e===65279)continue;c[u++]=e}else if((248&n)==240){if(i>=_)return this.interim[0]=n,u;if(d=a[i++],(192&d)!=128){i--;continue}if(i>=_)return this.interim[0]=n,this.interim[1]=d,u;if(f=a[i++],(192&f)!=128){i--;continue}if(i>=_)return this.interim[0]=n,this.interim[1]=d,this.interim[2]=f,u;if(g=a[i++],(192&g)!=128){i--;continue}if(e=(7&n)<<18|(63&d)<<12|(63&f)<<6|63&g,e<65536||e>1114111)continue;c[u++]=e}}return u}}},225:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.UnicodeV6=void 0;const a=[[768,879],[1155,1158],[1160,1161],[1425,1469],[1471,1471],[1473,1474],[1476,1477],[1479,1479],[1536,1539],[1552,1557],[1611,1630],[1648,1648],[1750,1764],[1767,1768],[1770,1773],[1807,1807],[1809,1809],[1840,1866],[1958,1968],[2027,2035],[2305,2306],[2364,2364],[2369,2376],[2381,2381],[2385,2388],[2402,2403],[2433,2433],[2492,2492],[2497,2500],[2509,2509],[2530,2531],[2561,2562],[2620,2620],[2625,2626],[2631,2632],[2635,2637],[2672,2673],[2689,2690],[2748,2748],[2753,2757],[2759,2760],[2765,2765],[2786,2787],[2817,2817],[2876,2876],[2879,2879],[2881,2883],[2893,2893],[2902,2902],[2946,2946],[3008,3008],[3021,3021],[3134,3136],[3142,3144],[3146,3149],[3157,3158],[3260,3260],[3263,3263],[3270,3270],[3276,3277],[3298,3299],[3393,3395],[3405,3405],[3530,3530],[3538,3540],[3542,3542],[3633,3633],[3636,3642],[3655,3662],[3761,3761],[3764,3769],[3771,3772],[3784,3789],[3864,3865],[3893,3893],[3895,3895],[3897,3897],[3953,3966],[3968,3972],[3974,3975],[3984,3991],[3993,4028],[4038,4038],[4141,4144],[4146,4146],[4150,4151],[4153,4153],[4184,4185],[4448,4607],[4959,4959],[5906,5908],[5938,5940],[5970,5971],[6002,6003],[6068,6069],[6071,6077],[6086,6086],[6089,6099],[6109,6109],[6155,6157],[6313,6313],[6432,6434],[6439,6440],[6450,6450],[6457,6459],[6679,6680],[6912,6915],[6964,6964],[6966,6970],[6972,6972],[6978,6978],[7019,7027],[7616,7626],[7678,7679],[8203,8207],[8234,8238],[8288,8291],[8298,8303],[8400,8431],[12330,12335],[12441,12442],[43014,43014],[43019,43019],[43045,43046],[64286,64286],[65024,65039],[65056,65059],[65279,65279],[65529,65531]],c=[[68097,68099],[68101,68102],[68108,68111],[68152,68154],[68159,68159],[119143,119145],[119155,119170],[119173,119179],[119210,119213],[119362,119364],[917505,917505],[917536,917631],[917760,917999]];let _;r.UnicodeV6=class{constructor(){if(this.version="6",!_){_=new Uint8Array(65536),_.fill(1),_[0]=0,_.fill(0,1,32),_.fill(0,127,160),_.fill(2,4352,4448),_[9001]=2,_[9002]=2,_.fill(2,11904,42192),_[12351]=1,_.fill(2,44032,55204),_.fill(2,63744,64256),_.fill(2,65040,65050),_.fill(2,65072,65136),_.fill(2,65280,65377),_.fill(2,65504,65511);for(let n=0;n<a.length;++n)_.fill(0,a[n][0],a[n][1]+1)}}wcwidth(n){return n<32?0:n<127?1:n<65536?_[n]:function(d,f){let g,u=0,e=f.length-1;if(d<f[0][0]||d>f[e][1])return!1;for(;e>=u;)if(g=u+e>>1,d>f[g][1])u=g+1;else{if(!(d<f[g][0]))return!0;e=g-1}return!1}(n,c)?0:n>=131072&&n<=196605||n>=196608&&n<=262141?2:1}}},5981:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.WriteBuffer=void 0;const c=a(8460),_=a(844);class n extends _.Disposable{constructor(f){super(),this._action=f,this._writeBuffer=[],this._callbacks=[],this._pendingData=0,this._bufferOffset=0,this._isSyncWriting=!1,this._syncCalls=0,this._didUserInput=!1,this._onWriteParsed=this.register(new c.EventEmitter),this.onWriteParsed=this._onWriteParsed.event}handleUserInput(){this._didUserInput=!0}writeSync(f,g){if(g!==void 0&&this._syncCalls>g)return void(this._syncCalls=0);if(this._pendingData+=f.length,this._writeBuffer.push(f),this._callbacks.push(void 0),this._syncCalls++,this._isSyncWriting)return;let u;for(this._isSyncWriting=!0;u=this._writeBuffer.shift();){this._action(u);const e=this._callbacks.shift();e&&e()}this._pendingData=0,this._bufferOffset=2147483647,this._isSyncWriting=!1,this._syncCalls=0}write(f,g){if(this._pendingData>5e7)throw new Error("write data discarded, use flow control to avoid losing data");if(!this._writeBuffer.length){if(this._bufferOffset=0,this._didUserInput)return this._didUserInput=!1,this._pendingData+=f.length,this._writeBuffer.push(f),this._callbacks.push(g),void this._innerWrite();setTimeout(()=>this._innerWrite())}this._pendingData+=f.length,this._writeBuffer.push(f),this._callbacks.push(g)}_innerWrite(f=0,g=!0){const u=f||Date.now();for(;this._writeBuffer.length>this._bufferOffset;){const e=this._writeBuffer[this._bufferOffset],s=this._action(e,g);if(s){const i=o=>Date.now()-u>=12?setTimeout(()=>this._innerWrite(0,o)):this._innerWrite(u,o);return void s.catch(o=>(queueMicrotask(()=>{throw o}),Promise.resolve(!1))).then(i)}const t=this._callbacks[this._bufferOffset];if(t&&t(),this._bufferOffset++,this._pendingData-=e.length,Date.now()-u>=12)break}this._writeBuffer.length>this._bufferOffset?(this._bufferOffset>50&&(this._writeBuffer=this._writeBuffer.slice(this._bufferOffset),this._callbacks=this._callbacks.slice(this._bufferOffset),this._bufferOffset=0),setTimeout(()=>this._innerWrite())):(this._writeBuffer.length=0,this._callbacks.length=0,this._pendingData=0,this._bufferOffset=0),this._onWriteParsed.fire()}}r.WriteBuffer=n},5941:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.toRgbString=r.parseColor=void 0;const a=/^([\da-f])\/([\da-f])\/([\da-f])$|^([\da-f]{2})\/([\da-f]{2})\/([\da-f]{2})$|^([\da-f]{3})\/([\da-f]{3})\/([\da-f]{3})$|^([\da-f]{4})\/([\da-f]{4})\/([\da-f]{4})$/,c=/^[\da-f]+$/;function _(n,d){const f=n.toString(16),g=f.length<2?"0"+f:f;switch(d){case 4:return f[0];case 8:return g;case 12:return(g+g).slice(0,3);default:return g+g}}r.parseColor=function(n){if(!n)return;let d=n.toLowerCase();if(d.indexOf("rgb:")===0){d=d.slice(4);const f=a.exec(d);if(f){const g=f[1]?15:f[4]?255:f[7]?4095:65535;return[Math.round(parseInt(f[1]||f[4]||f[7]||f[10],16)/g*255),Math.round(parseInt(f[2]||f[5]||f[8]||f[11],16)/g*255),Math.round(parseInt(f[3]||f[6]||f[9]||f[12],16)/g*255)]}}else if(d.indexOf("#")===0&&(d=d.slice(1),c.exec(d)&&[3,6,9,12].includes(d.length))){const f=d.length/3,g=[0,0,0];for(let u=0;u<3;++u){const e=parseInt(d.slice(f*u,f*u+f),16);g[u]=f===1?e<<4:f===2?e:f===3?e>>4:e>>8}return g}},r.toRgbString=function(n,d=16){const[f,g,u]=n;return`rgb:${_(f,d)}/${_(g,d)}/${_(u,d)}`}},5770:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.PAYLOAD_LIMIT=void 0,r.PAYLOAD_LIMIT=1e7},6351:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.DcsHandler=r.DcsParser=void 0;const c=a(482),_=a(8742),n=a(5770),d=[];r.DcsParser=class{constructor(){this._handlers=Object.create(null),this._active=d,this._ident=0,this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=d}registerHandler(g,u){this._handlers[g]===void 0&&(this._handlers[g]=[]);const e=this._handlers[g];return e.push(u),{dispose:()=>{const s=e.indexOf(u);s!==-1&&e.splice(s,1)}}}clearHandler(g){this._handlers[g]&&delete this._handlers[g]}setHandlerFallback(g){this._handlerFb=g}reset(){if(this._active.length)for(let g=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;g>=0;--g)this._active[g].unhook(!1);this._stack.paused=!1,this._active=d,this._ident=0}hook(g,u){if(this.reset(),this._ident=g,this._active=this._handlers[g]||d,this._active.length)for(let e=this._active.length-1;e>=0;e--)this._active[e].hook(u);else this._handlerFb(this._ident,"HOOK",u)}put(g,u,e){if(this._active.length)for(let s=this._active.length-1;s>=0;s--)this._active[s].put(g,u,e);else this._handlerFb(this._ident,"PUT",(0,c.utf32ToString)(g,u,e))}unhook(g,u=!0){if(this._active.length){let e=!1,s=this._active.length-1,t=!1;if(this._stack.paused&&(s=this._stack.loopPosition-1,e=u,t=this._stack.fallThrough,this._stack.paused=!1),!t&&e===!1){for(;s>=0&&(e=this._active[s].unhook(g),e!==!0);s--)if(e instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=s,this._stack.fallThrough=!1,e;s--}for(;s>=0;s--)if(e=this._active[s].unhook(!1),e instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=s,this._stack.fallThrough=!0,e}else this._handlerFb(this._ident,"UNHOOK",g);this._active=d,this._ident=0}};const f=new _.Params;f.addParam(0),r.DcsHandler=class{constructor(g){this._handler=g,this._data="",this._params=f,this._hitLimit=!1}hook(g){this._params=g.length>1||g.params[0]?g.clone():f,this._data="",this._hitLimit=!1}put(g,u,e){this._hitLimit||(this._data+=(0,c.utf32ToString)(g,u,e),this._data.length>n.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}unhook(g){let u=!1;if(this._hitLimit)u=!1;else if(g&&(u=this._handler(this._data,this._params),u instanceof Promise))return u.then(e=>(this._params=f,this._data="",this._hitLimit=!1,e));return this._params=f,this._data="",this._hitLimit=!1,u}}},2015:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.EscapeSequenceParser=r.VT500_TRANSITION_TABLE=r.TransitionTable=void 0;const c=a(844),_=a(8742),n=a(6242),d=a(6351);class f{constructor(s){this.table=new Uint8Array(s)}setDefault(s,t){this.table.fill(s<<4|t)}add(s,t,i,o){this.table[t<<8|s]=i<<4|o}addMany(s,t,i,o){for(let l=0;l<s.length;l++)this.table[t<<8|s[l]]=i<<4|o}}r.TransitionTable=f;const g=160;r.VT500_TRANSITION_TABLE=function(){const e=new f(4095),s=Array.apply(null,Array(256)).map((m,h)=>h),t=(m,h)=>s.slice(m,h),i=t(32,127),o=t(0,24);o.push(25),o.push.apply(o,t(28,32));const l=t(0,14);let v;for(v in e.setDefault(1,0),e.addMany(i,0,2,0),l)e.addMany([24,26,153,154],v,3,0),e.addMany(t(128,144),v,3,0),e.addMany(t(144,152),v,3,0),e.add(156,v,0,0),e.add(27,v,11,1),e.add(157,v,4,8),e.addMany([152,158,159],v,0,7),e.add(155,v,11,3),e.add(144,v,11,9);return e.addMany(o,0,3,0),e.addMany(o,1,3,1),e.add(127,1,0,1),e.addMany(o,8,0,8),e.addMany(o,3,3,3),e.add(127,3,0,3),e.addMany(o,4,3,4),e.add(127,4,0,4),e.addMany(o,6,3,6),e.addMany(o,5,3,5),e.add(127,5,0,5),e.addMany(o,2,3,2),e.add(127,2,0,2),e.add(93,1,4,8),e.addMany(i,8,5,8),e.add(127,8,5,8),e.addMany([156,27,24,26,7],8,6,0),e.addMany(t(28,32),8,0,8),e.addMany([88,94,95],1,0,7),e.addMany(i,7,0,7),e.addMany(o,7,0,7),e.add(156,7,0,0),e.add(127,7,0,7),e.add(91,1,11,3),e.addMany(t(64,127),3,7,0),e.addMany(t(48,60),3,8,4),e.addMany([60,61,62,63],3,9,4),e.addMany(t(48,60),4,8,4),e.addMany(t(64,127),4,7,0),e.addMany([60,61,62,63],4,0,6),e.addMany(t(32,64),6,0,6),e.add(127,6,0,6),e.addMany(t(64,127),6,0,0),e.addMany(t(32,48),3,9,5),e.addMany(t(32,48),5,9,5),e.addMany(t(48,64),5,0,6),e.addMany(t(64,127),5,7,0),e.addMany(t(32,48),4,9,5),e.addMany(t(32,48),1,9,2),e.addMany(t(32,48),2,9,2),e.addMany(t(48,127),2,10,0),e.addMany(t(48,80),1,10,0),e.addMany(t(81,88),1,10,0),e.addMany([89,90,92],1,10,0),e.addMany(t(96,127),1,10,0),e.add(80,1,11,9),e.addMany(o,9,0,9),e.add(127,9,0,9),e.addMany(t(28,32),9,0,9),e.addMany(t(32,48),9,9,12),e.addMany(t(48,60),9,8,10),e.addMany([60,61,62,63],9,9,10),e.addMany(o,11,0,11),e.addMany(t(32,128),11,0,11),e.addMany(t(28,32),11,0,11),e.addMany(o,10,0,10),e.add(127,10,0,10),e.addMany(t(28,32),10,0,10),e.addMany(t(48,60),10,8,10),e.addMany([60,61,62,63],10,0,11),e.addMany(t(32,48),10,9,12),e.addMany(o,12,0,12),e.add(127,12,0,12),e.addMany(t(28,32),12,0,12),e.addMany(t(32,48),12,9,12),e.addMany(t(48,64),12,0,11),e.addMany(t(64,127),12,12,13),e.addMany(t(64,127),10,12,13),e.addMany(t(64,127),9,12,13),e.addMany(o,13,13,13),e.addMany(i,13,13,13),e.add(127,13,0,13),e.addMany([27,156,24,26],13,14,0),e.add(g,0,2,0),e.add(g,8,5,8),e.add(g,6,0,6),e.add(g,11,0,11),e.add(g,13,13,13),e}();class u extends c.Disposable{constructor(s=r.VT500_TRANSITION_TABLE){super(),this._transitions=s,this._parseStack={state:0,handlers:[],handlerPos:0,transition:0,chunkPos:0},this.initialState=0,this.currentState=this.initialState,this._params=new _.Params,this._params.addParam(0),this._collect=0,this.precedingCodepoint=0,this._printHandlerFb=(t,i,o)=>{},this._executeHandlerFb=t=>{},this._csiHandlerFb=(t,i)=>{},this._escHandlerFb=t=>{},this._errorHandlerFb=t=>t,this._printHandler=this._printHandlerFb,this._executeHandlers=Object.create(null),this._csiHandlers=Object.create(null),this._escHandlers=Object.create(null),this.register((0,c.toDisposable)(()=>{this._csiHandlers=Object.create(null),this._executeHandlers=Object.create(null),this._escHandlers=Object.create(null)})),this._oscParser=this.register(new n.OscParser),this._dcsParser=this.register(new d.DcsParser),this._errorHandler=this._errorHandlerFb,this.registerEscHandler({final:"\\"},()=>!0)}_identifier(s,t=[64,126]){let i=0;if(s.prefix){if(s.prefix.length>1)throw new Error("only one byte as prefix supported");if(i=s.prefix.charCodeAt(0),i&&60>i||i>63)throw new Error("prefix must be in range 0x3c .. 0x3f")}if(s.intermediates){if(s.intermediates.length>2)throw new Error("only two bytes as intermediates are supported");for(let l=0;l<s.intermediates.length;++l){const v=s.intermediates.charCodeAt(l);if(32>v||v>47)throw new Error("intermediate must be in range 0x20 .. 0x2f");i<<=8,i|=v}}if(s.final.length!==1)throw new Error("final must be a single byte");const o=s.final.charCodeAt(0);if(t[0]>o||o>t[1])throw new Error(`final must be in range ${t[0]} .. ${t[1]}`);return i<<=8,i|=o,i}identToString(s){const t=[];for(;s;)t.push(String.fromCharCode(255&s)),s>>=8;return t.reverse().join("")}setPrintHandler(s){this._printHandler=s}clearPrintHandler(){this._printHandler=this._printHandlerFb}registerEscHandler(s,t){const i=this._identifier(s,[48,126]);this._escHandlers[i]===void 0&&(this._escHandlers[i]=[]);const o=this._escHandlers[i];return o.push(t),{dispose:()=>{const l=o.indexOf(t);l!==-1&&o.splice(l,1)}}}clearEscHandler(s){this._escHandlers[this._identifier(s,[48,126])]&&delete this._escHandlers[this._identifier(s,[48,126])]}setEscHandlerFallback(s){this._escHandlerFb=s}setExecuteHandler(s,t){this._executeHandlers[s.charCodeAt(0)]=t}clearExecuteHandler(s){this._executeHandlers[s.charCodeAt(0)]&&delete this._executeHandlers[s.charCodeAt(0)]}setExecuteHandlerFallback(s){this._executeHandlerFb=s}registerCsiHandler(s,t){const i=this._identifier(s);this._csiHandlers[i]===void 0&&(this._csiHandlers[i]=[]);const o=this._csiHandlers[i];return o.push(t),{dispose:()=>{const l=o.indexOf(t);l!==-1&&o.splice(l,1)}}}clearCsiHandler(s){this._csiHandlers[this._identifier(s)]&&delete this._csiHandlers[this._identifier(s)]}setCsiHandlerFallback(s){this._csiHandlerFb=s}registerDcsHandler(s,t){return this._dcsParser.registerHandler(this._identifier(s),t)}clearDcsHandler(s){this._dcsParser.clearHandler(this._identifier(s))}setDcsHandlerFallback(s){this._dcsParser.setHandlerFallback(s)}registerOscHandler(s,t){return this._oscParser.registerHandler(s,t)}clearOscHandler(s){this._oscParser.clearHandler(s)}setOscHandlerFallback(s){this._oscParser.setHandlerFallback(s)}setErrorHandler(s){this._errorHandler=s}clearErrorHandler(){this._errorHandler=this._errorHandlerFb}reset(){this.currentState=this.initialState,this._oscParser.reset(),this._dcsParser.reset(),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingCodepoint=0,this._parseStack.state!==0&&(this._parseStack.state=2,this._parseStack.handlers=[])}_preserveStack(s,t,i,o,l){this._parseStack.state=s,this._parseStack.handlers=t,this._parseStack.handlerPos=i,this._parseStack.transition=o,this._parseStack.chunkPos=l}parse(s,t,i){let o,l=0,v=0,m=0;if(this._parseStack.state)if(this._parseStack.state===2)this._parseStack.state=0,m=this._parseStack.chunkPos+1;else{if(i===void 0||this._parseStack.state===1)throw this._parseStack.state=1,new Error("improper continuation due to previous async handler, giving up parsing");const h=this._parseStack.handlers;let p=this._parseStack.handlerPos-1;switch(this._parseStack.state){case 3:if(i===!1&&p>-1){for(;p>=0&&(o=h[p](this._params),o!==!0);p--)if(o instanceof Promise)return this._parseStack.handlerPos=p,o}this._parseStack.handlers=[];break;case 4:if(i===!1&&p>-1){for(;p>=0&&(o=h[p](),o!==!0);p--)if(o instanceof Promise)return this._parseStack.handlerPos=p,o}this._parseStack.handlers=[];break;case 6:if(l=s[this._parseStack.chunkPos],o=this._dcsParser.unhook(l!==24&&l!==26,i),o)return o;l===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0;break;case 5:if(l=s[this._parseStack.chunkPos],o=this._oscParser.end(l!==24&&l!==26,i),o)return o;l===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0}this._parseStack.state=0,m=this._parseStack.chunkPos+1,this.precedingCodepoint=0,this.currentState=15&this._parseStack.transition}for(let h=m;h<t;++h){switch(l=s[h],v=this._transitions.table[this.currentState<<8|(l<160?l:g)],v>>4){case 2:for(let k=h+1;;++k){if(k>=t||(l=s[k])<32||l>126&&l<g){this._printHandler(s,h,k),h=k-1;break}if(++k>=t||(l=s[k])<32||l>126&&l<g){this._printHandler(s,h,k),h=k-1;break}if(++k>=t||(l=s[k])<32||l>126&&l<g){this._printHandler(s,h,k),h=k-1;break}if(++k>=t||(l=s[k])<32||l>126&&l<g){this._printHandler(s,h,k),h=k-1;break}}break;case 3:this._executeHandlers[l]?this._executeHandlers[l]():this._executeHandlerFb(l),this.precedingCodepoint=0;break;case 0:break;case 1:if(this._errorHandler({position:h,code:l,currentState:this.currentState,collect:this._collect,params:this._params,abort:!1}).abort)return;break;case 7:const p=this._csiHandlers[this._collect<<8|l];let b=p?p.length-1:-1;for(;b>=0&&(o=p[b](this._params),o!==!0);b--)if(o instanceof Promise)return this._preserveStack(3,p,b,v,h),o;b<0&&this._csiHandlerFb(this._collect<<8|l,this._params),this.precedingCodepoint=0;break;case 8:do switch(l){case 59:this._params.addParam(0);break;case 58:this._params.addSubParam(-1);break;default:this._params.addDigit(l-48)}while(++h<t&&(l=s[h])>47&&l<60);h--;break;case 9:this._collect<<=8,this._collect|=l;break;case 10:const L=this._escHandlers[this._collect<<8|l];let y=L?L.length-1:-1;for(;y>=0&&(o=L[y](),o!==!0);y--)if(o instanceof Promise)return this._preserveStack(4,L,y,v,h),o;y<0&&this._escHandlerFb(this._collect<<8|l),this.precedingCodepoint=0;break;case 11:this._params.reset(),this._params.addParam(0),this._collect=0;break;case 12:this._dcsParser.hook(this._collect<<8|l,this._params);break;case 13:for(let k=h+1;;++k)if(k>=t||(l=s[k])===24||l===26||l===27||l>127&&l<g){this._dcsParser.put(s,h,k),h=k-1;break}break;case 14:if(o=this._dcsParser.unhook(l!==24&&l!==26),o)return this._preserveStack(6,[],0,v,h),o;l===27&&(v|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingCodepoint=0;break;case 4:this._oscParser.start();break;case 5:for(let k=h+1;;k++)if(k>=t||(l=s[k])<32||l>127&&l<g){this._oscParser.put(s,h,k),h=k-1;break}break;case 6:if(o=this._oscParser.end(l!==24&&l!==26),o)return this._preserveStack(5,[],0,v,h),o;l===27&&(v|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingCodepoint=0}this.currentState=15&v}}}r.EscapeSequenceParser=u},6242:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.OscHandler=r.OscParser=void 0;const c=a(5770),_=a(482),n=[];r.OscParser=class{constructor(){this._state=0,this._active=n,this._id=-1,this._handlers=Object.create(null),this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}registerHandler(d,f){this._handlers[d]===void 0&&(this._handlers[d]=[]);const g=this._handlers[d];return g.push(f),{dispose:()=>{const u=g.indexOf(f);u!==-1&&g.splice(u,1)}}}clearHandler(d){this._handlers[d]&&delete this._handlers[d]}setHandlerFallback(d){this._handlerFb=d}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=n}reset(){if(this._state===2)for(let d=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;d>=0;--d)this._active[d].end(!1);this._stack.paused=!1,this._active=n,this._id=-1,this._state=0}_start(){if(this._active=this._handlers[this._id]||n,this._active.length)for(let d=this._active.length-1;d>=0;d--)this._active[d].start();else this._handlerFb(this._id,"START")}_put(d,f,g){if(this._active.length)for(let u=this._active.length-1;u>=0;u--)this._active[u].put(d,f,g);else this._handlerFb(this._id,"PUT",(0,_.utf32ToString)(d,f,g))}start(){this.reset(),this._state=1}put(d,f,g){if(this._state!==3){if(this._state===1)for(;f<g;){const u=d[f++];if(u===59){this._state=2,this._start();break}if(u<48||57<u)return void(this._state=3);this._id===-1&&(this._id=0),this._id=10*this._id+u-48}this._state===2&&g-f>0&&this._put(d,f,g)}}end(d,f=!0){if(this._state!==0){if(this._state!==3)if(this._state===1&&this._start(),this._active.length){let g=!1,u=this._active.length-1,e=!1;if(this._stack.paused&&(u=this._stack.loopPosition-1,g=f,e=this._stack.fallThrough,this._stack.paused=!1),!e&&g===!1){for(;u>=0&&(g=this._active[u].end(d),g!==!0);u--)if(g instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=u,this._stack.fallThrough=!1,g;u--}for(;u>=0;u--)if(g=this._active[u].end(!1),g instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=u,this._stack.fallThrough=!0,g}else this._handlerFb(this._id,"END",d);this._active=n,this._id=-1,this._state=0}}},r.OscHandler=class{constructor(d){this._handler=d,this._data="",this._hitLimit=!1}start(){this._data="",this._hitLimit=!1}put(d,f,g){this._hitLimit||(this._data+=(0,_.utf32ToString)(d,f,g),this._data.length>c.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}end(d){let f=!1;if(this._hitLimit)f=!1;else if(d&&(f=this._handler(this._data),f instanceof Promise))return f.then(g=>(this._data="",this._hitLimit=!1,g));return this._data="",this._hitLimit=!1,f}}},8742:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.Params=void 0;const a=2147483647;class c{static fromArray(n){const d=new c;if(!n.length)return d;for(let f=Array.isArray(n[0])?1:0;f<n.length;++f){const g=n[f];if(Array.isArray(g))for(let u=0;u<g.length;++u)d.addSubParam(g[u]);else d.addParam(g)}return d}constructor(n=32,d=32){if(this.maxLength=n,this.maxSubParamsLength=d,d>256)throw new Error("maxSubParamsLength must not be greater than 256");this.params=new Int32Array(n),this.length=0,this._subParams=new Int32Array(d),this._subParamsLength=0,this._subParamsIdx=new Uint16Array(n),this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}clone(){const n=new c(this.maxLength,this.maxSubParamsLength);return n.params.set(this.params),n.length=this.length,n._subParams.set(this._subParams),n._subParamsLength=this._subParamsLength,n._subParamsIdx.set(this._subParamsIdx),n._rejectDigits=this._rejectDigits,n._rejectSubDigits=this._rejectSubDigits,n._digitIsSub=this._digitIsSub,n}toArray(){const n=[];for(let d=0;d<this.length;++d){n.push(this.params[d]);const f=this._subParamsIdx[d]>>8,g=255&this._subParamsIdx[d];g-f>0&&n.push(Array.prototype.slice.call(this._subParams,f,g))}return n}reset(){this.length=0,this._subParamsLength=0,this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}addParam(n){if(this._digitIsSub=!1,this.length>=this.maxLength)this._rejectDigits=!0;else{if(n<-1)throw new Error("values lesser than -1 are not allowed");this._subParamsIdx[this.length]=this._subParamsLength<<8|this._subParamsLength,this.params[this.length++]=n>a?a:n}}addSubParam(n){if(this._digitIsSub=!0,this.length)if(this._rejectDigits||this._subParamsLength>=this.maxSubParamsLength)this._rejectSubDigits=!0;else{if(n<-1)throw new Error("values lesser than -1 are not allowed");this._subParams[this._subParamsLength++]=n>a?a:n,this._subParamsIdx[this.length-1]++}}hasSubParams(n){return(255&this._subParamsIdx[n])-(this._subParamsIdx[n]>>8)>0}getSubParams(n){const d=this._subParamsIdx[n]>>8,f=255&this._subParamsIdx[n];return f-d>0?this._subParams.subarray(d,f):null}getSubParamsAll(){const n={};for(let d=0;d<this.length;++d){const f=this._subParamsIdx[d]>>8,g=255&this._subParamsIdx[d];g-f>0&&(n[d]=this._subParams.slice(f,g))}return n}addDigit(n){let d;if(this._rejectDigits||!(d=this._digitIsSub?this._subParamsLength:this.length)||this._digitIsSub&&this._rejectSubDigits)return;const f=this._digitIsSub?this._subParams:this.params,g=f[d-1];f[d-1]=~g?Math.min(10*g+n,a):n}}r.Params=c},5741:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.AddonManager=void 0,r.AddonManager=class{constructor(){this._addons=[]}dispose(){for(let a=this._addons.length-1;a>=0;a--)this._addons[a].instance.dispose()}loadAddon(a,c){const _={instance:c,dispose:c.dispose,isDisposed:!1};this._addons.push(_),c.dispose=()=>this._wrappedAddonDispose(_),c.activate(a)}_wrappedAddonDispose(a){if(a.isDisposed)return;let c=-1;for(let _=0;_<this._addons.length;_++)if(this._addons[_]===a){c=_;break}if(c===-1)throw new Error("Could not dispose an addon that has not been loaded");a.isDisposed=!0,a.dispose.apply(a.instance),this._addons.splice(c,1)}}},8771:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.BufferApiView=void 0;const c=a(3785),_=a(511);r.BufferApiView=class{constructor(n,d){this._buffer=n,this.type=d}init(n){return this._buffer=n,this}get cursorY(){return this._buffer.y}get cursorX(){return this._buffer.x}get viewportY(){return this._buffer.ydisp}get baseY(){return this._buffer.ybase}get length(){return this._buffer.lines.length}getLine(n){const d=this._buffer.lines.get(n);if(d)return new c.BufferLineApiView(d)}getNullCell(){return new _.CellData}}},3785:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.BufferLineApiView=void 0;const c=a(511);r.BufferLineApiView=class{constructor(_){this._line=_}get isWrapped(){return this._line.isWrapped}get length(){return this._line.length}getCell(_,n){if(!(_<0||_>=this._line.length))return n?(this._line.loadCell(_,n),n):this._line.loadCell(_,new c.CellData)}translateToString(_,n,d){return this._line.translateToString(_,n,d)}}},8285:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.BufferNamespaceApi=void 0;const c=a(8771),_=a(8460),n=a(844);class d extends n.Disposable{constructor(g){super(),this._core=g,this._onBufferChange=this.register(new _.EventEmitter),this.onBufferChange=this._onBufferChange.event,this._normal=new c.BufferApiView(this._core.buffers.normal,"normal"),this._alternate=new c.BufferApiView(this._core.buffers.alt,"alternate"),this._core.buffers.onBufferActivate(()=>this._onBufferChange.fire(this.active))}get active(){if(this._core.buffers.active===this._core.buffers.normal)return this.normal;if(this._core.buffers.active===this._core.buffers.alt)return this.alternate;throw new Error("Active buffer is neither normal nor alternate")}get normal(){return this._normal.init(this._core.buffers.normal)}get alternate(){return this._alternate.init(this._core.buffers.alt)}}r.BufferNamespaceApi=d},7975:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.ParserApi=void 0,r.ParserApi=class{constructor(a){this._core=a}registerCsiHandler(a,c){return this._core.registerCsiHandler(a,_=>c(_.toArray()))}addCsiHandler(a,c){return this.registerCsiHandler(a,c)}registerDcsHandler(a,c){return this._core.registerDcsHandler(a,(_,n)=>c(_,n.toArray()))}addDcsHandler(a,c){return this.registerDcsHandler(a,c)}registerEscHandler(a,c){return this._core.registerEscHandler(a,c)}addEscHandler(a,c){return this.registerEscHandler(a,c)}registerOscHandler(a,c){return this._core.registerOscHandler(a,c)}addOscHandler(a,c){return this.registerOscHandler(a,c)}}},7090:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.UnicodeApi=void 0,r.UnicodeApi=class{constructor(a){this._core=a}register(a){this._core.unicodeService.register(a)}get versions(){return this._core.unicodeService.versions}get activeVersion(){return this._core.unicodeService.activeVersion}set activeVersion(a){this._core.unicodeService.activeVersion=a}}},744:function(I,r,a){var c=this&&this.__decorate||function(e,s,t,i){var o,l=arguments.length,v=l<3?s:i===null?i=Object.getOwnPropertyDescriptor(s,t):i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(e,s,t,i);else for(var m=e.length-1;m>=0;m--)(o=e[m])&&(v=(l<3?o(v):l>3?o(s,t,v):o(s,t))||v);return l>3&&v&&Object.defineProperty(s,t,v),v},_=this&&this.__param||function(e,s){return function(t,i){s(t,i,e)}};Object.defineProperty(r,"__esModule",{value:!0}),r.BufferService=r.MINIMUM_ROWS=r.MINIMUM_COLS=void 0;const n=a(8460),d=a(844),f=a(5295),g=a(2585);r.MINIMUM_COLS=2,r.MINIMUM_ROWS=1;let u=r.BufferService=class extends d.Disposable{get buffer(){return this.buffers.active}constructor(e){super(),this.isUserScrolling=!1,this._onResize=this.register(new n.EventEmitter),this.onResize=this._onResize.event,this._onScroll=this.register(new n.EventEmitter),this.onScroll=this._onScroll.event,this.cols=Math.max(e.rawOptions.cols||0,r.MINIMUM_COLS),this.rows=Math.max(e.rawOptions.rows||0,r.MINIMUM_ROWS),this.buffers=this.register(new f.BufferSet(e,this))}resize(e,s){this.cols=e,this.rows=s,this.buffers.resize(e,s),this._onResize.fire({cols:e,rows:s})}reset(){this.buffers.reset(),this.isUserScrolling=!1}scroll(e,s=!1){const t=this.buffer;let i;i=this._cachedBlankLine,i&&i.length===this.cols&&i.getFg(0)===e.fg&&i.getBg(0)===e.bg||(i=t.getBlankLine(e,s),this._cachedBlankLine=i),i.isWrapped=s;const o=t.ybase+t.scrollTop,l=t.ybase+t.scrollBottom;if(t.scrollTop===0){const v=t.lines.isFull;l===t.lines.length-1?v?t.lines.recycle().copyFrom(i):t.lines.push(i.clone()):t.lines.splice(l+1,0,i.clone()),v?this.isUserScrolling&&(t.ydisp=Math.max(t.ydisp-1,0)):(t.ybase++,this.isUserScrolling||t.ydisp++)}else{const v=l-o+1;t.lines.shiftElements(o+1,v-1,-1),t.lines.set(l,i.clone())}this.isUserScrolling||(t.ydisp=t.ybase),this._onScroll.fire(t.ydisp)}scrollLines(e,s,t){const i=this.buffer;if(e<0){if(i.ydisp===0)return;this.isUserScrolling=!0}else e+i.ydisp>=i.ybase&&(this.isUserScrolling=!1);const o=i.ydisp;i.ydisp=Math.max(Math.min(i.ydisp+e,i.ybase),0),o!==i.ydisp&&(s||this._onScroll.fire(i.ydisp))}};r.BufferService=u=c([_(0,g.IOptionsService)],u)},7994:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.CharsetService=void 0,r.CharsetService=class{constructor(){this.glevel=0,this._charsets=[]}reset(){this.charset=void 0,this._charsets=[],this.glevel=0}setgLevel(a){this.glevel=a,this.charset=this._charsets[a]}setgCharset(a,c){this._charsets[a]=c,this.glevel===a&&(this.charset=c)}}},1753:function(I,r,a){var c=this&&this.__decorate||function(i,o,l,v){var m,h=arguments.length,p=h<3?o:v===null?v=Object.getOwnPropertyDescriptor(o,l):v;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")p=Reflect.decorate(i,o,l,v);else for(var b=i.length-1;b>=0;b--)(m=i[b])&&(p=(h<3?m(p):h>3?m(o,l,p):m(o,l))||p);return h>3&&p&&Object.defineProperty(o,l,p),p},_=this&&this.__param||function(i,o){return function(l,v){o(l,v,i)}};Object.defineProperty(r,"__esModule",{value:!0}),r.CoreMouseService=void 0;const n=a(2585),d=a(8460),f=a(844),g={NONE:{events:0,restrict:()=>!1},X10:{events:1,restrict:i=>i.button!==4&&i.action===1&&(i.ctrl=!1,i.alt=!1,i.shift=!1,!0)},VT200:{events:19,restrict:i=>i.action!==32},DRAG:{events:23,restrict:i=>i.action!==32||i.button!==3},ANY:{events:31,restrict:i=>!0}};function u(i,o){let l=(i.ctrl?16:0)|(i.shift?4:0)|(i.alt?8:0);return i.button===4?(l|=64,l|=i.action):(l|=3&i.button,4&i.button&&(l|=64),8&i.button&&(l|=128),i.action===32?l|=32:i.action!==0||o||(l|=3)),l}const e=String.fromCharCode,s={DEFAULT:i=>{const o=[u(i,!1)+32,i.col+32,i.row+32];return o[0]>255||o[1]>255||o[2]>255?"":`\x1B[M${e(o[0])}${e(o[1])}${e(o[2])}`},SGR:i=>{const o=i.action===0&&i.button!==4?"m":"M";return`\x1B[<${u(i,!0)};${i.col};${i.row}${o}`},SGR_PIXELS:i=>{const o=i.action===0&&i.button!==4?"m":"M";return`\x1B[<${u(i,!0)};${i.x};${i.y}${o}`}};let t=r.CoreMouseService=class extends f.Disposable{constructor(i,o){super(),this._bufferService=i,this._coreService=o,this._protocols={},this._encodings={},this._activeProtocol="",this._activeEncoding="",this._lastEvent=null,this._onProtocolChange=this.register(new d.EventEmitter),this.onProtocolChange=this._onProtocolChange.event;for(const l of Object.keys(g))this.addProtocol(l,g[l]);for(const l of Object.keys(s))this.addEncoding(l,s[l]);this.reset()}addProtocol(i,o){this._protocols[i]=o}addEncoding(i,o){this._encodings[i]=o}get activeProtocol(){return this._activeProtocol}get areMouseEventsActive(){return this._protocols[this._activeProtocol].events!==0}set activeProtocol(i){if(!this._protocols[i])throw new Error(`unknown protocol "${i}"`);this._activeProtocol=i,this._onProtocolChange.fire(this._protocols[i].events)}get activeEncoding(){return this._activeEncoding}set activeEncoding(i){if(!this._encodings[i])throw new Error(`unknown encoding "${i}"`);this._activeEncoding=i}reset(){this.activeProtocol="NONE",this.activeEncoding="DEFAULT",this._lastEvent=null}triggerMouseEvent(i){if(i.col<0||i.col>=this._bufferService.cols||i.row<0||i.row>=this._bufferService.rows||i.button===4&&i.action===32||i.button===3&&i.action!==32||i.button!==4&&(i.action===2||i.action===3)||(i.col++,i.row++,i.action===32&&this._lastEvent&&this._equalEvents(this._lastEvent,i,this._activeEncoding==="SGR_PIXELS"))||!this._protocols[this._activeProtocol].restrict(i))return!1;const o=this._encodings[this._activeEncoding](i);return o&&(this._activeEncoding==="DEFAULT"?this._coreService.triggerBinaryEvent(o):this._coreService.triggerDataEvent(o,!0)),this._lastEvent=i,!0}explainEvents(i){return{down:!!(1&i),up:!!(2&i),drag:!!(4&i),move:!!(8&i),wheel:!!(16&i)}}_equalEvents(i,o,l){if(l){if(i.x!==o.x||i.y!==o.y)return!1}else if(i.col!==o.col||i.row!==o.row)return!1;return i.button===o.button&&i.action===o.action&&i.ctrl===o.ctrl&&i.alt===o.alt&&i.shift===o.shift}};r.CoreMouseService=t=c([_(0,n.IBufferService),_(1,n.ICoreService)],t)},6975:function(I,r,a){var c=this&&this.__decorate||function(t,i,o,l){var v,m=arguments.length,h=m<3?i:l===null?l=Object.getOwnPropertyDescriptor(i,o):l;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")h=Reflect.decorate(t,i,o,l);else for(var p=t.length-1;p>=0;p--)(v=t[p])&&(h=(m<3?v(h):m>3?v(i,o,h):v(i,o))||h);return m>3&&h&&Object.defineProperty(i,o,h),h},_=this&&this.__param||function(t,i){return function(o,l){i(o,l,t)}};Object.defineProperty(r,"__esModule",{value:!0}),r.CoreService=void 0;const n=a(1439),d=a(8460),f=a(844),g=a(2585),u=Object.freeze({insertMode:!1}),e=Object.freeze({applicationCursorKeys:!1,applicationKeypad:!1,bracketedPasteMode:!1,origin:!1,reverseWraparound:!1,sendFocus:!1,wraparound:!0});let s=r.CoreService=class extends f.Disposable{constructor(t,i,o){super(),this._bufferService=t,this._logService=i,this._optionsService=o,this.isCursorInitialized=!1,this.isCursorHidden=!1,this._onData=this.register(new d.EventEmitter),this.onData=this._onData.event,this._onUserInput=this.register(new d.EventEmitter),this.onUserInput=this._onUserInput.event,this._onBinary=this.register(new d.EventEmitter),this.onBinary=this._onBinary.event,this._onRequestScrollToBottom=this.register(new d.EventEmitter),this.onRequestScrollToBottom=this._onRequestScrollToBottom.event,this.modes=(0,n.clone)(u),this.decPrivateModes=(0,n.clone)(e)}reset(){this.modes=(0,n.clone)(u),this.decPrivateModes=(0,n.clone)(e)}triggerDataEvent(t,i=!1){if(this._optionsService.rawOptions.disableStdin)return;const o=this._bufferService.buffer;i&&this._optionsService.rawOptions.scrollOnUserInput&&o.ybase!==o.ydisp&&this._onRequestScrollToBottom.fire(),i&&this._onUserInput.fire(),this._logService.debug(`sending data "${t}"`,()=>t.split("").map(l=>l.charCodeAt(0))),this._onData.fire(t)}triggerBinaryEvent(t){this._optionsService.rawOptions.disableStdin||(this._logService.debug(`sending binary "${t}"`,()=>t.split("").map(i=>i.charCodeAt(0))),this._onBinary.fire(t))}};r.CoreService=s=c([_(0,g.IBufferService),_(1,g.ILogService),_(2,g.IOptionsService)],s)},9074:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.DecorationService=void 0;const c=a(8055),_=a(8460),n=a(844),d=a(6106);let f=0,g=0;class u extends n.Disposable{get decorations(){return this._decorations.values()}constructor(){super(),this._decorations=new d.SortedList(t=>t==null?void 0:t.marker.line),this._onDecorationRegistered=this.register(new _.EventEmitter),this.onDecorationRegistered=this._onDecorationRegistered.event,this._onDecorationRemoved=this.register(new _.EventEmitter),this.onDecorationRemoved=this._onDecorationRemoved.event,this.register((0,n.toDisposable)(()=>this.reset()))}registerDecoration(t){if(t.marker.isDisposed)return;const i=new e(t);if(i){const o=i.marker.onDispose(()=>i.dispose());i.onDispose(()=>{i&&(this._decorations.delete(i)&&this._onDecorationRemoved.fire(i),o.dispose())}),this._decorations.insert(i),this._onDecorationRegistered.fire(i)}return i}reset(){for(const t of this._decorations.values())t.dispose();this._decorations.clear()}*getDecorationsAtCell(t,i,o){var l,v,m;let h=0,p=0;for(const b of this._decorations.getKeyIterator(i))h=(l=b.options.x)!==null&&l!==void 0?l:0,p=h+((v=b.options.width)!==null&&v!==void 0?v:1),t>=h&&t<p&&(!o||((m=b.options.layer)!==null&&m!==void 0?m:"bottom")===o)&&(yield b)}forEachDecorationAtCell(t,i,o,l){this._decorations.forEachByKey(i,v=>{var m,h,p;f=(m=v.options.x)!==null&&m!==void 0?m:0,g=f+((h=v.options.width)!==null&&h!==void 0?h:1),t>=f&&t<g&&(!o||((p=v.options.layer)!==null&&p!==void 0?p:"bottom")===o)&&l(v)})}}r.DecorationService=u;class e extends n.Disposable{get isDisposed(){return this._isDisposed}get backgroundColorRGB(){return this._cachedBg===null&&(this.options.backgroundColor?this._cachedBg=c.css.toColor(this.options.backgroundColor):this._cachedBg=void 0),this._cachedBg}get foregroundColorRGB(){return this._cachedFg===null&&(this.options.foregroundColor?this._cachedFg=c.css.toColor(this.options.foregroundColor):this._cachedFg=void 0),this._cachedFg}constructor(t){super(),this.options=t,this.onRenderEmitter=this.register(new _.EventEmitter),this.onRender=this.onRenderEmitter.event,this._onDispose=this.register(new _.EventEmitter),this.onDispose=this._onDispose.event,this._cachedBg=null,this._cachedFg=null,this.marker=t.marker,this.options.overviewRulerOptions&&!this.options.overviewRulerOptions.position&&(this.options.overviewRulerOptions.position="full")}dispose(){this._onDispose.fire(),super.dispose()}}},4348:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.InstantiationService=r.ServiceCollection=void 0;const c=a(2585),_=a(8343);class n{constructor(...f){this._entries=new Map;for(const[g,u]of f)this.set(g,u)}set(f,g){const u=this._entries.get(f);return this._entries.set(f,g),u}forEach(f){for(const[g,u]of this._entries.entries())f(g,u)}has(f){return this._entries.has(f)}get(f){return this._entries.get(f)}}r.ServiceCollection=n,r.InstantiationService=class{constructor(){this._services=new n,this._services.set(c.IInstantiationService,this)}setService(d,f){this._services.set(d,f)}getService(d){return this._services.get(d)}createInstance(d,...f){const g=(0,_.getServiceDependencies)(d).sort((s,t)=>s.index-t.index),u=[];for(const s of g){const t=this._services.get(s.id);if(!t)throw new Error(`[createInstance] ${d.name} depends on UNKNOWN service ${s.id}.`);u.push(t)}const e=g.length>0?g[0].index:f.length;if(f.length!==e)throw new Error(`[createInstance] First service dependency of ${d.name} at position ${e+1} conflicts with ${f.length} static arguments`);return new d(...f,...u)}}},7866:function(I,r,a){var c=this&&this.__decorate||function(e,s,t,i){var o,l=arguments.length,v=l<3?s:i===null?i=Object.getOwnPropertyDescriptor(s,t):i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(e,s,t,i);else for(var m=e.length-1;m>=0;m--)(o=e[m])&&(v=(l<3?o(v):l>3?o(s,t,v):o(s,t))||v);return l>3&&v&&Object.defineProperty(s,t,v),v},_=this&&this.__param||function(e,s){return function(t,i){s(t,i,e)}};Object.defineProperty(r,"__esModule",{value:!0}),r.traceCall=r.setTraceLogger=r.LogService=void 0;const n=a(844),d=a(2585),f={trace:d.LogLevelEnum.TRACE,debug:d.LogLevelEnum.DEBUG,info:d.LogLevelEnum.INFO,warn:d.LogLevelEnum.WARN,error:d.LogLevelEnum.ERROR,off:d.LogLevelEnum.OFF};let g,u=r.LogService=class extends n.Disposable{get logLevel(){return this._logLevel}constructor(e){super(),this._optionsService=e,this._logLevel=d.LogLevelEnum.OFF,this._updateLogLevel(),this.register(this._optionsService.onSpecificOptionChange("logLevel",()=>this._updateLogLevel())),g=this}_updateLogLevel(){this._logLevel=f[this._optionsService.rawOptions.logLevel]}_evalLazyOptionalParams(e){for(let s=0;s<e.length;s++)typeof e[s]=="function"&&(e[s]=e[s]())}_log(e,s,t){this._evalLazyOptionalParams(t),e.call(console,(this._optionsService.options.logger?"":"xterm.js: ")+s,...t)}trace(e,...s){var t,i;this._logLevel<=d.LogLevelEnum.TRACE&&this._log((i=(t=this._optionsService.options.logger)===null||t===void 0?void 0:t.trace.bind(this._optionsService.options.logger))!==null&&i!==void 0?i:console.log,e,s)}debug(e,...s){var t,i;this._logLevel<=d.LogLevelEnum.DEBUG&&this._log((i=(t=this._optionsService.options.logger)===null||t===void 0?void 0:t.debug.bind(this._optionsService.options.logger))!==null&&i!==void 0?i:console.log,e,s)}info(e,...s){var t,i;this._logLevel<=d.LogLevelEnum.INFO&&this._log((i=(t=this._optionsService.options.logger)===null||t===void 0?void 0:t.info.bind(this._optionsService.options.logger))!==null&&i!==void 0?i:console.info,e,s)}warn(e,...s){var t,i;this._logLevel<=d.LogLevelEnum.WARN&&this._log((i=(t=this._optionsService.options.logger)===null||t===void 0?void 0:t.warn.bind(this._optionsService.options.logger))!==null&&i!==void 0?i:console.warn,e,s)}error(e,...s){var t,i;this._logLevel<=d.LogLevelEnum.ERROR&&this._log((i=(t=this._optionsService.options.logger)===null||t===void 0?void 0:t.error.bind(this._optionsService.options.logger))!==null&&i!==void 0?i:console.error,e,s)}};r.LogService=u=c([_(0,d.IOptionsService)],u),r.setTraceLogger=function(e){g=e},r.traceCall=function(e,s,t){if(typeof t.value!="function")throw new Error("not supported");const i=t.value;t.value=function(...o){if(g.logLevel!==d.LogLevelEnum.TRACE)return i.apply(this,o);g.trace(`GlyphRenderer#${i.name}(${o.map(v=>JSON.stringify(v)).join(", ")})`);const l=i.apply(this,o);return g.trace(`GlyphRenderer#${i.name} return`,l),l}}},7302:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.OptionsService=r.DEFAULT_OPTIONS=void 0;const c=a(8460),_=a(844),n=a(6114);r.DEFAULT_OPTIONS={cols:80,rows:24,cursorBlink:!1,cursorStyle:"block",cursorWidth:1,cursorInactiveStyle:"outline",customGlyphs:!0,drawBoldTextInBrightColors:!0,fastScrollModifier:"alt",fastScrollSensitivity:5,fontFamily:"courier-new, courier, monospace",fontSize:15,fontWeight:"normal",fontWeightBold:"bold",ignoreBracketedPasteMode:!1,lineHeight:1,letterSpacing:0,linkHandler:null,logLevel:"info",logger:null,scrollback:1e3,scrollOnUserInput:!0,scrollSensitivity:1,screenReaderMode:!1,smoothScrollDuration:0,macOptionIsMeta:!1,macOptionClickForcesSelection:!1,minimumContrastRatio:1,disableStdin:!1,allowProposedApi:!1,allowTransparency:!1,tabStopWidth:8,theme:{},rightClickSelectsWord:n.isMac,windowOptions:{},windowsMode:!1,windowsPty:{},wordSeparator:" ()[]{}',\"`",altClickMovesCursor:!0,convertEol:!1,termName:"xterm",cancelEvents:!1,overviewRulerWidth:0};const d=["normal","bold","100","200","300","400","500","600","700","800","900"];class f extends _.Disposable{constructor(u){super(),this._onOptionChange=this.register(new c.EventEmitter),this.onOptionChange=this._onOptionChange.event;const e=Object.assign({},r.DEFAULT_OPTIONS);for(const s in u)if(s in e)try{const t=u[s];e[s]=this._sanitizeAndValidateOption(s,t)}catch(t){console.error(t)}this.rawOptions=e,this.options=Object.assign({},e),this._setupOptions()}onSpecificOptionChange(u,e){return this.onOptionChange(s=>{s===u&&e(this.rawOptions[u])})}onMultipleOptionChange(u,e){return this.onOptionChange(s=>{u.indexOf(s)!==-1&&e()})}_setupOptions(){const u=s=>{if(!(s in r.DEFAULT_OPTIONS))throw new Error(`No option with key "${s}"`);return this.rawOptions[s]},e=(s,t)=>{if(!(s in r.DEFAULT_OPTIONS))throw new Error(`No option with key "${s}"`);t=this._sanitizeAndValidateOption(s,t),this.rawOptions[s]!==t&&(this.rawOptions[s]=t,this._onOptionChange.fire(s))};for(const s in this.rawOptions){const t={get:u.bind(this,s),set:e.bind(this,s)};Object.defineProperty(this.options,s,t)}}_sanitizeAndValidateOption(u,e){switch(u){case"cursorStyle":if(e||(e=r.DEFAULT_OPTIONS[u]),!function(s){return s==="block"||s==="underline"||s==="bar"}(e))throw new Error(`"${e}" is not a valid value for ${u}`);break;case"wordSeparator":e||(e=r.DEFAULT_OPTIONS[u]);break;case"fontWeight":case"fontWeightBold":if(typeof e=="number"&&1<=e&&e<=1e3)break;e=d.includes(e)?e:r.DEFAULT_OPTIONS[u];break;case"cursorWidth":e=Math.floor(e);case"lineHeight":case"tabStopWidth":if(e<1)throw new Error(`${u} cannot be less than 1, value: ${e}`);break;case"minimumContrastRatio":e=Math.max(1,Math.min(21,Math.round(10*e)/10));break;case"scrollback":if((e=Math.min(e,4294967295))<0)throw new Error(`${u} cannot be less than 0, value: ${e}`);break;case"fastScrollSensitivity":case"scrollSensitivity":if(e<=0)throw new Error(`${u} cannot be less than or equal to 0, value: ${e}`);break;case"rows":case"cols":if(!e&&e!==0)throw new Error(`${u} must be numeric, value: ${e}`);break;case"windowsPty":e=e??{}}return e}}r.OptionsService=f},2660:function(I,r,a){var c=this&&this.__decorate||function(f,g,u,e){var s,t=arguments.length,i=t<3?g:e===null?e=Object.getOwnPropertyDescriptor(g,u):e;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(f,g,u,e);else for(var o=f.length-1;o>=0;o--)(s=f[o])&&(i=(t<3?s(i):t>3?s(g,u,i):s(g,u))||i);return t>3&&i&&Object.defineProperty(g,u,i),i},_=this&&this.__param||function(f,g){return function(u,e){g(u,e,f)}};Object.defineProperty(r,"__esModule",{value:!0}),r.OscLinkService=void 0;const n=a(2585);let d=r.OscLinkService=class{constructor(f){this._bufferService=f,this._nextId=1,this._entriesWithId=new Map,this._dataByLinkId=new Map}registerLink(f){const g=this._bufferService.buffer;if(f.id===void 0){const o=g.addMarker(g.ybase+g.y),l={data:f,id:this._nextId++,lines:[o]};return o.onDispose(()=>this._removeMarkerFromLink(l,o)),this._dataByLinkId.set(l.id,l),l.id}const u=f,e=this._getEntryIdKey(u),s=this._entriesWithId.get(e);if(s)return this.addLineToLink(s.id,g.ybase+g.y),s.id;const t=g.addMarker(g.ybase+g.y),i={id:this._nextId++,key:this._getEntryIdKey(u),data:u,lines:[t]};return t.onDispose(()=>this._removeMarkerFromLink(i,t)),this._entriesWithId.set(i.key,i),this._dataByLinkId.set(i.id,i),i.id}addLineToLink(f,g){const u=this._dataByLinkId.get(f);if(u&&u.lines.every(e=>e.line!==g)){const e=this._bufferService.buffer.addMarker(g);u.lines.push(e),e.onDispose(()=>this._removeMarkerFromLink(u,e))}}getLinkData(f){var g;return(g=this._dataByLinkId.get(f))===null||g===void 0?void 0:g.data}_getEntryIdKey(f){return`${f.id};;${f.uri}`}_removeMarkerFromLink(f,g){const u=f.lines.indexOf(g);u!==-1&&(f.lines.splice(u,1),f.lines.length===0&&(f.data.id!==void 0&&this._entriesWithId.delete(f.key),this._dataByLinkId.delete(f.id)))}};r.OscLinkService=d=c([_(0,n.IBufferService)],d)},8343:(I,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.createDecorator=r.getServiceDependencies=r.serviceRegistry=void 0;const a="di$target",c="di$dependencies";r.serviceRegistry=new Map,r.getServiceDependencies=function(_){return _[c]||[]},r.createDecorator=function(_){if(r.serviceRegistry.has(_))return r.serviceRegistry.get(_);const n=function(d,f,g){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");(function(u,e,s){e[a]===e?e[c].push({id:u,index:s}):(e[c]=[{id:u,index:s}],e[a]=e)})(n,d,g)};return n.toString=()=>_,r.serviceRegistry.set(_,n),n}},2585:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.IDecorationService=r.IUnicodeService=r.IOscLinkService=r.IOptionsService=r.ILogService=r.LogLevelEnum=r.IInstantiationService=r.ICharsetService=r.ICoreService=r.ICoreMouseService=r.IBufferService=void 0;const c=a(8343);var _;r.IBufferService=(0,c.createDecorator)("BufferService"),r.ICoreMouseService=(0,c.createDecorator)("CoreMouseService"),r.ICoreService=(0,c.createDecorator)("CoreService"),r.ICharsetService=(0,c.createDecorator)("CharsetService"),r.IInstantiationService=(0,c.createDecorator)("InstantiationService"),function(n){n[n.TRACE=0]="TRACE",n[n.DEBUG=1]="DEBUG",n[n.INFO=2]="INFO",n[n.WARN=3]="WARN",n[n.ERROR=4]="ERROR",n[n.OFF=5]="OFF"}(_||(r.LogLevelEnum=_={})),r.ILogService=(0,c.createDecorator)("LogService"),r.IOptionsService=(0,c.createDecorator)("OptionsService"),r.IOscLinkService=(0,c.createDecorator)("OscLinkService"),r.IUnicodeService=(0,c.createDecorator)("UnicodeService"),r.IDecorationService=(0,c.createDecorator)("DecorationService")},1480:(I,r,a)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.UnicodeService=void 0;const c=a(8460),_=a(225);r.UnicodeService=class{constructor(){this._providers=Object.create(null),this._active="",this._onChange=new c.EventEmitter,this.onChange=this._onChange.event;const n=new _.UnicodeV6;this.register(n),this._active=n.version,this._activeProvider=n}dispose(){this._onChange.dispose()}get versions(){return Object.keys(this._providers)}get activeVersion(){return this._active}set activeVersion(n){if(!this._providers[n])throw new Error(`unknown Unicode version "${n}"`);this._active=n,this._activeProvider=this._providers[n],this._onChange.fire(n)}register(n){this._providers[n.version]=n}wcwidth(n){return this._activeProvider.wcwidth(n)}getStringCellWidth(n){let d=0;const f=n.length;for(let g=0;g<f;++g){let u=n.charCodeAt(g);if(55296<=u&&u<=56319){if(++g>=f)return d+this.wcwidth(u);const e=n.charCodeAt(g);56320<=e&&e<=57343?u=1024*(u-55296)+e-56320+65536:d+=this.wcwidth(e)}d+=this.wcwidth(u)}return d}}}},J={};function q(I){var r=J[I];if(r!==void 0)return r.exports;var a=J[I]={exports:{}};return te[I].call(a.exports,a,a.exports,q),a.exports}var Z={};return(()=>{var I=Z;Object.defineProperty(I,"__esModule",{value:!0}),I.Terminal=void 0;const r=q(9042),a=q(3236),c=q(844),_=q(5741),n=q(8285),d=q(7975),f=q(7090),g=["cols","rows"];class u extends c.Disposable{constructor(s){super(),this._core=this.register(new a.Terminal(s)),this._addonManager=this.register(new _.AddonManager),this._publicOptions=Object.assign({},this._core.options);const t=o=>this._core.options[o],i=(o,l)=>{this._checkReadonlyOptions(o),this._core.options[o]=l};for(const o in this._core.options){const l={get:t.bind(this,o),set:i.bind(this,o)};Object.defineProperty(this._publicOptions,o,l)}}_checkReadonlyOptions(s){if(g.includes(s))throw new Error(`Option "${s}" can only be set in the constructor`)}_checkProposedApi(){if(!this._core.optionsService.rawOptions.allowProposedApi)throw new Error("You must set the allowProposedApi option to true to use proposed API")}get onBell(){return this._core.onBell}get onBinary(){return this._core.onBinary}get onCursorMove(){return this._core.onCursorMove}get onData(){return this._core.onData}get onKey(){return this._core.onKey}get onLineFeed(){return this._core.onLineFeed}get onRender(){return this._core.onRender}get onResize(){return this._core.onResize}get onScroll(){return this._core.onScroll}get onSelectionChange(){return this._core.onSelectionChange}get onTitleChange(){return this._core.onTitleChange}get onWriteParsed(){return this._core.onWriteParsed}get element(){return this._core.element}get parser(){return this._parser||(this._parser=new d.ParserApi(this._core)),this._parser}get unicode(){return this._checkProposedApi(),new f.UnicodeApi(this._core)}get textarea(){return this._core.textarea}get rows(){return this._core.rows}get cols(){return this._core.cols}get buffer(){return this._buffer||(this._buffer=this.register(new n.BufferNamespaceApi(this._core))),this._buffer}get markers(){return this._checkProposedApi(),this._core.markers}get modes(){const s=this._core.coreService.decPrivateModes;let t="none";switch(this._core.coreMouseService.activeProtocol){case"X10":t="x10";break;case"VT200":t="vt200";break;case"DRAG":t="drag";break;case"ANY":t="any"}return{applicationCursorKeysMode:s.applicationCursorKeys,applicationKeypadMode:s.applicationKeypad,bracketedPasteMode:s.bracketedPasteMode,insertMode:this._core.coreService.modes.insertMode,mouseTrackingMode:t,originMode:s.origin,reverseWraparoundMode:s.reverseWraparound,sendFocusMode:s.sendFocus,wraparoundMode:s.wraparound}}get options(){return this._publicOptions}set options(s){for(const t in s)this._publicOptions[t]=s[t]}blur(){this._core.blur()}focus(){this._core.focus()}resize(s,t){this._verifyIntegers(s,t),this._core.resize(s,t)}open(s){this._core.open(s)}attachCustomKeyEventHandler(s){this._core.attachCustomKeyEventHandler(s)}registerLinkProvider(s){return this._core.registerLinkProvider(s)}registerCharacterJoiner(s){return this._checkProposedApi(),this._core.registerCharacterJoiner(s)}deregisterCharacterJoiner(s){this._checkProposedApi(),this._core.deregisterCharacterJoiner(s)}registerMarker(s=0){return this._verifyIntegers(s),this._core.registerMarker(s)}registerDecoration(s){var t,i,o;return this._checkProposedApi(),this._verifyPositiveIntegers((t=s.x)!==null&&t!==void 0?t:0,(i=s.width)!==null&&i!==void 0?i:0,(o=s.height)!==null&&o!==void 0?o:0),this._core.registerDecoration(s)}hasSelection(){return this._core.hasSelection()}select(s,t,i){this._verifyIntegers(s,t,i),this._core.select(s,t,i)}getSelection(){return this._core.getSelection()}getSelectionPosition(){return this._core.getSelectionPosition()}clearSelection(){this._core.clearSelection()}selectAll(){this._core.selectAll()}selectLines(s,t){this._verifyIntegers(s,t),this._core.selectLines(s,t)}dispose(){super.dispose()}scrollLines(s){this._verifyIntegers(s),this._core.scrollLines(s)}scrollPages(s){this._verifyIntegers(s),this._core.scrollPages(s)}scrollToTop(){this._core.scrollToTop()}scrollToBottom(){this._core.scrollToBottom()}scrollToLine(s){this._verifyIntegers(s),this._core.scrollToLine(s)}clear(){this._core.clear()}write(s,t){this._core.write(s,t)}writeln(s,t){this._core.write(s),this._core.write(`\r
`,t)}paste(s){this._core.paste(s)}refresh(s,t){this._verifyIntegers(s,t),this._core.refresh(s,t)}reset(){this._core.reset()}clearTextureAtlas(){this._core.clearTextureAtlas()}loadAddon(s){this._addonManager.loadAddon(this,s)}static get strings(){return r}_verifyIntegers(...s){for(const t of s)if(t===1/0||isNaN(t)||t%1!=0)throw new Error("This API only accepts integers")}_verifyPositiveIntegers(...s){for(const t of s)if(t&&(t===1/0||isNaN(t)||t%1!=0||t<0))throw new Error("This API only accepts positive integers")}}I.Terminal=u})(),Z})())}(fe)),fe.exports}var ye=be(),ve={exports:{}},ge;function we(){return ge||(ge=1,function(he,Se){(function(te,J){he.exports=J()})(self,function(){return(()=>{var te={};return(()=>{var J=te;Object.defineProperty(J,"__esModule",{value:!0}),J.FitAddon=void 0,J.FitAddon=class{constructor(){}activate(q){this._terminal=q}dispose(){}fit(){const q=this.proposeDimensions();if(!q||!this._terminal||isNaN(q.cols)||isNaN(q.rows))return;const Z=this._terminal._core;this._terminal.rows===q.rows&&this._terminal.cols===q.cols||(Z._renderService.clear(),this._terminal.resize(q.cols,q.rows))}proposeDimensions(){if(!this._terminal||!this._terminal.element||!this._terminal.element.parentElement)return;const q=this._terminal._core,Z=q._renderService.dimensions;if(Z.css.cell.width===0||Z.css.cell.height===0)return;const I=this._terminal.options.scrollback===0?0:q.viewport.scrollBarWidth,r=window.getComputedStyle(this._terminal.element.parentElement),a=parseInt(r.getPropertyValue("height")),c=Math.max(0,parseInt(r.getPropertyValue("width"))),_=window.getComputedStyle(this._terminal.element),n=a-(parseInt(_.getPropertyValue("padding-top"))+parseInt(_.getPropertyValue("padding-bottom"))),d=c-(parseInt(_.getPropertyValue("padding-right"))+parseInt(_.getPropertyValue("padding-left")))-I;return{cols:Math.max(2,Math.floor(d/Z.css.cell.width)),rows:Math.max(1,Math.floor(n/Z.css.cell.height))}}}})(),te})()})}(ve)),ve.exports}var Ee=we();const ke={Terminal:ye.Terminal,FitAddon:Ee.FitAddon};export{ke as default};
