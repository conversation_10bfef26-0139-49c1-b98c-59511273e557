"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.startSSEMcpServer = void 0;
const sse_js_1 = require("@modelcontextprotocol/sdk/server/sse.js");
const utils_1 = require("../utils/index.js");
const startSSEMcpServer = (server_1, ...args_1) => __awaiter(void 0, [server_1, ...args_1], void 0, function* (server, endpoint = "/sse", port = 1122) {
    const activeTransports = {};
    // Define the request handler for SSE-specific logic
    const handleRequest = (req, res) => __awaiter(void 0, void 0, void 0, function* () {
        var _a;
        if (!req.url) {
            res.writeHead(400).end("No URL");
            return;
        }
        const reqUrl = new URL(req.url, "http://localhost");
        // Handle GET requests to the SSE endpoint
        if (req.method === "GET" && reqUrl.pathname === endpoint) {
            const transport = new sse_js_1.SSEServerTransport("/messages", res);
            activeTransports[transport.sessionId] = transport;
            let closed = false;
            res.on("close", () => __awaiter(void 0, void 0, void 0, function* () {
                closed = true;
                try {
                    yield server.close();
                }
                catch (error) {
                    console.error("Error closing server:", error);
                }
                delete activeTransports[transport.sessionId];
            }));
            try {
                yield server.connect(transport);
                yield transport.send({
                    jsonrpc: "2.0",
                    method: "sse/connection",
                    params: { message: "SSE Connection established" },
                });
            }
            catch (error) {
                if (!closed) {
                    console.error("Error connecting to server:", error);
                    res.writeHead(500).end("Error connecting to server");
                }
            }
            return;
        }
        // Handle POST requests to the messages endpoint
        if (req.method === "POST" && ((_a = req.url) === null || _a === void 0 ? void 0 : _a.startsWith("/messages"))) {
            const sessionId = new URL(req.url, "https://example.com").searchParams.get("sessionId");
            if (!sessionId) {
                res.writeHead(400).end("No sessionId");
                return;
            }
            const activeTransport = activeTransports[sessionId];
            if (!activeTransport) {
                res.writeHead(400).end("No active transport");
                return;
            }
            yield activeTransport.handlePostMessage(req, res);
            return;
        }
        // If we reach here, no handler matched
        res.writeHead(404).end("Not found");
    });
    // Custom cleanup for SSE server
    const cleanup = () => {
        // Close all active transports
        for (const transport of Object.values(activeTransports)) {
            transport.close();
        }
        server.close();
    };
    // Create the HTTP server using our factory
    (0, utils_1.createBaseHttpServer)(port, endpoint, {
        handleRequest,
        cleanup,
        serverType: "SSE Server",
    });
});
exports.startSSEMcpServer = startSSEMcpServer;
