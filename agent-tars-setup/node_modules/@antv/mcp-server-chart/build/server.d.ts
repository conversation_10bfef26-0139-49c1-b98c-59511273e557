import { Server } from "@modelcontextprotocol/sdk/server/index.js";
/**
 * Creates and configures an MCP server for chart generation.
 */
export declare function createServer(): Server;
/**
 * Runs the server with stdio transport.
 */
export declare function runStdioServer(): Promise<void>;
/**
 * Runs the server with SSE transport.
 */
export declare function runSSEServer(endpoint?: string, port?: number): Promise<void>;
/**
 * Runs the server with HTTP streamable transport.
 */
export declare function runHTTPStreamableServer(endpoint?: string, port?: number): Promise<void>;
