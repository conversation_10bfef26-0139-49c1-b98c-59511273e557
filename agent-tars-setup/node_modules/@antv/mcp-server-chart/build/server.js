"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createServer = createServer;
exports.runStdioServer = runStdioServer;
exports.runSSEServer = runSSEServer;
exports.runHTTPStreamableServer = runHTTPStreamableServer;
const index_js_1 = require("@modelcontextprotocol/sdk/server/index.js");
const types_js_1 = require("@modelcontextprotocol/sdk/types.js");
const Charts = __importStar(require("./charts/index.js"));
const services_1 = require("./services/index.js");
const callTool_1 = require("./utils/callTool.js");
/**
 * Creates and configures an MCP server for chart generation.
 */
function createServer() {
    const server = new index_js_1.Server({
        name: "mcp-server-chart",
        version: "0.7.1",
    }, {
        capabilities: {
            tools: {},
        },
    });
    setupToolHandlers(server);
    server.onerror = (error) => console.error("[MCP Error]", error);
    process.on("SIGINT", () => __awaiter(this, void 0, void 0, function* () {
        yield server.close();
        process.exit(0);
    }));
    return server;
}
/**
 * Sets up tool handlers for the MCP server.
 */
function setupToolHandlers(server) {
    server.setRequestHandler(types_js_1.ListToolsRequestSchema, () => __awaiter(this, void 0, void 0, function* () {
        return ({
            tools: Object.values(Charts).map((chart) => chart.tool),
        });
    }));
    server.setRequestHandler(types_js_1.CallToolRequestSchema, (request) => __awaiter(this, void 0, void 0, function* () {
        return yield (0, callTool_1.callTool)(request.params.name, request.params.arguments);
    }));
}
/**
 * Runs the server with stdio transport.
 */
function runStdioServer() {
    return __awaiter(this, void 0, void 0, function* () {
        const server = createServer();
        yield (0, services_1.startStdioMcpServer)(server);
    });
}
/**
 * Runs the server with SSE transport.
 */
function runSSEServer() {
    return __awaiter(this, arguments, void 0, function* (endpoint = "/sse", port = 1122) {
        const server = createServer();
        yield (0, services_1.startSSEMcpServer)(server, endpoint, port);
    });
}
/**
 * Runs the server with HTTP streamable transport.
 */
function runHTTPStreamableServer() {
    return __awaiter(this, arguments, void 0, function* (endpoint = "/mcp", port = 1122) {
        yield (0, services_1.startHTTPStreamableServer)(createServer, endpoint, port);
    });
}
