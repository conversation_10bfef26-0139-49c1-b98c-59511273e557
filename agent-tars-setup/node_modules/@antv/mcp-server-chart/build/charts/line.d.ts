import { z } from "zod";
export declare const line: {
    schema: {
        data: z.<PERSON><PERSON><z.ZodObject<{
            time: z.ZodString;
            value: z.ZodNumber;
        }, "strip", z.ZodTypeAny, {
            value: number;
            time: string;
        }, {
            value: number;
            time: string;
        }>, "atleastone">;
        stack: z.Zod<PERSON>efault<z.ZodOptional<z.ZodBoolean>>;
        theme: z.ZodDefault<z.ZodOptional<z.ZodEnum<["default", "academy"]>>>;
        width: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        height: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        title: z.<PERSON>odDefault<z.ZodOptional<z.ZodString>>;
        axisXTitle: z.ZodDefault<z.ZodOptional<z.ZodString>>;
        axisYTitle: z.ZodDefault<z.ZodOptional<z.ZodString>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
