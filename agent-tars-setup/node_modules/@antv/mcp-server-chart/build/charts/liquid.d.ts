import { z } from "zod";
export declare const liquid: {
    schema: {
        percent: z.ZodNumber;
        shape: z.<PERSON>od<PERSON><PERSON><PERSON><z.ZodOptional<z.ZodEnum<["circle", "rect", "pin", "triangle"]>>>;
        theme: z.<PERSON><PERSON><PERSON><PERSON><PERSON><z.ZodOptional<z.ZodEnum<["default", "academy"]>>>;
        width: z.Z<PERSON><PERSON><PERSON><z.ZodOptional<z.ZodNumber>>;
        height: z.Zod<PERSON>ef<PERSON><z.ZodOptional<z.ZodNumber>>;
        title: z.<PERSON>od<PERSON><PERSON>ault<z.ZodOptional<z.ZodString>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
