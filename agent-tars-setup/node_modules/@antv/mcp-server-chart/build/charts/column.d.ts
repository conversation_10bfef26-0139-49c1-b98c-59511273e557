import { z } from "zod";
export declare const column: {
    schema: {
        data: z.<PERSON><z.ZodObject<{
            category: z.ZodString;
            value: z.ZodNumber;
            group: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            value: number;
            category: string;
            group?: string | undefined;
        }, {
            value: number;
            category: string;
            group?: string | undefined;
        }>, "atleastone">;
        group: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
        stack: z.ZodDefault<z.ZodOptional<z.ZodBoolean>>;
        theme: z.ZodDefault<z.ZodOptional<z.ZodEnum<["default", "academy"]>>>;
        width: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        height: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        title: z.ZodDefault<z.ZodOptional<z.ZodString>>;
        axisXTitle: z.<PERSON><PERSON><PERSON><z.ZodOptional<z.ZodString>>;
        axisYTitle: z.Zod<PERSON>efault<z.ZodOptional<z.ZodString>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
