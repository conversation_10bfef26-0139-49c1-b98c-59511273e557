import { z } from "zod";
export declare const dualAxes: {
    schema: {
        categories: z.Zod<PERSON>rray<z.ZodString, "atleastone">;
        series: z.<PERSON><PERSON><z.ZodObject<{
            type: z.ZodEnum<["column", "line"]>;
            data: z.<PERSON><z.ZodNumber, "many">;
            axisYTitle: z.Z<PERSON>ptional<z.ZodDefault<z.ZodString>>;
        }, "strip", z.ZodType<PERSON>ny, {
            type: "column" | "line";
            data: number[];
            axisYTitle?: string | undefined;
        }, {
            type: "column" | "line";
            data: number[];
            axisYTitle?: string | undefined;
        }>, "atleastone">;
        theme: z.<PERSON>od<PERSON>efault<z.ZodOptional<z.ZodEnum<["default", "academy"]>>>;
        width: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        height: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        title: z.<PERSON><PERSON><PERSON><z.ZodOptional<z.ZodString>>;
        axisXTitle: z.ZodDefault<z.ZodOptional<z.ZodString>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
