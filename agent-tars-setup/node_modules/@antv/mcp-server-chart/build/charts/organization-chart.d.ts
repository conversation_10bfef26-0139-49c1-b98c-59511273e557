import { z } from "zod";
export type OrganizationChartDatumType = {
    name: string;
    description?: string;
    children?: OrganizationChartDatumType[];
};
export declare const organizationChart: {
    schema: {
        data: z.ZodType<OrganizationChartDatumType, z.ZodTypeDef, OrganizationChartDatumType>;
        orient: z.Z<PERSON><PERSON><PERSON><z.ZodEnum<["horizontal", "vertical"]>>;
        theme: z.<PERSON>od<PERSON>efault<z.ZodOptional<z.ZodEnum<["default", "academy"]>>>;
        width: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        height: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
