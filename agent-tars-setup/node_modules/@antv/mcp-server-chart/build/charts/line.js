"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.line = void 0;
const zod_1 = require("zod");
const utils_1 = require("../utils/index.js");
const base_1 = require("./base.js");
// Line chart data schema
const data = zod_1.z.object({
    time: zod_1.z.string(),
    value: zod_1.z.number(),
});
// Line chart input schema
const schema = {
    data: zod_1.z
        .array(data)
        .describe("Data for line chart, such as, [{ time: '2015', value: 23 }].")
        .nonempty({ message: "Line chart data cannot be empty." }),
    stack: zod_1.z
        .boolean()
        .optional()
        .default(false)
        .describe("Whether stacking is enabled. When enabled, line charts require a 'group' field in the data."),
    theme: base_1.ThemeSchema,
    width: base_1.WidthSchema,
    height: base_1.HeightSchema,
    title: base_1.TitleSchema,
    axisXTitle: base_1.AxisXTitleSchema,
    axisYTitle: base_1.AxisYTitleSchema,
};
// Line chart tool descriptor
const tool = {
    name: "generate_line_chart",
    description: "Generate a line chart to show trends over time, such as, the ratio of Apple computer sales to Apple's profits changed from 2000 to 2016.",
    inputSchema: (0, utils_1.zodToJsonSchema)(schema),
};
exports.line = {
    schema,
    tool,
};
