import { z } from "zod";
import { type TreeDataType } from "../utils/validator";
export declare const fishboneDiagram: {
    schema: {
        data: z.ZodEffects<z.ZodType<TreeDataType, z.ZodTypeDef, TreeDataType>, TreeDataType, TreeDataType>;
        theme: z.<PERSON>od<PERSON><PERSON><PERSON><z.ZodOptional<z.ZodEnum<["default", "academy"]>>>;
        width: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        height: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
