/**
 * export all charts as named exports to match the chart type
 */
export { area } from "./area";
export { bar } from "./bar";
export { boxplot } from "./boxplot";
export { column } from "./column";
export { districtMap as "district-map" } from "./district-map";
export { dualAxes as "dual-axes" } from "./dual-axes";
export { fishboneDiagram as "fishbone-diagram" } from "./fishbone-diagram";
export { flowDiagram as "flow-diagram" } from "./flow-diagram";
export { funnel } from "./funnel";
export { histogram } from "./histogram";
export { line } from "./line";
export { liquid } from "./liquid";
export { mindMap as "mind-map" } from "./mind-map";
export { networkGraph as "network-graph" } from "./network-graph";
export { organizationChart as "organization-chart" } from "./organization-chart";
export { pathMap as "path-map" } from "./path-map";
export { pie } from "./pie";
export { pinMap as "pin-map" } from "./pin-map";
export { radar } from "./radar";
export { sankey } from "./sankey";
export { scatter } from "./scatter";
export { treemap } from "./treemap";
export { venn } from "./venn";
export { violin } from "./violin";
export { wordCloud as "word-cloud" } from "./word-cloud";
