import { z } from "zod";
export declare const histogram: {
    schema: {
        data: z.<PERSON><z.ZodN<PERSON><PERSON>, "atleastone">;
        binNumber: z.<PERSON>od<PERSON>efault<z.ZodOptional<z.<PERSON>odU<PERSON><[z.<PERSON><PERSON>, z<PERSON>, z.<PERSON><PERSON><PERSON>]>>>;
        theme: z.<PERSON>od<PERSON><PERSON><PERSON><z.ZodOptional<z.ZodEnum<["default", "academy"]>>>;
        width: z.Zod<PERSON>efault<z.ZodOptional<z.ZodNumber>>;
        height: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        title: z.<PERSON>odD<PERSON>ault<z.ZodOptional<z.ZodString>>;
        axisXTitle: z.<PERSON>od<PERSON>efault<z.ZodOptional<z.ZodString>>;
        axisYTitle: z.ZodDefault<z.ZodOptional<z.ZodString>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
