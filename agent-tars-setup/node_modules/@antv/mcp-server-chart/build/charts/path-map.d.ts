import { z } from "zod";
export declare const pathMap: {
    schema: {
        title: z.ZodString;
        data: z.<PERSON><z.ZodObject<{
            data: z.<PERSON><PERSON>y<z.ZodString, "atleastone">;
        }, "strip", z.<PERSON>od<PERSON>ype<PERSON>ny, {
            data: [string, ...string[]];
        }, {
            data: [string, ...string[]];
        }>, "atleastone">;
        width: z.<PERSON>od<PERSON>efault<z.ZodOptional<z.ZodNumber>>;
        height: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
