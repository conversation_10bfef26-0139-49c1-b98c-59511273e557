import { z } from "zod";
export declare const radar: {
    schema: {
        data: z.<PERSON><z.ZodObject<{
            name: z.ZodString;
            value: z.ZodNumber;
            group: z.ZodOptional<z.ZodString>;
        }, "strip", z.ZodTypeAny, {
            value: number;
            name: string;
            group?: string | undefined;
        }, {
            value: number;
            name: string;
            group?: string | undefined;
        }>, "atleastone">;
        theme: z.<PERSON>od<PERSON><PERSON><PERSON><z.ZodOptional<z.ZodEnum<["default", "academy"]>>>;
        width: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        height: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
        title: z.<PERSON>od<PERSON><PERSON>ault<z.ZodOptional<z.ZodString>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
