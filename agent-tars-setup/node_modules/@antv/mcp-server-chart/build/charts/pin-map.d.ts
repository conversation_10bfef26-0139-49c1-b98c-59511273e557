import { z } from "zod";
export declare const pinMap: {
    schema: {
        title: z.ZodString;
        data: z.<PERSON><z.ZodString, "atleastone">;
        markerPopup: z.ZodOptional<z.ZodObject<{
            type: z.<PERSON>odDefault<z.ZodString>;
            width: z.<PERSON>odD<PERSON>ault<z.ZodNumber>;
            height: z.ZodD<PERSON><PERSON><z.ZodNumber>;
            borderRadius: z.ZodDefault<z.ZodNumber>;
        }, "strip", z.ZodTypeAny, {
            type: string;
            width: number;
            height: number;
            borderRadius: number;
        }, {
            type?: string | undefined;
            width?: number | undefined;
            height?: number | undefined;
            borderRadius?: number | undefined;
        }>>;
        width: z.<PERSON>odDefault<z.ZodOptional<z.ZodNumber>>;
        height: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
    };
    tool: {
        name: string;
        description: string;
        inputSchema: import("zod-to-json-schema").JsonSchema7Type & {
            $schema?: string | undefined;
            definitions?: {
                [key: string]: import("zod-to-json-schema").JsonSchema7Type;
            } | undefined;
        };
    };
};
