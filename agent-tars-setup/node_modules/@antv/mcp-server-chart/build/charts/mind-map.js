"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mindMap = void 0;
const zod_1 = require("zod");
const utils_1 = require("../utils/index.js");
const validator_1 = require("../utils/validator.js");
const base_1 = require("./base.js");
// Mind map node schema
const MindMapNodeSchema = zod_1.z.lazy(() => zod_1.z.object({
    name: zod_1.z.string(),
    children: zod_1.z.array(MindMapNodeSchema).optional(),
}));
// Mind map chart input schema
const schema = {
    data: MindMapNodeSchema.describe("Data for mind map chart, such as, { name: 'main topic', children: [{ name: 'topic 1', children: [{ name:'subtopic 1-1' }] }.").refine(validator_1.validatedTreeDataSchema, {
        message: "Invalid parameters: node name is not unique.",
        path: ["data"],
    }),
    theme: base_1.ThemeSchema,
    width: base_1.WidthSchema,
    height: base_1.HeightSchema,
};
// Mind map chart tool descriptor
const tool = {
    name: "generate_mind_map",
    description: "Generate a mind map chart to organizes and presents information in a hierarchical structure with branches radiating from a central topic, such as, a diagram showing the relationship between a main topic and its subtopics.",
    inputSchema: (0, utils_1.zodToJsonSchema)(schema),
};
exports.mindMap = {
    schema,
    tool,
};
