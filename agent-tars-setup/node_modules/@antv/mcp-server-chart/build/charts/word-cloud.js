"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.wordCloud = void 0;
const zod_1 = require("zod");
const utils_1 = require("../utils/index.js");
const base_1 = require("./base.js");
// Word cloud data schema
const data = zod_1.z.object({
    text: zod_1.z.string(),
    value: zod_1.z.number(),
});
// Word cloud input schema
const schema = {
    data: zod_1.z
        .array(data)
        .describe("Data for word cloud chart, such as, [{ value: 4.272, text: '形成' }].")
        .nonempty({ message: "Word cloud chart data cannot be empty." }),
    theme: base_1.ThemeSchema,
    width: base_1.WidthSchema,
    height: base_1.HeightSchema,
    title: base_1.TitleSchema,
};
// Word cloud tool descriptor
const tool = {
    name: "generate_word_cloud_chart",
    description: "Generate a word cloud chart to show word frequency or weight through text size variation, such as, analyzing common words in social media, reviews, or feedback.",
    inputSchema: (0, utils_1.zodToJsonSchema)(schema),
};
exports.wordCloud = {
    schema,
    tool,
};
