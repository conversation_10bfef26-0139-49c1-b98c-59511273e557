"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createBaseHttpServer = createBaseHttpServer;
const node_http_1 = __importDefault(require("node:http"));
/**
 * Handles CORS headers for incoming requests
 */
function handleCORS(req, res) {
    if (req.headers.origin) {
        try {
            const origin = new URL(req.headers.origin);
            res.setHeader("Access-Control-Allow-Origin", origin.origin);
            res.setHeader("Access-Control-Allow-Credentials", "true");
            res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
            res.setHeader("Access-Control-Allow-Headers", "*");
        }
        catch (error) {
            console.error("Error parsing origin:", error);
        }
    }
}
/**
 * Handles common endpoints like health check and ping
 * @returns true if the request was handled, false otherwise
 */
function handleCommonEndpoints(req, res) {
    if (!req.url) {
        res.writeHead(400).end("No URL");
        return true;
    }
    if (req.method === "GET" && req.url === "/health") {
        res.writeHead(200, { "Content-Type": "text/plain" }).end("OK");
        return true;
    }
    if (req.method === "GET" && req.url === "/ping") {
        res.writeHead(200).end("pong");
        return true;
    }
    return false;
}
/**
 * Sets up signal handlers for graceful shutdown
 */
function setupCleanupHandlers(httpServer, customCleanup) {
    const cleanup = () => {
        console.log("\nClosing server...");
        // Execute custom cleanup if provided
        if (customCleanup)
            customCleanup();
        httpServer.close(() => {
            console.log("Server closed");
            process.exit(0);
        });
    };
    process.on("SIGINT", cleanup);
    process.on("SIGTERM", cleanup);
}
/**
 * Logs server startup information with formatted URLs
 */
function logServerStartup(serverType, port, endpoint) {
    const serverUrl = `http://localhost:${port}${endpoint}`;
    const healthUrl = `http://localhost:${port}/health`;
    const pingUrl = `http://localhost:${port}/ping`;
    console.log(`${serverType} running on: \x1b[32m\u001B[4m${serverUrl}\u001B[0m\x1b[0m`);
    console.log("\nTest endpoints:");
    console.log(`• Health check: \u001B[4m${healthUrl}\u001B[0m`);
    console.log(`• Ping test: \u001B[4m${pingUrl}\u001B[0m`);
}
/**
 * Creates a base HTTP server with common functionality
 */
function createBaseHttpServer(port, endpoint, handlers) {
    const httpServer = node_http_1.default.createServer((req, res) => __awaiter(this, void 0, void 0, function* () {
        // Handle CORS for all requests
        handleCORS(req, res);
        // Handle OPTIONS requests
        if (req.method === "OPTIONS") {
            res.writeHead(204).end();
            return;
        }
        // Handle common endpoints like health and ping
        if (handleCommonEndpoints(req, res))
            return;
        // Pass remaining requests to the specific handler
        try {
            yield handlers.handleRequest(req, res);
        }
        catch (error) {
            console.error(`Error in ${handlers.serverType} request handler:`, error);
            res.writeHead(500).end("Internal Server Error");
        }
    }));
    // Set up cleanup handlers
    setupCleanupHandlers(httpServer, handlers.cleanup);
    // Start listening and log server info
    httpServer.listen(port, () => {
        logServerStartup(handlers.serverType, port, endpoint);
    });
    return httpServer;
}
