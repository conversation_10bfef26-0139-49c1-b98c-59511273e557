"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBody = getBody;
function getBody(request) {
    return new Promise((resolve) => {
        const bodyParts = [];
        let body;
        request
            .on("data", (chunk) => {
            bodyParts.push(chunk);
        })
            .on("end", () => {
            body = Buffer.concat(bodyParts).toString();
            resolve(JSON.parse(body));
        });
    });
}
