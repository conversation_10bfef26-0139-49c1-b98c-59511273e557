"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getVisRequestServer = getVisRequestServer;
exports.getServiceIdentifier = getServiceIdentifier;
const node_process_1 = __importDefault(require("node:process"));
/**
 * Get the VIS_REQUEST_SERVER from environment variables.
 */
function getVisRequestServer() {
    return (node_process_1.default.env.VIS_REQUEST_SERVER ||
        "https://antv-studio.alipay.com/api/gpt-vis");
}
/**
 * Get the `SERVICE_ID` from environment variables.
 */
function getServiceIdentifier() {
    return node_process_1.default.env.SERVICE_ID;
}
