# Agent TARS with Chart Server Setup

This directory contains the configuration and scripts to run Agent TARS with integrated chart visualization capabilities using `@antv/mcp-server-chart`.

## 🚀 Quick Start

1. **Setup Environment:**
   ```bash
   source ./setup-env.sh
   ```

2. **Configure API Keys:**
   ```bash
   nano .env  # Edit with your OpenAI API key
   ```

3. **Start Agent TARS:**
   ```bash
   ./start-agent-tars-with-chart.sh
   ```

## 📊 Chart Capabilities

The integrated MCP Chart Server provides 25+ visualization types:

### Basic Charts
- Line Chart, Bar Chart, Pie Chart
- Area Chart, Scatter Plot, Histogram

### Advanced Visualizations
- Heatmap, Treemap, Sankey Diagram
- Radar Chart, Funnel Chart, Gauge Chart
- Box Plot, Violin Plot, Waterfall Chart

### Geographic & Network
- Map visualizations, Network graphs
- Flow diagrams, Organizational charts

## 🔧 Configuration

### Files
- `agent-tars.config.json` - Main configuration file
- `.env` - Environment variables (API keys)
- `start-agent-tars-with-chart.sh` - Startup script

### Environment Variables
```bash
OPENAI_API_KEY=your_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
AGENT_TARS_PORT=8888
```

## 🎯 Usage Examples

Once Agent TARS is running, you can ask it to:

- "Create a bar chart showing sales data"
- "Generate a line graph of temperature trends"
- "Make a pie chart of market share distribution"
- "Build a heatmap of user activity"

The chart server will automatically handle the visualization generation.

## 🛠️ Troubleshooting

### Common Issues
1. **Permission errors**: Use local installation instead of global
2. **API key missing**: Check `.env` file configuration
3. **Port conflicts**: Change port in config file

### Logs
Check Agent TARS logs in the terminal for detailed error messages.

## 📚 Resources

- [Agent TARS Documentation](https://agent-tars.com)
- [MCP Server Chart GitHub](https://github.com/antvis/mcp-server-chart)
- [AntV Visualization Library](https://antv.vision/)
