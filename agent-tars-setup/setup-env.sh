#!/bin/bash

# Environment Setup for Agent TARS with Chart Server
# Usage: source ./setup-env.sh

echo "🔧 Setting up environment for Agent TARS with Chart Server..."

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    cat > .env << 'EOF'
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# Alternative: Use other providers
# ANTHROPIC_API_KEY=your_anthropic_key_here
# GOOGLE_API_KEY=your_google_key_here

# Agent TARS Configuration
AGENT_TARS_PORT=8888
AGENT_TARS_DEBUG=false
EOF
    echo "📝 Created .env file. Please edit it with your API keys."
else
    echo "✅ .env file already exists"
fi

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
    echo "🔑 Environment variables loaded"
fi

echo ""
echo "📋 Next steps:"
echo "1. Edit .env file with your API keys: nano .env"
echo "2. Start Agent TARS: ./start-agent-tars-with-chart.sh"
echo ""
echo "🎯 Available chart types with mcp-server-chart:"
echo "   - Line charts, Bar charts, Pie charts"
echo "   - Scatter plots, Area charts, Histograms"
echo "   - Heatmaps, Treemaps, Sankey diagrams"
echo "   - And 25+ more visualization types!"
