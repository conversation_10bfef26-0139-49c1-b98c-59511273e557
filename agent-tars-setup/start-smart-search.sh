#!/bin/bash

# Agent TARS with Smart Search Strategy
# 使用多个搜索引擎避开Google限制
# Usage: ./start-smart-search.sh

echo "🧠 Starting Agent TARS with Smart Search Strategy..."
echo "🔍 搜索策略：Bing + DuckDuckGo + 百度 (避开Google限制)"
echo "🌐 Web浏览：✅ 支持VPN代理"
echo "📊 图表生成：✅ 完全可用"
echo "💻 编程帮助：✅ DeepSeek优化"
echo "🔒 VPN兼容：✅ 代理127.0.0.1:7890"
echo "📍 Web UI: http://localhost:8888"
echo "⏹️  Press Ctrl+C to stop"
echo ""

# 设置环境变量
export PLAYWRIGHT_BROWSERS_PATH=./playwright-browsers
export PLAYWRIGHT_HEADLESS=true
export HTTP_PROXY=http://127.0.0.1:7890
export HTTPS_PROXY=http://127.0.0.1:7890

# 设置User-Agent避免检测
export PLAYWRIGHT_USER_AGENT="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# 检查Playwright浏览器
if [ ! -d "./playwright-browsers" ]; then
    echo "📥 Installing Playwright browsers..."
    npx playwright install chromium --with-deps
    echo "✅ Playwright browsers installed"
fi

echo "🎭 Playwright configured with anti-detection measures"
echo "🔍 Primary search: Bing (Google alternative)"
echo "🦆 Backup search: DuckDuckGo (privacy-focused)"
echo "🇨🇳 Chinese search: Baidu (中文优化)"
echo ""
echo "💡 搜索建议："
echo "   - 使用 'search with bing: 关键词' 指定搜索引擎"
echo "   - 使用 'browse: 网址' 直接访问网站"
echo "   - 避免频繁搜索同一关键词"
echo ""
echo "🚀 Starting Agent TARS..."

# 启动Agent TARS
npx @agent-tars/cli@latest start \
    --config ./agent-tars-with-playwright.config.json \
    --open \
    --port 8888 \
    --stream

echo ""
echo "✅ Agent TARS with Smart Search stopped"
