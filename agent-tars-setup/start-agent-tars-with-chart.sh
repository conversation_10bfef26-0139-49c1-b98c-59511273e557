#!/bin/bash

# Agent TARS with Chart Server Startup Script
# Usage: ./start-agent-tars-with-chart.sh

echo "🚀 Starting Agent TARS with Chart Server..."
echo "📊 Chart visualization capabilities enabled"
echo "📍 Web UI will be available at: http://localhost:8888"
echo "⏹️  Press Ctrl+C to stop"
echo ""

# Set environment variables if not already set
export OPENAI_API_KEY=${OPENAI_API_KEY:-""}
export OPENAI_BASE_URL=${OPENAI_BASE_URL:-""}

# Check if OpenAI API key is set
if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️  Warning: OPENAI_API_KEY not set. You'll need to configure it in the UI."
fi

# Start Agent TARS with configuration
npx @agent-tars/cli@latest start \
    --config ./agent-tars.config.json \
    --open \
    --port 8888 \
    --stream

echo ""
echo "✅ Agent TARS with Chart Server stopped"
