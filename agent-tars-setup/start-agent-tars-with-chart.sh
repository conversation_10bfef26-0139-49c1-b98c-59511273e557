#!/bin/bash

# Agent TARS with Chart Server Startup Script
# Usage: ./start-agent-tars-with-chart.sh

echo "🚀 Starting Agent TARS with Chart Server..."
echo "📊 Chart visualization capabilities enabled"
echo "📍 Web UI will be available at: http://localhost:8888"
echo "⏹️  Press Ctrl+C to stop"
echo ""

# Load environment variables from .env file
if [ -f .env ]; then
    echo "🔑 Loading environment variables from .env..."
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)
    echo "✅ Environment variables loaded"
else
    echo "❌ .env file not found!"
    exit 1
fi

# Check which provider is active
echo "🤖 Active provider: $ACTIVE_PROVIDER"
echo "📝 Model: $MODEL_NAME"

# Check if API key is set
if [ -z "$API_KEY" ]; then
    echo "❌ API_KEY not set. Please run ./configure-api-key.sh first"
    exit 1
fi

# Start Agent TARS with configuration
npx @agent-tars/cli@latest start \
    --config ./agent-tars.config.json \
    --open \
    --port 8888 \
    --stream

echo ""
echo "✅ Agent TARS with Chart Server stopped"
