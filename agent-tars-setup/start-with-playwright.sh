#!/bin/bash

# Agent TARS with Playwright MCP Server (VPN Compatible)
# 通过Playwright MCP恢复web搜索和浏览功能
# Usage: ./start-with-playwright.sh

echo "🎭 Starting Agent TARS with Playwright MCP Server..."
echo "🌐 Web搜索功能：✅ 通过Playwright MCP恢复"
echo "🔍 网页浏览：✅ 支持VPN代理"
echo "📊 图表生成：✅ 完全可用"
echo "💻 编程帮助：✅ DeepSeek优化"
echo "🔒 VPN兼容：✅ 代理127.0.0.1:7890"
echo "📍 Web UI: http://localhost:8888"
echo "⏹️  Press Ctrl+C to stop"
echo ""

# 设置Playwright环境变量
export PLAYWRIGHT_BROWSERS_PATH=./playwright-browsers
export PLAYWRIGHT_HEADLESS=true
export PLAYWRIGHT_PROXY=http://127.0.0.1:7890

# 检查Playwright浏览器是否已安装
if [ ! -d "./playwright-browsers" ]; then
    echo "📥 Installing Playwright browsers (first time only)..."
    npx playwright install chromium
    echo "✅ Playwright browsers installed"
fi

echo "🎭 Playwright MCP Server configured with proxy support"
echo "🚀 Starting Agent TARS..."

# 启动Agent TARS with Playwright MCP
npx @agent-tars/cli@latest start \
    --config ./agent-tars-with-playwright.config.json \
    --open \
    --port 8888 \
    --stream

echo ""
echo "✅ Agent TARS with Playwright stopped"
