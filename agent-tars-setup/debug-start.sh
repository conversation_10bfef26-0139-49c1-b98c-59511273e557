#!/bin/bash

echo "🔍 Agent TARS Debug Startup"
echo "=========================="

# Check if npx is available
echo "1. Checking npx..."
which npx
npx --version

echo ""
echo "2. Checking @agent-tars/cli..."
npx @agent-tars/cli@latest --version

echo ""
echo "3. Testing basic start command..."
echo "Starting Agent TARS with basic config..."

npx @agent-tars/cli@latest start \
  --model.provider openai \
  --model.id gpt-4o-mini \
  --model.apiKey ******************************************************************************************************************************************************************** \
  --port 8888 \
  --open
