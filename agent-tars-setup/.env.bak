# =============================================================================
# API PROVIDER OPTIONS - Choose the most cost-efficient option for you!
# =============================================================================

# 💰 COST COMPARISON (per 1M tokens input/output):
# 1. Groq (FREE tier)           - $0.00 / $0.00 (limited)
# 2. OpenRouter (Multiple)      - $0.20 / $0.20 (cheapest models)
# 3. DeepSeek                   - $0.14 / $0.28 (very cheap)
# 4. Google Gemini              - $0.075 / $0.30 (generous free tier)
# 5. Moonshot (Chinese)         - $0.50 / $1.50 (good value)
# 6. OpenAI GPT-4o-mini         - $0.15 / $0.60 (balanced)
# 7. Anthropic Claude           - $3.00 / $15.00 (premium)
# 8. Local (Ollama)             - $0.00 (free, runs locally)

# =============================================================================
# 1. 🚀 GROQ (FASTEST & FREE) - Recommended for testing
# =============================================================================
GROQ_API_KEY=your_groq_api_key_here
GROQ_BASE_URL=https://api.groq.com/openai/v1
GROQ_MODEL=llama-3.1-70b-versatile

# =============================================================================
# 2. 🌐 OPENROUTER (CHEAPEST MODELS) - Best value
# =============================================================================
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_MODEL=meta-llama/llama-3.1-8b-instruct:free

# =============================================================================
# 3. 🧠 DEEPSEEK (VERY CHEAP) - Great for coding
# =============================================================================
DEEPSEEK_API_KEY=***********************************
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat

# =============================================================================
# 4. 🔮 GOOGLE GEMINI (CHEAP + FREE TIER) - Great value with free tier
# =============================================================================
GOOGLE_API_KEY=AIzaSyDmsncJo0lzSTjuVwWdoAJyeE_t2qN_qIQ
GOOGLE_BASE_URL=https://generativelanguage.googleapis.com/v1beta/openai
GOOGLE_MODEL=gemini-1.5-flash

# =============================================================================
# 5. 🌙 MOONSHOT (CHINESE) - Good value, Chinese support
# =============================================================================
MOONSHOT_API_KEY=your_moonshot_api_key_here
MOONSHOT_BASE_URL=https://api.moonshot.cn/v1
MOONSHOT_MODEL=moonshot-v1-8k

# =============================================================================
# 6. 🤖 OPENAI (STANDARD) - Balanced performance
# =============================================================================
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini

# =============================================================================
# 7. 🎭 ANTHROPIC CLAUDE (PREMIUM) - Best quality
# =============================================================================
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_BASE_URL=https://api.anthropic.com
ANTHROPIC_MODEL=claude-3-haiku-20240307

# =============================================================================
# 8. 🏠 LOCAL OLLAMA (FREE) - Runs on your computer
# =============================================================================
OLLAMA_BASE_URL=http://localhost:11434/v1
OLLAMA_MODEL=llama3.1:8b
OLLAMA_API_KEY=ollama

# =============================================================================
# AGENT TARS CONFIGURATION
# =============================================================================
AGENT_TARS_PORT=8888
AGENT_TARS_DEBUG=false

# =============================================================================
# ACTIVE PROVIDER - Uncomment ONE section below to activate
# =============================================================================

# Option 1: Use Groq (FREE & FAST)
# ACTIVE_PROVIDER=groq
# API_KEY=${GROQ_API_KEY}
# BASE_URL=${GROQ_BASE_URL}
# MODEL_NAME=${GROQ_MODEL}

# Option 2: Use OpenRouter (CHEAPEST)
# ACTIVE_PROVIDER=openrouter
# API_KEY=${OPENROUTER_API_KEY}
# BASE_URL=${OPENROUTER_BASE_URL}
# MODEL_NAME=${OPENROUTER_MODEL}

Option: Use DeepSeek (VERY CHEAP)
# ACTIVE_PROVIDER=
# API_KEY=
# BASE_URL=
# MODEL_NAME=

# Option: Use Google Gemini (CHEAP + FREE TIER)
# ACTIVE_PROVIDER=google
# API_KEY=${GOOGLE_API_KEY}
# BASE_URL=${GOOGLE_BASE_URL}
# MODEL_NAME=${GOOGLE_MODEL}

# Option 5: Use Moonshot (GOOD VALUE)
# ACTIVE_PROVIDER=moonshot
# API_KEY=${MOONSHOT_API_KEY}
# BASE_URL=${MOONSHOT_BASE_URL}
# MODEL_NAME=${MOONSHOT_MODEL}

# Option 6: Use OpenAI (STANDARD)
# ACTIVE_PROVIDER=
# API_KEY=
# BASE_URL=
# MODEL_NAME=

# Option 7: Use Anthropic (PREMIUM)
# ACTIVE_PROVIDER=anthropic
# API_KEY=${ANTHROPIC_API_KEY}
# BASE_URL=${ANTHROPIC_BASE_URL}
# MODEL_NAME=${ANTHROPIC_MODEL}

# Option 8: Use Local Ollama (FREE)
# ACTIVE_PROVIDER=ollama
# API_KEY=${OLLAMA_API_KEY}
# BASE_URL=${OLLAMA_BASE_URL}
# MODEL_NAME=${OLLAMA_MODEL}
