#!/usr/bin/env python3
"""
Complete fix for ComfyUI PyTorch loading issues
"""

import os
import sys
import torch
import pickle

# Add ComfyUI to path
sys.path.insert(0, '/root/ComfyUI')

def apply_complete_fix():
    """Apply all necessary fixes for ComfyUI"""
    
    # 1. Fix PyTorch safe loading
    try:
        safe_globals = [
            'numpy.core.multiarray.scalar',
            'numpy.core.multiarray._reconstruct',
            'numpy.ndarray',
            'numpy.dtype',
            'collections.OrderedDict',
            'builtins.dict',
            'builtins.list',
            'builtins.tuple',
            'builtins.set',
            'builtins.frozenset',
            'torch.nn.parameter.Parameter',
            'torch.Tensor',
            'torch.Size'
        ]
        
        for global_name in safe_globals:
            try:
                torch.serialization.add_safe_globals([global_name])
                print(f"✅ Added {global_name} to safe globals")
            except Exception as e:
                print(f"⚠️  Could not add {global_name}: {e}")
    
    except Exception as e:
        print(f"❌ Error setting up safe globals: {e}")
    
    # 2. Set environment variables
    os.environ['PYTORCH_ENABLE_MPS_FALLBACK'] = '1'
    os.environ['TORCH_LOGS'] = '+dynamo'
    os.environ['CUDA_VISIBLE_DEVICES'] = '0'
    
    # 3. Monkey patch torch.load to use weights_only=False
    original_load = torch.load
    
    def patched_load(*args, **kwargs):
        if 'weights_only' not in kwargs:
            kwargs['weights_only'] = False
        return original_load(*args, **kwargs)
    
    torch.load = patched_load
    
    print("🎉 Complete fix applied successfully!")
    print(f"PyTorch version: {torch.__version__}")
    
    return True

if __name__ == "__main__":
    apply_complete_fix()
