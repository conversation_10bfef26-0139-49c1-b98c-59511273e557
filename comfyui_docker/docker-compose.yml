services:
  comfyui:
    build: .
    ports:
      - "8188:8188"
    volumes:
      - ./input:/root/ComfyUI/input
      - ./output:/root/ComfyUI/output
      - ./models/checkpoints:/root/ComfyUI/models/checkpoints
      - ./models/loras:/root/ComfyUI/models/loras
      - ./models/vae:/root/ComfyUI/models/vae
      - ./models/controlnet:/root/ComfyUI/models/controlnet
      - ./models/upscale_models:/root/ComfyUI/models/upscale_models
    command: ["python", "main.py", "--listen", "0.0.0.0", "--port", "8188", "--cpu"]
    restart: unless-stopped
