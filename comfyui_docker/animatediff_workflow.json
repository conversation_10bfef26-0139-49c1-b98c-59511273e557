{"last_node_id": 12, "last_link_id": 18, "nodes": [{"id": 1, "type": "CheckpointLoaderSimple", "pos": [50, 100], "size": {"0": 315, "1": 98}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "MODEL", "type": "MODEL", "links": [1], "slot_index": 0}, {"name": "CLIP", "type": "CLIP", "links": [2], "slot_index": 1}, {"name": "VAE", "type": "VAE", "links": [3], "slot_index": 2}], "properties": {"Node name for S&R": "CheckpointLoaderSimple"}, "widgets_values": ["SD1.5/v1-5-pruned-emaonly.ckpt"]}, {"id": 2, "type": "CLIPTextEncode", "pos": [400, 50], "size": {"0": 400, "1": 200}, "flags": {}, "order": 1, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 2}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [4], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["a beautiful girl dancing, high quality, detailed, masterpiece, animation, motion"]}, {"id": 3, "type": "CLIPTextEncode", "pos": [400, 300], "size": {"0": 400, "1": 200}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "clip", "type": "CLIP", "link": 2}], "outputs": [{"name": "CONDITIONING", "type": "CONDITIONING", "links": [5], "slot_index": 0}], "properties": {"Node name for S&R": "CLIPTextEncode"}, "widgets_values": ["blurry, low quality, distorted, static"]}, {"id": 8, "type": "ADE_AnimateDiffLoaderWithContext", "pos": [50, 250], "size": {"0": 315, "1": 150}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 1}], "outputs": [{"name": "MODEL", "type": "MODEL", "links": [10], "slot_index": 0}], "properties": {"Node name for S&R": "ADE_AnimateDiffLoaderWithContext"}, "widgets_values": ["mm_sd_v15_v2.ckpt", "sqrt_linear", 1.0, false]}, {"id": 4, "type": "K<PERSON><PERSON><PERSON>", "pos": [850, 100], "size": {"0": 315, "1": 262}, "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "model", "type": "MODEL", "link": 10}, {"name": "positive", "type": "CONDITIONING", "link": 4}, {"name": "negative", "type": "CONDITIONING", "link": 5}, {"name": "latent_image", "type": "LATENT", "link": 6}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [7], "slot_index": 0}], "properties": {"Node name for S&R": "K<PERSON><PERSON><PERSON>"}, "widgets_values": [42, "fixed", 20, 7.5, "euler", "normal", 1.0]}, {"id": 5, "type": "EmptyLatentImage", "pos": [400, 550], "size": {"0": 315, "1": 106}, "flags": {}, "order": 5, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [6], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 16]}, {"id": 6, "type": "VAEDecode", "pos": [1200, 100], "size": {"0": 210, "1": 46}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 7}, {"name": "vae", "type": "VAE", "link": 3}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [8, 9], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 7, "type": "SaveImage", "pos": [1450, 100], "size": {"0": 315, "1": 58}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 8}], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["AnimateDiff_frames"]}, {"id": 9, "type": "VHS_VideoCombine", "pos": [1450, 200], "size": {"0": 315, "1": 300}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 9}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 8, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "pingpong": false, "save_output": true, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00001.mp4", "subfolder": "", "type": "output", "format": "video/h264-mp4"}}}}], "links": [[1, 1, 0, 4, 0, "MODEL"], [2, 1, 1, 2, 0, "CLIP"], [2, 1, 1, 3, 0, "CLIP"], [3, 1, 2, 6, 1, "VAE"], [4, 2, 0, 4, 1, "CONDITIONING"], [5, 3, 0, 4, 2, "CONDITIONING"], [6, 5, 0, 4, 3, "LATENT"], [7, 4, 0, 6, 0, "LATENT"], [8, 6, 0, 7, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}