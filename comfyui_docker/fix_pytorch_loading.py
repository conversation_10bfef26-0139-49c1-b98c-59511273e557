#!/usr/bin/env python3
"""
Fix PyTorch 2.6 weights_only loading issue for ComfyUI
"""

import os
import sys
import torch

def fix_pytorch_loading():
    """Add safe globals for numpy compatibility"""
    try:
        # Add numpy.core.multiarray.scalar to safe globals
        torch.serialization.add_safe_globals(['numpy.core.multiarray.scalar'])
        print("✅ Added numpy.core.multiarray.scalar to safe globals")
        
        # Also add other common numpy globals that might be needed
        safe_globals = [
            'numpy.core.multiarray.scalar',
            'numpy.core.multiarray._reconstruct',
            'numpy.ndarray',
            'numpy.dtype',
            'collections.OrderedDict'
        ]
        
        for global_name in safe_globals:
            try:
                torch.serialization.add_safe_globals([global_name])
                print(f"✅ Added {global_name} to safe globals")
            except Exception as e:
                print(f"⚠️  Could not add {global_name}: {e}")
        
        print("🎉 PyTorch loading fix applied successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error applying PyTorch fix: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Applying PyTorch 2.6 loading fix...")
    print(f"PyTorch version: {torch.__version__}")
    fix_pytorch_loading()
