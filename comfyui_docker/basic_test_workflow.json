{"last_node_id": 5, "last_link_id": 5, "nodes": [{"id": 1, "type": "EmptyLatentImage", "pos": [50, 100], "size": {"0": 315, "1": 106}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "LATENT", "type": "LATENT", "links": [1], "slot_index": 0}], "properties": {"Node name for S&R": "EmptyLatentImage"}, "widgets_values": [512, 512, 1]}, {"id": 2, "type": "VAELoader", "pos": [50, 250], "size": {"0": 315, "1": 58}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "VAE", "type": "VAE", "links": [2], "slot_index": 0}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["wan-native/wan_2.1_vae.safetensors"]}, {"id": 3, "type": "VAEDecode", "pos": [400, 100], "size": {"0": 210, "1": 46}, "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 1}, {"name": "vae", "type": "VAE", "link": 2}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [3], "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 4, "type": "SaveImage", "pos": [650, 100], "size": {"0": 315, "1": 58}, "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 3}], "properties": {"Node name for S&R": "SaveImage"}, "widgets_values": ["basic_test"]}], "links": [[1, 1, 0, 3, 0, "LATENT"], [2, 2, 0, 3, 1, "VAE"], [3, 3, 0, 4, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {}, "version": 0.4}