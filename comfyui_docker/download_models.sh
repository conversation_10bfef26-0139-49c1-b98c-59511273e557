#!/bin/bash

# 轻量级文本转视频模型下载脚本
# 适用于7.8GB内存系统

echo "开始下载轻量级T2V模型..."

# 创建必要的目录
mkdir -p models/animatediff_models
mkdir -p models/checkpoints
mkdir -p models/vae

# 1. AnimateDiff Motion Module (推荐)
echo "下载 AnimateDiff Motion Module..."
cd models/animatediff_models
curl -L -C - -o "mm_sd_v15_v2.ckpt" "https://huggingface.co/guoyww/animatediff/resolve/main/mm_sd_v15_v2.ckpt"

# 2. Stable Diffusion 1.5 Base Model (AnimateDiff需要)
echo "下载 SD 1.5 基础模型..."
cd ../checkpoints
curl -L -C - -o "v1-5-pruned-emaonly.ckpt" "https://huggingface.co/runwayml/stable-diffusion-v1-5/resolve/main/v1-5-pruned-emaonly.ckpt"

# 3. VAE模型
echo "下载 VAE 模型..."
cd ../vae
curl -L -C - -o "vae-ft-mse-840000-ema-pruned.ckpt" "https://huggingface.co/stabilityai/sd-vae-ft-mse-original/resolve/main/vae-ft-mse-840000-ema-pruned.ckpt"

# 4. 可选：Stable Video Diffusion
echo "下载 SVD 模型 (可选)..."
cd ../checkpoints
curl -L -C - -o "svd_xt.safetensors" "https://huggingface.co/stabilityai/stable-video-diffusion-img2vid-xt/resolve/main/svd_xt.safetensors"

echo "模型下载完成！"
echo ""
echo "已下载的模型："
echo "- AnimateDiff Motion Module: models/animatediff_models/mm_sd_v15_v2.ckpt"
echo "- SD 1.5 Base: models/checkpoints/v1-5-pruned-emaonly.ckpt"
echo "- VAE: models/vae/vae-ft-mse-840000-ema-pruned.ckpt"
echo "- SVD (可选): models/checkpoints/svd_xt.safetensors"
echo ""
echo "总内存需求: ~4-5GB (比之前的21GB大幅减少)"
