# -*- coding: utf-8 -*-
"""
This script generates realistic mock data for a mid-sized apparel company.
It creates data for products, employees, sales, and marketing campaigns,
and then saves it into an Excel file with multiple sheets.

Required libraries:
- pandas: For data manipulation and Excel export.
- Faker: For generating realistic fake data (names, dates, etc.).
- openpyxl: Pandas requires this engine for writing to .xlsx files.

Install them using pip:
pip install pandas Faker openpyxl
"""

import pandas as pd
from faker import Faker
import random
import numpy as np
from datetime import datetime, timedelta

# --- Configuration ---
NUM_PRODUCTS = 50
NUM_EMPLOYEES = 200
NUM_SALES = 10000
NUM_CAMPAIGNS = 20
OUTPUT_FILENAME = 'n8n_projects/company_data.xlsx'

# Initialize Faker for data generation
fake = Faker('zh_CN') # Using Chinese locale for more relevant names

# --- 1. Generate Products Data ---
print("Generating products...")
product_categories = ['连衣裙', '半身裙', '衬衫', 'T恤', '外套', '裤子']
products_data = []
for i in range(1, NUM_PRODUCTS + 1):
    category = random.choice(product_categories)
    product_name = f"{category}款式-{chr(65 + i % 26)}{i}"
    cost_price = round(random.uniform(50, 300), 2)
    selling_price = round(cost_price * random.uniform(1.5, 3.0), 2)
    products_data.append({
        "product_id": i,
        "product_name": product_name,
        "category": category,
        "cost_price": cost_price,
        "selling_price": selling_price
    })
products_df = pd.DataFrame(products_data)

# --- 2. Generate Employees Data ---
print("Generating employees...")
departments = ['财务', '销售', '运营', '人力', '研发', '市场']
employees_data = []
for i in range(1, NUM_EMPLOYEES + 1):
    department = random.choice(departments)
    employees_data.append({
        "employee_id": i,
        "full_name": fake.name(),
        "department": department,
        "position": f"{department}专员",
        "hire_date": fake.date_between(start_date='-5y', end_date='today'),
        "salary": round(random.uniform(8000, 30000), 2)
    })
employees_df = pd.DataFrame(employees_data)

# --- 3. Generate Sales Data ---
print("Generating sales...")
sales_data = []
product_ids = products_df['product_id'].tolist()
employee_ids = employees_df[employees_df['department'] == '销售']['employee_id'].tolist()
regions = ['华东', '华南', '华北', '华中', '西南', '西北']
for i in range(1, NUM_SALES + 1):
    product_id = random.choice(product_ids)
    quantity = random.randint(1, 5)
    selling_price = products_df.loc[products_df['product_id'] == product_id, 'selling_price'].iloc[0]
    sales_data.append({
        "sale_id": i,
        "product_id": product_id,
        "employee_id": random.choice(employee_ids),
        "sale_date": fake.date_between(start_date='-1y', end_date='today'),
        "quantity": quantity,
        "total_price": round(quantity * selling_price, 2),
        "region": random.choice(regions)
    })
sales_df = pd.DataFrame(sales_data)

# --- 4. Generate Marketing Campaigns Data ---
print("Generating marketing campaigns...")
campaigns_data = []
channels = ['社交媒体', '邮件营销', '搜索引擎', '线下活动']
for i in range(1, NUM_CAMPAIGNS + 1):
    start_date = fake.date_between(start_date='-2y', end_date='-1y')
    campaigns_data.append({
        "campaign_id": i,
        "campaign_name": f"{start_date.year}年{random.choice(product_categories)}推广活动",
        "start_date": start_date,
        "end_date": start_date + timedelta(days=random.randint(14, 60)),
        "budget": round(random.uniform(50000, 500000), 2),
        "channel": random.choice(channels),
        "roi": round(random.uniform(1.5, 5.0), 2)
    })
campaigns_df = pd.DataFrame(campaigns_data)

# --- 5. Export to Excel ---
print(f"Exporting all data to {OUTPUT_FILENAME}...")
with pd.ExcelWriter(OUTPUT_FILENAME, engine='openpyxl') as writer:
    products_df.to_excel(writer, sheet_name='products', index=False)
    employees_df.to_excel(writer, sheet_name='employees', index=False)
    sales_df.to_excel(writer, sheet_name='sales', index=False)
    campaigns_df.to_excel(writer, sheet_name='marketing_campaigns', index=False)

print("Data generation complete!")
