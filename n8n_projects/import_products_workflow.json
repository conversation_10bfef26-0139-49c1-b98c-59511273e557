{"name": "1 - Import Products (Google Sheets)", "nodes": [{"parameters": {}, "id": "a1b2c3d4-e5f6-4a7b-8c9d-0e1f2a3b4c5d", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300]}, {"parameters": {"operation": "read", "documentId": "", "sheetName": "products", "options": {"valueRenderMode": "FORMATTED_VALUE", "representation": "2d"}}, "id": "google-sheets-node-1", "name": "Read Products from GSheet", "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [550, 300], "credentials": {"googleSheetsOAuth2Api": {"id": "", "name": ""}}}, {"parameters": {"operation": "insert", "schema": "public", "table": "products", "columns": "product_name,category,cost_price,selling_price", "options": {}}, "id": "d4e5f6a7-b8c9-4d0e-1f2a-3b4c5d6e7f8g", "name": "Insert Products", "type": "n8n-nodes-postgres-enc.postgresEnc", "typeVersion": 1, "position": [850, 300], "credentials": {"postgresEnc": {"id": "temp-postgres-credential", "name": "My Local Postgres DB"}}}], "connections": {"Start": {"main": [[{"node": "Read Products from GSheet", "type": "main", "index": 0}]]}, "Read Products from GSheet": {"main": [[{"node": "Insert Products", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "id": "import-products-gsheets"}