{"name": "Import Company Data from Excel to PostgreSQL", "nodes": [{"parameters": {}, "id": "3f8a6f9f-925a-4b89-883d-6a205f5e31d1", "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "filePath", "value": "/home/<USER>/.n8n/company_data.xlsx"}]}, "options": {}}, "id": "e053e64a-164a-4a5b-9341-3c79d12a4e0f", "name": "Set File Path", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [460, 300]}, {"parameters": {}, "id": "3a8b0b1a-9b4a-4b3a-8c1d-2e3f4g5h6i7j", "name": "Split Flow", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"fileName": "={{$node[\"Set File Path\"].json[\"filePath\"]}}", "options": {}}, "id": "a1b2c3d4-e5f6-4a7b-8c9d-0e1f2a3b4c5d", "name": "Read Products Sheet", "type": "n8n-nodes-base.spreadsheetFile", "typeVersion": 2, "position": [900, -100]}, {"parameters": {"operation": "insert", "schema": "public", "table": "products", "columns": "product_name,category,cost_price,selling_price", "options": {}}, "id": "b2c3d4e5-f6a7-4b8c-9d0e-1f2a3b4c5d6e", "name": "Insert Products", "type": "n8n-nodes-base.postgres", "typeVersion": 3.2, "position": [1120, -100], "credentials": {"postgres": {"id": "temp-postgres-credential", "name": "My Local Postgres DB", "data": {"host": "postgres", "port": 5432, "database": "n8n_analytics", "user": "n8nuser", "password": "Me249500_xs!!"}}}}, {"parameters": {"fileName": "={{$node[\"Set File Path\"].json[\"filePath\"]}}", "options": {"sheetName": "employees"}}, "id": "c3d4e5f6-a7b8-4c9d-0e1f-2a3b4c5d6e7f", "name": "Read Employees Sheet", "type": "n8n-nodes-base.spreadsheetFile", "typeVersion": 2, "position": [900, 100]}, {"parameters": {"operation": "insert", "schema": "public", "table": "employees", "columns": "full_name,department,position,hire_date,salary", "options": {}}, "id": "d4e5f6a7-b8c9-4d0e-1f2a-3b4c5d6e7f8g", "name": "Insert Employees", "type": "n8n-nodes-base.postgres", "typeVersion": 3.2, "position": [1120, 100], "credentials": {"postgres": {"id": "temp-postgres-credential", "name": "My Local Postgres DB"}}}, {"parameters": {"fileName": "={{$node[\"Set File Path\"].json[\"filePath\"]}}", "options": {"sheetName": "sales"}}, "id": "e5f6a7b8-c9d0-4e1f-2a3b-4c5d6e7f8g9h", "name": "Read Sales Sheet", "type": "n8n-nodes-base.spreadsheetFile", "typeVersion": 2, "position": [900, 300]}, {"parameters": {"operation": "insert", "schema": "public", "table": "sales", "columns": "product_id,employee_id,sale_date,quantity,total_price,region", "options": {}}, "id": "f6a7b8c9-d0e1-4f2a-3b4c-5d6e7f8g9h0i", "name": "Insert Sales", "type": "n8n-nodes-base.postgres", "typeVersion": 3.2, "position": [1120, 300], "credentials": {"postgres": {"id": "temp-postgres-credential", "name": "My Local Postgres DB"}}}, {"parameters": {"fileName": "={{$node[\"Set File Path\"].json[\"filePath\"]}}", "options": {"sheetName": "marketing_campaigns"}}, "id": "g7h8i9j0-k1l2-4m3n-4o5p-6q7r8s9t0u1v", "name": "Read Campaigns Sheet", "type": "n8n-nodes-base.spreadsheetFile", "typeVersion": 2, "position": [900, 500]}, {"parameters": {"operation": "insert", "schema": "public", "table": "marketing_campaigns", "columns": "campaign_name,start_date,end_date,budget,channel,roi", "options": {}}, "id": "h8i9j0k1-l2m3-4n4o-5p6q-7r8s9t0u1v2w", "name": "Insert Campaigns", "type": "n8n-nodes-base.postgres", "typeVersion": 3.2, "position": [1120, 500], "credentials": {"postgres": {"id": "temp-postgres-credential", "name": "My Local Postgres DB"}}}], "connections": {"Start": {"main": [[{"node": "Set File Path", "type": "main", "index": 0}]]}, "Set File Path": {"main": [[{"node": "Split Flow", "type": "main", "index": 0}]]}, "Split Flow": {"main": [[{"node": "Read Products Sheet", "type": "main", "index": 0}], [{"node": "Read Employees Sheet", "type": "main", "index": 0}], [{"node": "Read Sales Sheet", "type": "main", "index": 0}], [{"node": "Read Campaigns Sheet", "type": "main", "index": 0}]]}, "Read Products Sheet": {"main": [[{"node": "Insert Products", "type": "main", "index": 0}]]}, "Read Employees Sheet": {"main": [[{"node": "Insert Employees", "type": "main", "index": 0}]]}, "Read Sales Sheet": {"main": [[{"node": "Insert Sales", "type": "main", "index": 0}]]}, "Read Campaigns Sheet": {"main": [[{"node": "Insert Campaigns", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "id": "import-data"}