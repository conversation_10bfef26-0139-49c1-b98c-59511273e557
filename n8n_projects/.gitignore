# Environment variables
.env

# Node.js dependencies
node_modules/
package-lock.json

# Logs
logs/
*.log
npm-debug.log*

# Build output
dist/
build/

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo

# Operating System files
.DS_Store
Thumbs.db

# Temporary files
tmp/
temp/

# Reports output
Reports/
uploads/

# Cache
.cache/
.npm/

# Test coverage
coverage/

# Sample reports and guidance
Guidence/
Sample reports/
Industry_report_agent/Docker部署Trae行业分析报告智能体操作指南.pdf