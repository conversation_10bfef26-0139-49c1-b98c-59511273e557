-- Drop tables if they exist to allow for a clean slate on re-run
DROP TABLE IF EXISTS sales;
DROP TABLE IF EXISTS products;
DROP TABLE IF EXISTS employees;
DROP TABLE IF EXISTS marketing_campaigns;

-- Create Products Table
-- Stores information about each clothing item
CREATE TABLE products (
    product_id SERIAL PRIMARY KEY,
    product_name VARCHAR(100) NOT NULL,
    category VARCHAR(50),
    cost_price NUMERIC(10, 2) NOT NULL,
    selling_price NUMERIC(10, 2) NOT NULL
);

-- Create Employees Table
-- Stores information about company employees
CREATE TABLE employees (
    employee_id SERIAL PRIMARY KEY,
    full_name VARCHAR(100) NOT NULL,
    department VARCHAR(50) NOT NULL,
    position VARCHAR(50),
    hire_date DATE NOT NULL,
    salary NUMERIC(10, 2)
);

-- Create Sales Table
-- Stores daily sales transaction data
CREATE TABLE sales (
    sale_id SERIAL PRIMARY KEY,
    product_id INT REFERENCES products(product_id),
    employee_id INT REFERENCES employees(employee_id),
    sale_date DATE NOT NULL,
    quantity INT NOT NULL,
    total_price NUMERIC(10, 2) NOT NULL,
    region VARCHAR(50)
);

-- Create Marketing Campaigns Table
-- Stores data about marketing efforts
CREATE TABLE marketing_campaigns (
    campaign_id SERIAL PRIMARY KEY,
    campaign_name VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    budget NUMERIC(12, 2),
    channel VARCHAR(50), -- e.g., 'Social Media', 'Email', 'PPC'
    roi NUMERIC(5, 2) -- Return on Investment
);

-- Add some comments to explain the schema
COMMENT ON TABLE products IS 'Stores information about each clothing item, including cost and selling price.';
COMMENT ON TABLE employees IS 'Central repository for employee data, including department and salary information.';
COMMENT ON TABLE sales IS 'Transactional table recording every sale, linking products and employees.';
COMMENT ON TABLE marketing_campaigns IS 'Tracks marketing campaigns, their budgets, and performance (ROI).';
