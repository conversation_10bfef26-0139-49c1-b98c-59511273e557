{"name": "3.2-basic_industry_research_report", "nodes": [{"parameters": {"jsCode": "// n8n代码节点：专业咨询报告HTML生成器（A4报告风格）\n// 输入：来自OpenAI节点的结构化JSON报告数据\n// 输出：完整的HTML报告，A4连续页面风格，确保所有图表正确渲染\n\nconst reportData = $json.output || $json;\n\n// ===== 核心配置 =====\nconst config = {\n  title: reportData[\"目录\"] && reportData[\"目录\"][0] ? \n    `${reportData[\"目录\"][0]}行业研究报告` : '行业研究报告',\n  generateTime: new Date().toLocaleDateString('zh-CN', {\n    year: 'numeric',\n    month: 'long', \n    day: 'numeric'\n  }),\n  colors: ['#2563eb', '#059669', '#dc2626', '#d97706', '#7c3aed', '#0891b2', '#65a30d', '#ea580c']\n};\n\n// ===== 全局变量 =====\nlet globalChartIndex = 0;\nlet allChartScripts = '';\nlet usedChartKeys = new Set();\n\n// ===== 工具函数 =====\n\n// 生成唯一ID\nfunction generateId(text) {\n  return text.replace(/[^\\u4e00-\\u9fa5\\w]/g, '-').toLowerCase();\n}\n\n// 生成唯一图表ID\nfunction generateChartId() {\n  return `chart-${globalChartIndex++}`;\n}\n\n// 渲染目录导航\nfunction renderTableOfContents(chapters) {\n  const tocItems = chapters.map((chapter, index) => `\n    <div class=\"toc-item\">\n      <span class=\"toc-number\">${index + 1}.</span>\n      <a href=\"#${generateId(chapter)}\" class=\"toc-link\">${chapter}</a>\n      <span class=\"toc-dots\"></span>\n      <span class=\"toc-page\">${index + 2}</span>\n    </div>\n  `).join('');\n\n  return `\n    <div class=\"page-section\">\n      <h2 class=\"section-title\">目录</h2>\n      <div class=\"toc-container\">\n        ${tocItems}\n        <div class=\"toc-item\">\n          <span class=\"toc-number\">附录.</span>\n          <a href=\"#data-sources\" class=\"toc-link\">数据来源</a>\n          <span class=\"toc-dots\"></span>\n          <span class=\"toc-page\">${chapters.length + 2}</span>\n        </div>\n      </div>\n    </div>\n  `;\n}\n\n\n\n// 智能内容渲染\nfunction renderContent(content, chapterName = '') {\n  if (!content) return '<p class=\"no-data\">暂无数据</p>';\n  \n  // 数组内容处理\n  if (Array.isArray(content)) {\n    // 企业列表特殊处理\n    if (chapterName.includes('企业') || chapterName.includes('公司')) {\n      return `\n        <div class=\"company-grid\">\n          ${content.map((item, index) => `\n            <div class=\"company-item company-${index % 4}\">\n              <div class=\"company-name\">${item}</div>\n            </div>\n          `).join('')}\n        </div>\n      `;\n    }\n    \n    // 一般列表内容\n    return `\n      <div class=\"content-list\">\n        ${content.map(item => `\n          <div class=\"list-item\">\n            <span class=\"bullet\">•</span>\n            <span class=\"item-text\">${item}</span>\n          </div>\n        `).join('')}\n      </div>\n    `;\n  }\n  \n  // 对象内容处理\n  if (typeof content === 'object' && content !== null) {\n    return Object.entries(content).map(([key, value]) => `\n      <div class=\"subsection\">\n        <h4 class=\"subsection-title\">${key}</h4>\n        <div class=\"subsection-content\">\n          ${renderContent(value, key)}\n        </div>\n      </div>\n    `).join('');\n  }\n  \n  // 文本内容处理\n  return `<div class=\"text-content\">${content}</div>`;\n}\n\n// 增强图表渲染函数\nfunction renderChart(chartKey, chartData) {\n  if (!chartData) {\n    console.log(`图表数据为空: ${chartKey}`);\n    return { html: '', script: '' };\n  }\n  \n  const chartId = generateChartId();\n  console.log(`渲染图表: ${chartKey}, ID: ${chartId}`, chartData);\n  \n  // SWOT分析特殊处理\n  if (chartKey === 'SWOT' || (chartData.优势 && chartData.劣势)) {\n    return renderSWOTChart(chartId, chartData, chartKey);\n  }\n  \n  // 检查常规图表数据\n  if (!chartData.values || !Array.isArray(chartData.values) || chartData.values.length === 0) {\n    console.log(`图表数据values无效: ${chartKey}`, chartData.values);\n    return { html: '', script: '' };\n  }\n  \n  const chartType = chartData.type || 'line';\n  const xAxisData = chartData.years || chartData.labels || chartData.categories || [];\n  \n  // 生成图表HTML\n  const chartHtml = `\n    <div class=\"chart-container\">\n      <div class=\"chart-title\">${chartKey}</div>\n      <div id=\"${chartId}\" class=\"chart-canvas\"></div>\n      ${chartData.interpretation ? `\n        <div class=\"chart-interpretation\">\n          <div class=\"interpretation-title\">图表解读</div>\n          <div class=\"interpretation-text\">${chartData.interpretation}</div>\n        </div>\n      ` : ''}\n    </div>\n  `;\n  \n  // 生成图表配置\n  const chartScript = generateChartScript(chartId, chartKey, chartData, chartType, xAxisData);\n  \n  return { html: chartHtml, script: chartScript };\n}\n\n// 生成图表脚本\nfunction generateChartScript(chartId, chartKey, chartData, chartType, xAxisData) {\n  const series = generateChartSeries(chartKey, chartData, chartType, xAxisData);\n  \n  return `\n    // 渲染图表: ${chartKey}\n    (function() {\n      try {\n        const chartElement = document.getElementById('${chartId}');\n        if (!chartElement) {\n          console.error('图表容器未找到: ${chartId}');\n          return;\n        }\n        \n        const chart = echarts.init(chartElement);\n        const option = {\n          tooltip: {\n            trigger: '${chartType === 'pie' ? 'item' : 'axis'}',\n            backgroundColor: 'rgba(255, 255, 255, 0.95)',\n            borderColor: '#e5e7eb',\n            borderWidth: 1,\n            textStyle: { color: '#374151', fontSize: 12 }\n          },\n          legend: {\n            show: ${chartType === 'pie' || (chartData.subSeries && Object.keys(chartData.subSeries).length > 0)},\n            bottom: '8%',\n            textStyle: { color: '#6b7280', fontSize: 11 }\n          },\n          ${chartType === 'pie' ? '' : `\n          grid: {\n            left: '8%',\n            right: '8%',\n            bottom: '15%',\n            top: '10%',\n            containLabel: true\n          },\n          xAxis: {\n            type: 'category',\n            data: ${JSON.stringify(xAxisData)},\n            axisLine: { lineStyle: { color: '#e5e7eb' } },\n            axisLabel: { \n              color: '#6b7280',\n              fontSize: 11,\n              rotate: ${xAxisData.length > 6 ? 30 : 0}\n            }\n          },\n          yAxis: {\n            type: 'value',\n            name: '${chartData.unit || ''}',\n            nameTextStyle: { color: '#6b7280', fontSize: 11 },\n            axisLine: { lineStyle: { color: '#e5e7eb' } },\n            axisLabel: { color: '#6b7280', fontSize: 11 },\n            splitLine: { lineStyle: { color: '#f3f4f6' } }\n          },`}\n          series: ${series}\n        };\n        \n        chart.setOption(option);\n        \n        // 响应式处理\n        window.addEventListener('resize', () => chart.resize());\n        \n        console.log('图表渲染成功: ${chartKey}');\n      } catch (error) {\n        console.error('图表渲染失败: ${chartKey}', error);\n      }\n    })();\n  `;\n}\n\n// 生成图表系列数据\nfunction generateChartSeries(chartKey, chartData, chartType, xAxisData) {\n  // 饼图处理\n  if (chartType === 'pie') {\n    const pieData = xAxisData.map((label, index) => ({\n      name: label,\n      value: chartData.values[index],\n      itemStyle: { color: config.colors[index % config.colors.length] }\n    }));\n    \n    return JSON.stringify([{\n      name: chartKey,\n      type: 'pie',\n      radius: ['45%', '75%'],\n      center: ['50%', '50%'],\n      data: pieData,\n      label: {\n        show: true,\n        formatter: '{b}: {c}' + (chartData.unit || ''),\n        color: '#374151',\n        fontSize: 11\n      },\n      emphasis: {\n        itemStyle: {\n          shadowBlur: 10,\n          shadowOffsetX: 0,\n          shadowColor: 'rgba(0, 0, 0, 0.5)'\n        }\n      }\n    }]);\n  }\n  \n  // 多系列数据处理\n  if (chartData.subSeries) {\n    const series = Object.entries(chartData.subSeries).map(([name, values], index) => ({\n      name,\n      type: chartType,\n      data: values,\n      itemStyle: { color: config.colors[index % config.colors.length] },\n      smooth: chartType === 'line',\n      showSymbol: chartType === 'line'\n    }));\n    return JSON.stringify(series);\n  }\n  \n  // 单系列数据\n  return JSON.stringify([{\n    name: chartKey,\n    type: chartType,\n    data: chartData.values,\n    itemStyle: { color: config.colors[0] },\n    smooth: chartType === 'line',\n    showSymbol: chartType === 'line'\n  }]);\n}\n\n// SWOT分析专用图表\nfunction renderSWOTChart(chartId, swotData, chartKey) {\n  const swotItems = [\n    { name: '优势', key: '优势', color: '#059669', items: swotData.优势 || [] },\n    { name: '劣势', key: '劣势', color: '#dc2626', items: swotData.劣势 || [] },\n    { name: '机会', key: '机会', color: '#2563eb', items: swotData.机会 || [] },\n    { name: '威胁', key: '威胁', color: '#d97706', items: swotData.威胁 || [] }\n  ];\n  \n  const chartHtml = `\n    <div class=\"chart-container\">\n      <div class=\"chart-title\">SWOT分析</div>\n      <div class=\"swot-layout\">\n        <div id=\"${chartId}\" class=\"swot-chart\"></div>\n        <div class=\"swot-details\">\n          ${swotItems.map(item => `\n            <div class=\"swot-category\" style=\"border-left-color: ${item.color}\">\n              <div class=\"swot-category-title\" style=\"color: ${item.color}\">\n                ${item.name} (${item.items.length}项)\n              </div>\n              <div class=\"swot-items\">\n                ${item.items.map(i => `<div class=\"swot-item\">• ${i}</div>`).join('')}\n              </div>\n            </div>\n          `).join('')}\n        </div>\n      </div>\n    </div>\n  `;\n  \n  const chartScript = `\n    // SWOT分析图表\n    (function() {\n      try {\n        const chartElement = document.getElementById('${chartId}');\n        if (!chartElement) {\n          console.error('SWOT图表容器未找到: ${chartId}');\n          return;\n        }\n        \n        const chart = echarts.init(chartElement);\n        const option = {\n          tooltip: {\n            trigger: 'item',\n            formatter: function(params) {\n              const items = ${JSON.stringify(swotItems)}[params.dataIndex].items;\n              return '<strong>' + params.name + '</strong><br/>' + \n                     items.slice(0, 3).map(i => '• ' + i).join('<br/>') +\n                     (items.length > 3 ? '<br/>...' : '');\n            }\n          },\n          legend: {\n            bottom: '5%',\n            textStyle: { color: '#6b7280', fontSize: 11 }\n          },\n          series: [{\n            type: 'pie',\n            radius: ['40%', '70%'],\n            center: ['50%', '45%'],\n            data: ${JSON.stringify(swotItems.map(item => ({\n              name: item.name,\n              value: item.items.length,\n              itemStyle: { color: item.color }\n            })))},\n            label: {\n              show: true,\n              formatter: '{b}: {c}项',\n              color: '#374151',\n              fontSize: 11\n            }\n          }]\n        };\n        \n        chart.setOption(option);\n        window.addEventListener('resize', () => chart.resize());\n        \n        console.log('SWOT图表渲染成功: ${chartId}');\n      } catch (error) {\n        console.error('SWOT图表渲染失败: ${chartId}', error);\n      }\n    })();\n  `;\n  \n  return { html: chartHtml, script: chartScript };\n}\n\n// 智能图表匹配\nfunction findMatchingChart(chapterName, chartData) {\n  // 精确匹配\n  if (chartData[chapterName]) {\n    return { key: chapterName, data: chartData[chapterName] };\n  }\n  \n  // 模糊匹配规则\n  const matchRules = {\n    '市场概述': ['市场规模', '总体规模', '整体规模'],\n    '竞争格局': ['市场份额', '份额分析', '竞争份额'],\n    '发展趋势': ['增长率', '增长趋势', '发展速度'],\n    '技术趋势': ['技术发展', '技术增长', '创新指数'],\n    '区域分析': ['地区分布', '区域占比', '地域分析']\n  };\n  \n  // 应用匹配规则\n  if (matchRules[chapterName]) {\n    for (const rule of matchRules[chapterName]) {\n      for (const [key, data] of Object.entries(chartData)) {\n        if (key.includes(rule) || rule.includes(key)) {\n          return { key, data };\n        }\n      }\n    }\n  }\n  \n  // 通用模糊匹配\n  for (const [key, data] of Object.entries(chartData)) {\n    if (chapterName.includes(key) || key.includes(chapterName)) {\n      return { key, data };\n    }\n  }\n  \n  return null;\n}\n\n// 渲染章节\nfunction renderChapter(chapterName, content, chartData) {\n  const chapterId = generateId(chapterName);\n  \n  // 查找匹配的图表\n  let matchedChart = null;\n  \n  // SWOT特殊处理\n  if (chapterName === 'SWOT' && typeof content === 'object' && content.优势) {\n    matchedChart = { key: 'SWOT', data: content };\n  } else {\n    // 常规图表匹配\n    matchedChart = findMatchingChart(chapterName, chartData);\n  }\n  \n  const hasChart = matchedChart && (\n    (matchedChart.data.values && matchedChart.data.values.length > 0) || \n    matchedChart.data.优势\n  );\n  \n  console.log(`章节: ${chapterName}, 匹配图表: ${matchedChart?.key}`, hasChart ? '有数据' : '无数据');\n  \n  // 渲染图表\n  let chartBlock = { html: '', script: '' };\n  if (hasChart) {\n    chartBlock = renderChart(matchedChart.key, matchedChart.data);\n    allChartScripts += chartBlock.script;\n    usedChartKeys.add(matchedChart.key);\n  }\n  \n  // 根据内容和图表决定布局\n  let sectionContent = '';\n  \n  // SWOT特殊处理：只显示图表，不显示重复的文字内容\n  if (chapterName === 'SWOT' && hasChart) {\n    sectionContent = `\n      <div class=\"single-column-layout\">\n        <div class=\"chart-section\">\n          ${chartBlock.html}\n        </div>\n      </div>\n    `;\n  } else if (hasChart && typeof content === 'string' && content.length > 300) {\n    // 图文并排布局\n    sectionContent = `\n      <div class=\"two-column-layout\">\n        <div class=\"text-column\">\n          ${renderContent(content, chapterName)}\n        </div>\n        <div class=\"chart-column\">\n          ${chartBlock.html}\n        </div>\n      </div>\n    `;\n  } else if (hasChart) {\n    // 图表在下方\n    sectionContent = `\n      <div class=\"single-column-layout\">\n        ${renderContent(content, chapterName)}\n        <div class=\"chart-section\">\n          ${chartBlock.html}\n        </div>\n      </div>\n    `;\n  } else {\n    // 纯文本布局\n    sectionContent = `\n      <div class=\"single-column-layout\">\n        ${renderContent(content, chapterName)}\n      </div>\n    `;\n  }\n  \n  return `\n    <div class=\"page-section\">\n      <h2 id=\"${chapterId}\" class=\"section-title\">${chapterName}</h2>\n      ${sectionContent}\n    </div>\n  `;\n}\n\n// ===== 主要渲染逻辑 =====\n\nconst chapters = reportData[\"目录\"] || Object.keys(reportData).filter(key => \n  !['目录', '图表数据', '数据来源'].includes(key)\n);\n\nconst chartData = reportData[\"图表数据\"] || {};\nlet allChapters = '';\n\n// 渲染所有章节\nchapters.forEach(chapterName => {\n  const content = reportData[chapterName];\n  if (content) {\n    allChapters += renderChapter(chapterName, content, chartData);\n  }\n});\n\n// 处理未被章节引用的独立图表\nObject.keys(chartData).forEach(chartKey => {\n  if (!usedChartKeys.has(chartKey)) {\n    const chartResult = renderChart(chartKey, chartData[chartKey]);\n    if (chartResult.html) {\n      allChapters += `\n        <div class=\"page-section\">\n          <h2 id=\"${generateId(chartKey)}\" class=\"section-title\">${chartKey}</h2>\n          <div class=\"single-column-layout\">\n            ${chartResult.html}\n          </div>\n        </div>\n      `;\n      allChartScripts += chartResult.script;\n    }\n  }\n});\n\n// 数据来源章节\nconst dataSourcesSection = `\n  <div class=\"page-section\">\n    <h2 id=\"data-sources\" class=\"section-title\">数据来源</h2>\n    <div class=\"data-sources-content\">\n      <div class=\"sources-grid\">\n        <div class=\"sources-column\">\n          <h4 class=\"sources-subtitle\">主要数据源</h4>\n          <ul class=\"sources-list\">\n            <li>公开互联网数据搜索</li>\n            <li>行业研究报告</li>\n            <li>政府统计数据</li>\n            <li>企业公开信息</li>\n          </ul>\n        </div>\n        <div class=\"sources-column\">\n          <h4 class=\"sources-subtitle\">研究方法</h4>\n          <ul class=\"sources-list\">\n            <li>AI智能数据采集</li>\n            <li>多源数据交叉验证</li>\n            <li>专业分析模型</li>\n            <li>可视化数据呈现</li>\n          </ul>\n        </div>\n      </div>\n      <div class=\"disclaimer\">\n        <strong>免责声明：</strong>本报告基于公开数据和AI分析生成，仅供参考。投资决策请结合专业咨询意见。\n      </div>\n    </div>\n  </div>\n`;\n\n// ===== 生成完整HTML =====\nconst finalHTML = `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>${config.title}</title>\n  <script src=\"https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js\"></script>\n  <style>\n    /* A4报告样式 */\n    * {\n      margin: 0;\n      padding: 0;\n      box-sizing: border-box;\n    }\n    \n    body {\n      font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;\n      line-height: 1.6;\n      color: #2d3748;\n      background: #ffffff;\n      font-size: 14px;\n    }\n    \n    .report-container {\n      max-width: 210mm;\n      margin: 0 auto;\n      padding: 20mm;\n      background: white;\n      box-shadow: 0 0 20px rgba(0,0,0,0.1);\n    }\n    \n    /* 报告头部 */\n    .report-header {\n      text-align: center;\n      margin-bottom: 40px;\n      padding-bottom: 30px;\n      border-bottom: 3px solid #2563eb;\n    }\n    \n    .report-title {\n      font-size: 28px;\n      font-weight: bold;\n      color: #1a202c;\n      margin-bottom: 15px;\n      line-height: 1.3;\n    }\n    \n    .report-subtitle {\n      font-size: 16px;\n      color: #4a5568;\n      margin-bottom: 10px;\n    }\n    \n    .report-date {\n      font-size: 14px;\n      color: #718096;\n    }\n    \n    /* 页面章节 */\n    .page-section {\n      margin-bottom: 40px;\n      page-break-inside: avoid;\n    }\n    \n    .section-title {\n      font-size: 20px;\n      font-weight: bold;\n      color: #2d3748;\n      margin-bottom: 20px;\n      padding-bottom: 8px;\n      border-bottom: 2px solid #e2e8f0;\n      position: relative;\n    }\n    \n    .section-title::before {\n      content: '';\n      position: absolute;\n      bottom: -2px;\n      left: 0;\n      width: 60px;\n      height: 2px;\n      background: #2563eb;\n    }\n    \n    /* 目录样式 */\n    .toc-container {\n      background: #f8fafc;\n      padding: 20px;\n      border-radius: 8px;\n      border: 1px solid #e2e8f0;\n    }\n    \n    .toc-item {\n      display: flex;\n      align-items: center;\n      padding: 8px 0;\n      border-bottom: 1px dotted #cbd5e1;\n    }\n    \n    .toc-item:last-child {\n      border-bottom: none;\n    }\n    \n    .toc-number {\n      font-weight: bold;\n      color: #2563eb;\n      min-width: 30px;\n    }\n    \n    .toc-link {\n      color: #2d3748;\n      text-decoration: none;\n      flex: 1;\n    }\n    \n    .toc-link:hover {\n      color: #2563eb;\n    }\n    \n    .toc-dots {\n      flex: 1;\n      border-bottom: 1px dotted #cbd5e1;\n      margin: 0 10px;\n      height: 1px;\n    }\n    \n    .toc-page {\n      color: #718096;\n      font-weight: bold;\n      min-width: 20px;\n      text-align: right;\n    }\n    \n    /* 布局样式 */\n    .single-column-layout {\n      width: 100%;\n    }\n    \n    .two-column-layout {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 30px;\n      align-items: start;\n    }\n    \n    .text-column {\n      padding-right: 15px;\n    }\n    \n    .chart-column {\n      padding-left: 15px;\n    }\n    \n    .chart-section {\n      margin-top: 25px;\n    }\n    \n\n    \n    /* 内容样式 */\n    .text-content {\n      text-align: justify;\n      line-height: 1.8;\n      color: #2d3748;\n      word-wrap: break-word;\n      word-break: normal;\n    }\n    \n    .content-list {\n      margin: 15px 0;\n    }\n    \n    .list-item {\n      display: flex;\n      align-items: flex-start;\n      margin-bottom: 8px;\n    }\n    \n    .bullet {\n      color: #2563eb;\n      font-weight: bold;\n      margin-right: 8px;\n      margin-top: 2px;\n      flex-shrink: 0;\n    }\n    \n    .item-text {\n      flex: 1;\n      line-height: 1.6;\n    }\n    \n    .subsection {\n      margin: 20px 0;\n    }\n    \n    .subsection-title {\n      font-size: 16px;\n      font-weight: bold;\n      color: #2d3748;\n      margin-bottom: 12px;\n      position: relative;\n      padding-left: 15px;\n    }\n    \n    .subsection-title::before {\n      content: '';\n      position: absolute;\n      left: 0;\n      top: 50%;\n      transform: translateY(-50%);\n      width: 4px;\n      height: 16px;\n      background: #2563eb;\n      border-radius: 2px;\n    }\n    \n    .subsection-content {\n      padding-left: 15px;\n    }\n    \n    /* 企业网格 */\n    .company-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n      gap: 12px;\n      margin: 15px 0;\n    }\n    \n    .company-item {\n      padding: 12px;\n      border-radius: 6px;\n      text-align: center;\n      border: 2px solid;\n      transition: all 0.3s ease;\n    }\n    \n    .company-0 { border-color: #2563eb; background: #eff6ff; }\n    .company-1 { border-color: #059669; background: #ecfdf5; }\n    .company-2 { border-color: #dc2626; background: #fef2f2; }\n    .company-3 { border-color: #d97706; background: #fffbeb; }\n    \n    .company-name {\n      font-weight: bold;\n      color: #1a202c;\n    }\n    \n    /* 图表样式 */\n    .chart-container {\n      background: #ffffff;\n      border: 1px solid #e2e8f0;\n      border-radius: 8px;\n      padding: 20px;\n      margin: 20px 0;\n    }\n    \n    .chart-title {\n      font-size: 16px;\n      font-weight: bold;\n      color: #2d3748;\n      text-align: center;\n      margin-bottom: 15px;\n      padding-bottom: 10px;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    .chart-canvas {\n      width: 100%;\n      height: 300px;\n      min-height: 300px;\n    }\n    \n    .chart-interpretation {\n      margin-top: 15px;\n      background: #f0f9ff;\n      border: 1px solid #bae6fd;\n      border-radius: 6px;\n      padding: 15px;\n    }\n    \n    .interpretation-title {\n      font-weight: bold;\n      color: #0369a1;\n      margin-bottom: 8px;\n    }\n    \n    .interpretation-text {\n      color: #0c4a6e;\n      line-height: 1.6;\n    }\n    \n    /* SWOT专用样式 */\n    .swot-layout {\n      display: block;\n      width: 100%;\n    }\n    \n    .swot-chart {\n      width: 100%;\n      height: 300px;\n      margin-bottom: 25px;\n    }\n    \n    .swot-details {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 20px;\n      max-width: 800px;\n      margin: 0 auto;\n    }\n    \n    .swot-category {\n      border-left: 4px solid;\n      padding: 15px;\n      margin-bottom: 0;\n      background: #f8fafc;\n      border-radius: 0 8px 8px 0;\n      box-shadow: 0 1px 3px rgba(0,0,0,0.1);\n      min-height: 120px;\n      display: flex;\n      flex-direction: column;\n    }\n    \n    .swot-category-title {\n      font-weight: bold;\n      margin-bottom: 12px;\n      font-size: 15px;\n      color: inherit;\n      flex-shrink: 0;\n    }\n    \n    .swot-items {\n      font-size: 13px;\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      justify-content: center;\n    }\n    \n    .swot-item {\n      margin-bottom: 6px;\n      color: #4a5568;\n      line-height: 1.5;\n      padding-left: 12px;\n      position: relative;\n    }\n    \n    .swot-item::before {\n      content: '•';\n      color: inherit;\n      position: absolute;\n      left: 0;\n      top: 0;\n      font-weight: bold;\n    }\n    \n    /* 数据来源样式 */\n    .data-sources-content {\n      background: #f8fafc;\n      border: 1px solid #e2e8f0;\n      border-radius: 8px;\n      padding: 20px;\n    }\n    \n    .sources-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: 30px;\n      margin-bottom: 20px;\n    }\n    \n    .sources-subtitle {\n      font-size: 16px;\n      font-weight: bold;\n      color: #2d3748;\n      margin-bottom: 12px;\n    }\n    \n    .sources-list {\n      list-style: none;\n    }\n    \n    .sources-list li {\n      padding: 4px 0;\n      padding-left: 15px;\n      position: relative;\n    }\n    \n    .sources-list li::before {\n      content: '•';\n      color: #2563eb;\n      font-weight: bold;\n      position: absolute;\n      left: 0;\n    }\n    \n    .disclaimer {\n      background: #fef3c7;\n      border: 1px solid #f59e0b;\n      border-radius: 6px;\n      padding: 15px;\n      color: #92400e;\n      font-size: 13px;\n      line-height: 1.5;\n    }\n    \n    .no-data {\n      color: #718096;\n      font-style: italic;\n      text-align: center;\n      padding: 20px;\n    }\n    \n    /* 移动端响应式设计 */\n    @media (max-width: 768px) {\n      .report-container {\n        max-width: 100%;\n        padding: 15px;\n        margin: 0;\n        box-shadow: none;\n      }\n      \n      .report-title {\n        font-size: 22px;\n        line-height: 1.2;\n      }\n      \n      .report-subtitle {\n        font-size: 14px;\n      }\n      \n      .section-title {\n        font-size: 18px;\n        margin-bottom: 15px;\n      }\n      \n      .two-column-layout {\n        display: block;\n      }\n      \n      .text-column,\n      .chart-column {\n        padding: 0;\n        margin-bottom: 20px;\n      }\n      \n      .sources-grid {\n        grid-template-columns: 1fr;\n        gap: 20px;\n      }\n      \n      .swot-layout {\n        display: block;\n      }\n      \n      .swot-chart {\n        margin-bottom: 20px;\n      }\n      \n      .swot-details {\n        grid-template-columns: 1fr;\n        gap: 15px;\n        max-width: none;\n      }\n      \n      .company-grid {\n        grid-template-columns: 1fr 1fr;\n        gap: 10px;\n      }\n      \n      .chart-canvas {\n        height: 250px;\n        min-height: 250px;\n      }\n      \n      .swot-chart {\n        height: 250px;\n      }\n      \n      .toc-container {\n        padding: 15px;\n      }\n      \n      .toc-item {\n        flex-wrap: wrap;\n      }\n      \n      .toc-dots {\n        display: none;\n      }\n      \n      .toc-page {\n        margin-left: auto;\n      }\n    }\n    \n    /* 小屏幕设备 */\n    @media (max-width: 480px) {\n      .report-container {\n        padding: 10px;\n      }\n      \n      .report-title {\n        font-size: 20px;\n      }\n      \n      .section-title {\n        font-size: 16px;\n      }\n      \n      .company-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      .chart-canvas {\n        height: 200px;\n        min-height: 200px;\n      }\n      \n      .swot-chart {\n        height: 200px;\n      }\n      \n      .swot-category {\n        min-height: auto;\n        padding: 12px;\n      }\n      \n      .swot-category-title {\n        font-size: 14px;\n      }\n      \n      .swot-items {\n        font-size: 12px;\n      }\n    }\n    \n    /* 打印样式 */\n    @media print {\n      body { background: white; }\n      .report-container { \n        box-shadow: none; \n        max-width: none;\n        padding: 15mm;\n      }\n      .page-section { page-break-inside: avoid; }\n      .chart-container { page-break-inside: avoid; }\n    }\n    \n    /* 平滑滚动 */\n    html { scroll-behavior: smooth; }\n  </style>\n</head>\n<body>\n  <div class=\"report-container\">\n    \n    <!-- 报告头部 -->\n    <header class=\"report-header\">\n      <h1 class=\"report-title\">${config.title}</h1>\n      <p class=\"report-subtitle\">AI智能生成 · 专业分析报告</p>\n      <p class=\"report-date\">生成时间：${config.generateTime}</p>\n    </header>\n    \n    <!-- 目录 -->\n    ${renderTableOfContents(chapters)}\n    \n    <!-- 报告主体 -->\n    <main>\n      ${allChapters}\n      ${dataSourcesSection}\n    </main>\n    \n    <!-- 页脚 -->\n    <footer style=\"text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e2e8f0; color: #718096; font-size: 12px;\">\n      <p>本报告由AI智能生成，数据来源于公开信息 | 图表数量: ${globalChartIndex} | © ${new Date().getFullYear()} 行业研究报告生成器</p>\n    </footer>\n    \n  </div>\n  \n  <script>\n    // 等待DOM加载完成\n    document.addEventListener('DOMContentLoaded', function() {\n      console.log('🚀 开始渲染专业咨询报告...');\n      console.log('📊 预期图表数量:', ${globalChartIndex});\n      \n      // 检查ECharts是否加载\n      if (typeof echarts === 'undefined') {\n        console.error('❌ ECharts未加载，图表将无法显示');\n        return;\n      }\n      \n      console.log('✅ ECharts已加载，开始渲染图表...');\n      \n      // 渲染所有图表\n      ${allChartScripts}\n      \n      console.log('✅ 专业咨询报告已生成完成');\n      console.log('📊 实际图表数量:', ${globalChartIndex});\n      console.log('📝 章节数量:', ${chapters.length});\n    });\n  </script>\n</body>\n</html>`;\n\n// 返回结果给n8n\nreturn {\n  json: {\n    html: finalHTML,\n    metadata: {\n      title: config.title,\n      generateTime: config.generateTime,\n      chaptersCount: chapters.length,\n      chartsCount: globalChartIndex,\n      usedChartKeys: Array.from(usedChartKeys)\n    }\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2760, -180], "id": "507886f4-8757-4e18-970f-31b0bee781bd", "name": "html"}, {"parameters": {"operation": "write", "fileName": "./report.html", "options": {"append": false}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [3192, -80], "id": "beb7a23f-0e2c-42a7-9cbc-cc2910b5649f", "name": "Read/Write Files from Disk", "notesInFlow": false}, {"parameters": {"jsCode": "// 获取HTML内容，支持多种数据结构\nlet html;\nif (items[0].json.output) {\n  html = items[0].json.output;\n} else if (items[0].json.html) {\n  html = items[0].json.html;\n} else if (items[0].json.data) {\n  html = items[0].json.data;\n} else {\n  // 如果都没有，尝试获取整个json作为字符串\n  html = JSON.stringify(items[0].json);\n}\n\n// 确保html是字符串类型\nif (typeof html !== 'string') {\n  html = String(html);\n}\n\n// 移除Markdown代码块标记并清理内容\nconst cleanHtml = html.replace(/^```html\\s*|```\\s*$/g, \"\").trim();\n// 创建二进制数据\nreturn {\n  json: {\n    data: cleanHtml\n  },\n  binary: {\n    data: {\n      data: Buffer.from(cleanHtml).toString('base64'),\n      mimeType: 'text/html',\n      fileName: 'report.html'\n    }\n  }\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2972, -80], "id": "69040ef7-3c6a-475b-a0f3-1af375731e1a", "name": "BinaryFile"}, {"parameters": {"promptType": "define", "text": "=请基于以下多条行业摘要，撰写一份结构化、详细、适合可视化的行业研究报告，只输出一个完整的 JSON 对象，内容如下：\n\n{{ $json.allSummaries }}\n\n要求：\n1. 只输出单一有效 JSON，不要输出任何解释、代码块标记、数组或多余内容。\n2. 报告分为多个章节：市场概述、细分市场、区域分析、竞争格局、SWOT、政策环境、技术趋势、未来预测等。\n3. 每章节内容不少于200字，包含真实数据和引用。\n4. 输出格式为严格有效的JSON，结构如下\n{\n\"目录\": [\"市场概述\", \"细分市场\", ...],\n\"市场概述\": \"...\",\n\"细分市场\": [\"...\", \"...\"],\n\"区域分析\": [\"...\", \"...\"],\n\"竞争格局\": {\"主要企业\": [...], \"市场份额\": [...]},\n\"SWOT\": {\"优势\": [...], \"劣势\": [...], \"机会\": [...], \"威胁\": [...]},\n\"政策环境\": \"...\",\n\"技术趋势\": \"...\",\n\"未来预测\": \"...\",\n\"图表数据\": {\n\"市场规模\": {\n\"type\": \"line\",  // 图表类型：line, bar, pie, scatter\n\"years\": [2020, 2021, 2022, 2023],\n\"values\": [1000, 1500, 2000, 2800],\n\"unit\": \"亿元\",\n\"interpretation\": \"市场规模持续增长，年复合增长率达到40%\",\n\"subSeries\": {  // 可选：多系列数据\n\"中国\": [500, 800, 1200, 1600],\n\"美国\": [300, 400, 500, 700]\n}\n},\n\"市场份额\": {...}, \"增长率\": {...}, ...\n}\n}\n5. 图表数据要求：\n   - 每个图表必须包含interpretation字段，提供专业解读\n   - 数据来源要明确，数值要真实可信\n   - 支持多维度对比（如地区、时间、产品类别）\n   - 图表类型要与数据特性匹配\n\n6. 内容长度控制：\n   - 短内容（<120字）将自动渲染为卡片样式\n   - 数组内容将渲染为标签卡片\n   - 长文本保持段落格式\n7. 内容必须为中文。\n8. 如果无法生成完整内容，也必须输出结构完整的 JSON，字段内容可为空字符串或空数组。\n\n", "hasOutputParser": true, "options": {"systemMessage": "=你是一名资深行业分析师，擅长将多条行业摘要整合为一份结构化、可视化友好的行业研究报告。你的输出必须是单一的、完整的 JSON 对象，不允许有任何解释、代码块标记、数组包裹或多余内容。"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [2380, -180], "id": "8ed66be6-2c3a-4ad7-8237-8fb352c8a531", "name": "OpenAI"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n    \"type\": \"object\",\n    \"properties\": {\n      \"目录\": {\n        \"type\": \"array\",\n        \"items\": { \"type\": \"string\" },\n        \"description\": \"报告章节目录\"\n      },\n      \"市场概述\": { \"type\": \"string\", \"description\": \"行业整体情况摘要\" },\n      \"细分市场\": {\n        \"type\": \"array\",\n        \"items\": { \"type\": \"string\" },\n        \"description\": \"行业细分市场分析\"\n      },\n      \"区域分析\": {\n        \"type\": \"array\",\n        \"items\": { \"type\": \"string\" },\n        \"description\": \"不同地区的市场表现\"\n      },\n      \"竞争格局\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"主要企业\": {\n            \"type\": \"array\",\n            \"items\": { \"type\": \"string\" },\n            \"description\": \"行业主要企业\"\n          },\n          \"市场份额\": {\n            \"type\": \"array\",\n            \"items\": { \"type\": \"string\" },\n            \"description\": \"主要企业市场份额描述\"\n          }\n        },\n        \"required\": [\"主要企业\", \"市场份额\"]\n      },\n      \"SWOT\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"优势\": { \"type\": \"array\", \"items\": { \"type\": \"string\" } },\n          \"劣势\": { \"type\": \"array\", \"items\": { \"type\": \"string\" } },\n          \"机会\": { \"type\": \"array\", \"items\": { \"type\": \"string\" } },\n          \"威胁\": { \"type\": \"array\", \"items\": { \"type\": \"string\" } }\n        },\n        \"required\": [\"优势\", \"劣势\", \"机会\", \"威胁\"]\n      },\n      \"政策环境\": { \"type\": \"string\", \"description\": \"行业相关政策环境\" },\n      \"技术趋势\": { \"type\": \"string\", \"description\": \"行业技术发展趋势\" },\n      \"未来预测\": { \"type\": \"string\", \"description\": \"未来3-5年行业预测\" },\n      \"图表数据\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"市场规模\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"type\": { \"type\": \"string\", \"enum\": [\"line\", \"bar\", \"pie\", \"scatter\"], \"description\": \"图表类型\" },\n              \"years\": { \"type\": \"array\", \"items\": { \"type\": [\"string\", \"number\"] }, \"description\": \"年份或时间轴\" },\n              \"values\": { \"type\": \"array\", \"items\": { \"type\": \"number\" }, \"description\": \"主要数据值\" },\n              \"unit\": { \"type\": \"string\", \"description\": \"数据单位\" },\n              \"interpretation\": { \"type\": \"string\", \"description\": \"图表解读说明\" },\n              \"subSeries\": {\n                \"type\": \"object\",\n                \"additionalProperties\": {\n                  \"type\": \"array\",\n                  \"items\": { \"type\": \"number\" }\n                },\n                \"description\": \"多系列数据，如不同地区或分类的数据\"\n              }\n            },\n            \"required\": [\"values\", \"unit\"]\n          },\n          \"市场份额\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"type\": { \"type\": \"string\", \"enum\": [\"line\", \"bar\", \"pie\", \"scatter\"], \"description\": \"图表类型\" },\n              \"labels\": { \"type\": \"array\", \"items\": { \"type\": \"string\" }, \"description\": \"标签或分类\" },\n              \"values\": { \"type\": \"array\", \"items\": { \"type\": \"number\" }, \"description\": \"数据值\" },\n              \"unit\": { \"type\": \"string\", \"description\": \"数据单位\" },\n              \"interpretation\": { \"type\": \"string\", \"description\": \"图表解读说明\" }\n            },\n            \"required\": [\"labels\", \"values\", \"unit\"]\n          },\n          \"增长率\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"type\": { \"type\": \"string\", \"enum\": [\"line\", \"bar\", \"pie\", \"scatter\"], \"description\": \"图表类型\" },\n              \"years\": { \"type\": \"array\", \"items\": { \"type\": [\"string\", \"number\"] }, \"description\": \"年份\" },\n              \"values\": { \"type\": \"array\", \"items\": { \"type\": \"number\" }, \"description\": \"增长率数据\" },\n              \"unit\": { \"type\": \"string\", \"description\": \"数据单位\" },\n              \"interpretation\": { \"type\": \"string\", \"description\": \"图表解读说明\" }\n            },\n            \"required\": [\"years\", \"values\", \"unit\"]\n          }\n        },\n        \"additionalProperties\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"type\": { \"type\": \"string\", \"enum\": [\"line\", \"bar\", \"pie\", \"scatter\"], \"description\": \"图表类型\" },\n            \"years\": { \"type\": \"array\", \"items\": { \"type\": [\"string\", \"number\"] }, \"description\": \"年份或时间轴\" },\n            \"labels\": { \"type\": \"array\", \"items\": { \"type\": \"string\" }, \"description\": \"标签或分类\" },\n            \"categories\": { \"type\": \"array\", \"items\": { \"type\": \"string\" }, \"description\": \"分类\" },\n            \"values\": { \"type\": \"array\", \"items\": { \"type\": \"number\" }, \"description\": \"数据值\" },\n            \"unit\": { \"type\": \"string\", \"description\": \"数据单位\" },\n            \"interpretation\": { \"type\": \"string\", \"description\": \"图表解读说明\" },\n            \"subSeries\": {\n              \"type\": \"object\",\n              \"additionalProperties\": {\n                \"type\": \"array\",\n                \"items\": { \"type\": \"number\" }\n              },\n              \"description\": \"多系列数据\"\n            }\n          },\n          \"required\": [\"values\", \"unit\"]\n        },\n        \"required\": [\"市场规模\", \"市场份额\", \"增长率\"]\n      }\n    },\n    \"required\": [\n      \"目录\", \"市场概述\", \"细分市场\", \"区域分析\", \"竞争格局\", \"SWOT\",\n      \"政策环境\", \"技术趋势\", \"未来预测\", \"图表数据\"\n    ]\n  }"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [2524, 40], "id": "c0fe9930-aa24-4010-8675-06ae85483550", "name": "Structured Output Parser"}, {"parameters": {"jsCode": "// 2.1_node(SetParams_Enhanced) - 简化修复版\n// 专门解决\"跨境物流\"输入问题\n\n// 直接从第一个输入项获取数据\nconst items = $input.all();\nconst firstItem = items[0];\nconst inputData = firstItem.json;\n\n// 详细调试输出\nconsole.log('=== 调试信息 ===');\nconsole.log('输入项数量:', items.length);\nconsole.log('第一项数据:', JSON.stringify(inputData, null, 2));\nconsole.log('industry字段:', inputData.industry);\nconsole.log('industry类型:', typeof inputData.industry);\n\n// 获取行业信息 - 多种尝试方式\nlet industry = '';\n\n// 方式1: 直接获取industry字段\nif (inputData.industry) {\n  industry = String(inputData.industry).trim();\n  console.log('方式1成功 - industry:', industry);\n}\n\n// 方式2: 如果没有industry，尝试其他字段\nif (!industry && inputData.chatInput) {\n  industry = String(inputData.chatInput).trim();\n  console.log('方式2成功 - chatInput:', industry);\n}\n\n// 方式3: 尝试query字段\nif (!industry && inputData.query) {\n  industry = String(inputData.query).trim();\n  console.log('方式3成功 - query:', industry);\n}\n\n// 方式4: 尝试keyword字段\nif (!industry && inputData.keyword) {\n  industry = String(inputData.keyword).trim();\n  console.log('方式4成功 - keyword:', industry);\n}\n\nconsole.log('最终industry值:', industry);\nconsole.log('industry长度:', industry.length);\n\n// 如果还是没有获取到行业信息\nif (!industry) {\n  console.log('错误：未能获取到行业信息');\n  return [{\n    json: {\n      error: true,\n      message: '未能获取到行业关键词',\n      debug: {\n        inputData: inputData,\n        allKeys: Object.keys(inputData),\n        industryField: inputData.industry,\n        chatInputField: inputData.chatInput,\n        queryField: inputData.query,\n        keywordField: inputData.keyword\n      }\n    }\n  }];\n}\n\n// 获取其他参数\nconst region = String(inputData.region || '中国').trim();\nconst timeRange = String(inputData.timeRange || '2024-2025').trim();\nconst subSector = String(inputData.subSector || '').trim();\nconst reportVersion = inputData.reportVersion || 'enhanced';\nconst reportDepth = inputData.reportDepth || 'comprehensive';\nconst analysisType = inputData.analysisType || 'full';\nconst outputLanguage = inputData.outputLanguage || 'zh-CN';\nconst includeCharts = inputData.includeCharts !== false;\n\nconsole.log('所有参数获取完成');\nconsole.log('industry:', industry);\nconsole.log('region:', region);\nconsole.log('timeRange:', timeRange);\n\n// 生成搜索关键词\nconst searchVariants = [\n  `${industry} 市场规模 ${timeRange}`,\n  `${industry} 行业分析报告 ${region}`,\n  `${industry} 产业发展现状`,\n  `${industry} 市场容量 统计数据`,\n  `${industry} 竞争格局 主要企业`,\n  `${industry} 市场份额 领军企业`,\n  `${industry} 行业集中度分析`,\n  `${industry} 企业排名 ${timeRange}`,\n  `${industry} 技术发展趋势`,\n  `${industry} 创新技术 新兴技术`,\n  `${industry} 数字化转型`,\n  `${industry} 政策支持 国家政策`,\n  `${industry} 行业标准 监管政策`,\n  `${industry} 产业政策 ${region}`,\n  `${industry} 投资趋势 融资情况`,\n  `${industry} 供应链分析`,\n  `${industry} 产业链上下游`,\n  `${industry} ${region} 发展情况`,\n  `${industry} 区域分布 地域特色`,\n  `${industry} 未来展望 发展前景`\n];\n\n// 生成章节配置\nconst sections = [\n  '市场概述', '细分市场', '区域分析', '竞争格局', \n  'SWOT分析', '技术趋势', '政策环境', '投资分析', \n  '供应链分析', '风险评估', '未来预测', '战略建议'\n];\n\n// 生成搜索配置\nconst searchConfig = {\n  maxResults: 30,\n  includeImages: includeCharts,\n  searchDepth: reportDepth,\n  timeRange: timeRange,\n  region: region\n};\n\nconsole.log('生成搜索词数量:', searchVariants.length);\nconsole.log('生成章节数量:', sections.length);\n\n// 返回成功结果\nconst result = {\n  json: {\n    // 基础参数\n    industry: industry,\n    region: region,\n    timeRange: timeRange,\n    subSector: subSector,\n    \n    // 增强版参数\n    reportVersion: reportVersion,\n    reportDepth: reportDepth,\n    analysisType: analysisType,\n    outputLanguage: outputLanguage,\n    includeCharts: includeCharts,\n    sections: sections,\n    \n    // 搜索配置\n    searchVariants: searchVariants,\n    searchConfig: searchConfig,\n    \n    // 元数据\n    timestamp: new Date().toISOString(),\n    configVersion: '2.1-simple-fixed',\n    estimatedTokens: 25000,\n    \n    // 状态\n    error: false,\n    message: `参数设置成功 - 行业: ${industry}`,\n    \n    // 调试信息\n    debug: {\n      originalInput: inputData,\n      industrySource: 'industry字段',\n      searchVariantsCount: searchVariants.length,\n      sectionsCount: sections.length,\n      processingTime: new Date().toISOString()\n    }\n  }\n};\n\nconsole.log('返回结果:', JSON.stringify(result, null, 2));\n\nreturn [result];\n\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [240, -180], "id": "37b1bbe7-55a5-4ceb-a62e-ebec4e4ec805", "name": "Set"}, {"parameters": {"query": "={{ $json.searchVariants }}行业分析 市场数据 {{ $('Set').item.json.timeRange }}", "options": {"search_depth": "advanced", "max_results": 10, "include_raw_content": true, "include_images": false}}, "type": "@tavily/n8n-nodes-tavily.tavily", "typeVersion": 1, "position": [680, -280], "id": "076a3a11-fa83-462f-ab7a-842452e76967", "name": "<PERSON><PERSON>Sear<PERSON>", "credentials": {"tavilyApi": {"id": "MYBbUyqdPTUvZuXR", "name": "Tavily account"}}}, {"parameters": {"url": "https://api.302.ai/searchapi/search", "sendQuery": true, "queryParameters": {"parameters": [{"name": "q", "value": "={{ $json.searchVariants }}"}, {"name": "=region", "value": "={{ $('Set').item.json.region }}"}, {"name": "timeRange", "value": "={{ $('Set').item.json.timestamp }}"}, {"name": "num", "value": "10"}, {"name": "api_key", "value": "sk-LMfpbQt8WPp71EXwFmW6fedhTJX9J4lag5znVNRReG5fWPSc"}, {"name": "engine", "value": "google"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [680, -80], "id": "96b8d9cc-3a41-4609-aae1-8e89642bf784", "name": "HTTP Request"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [900, -180], "id": "fd519969-cdc7-42f1-a08d-787fdbf8d6d5", "name": "<PERSON><PERSON>"}, {"parameters": {"fieldToSplitOut": "searchVariants", "include": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldsToInclude": "={{ $json.region }}{{ $json.timestamp }}", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [460, -80], "id": "a2326edc-3ffa-448f-84e9-041f7262bc0f", "name": "Split Out"}, {"parameters": {"jsCode": "// 4b_node(CleanMergeData)\n// 作用：清洗和合并 Tavily、302ai 等多源搜索数据，提取主要内容，输出结构化 JSON，便于 LLM 分析\n// 输入：Merge 节点输出的原始大 JSON\n// 输出：{ allContents: [...], raw: {...} }\n\nconst input = $json;\nlet allContents = [];\n\n// 递归提取所有 results/organic_results/snippet/content/title 字段\nfunction extractText(obj) {\n  if (Array.isArray(obj)) {\n    obj.forEach(item => extractText(item));\n  } else if (typeof obj === 'object' && obj !== null) {\n    for (const key in obj) {\n      if ([\"content\", \"snippet\", \"title\", \"description\"].includes(key) && typeof obj[key] === 'string') {\n        allContents.push(obj[key]);\n      } else if (Array.isArray(obj[key]) || typeof obj[key] === 'object') {\n        extractText(obj[key]);\n      }\n    }\n  }\n}\n\nextractText(input);\n\n// 去重、去空\nallContents = Array.from(new Set(allContents)).filter(Boolean);\n\nreturn [{\n  json: {\n    allContents,\n    raw: input // 保留原始数据，便于 LLM 按需引用\n  }\n}]; "}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1120, -180], "id": "511b390e-39ef-492e-8459-4a442eab5ce2", "name": "数据清洗"}, {"parameters": {"fieldToSplitOut": "allContents", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1340, -180], "id": "0ae02c2b-4b17-4c16-8733-bbe11af70549", "name": "Split Out1"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1560, -180], "id": "3e21f4fa-fbeb-4cbf-9b73-26122df0859a", "name": "Wait", "webhookId": "7e55e133-d805-48fb-83e8-888fa62a1873"}, {"parameters": {"options": {"maxTokens": 8192, "temperature": 0.3, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [1868, 40], "id": "82079a85-a794-4df1-9cd3-47acd71b06a2", "name": "DeepSeek Chat Model2", "credentials": {"deepSeekApi": {"id": "IKqWJBBXULhRu8mZ", "name": "DeepSeek account"}}}, {"parameters": {"promptType": "define", "text": "=请对以下原始行业内容进行专业中文摘要，要求：\n1. 摘要应涵盖所有关键信息、数据、趋势、企业、市场规模等要素。\n2. 摘要不少于200字，内容准确、客观、无主观臆断。\n3. 只输出摘要正文，不要输出任何解释、格式说明或多余内容。\n\n原始内容如下：\n{{ $json.allContents }}", "messages": {"messageValues": [{"message": "=你是一名专业的行业分析师，擅长从原始互联网数据中提炼关键信息。你的任务是对输入的行业原文内容进行专业、准确、结构化的中文摘要，便于后续归纳和分析。请确保摘要内容简明扼要、信息密集、无废话。"}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [1780, -180], "id": "2321dde9-22c3-48fe-9703-3ed24c6bda62", "name": "分批摘要"}, {"parameters": {"options": {"maxTokens": 8192, "responseFormat": "json_object", "temperature": 0.4, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [2404, 40], "id": "37301e65-b89f-4a33-b6df-02dda2b5905a", "name": "DeepSeek Chat Model", "credentials": {"deepSeekApi": {"id": "IKqWJBBXULhRu8mZ", "name": "DeepSeek account"}}}, {"parameters": {"jsCode": "// 合并所有输入项为一个 allSummaries 数组，只输出一个 item\nreturn [{\n  json: {\n    allSummaries: $items().map(item => item.json.summary || item.json.output || item.json.text || item.json)\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2156, -180], "id": "11b1eafd-4db4-44d9-a0eb-6e6322c8241a", "name": "allSumaries"}, {"parameters": {"method": "POST", "url": "https://mcp-on-edge.edgeone.site/mcp-server", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content_Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"jsonrpc\": \"2.0\",\n  \"id\": 1,\n  \"method\": \"tools/call\",\n  \"params\": {\n    \"name\": \"deploy-html\",\n    \"arguments\": {\n      \"value\": \"{{ JSON.stringify($node['html'].json.html).slice(1, -1) }}\"\n    }\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2972, -280], "id": "ccd01a3d-8955-42f0-85b4-2cd54eb8a7ae", "name": "发布"}, {"parameters": {"httpMethod": "POST", "path": "/industry-report", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [40, -180], "id": "b3062793-3dd7-458b-b8c5-ff4e0592be5f", "name": "Webhook", "webhookId": "7cd2e465-8b72-4d5f-9edd-dcedd83c096c"}], "connections": {"html": {"main": [[{"node": "BinaryFile", "type": "main", "index": 0}, {"node": "发布", "type": "main", "index": 0}]]}, "BinaryFile": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "html", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "OpenAI", "type": "ai_outputParser", "index": 0}]]}, "Set": {"main": [[{"node": "Split Out", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>Sear<PERSON>", "type": "main", "index": 0}]]}, "TavilySearch": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "数据清洗", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "数据清洗": {"main": [[{"node": "Split Out1", "type": "main", "index": 0}]]}, "Split Out1": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "分批摘要", "type": "main", "index": 0}]]}, "DeepSeek Chat Model2": {"ai_languageModel": [[{"node": "分批摘要", "type": "ai_languageModel", "index": 0}]]}, "分批摘要": {"main": [[{"node": "allSumaries", "type": "main", "index": 0}]]}, "DeepSeek Chat Model": {"ai_languageModel": [[{"node": "OpenAI", "type": "ai_languageModel", "index": 0}]]}, "allSumaries": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}