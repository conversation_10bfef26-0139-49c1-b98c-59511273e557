{"name": "7-crawl4ai_flow", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1984, 400], "id": "0b1374a6-8fde-4225-b5df-fe2790cb4592", "name": "When chat message received", "webhookId": "857a9074-d9ed-41be-8110-fe337e61eacd"}, {"parameters": {"url": "={{ $json.chatInput }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1760, 400], "id": "4c774495-4ffb-4807-81ff-d966e4078296", "name": "HTTP Request"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.xml", "typeVersion": 1, "position": [-1536, 400], "id": "d0727167-9c60-47ba-8e85-180cd94330aa", "name": "XML"}, {"parameters": {"fieldToSplitOut": "urlset.url", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-1328, 400], "id": "c633495b-4121-421a-8a70-2df100d0ffbe", "name": "Split Out"}, {"parameters": {"maxItems": 2}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [-1104, 400], "id": "1098ecdc-cc22-4b81-b602-353b7cedb74a", "name": "Limit"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-880, 400], "id": "60c4ec88-fb73-4e2b-beae-7bf86ce5e2f9", "name": "Loop Over Items"}, {"parameters": {"method": "POST", "url": "http://host.docker.internal:11235/crawl", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyParameters": {"parameters": [{"name": "urls", "value": "={{ $json.loc }}"}, {"name": "priority", "value": "10"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-656, 464], "id": "6d089593-9779-45c6-8fc0-3886d50feb65", "name": "HTTP Request1", "credentials": {"httpHeaderAuth": {"id": "STWPHcpegtwciX3H", "name": "Header Auth account"}}}, {"parameters": {"amount": 7}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-656, 256], "id": "6651f636-8c83-4f9a-b0d0-ac8ad111a712", "name": "Wait", "webhookId": "c6c6d7cd-f551-488b-b243-e2583072d758"}, {"parameters": {"url": "=http://host.docker.internal:11235/task/{{ $json.task_id }}", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-448, 160], "id": "cc5d624d-0bd8-438e-b3c6-45600bd6cab5", "name": "HTTP Request2", "credentials": {"httpHeaderAuth": {"id": "STWPHcpegtwciX3H", "name": "Header Auth account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "be7e21a2-123c-4c78-bdcc-9992425ccc13", "leftValue": "={{ $json.status }}", "rightValue": "completed", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-224, 160], "id": "694a69e0-afed-4040-98d3-96d0de389bd1", "name": "If"}, {"parameters": {"promptType": "define", "text": "=角色设定:\n你是一名信息结构化和知识库开发的专家, 请始终保持专业态度。你的任务是将 markdown 数据整理为适合 LLM 驱动的 RAG 知识库的结构化、易读格式。\n\n任务要求:\n1. 内容解析\n  - 识别 markdown 数据中的关键内容和主要结构。\n2. 结构化整理\n  - 以清晰的标题和分层逻辑组织信息, 使其易于检索和理解。\n  - 保留所有可能对回答用户查询有价值的细节。\n3. 创建 FAQ (如适用)\n  - 根据内容提炼出常见问题, 并提供清晰、直接的解答。\n4. 提升可读性\n  - 采用项目符号、编号列表、段落分隔等格式优化排版, 使内容更直观。\n5. 优化输出\n  - 严格去除 AI 生成的附加说明, 仅保留清理后的核心数据。\n\n响应规则:\n1. 完整性: 确保所有相关信息完整保留, 避免丢失对搜索和理解有价值的内容。\n2. 精准性: FAQ 需紧密围绕内容, 确保清晰、简洁且符合用户需求。\n3. 结构化优: 确保最终输出便于分块存储、向量化处理, 并支持高效检索。\n\n数据输入:\n\n<markdown>{{ $json.result.markdown }}</markdown> \n\n", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [0, 0], "id": "2f728445-965d-489b-922a-2a5e63a4a1ca", "name": "AI Agent"}, {"parameters": {"assignments": {"assignments": [{"id": "924399ec-5672-47e1-8a23-4d1871389453", "name": "task_id", "value": "={{ $('HTTP Request1').item.json.task_id }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [80, 400], "id": "9a350efb-7c65-4f94-9cd3-c93d5cbfe6cd", "name": "<PERSON>"}, {"parameters": {"operation": "toText", "sourceProperty": "output", "options": {"fileName": "={{ $json.output.split('\\n')[0] }}.md"}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [384, 112], "id": "0d58ba4d-9e91-49e3-8e86-4eefed2fb16f", "name": "Convert to File"}, {"parameters": {"operation": "write", "fileName": "=/home/<USER>/{{ new Date().toISOString() }}.md", "options": {}}, "type": "n8n-nodes-base.readWriteFile", "typeVersion": 1, "position": [608, 112], "id": "0c6288bc-1e55-4ddc-a7e5-5a4ab19a356a", "name": "Read/Write Files from Disk"}, {"parameters": {"options": {"maxOutputTokens": 2048, "temperature": 0.4, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [16, 176], "id": "44b3ab2e-7d0a-4298-8c6e-299f2e6bfd3f", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "cdwHRnakv6TIFeEn", "name": "Google Gemini(PaLM) Api account"}}}], "connections": {"When chat message received": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "XML", "type": "main", "index": 0}]]}, "XML": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Wait", "type": "main", "index": 0}], [{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "HTTP Request2", "type": "main", "index": 0}]]}, "HTTP Request2": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}], [{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Read/Write Files from Disk", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}