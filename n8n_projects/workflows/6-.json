{"name": "6-定时新闻简报", "nodes": [{"parameters": {"url": "https://hnrss.org/newest?points=10&q=n8n", "options": {}}, "type": "n8n-nodes-base.rssFeedRead", "typeVersion": 1.2, "position": [-420, -60], "id": "ddcb7640-187e-4561-aa67-19e8a4fdd2aa", "name": "RSS Read"}, {"parameters": {"resource": "<PERSON><PERSON><PERSON>", "operation": "Scrape A Url And Get Its Content", "url": "={{ $json.link }}", "formats": ["markdown"], "requestOptions": {}}, "type": "n8n-nodes-firecrawl.fireCrawl", "typeVersion": 1, "position": [20, -60], "id": "6d534408-78be-4e6d-9b6d-1ac3291cfbc6", "name": "Scrape A Url And Get Its Content", "credentials": {"fireCrawlApi": {"id": "515b3xTXkkCaHkpz", "name": "FireCrawl account"}}}, {"parameters": {"maxItems": 5}, "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [-200, -60], "id": "ea5e0198-0435-425b-9b84-5782d463d934", "name": "Limit"}, {"parameters": {"modelName": "models/gemini-2.0-flash", "options": {"maxOutputTokens": 4000, "temperature": 0.4, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [328, 160], "id": "23f87e2a-ba13-44dd-8e6b-3b3f954c2bcc", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "cdwHRnakv6TIFeEn", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "<EMAIL>, <EMAIL>", "subject": "新闻简报", "html": "={{ $json.html }}", "options": {}}, "type": "n8n-nodes-base.emailSend", "typeVersion": 2.1, "position": [840, -60], "id": "cf8c807b-3952-45a8-b1be-abc4f7cd6aa9", "name": "Send email", "webhookId": "9fe32808-13fa-423b-91a2-c6fc9c06bb4b", "credentials": {"smtp": {"id": "AMRtcync0rNPt8cm", "name": "SMTP account"}}}, {"parameters": {"promptType": "define", "text": "=以下是若干新闻文章的 JSON 列表：\n\n{{ JSON.stringify($json, null, 2) }}\n\n你的任务：\n- 对每条新闻，提取3-5条关键信息，总结一句话摘要。\n- 输出美观、结构化的新闻简报，格式为纯 HTML（不要 markdown，不要任何代码块标记）。\n- 每条新闻请用<div class=\"news-card\">包裹，内容包括：\n  - <h2><a href=\"原文链接\">标题</a></h2>\n  - <div class=\"meta\">\n      <div><b>作者：</b>作者名</div>\n      <div><b>发布时间：</b>日期</div>\n      <div><b>描述：</b>简要描述</div>\n    </div>\n  - <img src=\"主图链接\" style=\"max-width:100%;margin:10px 0;\" />（如有）\n  - <p class=\"summary\">一句话摘要</p>\n  - <ul class=\"keypoints\"><li>要点1</li><li>要点2</li>...</ul>\n- 每条新闻之间用<hr>分隔。\n- 所有内容必须为中文。\n- 不要输出任何 markdown 或代码块标记（如```html）。\n- 不要输出多余解释，只输出 HTML。\n\n请在HTML头部加一段内联<style>，让.news-card有圆角、阴影、间距，整体更美观。\n只输出 HTML 新闻简报，无需其他说明。\n", "options": {"systemMessage": "=你是一位专业的新闻编辑和HTML设计师。你的任务是将原始新闻数据转化为简洁、可读、美观、结构化的中文新闻简报。始终使用语义化HTML标签，突出关键信息，所有输出必须为中文，不要添加任何markdown或代码块标记，输出内容可直接用于邮件或网页。"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [240, -60], "id": "ac386ae9-69fb-4005-ba53-1cc2d4ed7c33", "name": "AI Agent"}, {"parameters": {"jsCode": "// 假设 input 是 items 数组，每个 item.json.output 是一条完整 HTML\nlet htmls = items.map(item => {\n  let html = item.json.output || '';\n  // 去除代码块标记\n  html = html.replace(/^```html\\s*/i, '').replace(/```$/i, '').trim();\n  // 只保留 <body> 内部内容（可选，防止重复 <html>、<head>）\n  const bodyMatch = html.match(/<body[^>]*>([\\\\s\\\\S]*?)<\\/body>/i);\n  return bodyMatch ? bodyMatch[1] : html;\n});\n\n// 可选：拼接一个统一的头部和样式\nconst style = `\n<style>\n.news-card { border-radius:8px; box-shadow:0 2px 4px rgba(0,0,0,0.1); padding:16px; margin-bottom:20px; background:#fff; }\n.news-card h2 a { color:#333; text-decoration:none; }\n.news-card .meta { font-size:14px; color:#777; margin-bottom:10px; }\n.news-card .summary { font-size:16px; line-height:1.4; margin-bottom:10px; }\n.news-card .keypoints { list-style:disc; margin-left:20px; }\nhr { border:0; height:1px; background:#eee; margin:20px 0; }\n</style>\n`;\n\nconst html = `\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n<meta charset=\"UTF-8\">\n<title>新闻简报</title>\n${style}\n</head>\n<body>\n${htmls.join('\\n<hr>\\n')}\n</body>\n</html>\n`;\n\nreturn { html };"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [616, -60], "id": "6133c897-49c2-43c3-80fe-badaa5abd9bc", "name": "Code"}, {"parameters": {"rule": {"interval": [{"triggerAtHour": 8}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [-640, -60], "id": "7bb67ccd-0697-4b0c-9ba1-7a690a0fd2e4", "name": "Schedule Trigger"}], "connections": {"RSS Read": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Limit": {"main": [[{"node": "Scrape A Url And Get Its Content", "type": "main", "index": 0}]]}, "Scrape A Url And Get Its Content": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Send email", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "RSS Read", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner"}}