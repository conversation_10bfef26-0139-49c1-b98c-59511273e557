{"name": "3.3-Better_industry_research_report", "nodes": [{"parameters": {"query": "={{ $json.allSearchTerms[0] }} {{ $json.allSearchTerms[1] }} {{ $json.allSearchTerms[2] }}", "options": {"search_depth": "advanced", "max_results": 10, "include_answer": "advanced", "include_raw_content": true, "include_images": true}}, "type": "@tavily/n8n-nodes-tavily.tavily", "typeVersion": 1, "position": [-3400, -180], "id": "3fcac1b6-8669-4f19-a7d4-6430e1dbc6f0", "name": "<PERSON><PERSON>Sear<PERSON>", "credentials": {"tavilyApi": {"id": "MYBbUyqdPTUvZuXR", "name": "Tavily account"}}}, {"parameters": {"url": "https://api.302.ai/searchapi/search", "sendQuery": true, "queryParameters": {"parameters": [{"name": "q", "value": "={{ $json.allSearchTerms.slice(3, 6).join(' ') }}"}, {"name": "=location", "value": "={{ $json.region === '中国' ? 'China' : 'Global' }}"}, {"name": "hl", "value": "={{ $json.language === 'zh-CN' ? 'zh-cn' : 'en' }}"}, {"name": "num", "value": "10"}, {"name": "api_key", "value": "sk-LMfpbQt8WPp71EXwFmW6fedhTJX9J4lag5znVNRReG5fWPSc"}, {"name": "engine", "value": "google"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}, {"name": "User-Agent", "value": "n8n-industry-report-generator"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-3400, 20], "id": "e682866c-5243-4aa5-ac76-17d6a27174b8", "name": "HTTP Request"}, {"parameters": {"jsCode": "// 4. 数据预处理节点 - 多源数据清洗和结构化\n// 标注：ENHANCED - 智能数据清洗，内容去重，质量评分\n// 修改标注：★ 新增数据质量评分 ★ 智能内容分类 ★ 图片处理\n\nconst allItems = $input.all();\nconsole.log('=== 数据预处理开始 ===');\nconsole.log('输入数据源数量:', allItems.length);\n\n// ===== 数据源分类 =====\nconst dataSources = {\n  tavily: [],\n  serp: [],\n  images: [],\n  enterprise: [],\n  unknown: []\n};\n\n// 分类输入数据\nallItems.forEach((item, index) => {\n  const data = item.json;\n  console.log(`处理数据源 ${index}:`, data.source || 'unknown');\n  \n  if (data.source === 'tavily' || data.results) {\n    dataSources.tavily.push(data);\n  } else if (data.source === 'serp' || data.organic_results) {\n    dataSources.serp.push(data);\n  } else if (data.source === 'images' || (data.results && data.results[0]?.urls)) {\n    dataSources.images.push(data);\n  } else if (data.source === 'enterprise' || data.companies) {\n    dataSources.enterprise.push(data);\n  } else {\n    dataSources.unknown.push(data);\n  }\n});\n\n// ===== 内容清洗函数 =====\nfunction cleanText(text) {\n  if (!text || typeof text !== 'string') return '';\n  \n  return text\n    .replace(/[\\r\\n\\t]+/g, ' ')           // 清理换行和制表符\n    .replace(/\\s+/g, ' ')                 // 合并多个空格\n    .replace(/[^\\u4e00-\\u9fa5\\w\\s.,!?;:()[\\]{}\"\"''—-]/g, '') // 保留中英文和基本标点\n    .trim()\n    .substring(0, 2000);                  // 限制长度\n}\n\nfunction calculateQualityScore(content, title = '') {\n  let score = 0;\n  \n  // 内容长度评分 (0-30分)\n  const length = content.length;\n  if (length > 500) score += 30;\n  else if (length > 200) score += 20;\n  else if (length > 100) score += 10;\n  \n  // 信息密度评分 (0-25分)\n  const infoKeywords = ['市场', '规模', '增长', '企业', '技术', '政策', '投资', '竞争', '份额', '趋势'];\n  const keywordCount = infoKeywords.filter(keyword => content.includes(keyword)).length;\n  score += Math.min(keywordCount * 3, 25);\n  \n  // 数据完整性评分 (0-25分)\n  const dataPatterns = [/\\d+%/, /\\d+亿/, /\\d+万/, /\\d{4}年/, /\\d+\\.?\\d*倍/];\n  const dataCount = dataPatterns.filter(pattern => pattern.test(content)).length;\n  score += Math.min(dataCount * 5, 25);\n  \n  // 标题质量评分 (0-20分)\n  if (title) {\n    if (title.length > 10 && title.length < 100) score += 10;\n    if (infoKeywords.some(keyword => title.includes(keyword))) score += 10;\n  }\n  \n  return Math.min(score, 100);\n}\n\n// ===== 处理Tavily数据 =====\nconst processedTavily = dataSources.tavily.flatMap(source => {\n  if (!source.results) return [];\n  \n  return source.results.map(item => ({\n    source: 'tavily',\n    title: cleanText(item.title || ''),\n    content: cleanText(item.content || ''),\n    url: item.url || '',\n    score: item.score || 0,\n    qualityScore: calculateQualityScore(item.content || '', item.title || ''),\n    type: 'text'\n  })).filter(item => item.content.length > 50 && item.qualityScore > 30);\n});\n\n// ===== 处理SERP数据 =====\nconst processedSerp = dataSources.serp.flatMap(source => {\n  if (!source.organic_results) return [];\n  \n  return source.organic_results.map(item => ({\n    source: 'serp',\n    title: cleanText(item.title || ''),\n    content: cleanText(item.snippet || ''),\n    url: item.link || '',\n    position: item.position || 999,\n    qualityScore: calculateQualityScore(item.snippet || '', item.title || ''),\n    type: 'text'\n  })).filter(item => item.content.length > 30 && item.qualityScore > 25);\n});\n\n// ===== 处理图片数据 (新增) =====\nconst processedImages = dataSources.images.flatMap(source => {\n  if (!source.results) return [];\n  \n  return source.results.slice(0, 8).map(item => ({\n    source: 'images',\n    id: item.id,\n    url: item.urls?.regular || item.urls?.small || '',\n    alt: cleanText(item.alt_description || ''),\n    author: item.user?.name || 'Unknown',\n    type: 'image',\n    qualityScore: item.urls?.regular ? 80 : 60\n  })).filter(item => item.url);\n});\n\n// ===== 处理企业数据 (新增) =====\nconst processedEnterprise = dataSources.enterprise.flatMap(source => {\n  if (!source.companies) return [];\n  \n  return source.companies.slice(0, 15).map(company => ({\n    source: 'enterprise',\n    name: cleanText(company.name || ''),\n    capital: company.regCapital || '',\n    established: company.establishTime || '',\n    industry: cleanText(company.industry || ''),\n    business: cleanText(company.businessScope || ''),\n    type: 'company',\n    qualityScore: company.name ? 70 : 30\n  })).filter(item => item.name);\n});\n\n// ===== 内容去重 =====\nfunction removeDuplicates(items) {\n  const seen = new Set();\n  return items.filter(item => {\n    const key = item.title + item.content.substring(0, 100);\n    if (seen.has(key)) return false;\n    seen.add(key);\n    return true;\n  });\n}\n\n// ===== 内容分类 (新增) =====\nfunction categorizeContent(items) {\n  const categories = {\n    market: [],      // 市场相关\n    competition: [], // 竞争相关\n    technology: [],  // 技术相关\n    policy: [],      // 政策相关\n    investment: [],  // 投资相关\n    general: []      // 通用内容\n  };\n  \n  const categoryKeywords = {\n    market: ['市场', '规模', '容量', '需求', '供给', '消费'],\n    competition: ['竞争', '份额', '排名', '领先', '主导', '企业'],\n    technology: ['技术', '创新', '研发', '专利', '数字化', '智能'],\n    policy: ['政策', '法规', '标准', '监管', '政府', '国家'],\n    investment: ['投资', '融资', '资本', '估值', 'IPO', '并购']\n  };\n  \n  items.forEach(item => {\n    const text = (item.title + ' ' + item.content).toLowerCase();\n    let categorized = false;\n    \n    for (const [category, keywords] of Object.entries(categoryKeywords)) {\n      if (keywords.some(keyword => text.includes(keyword))) {\n        categories[category].push(item);\n        categorized = true;\n        break;\n      }\n    }\n    \n    if (!categorized) {\n      categories.general.push(item);\n    }\n  });\n  \n  return categories;\n}\n\n// ===== 数据整合 =====\nconst allTextContent = [...processedTavily, ...processedSerp];\nconst uniqueTextContent = removeDuplicates(allTextContent);\nconst categorizedContent = categorizeContent(uniqueTextContent);\n\n// 按质量评分排序\nObject.keys(categorizedContent).forEach(category => {\n  categorizedContent[category].sort((a, b) => b.qualityScore - a.qualityScore);\n});\n\n// ===== 数据统计 =====\nconst statistics = {\n  totalSources: allItems.length,\n  textItems: uniqueTextContent.length,\n  imageItems: processedImages.length,\n  companyItems: processedEnterprise.length,\n  averageQuality: uniqueTextContent.length > 0 \n    ? Math.round(uniqueTextContent.reduce((sum, item) => sum + item.qualityScore, 0) / uniqueTextContent.length)\n    : 0,\n  categoryDistribution: Object.fromEntries(\n    Object.entries(categorizedContent).map(([key, items]) => [key, items.length])\n  )\n};\n\nconsole.log('数据处理完成:', statistics);\n\n// ===== 动态User Prompt生成 =====\nfunction generateDynamicPrompts(industry, categorizedContent) {\n  console.log('🎯 开始生成动态User Prompt...');\n\n  const prompts = {};\n  const baseContext = `\n基于以下搜索数据分析${industry}行业：\n\n**搜索数据摘要**：\n- 市场相关内容：${categorizedContent.market.length}条\n- 竞争相关内容：${categorizedContent.competition.length}条\n- 技术相关内容：${categorizedContent.technology.length}条\n- 政策相关内容：${categorizedContent.policy.length}条\n- 其他内容：${categorizedContent.general.length}条\n\n**分析要求**：\n- 使用专业术语和行业标准表达\n- 提供具体数据支撑观点\n- 保持客观中性的分析态度\n- 字数控制在600-800字\n`;\n\n  // 为实际的5.1-5.5节点生成专门的提示词\n  const promptTemplates = {\n    executive: `\n**执行摘要专家角色**：\n你是一名${industry}行业咨询顾问，请撰写执行摘要：\n\n1. **行业概况**：\n   - 行业定义和范围\n   - 发展阶段判断\n   - 整体发展水平\n\n2. **关键发现**：\n   - 最重要的市场发现\n   - 核心竞争要素\n   - 主要发展趋势\n\n3. **战略建议**：\n   - 投资机会建议\n   - 风险防范建议\n   - 发展策略建议\n\n**输出格式**：高度概括的执行摘要，突出核心观点和建议。\n`,\n    market: `\n**市场分析专家角色**：\n你是一名资深的${industry}市场分析师，请从以下角度深度分析：\n\n1. **市场规模与增长**：\n   - 当前市场规模（具体数值）\n   - 历史增长趋势和增长率\n   - 未来3-5年增长预测\n\n2. **市场结构分析**：\n   - 细分市场占比\n   - 区域市场分布\n   - 用户群体特征\n\n3. **市场驱动因素**：\n   - 主要增长驱动力\n   - 市场机遇识别\n   - 消费趋势变化\n\n**输出格式**：专业的市场分析报告段落，包含具体数据和专业见解。同时生成相关的Chart.js图表配置。\n`,\n    competition: `\n**竞争分析专家角色**：\n你是一名${industry}竞争情报分析师，请深入分析：\n\n1. **竞争格局概述**：\n   - 市场集中度分析\n   - 主要竞争者识别\n   - 市场份额分布\n\n2. **重点企业分析**：\n   - 头部企业优势分析\n   - 商业模式对比\n   - 竞争策略评估\n\n3. **竞争趋势预判**：\n   - 竞争格局变化趋势\n   - 新进入者威胁\n   - 行业整合可能性\n\n**输出格式**：结构化的竞争分析，突出关键竞争要素。包含竞争格局图表配置。\n`,\n    technology: `\n**技术分析专家角色**：\n你是一名${industry}技术发展研究员，请分析：\n\n1. **技术现状评估**：\n   - 核心技术水平\n   - 技术成熟度分析\n   - 技术标准化程度\n\n2. **创新趋势识别**：\n   - 新兴技术应用\n   - 技术发展方向\n   - 研发投入情况\n\n3. **技术影响评估**：\n   - 技术对行业的影响\n   - 技术壁垒分析\n   - 技术风险评估\n\n**输出格式**：技术导向的专业分析，重点关注技术创新和应用。包含技术趋势图表。\n`,\n    investment: `\n**投资分析专家角色**：\n你是一名专注${industry}的投资顾问，请提供：\n\n1. **投资环境分析**：\n   - 行业投资热度\n   - 资本市场表现\n   - 投资回报情况\n\n2. **投资机会识别**：\n   - 重点投资领域\n   - 投资价值评估\n   - 投资时机判断\n\n3. **投资风险评估**：\n   - 主要投资风险\n   - 风险缓解策略\n   - 投资建议\n\n**输出格式**：投资导向的专业建议，包含具体的投资观点和风险评估图表。\n`\n  };\n\n  // 为实际的5.1-5.5节点生成Prompt\n  const nodeMapping = {\n    '5.1': 'executive',    // 执行摘要 (总是执行)\n    '5.2': 'market',       // 市场类章节 (条件执行)\n    '5.3': 'competition',  // 竞争类章节 (条件执行)\n    '5.4': 'technology',   // 技术类章节 (条件执行)\n    '5.5': 'investment'    // 投资类章节 (条件执行)\n  };\n\n  Object.entries(nodeMapping).forEach(([nodeId, category]) => {\n    const relevantData = categorizedContent[category] || categorizedContent.general || [];\n\n    prompts[nodeId] = {\n      systemPrompt: promptTemplates[category],\n      userPrompt: baseContext + `\\n\\n**重点分析数据**：\\n${JSON.stringify(relevantData.slice(0, 10), null, 2)}`,\n      category: category,\n      nodeId: nodeId,\n      dataCount: relevantData.length,\n      shouldExecute: category === 'executive' || relevantData.length > 0 // 执行摘要总是执行，其他看数据\n    };\n  });\n\n  console.log(`✅ 生成了${Object.keys(prompts).length}个节点的动态Prompt`);\n  console.log('节点执行状态:', Object.fromEntries(\n    Object.entries(prompts).map(([nodeId, config]) => [nodeId, config.shouldExecute])\n  ));\n\n  return prompts;\n}\n\n// 获取行业信息（从原始配置中获取）\nconst industry = allItems[0]?.json?.industry || '未知行业';\nconst dynamicPrompts = generateDynamicPrompts(industry, categorizedContent);\n\n// ===== 返回结果 =====\nreturn [{\n  json: {\n    // 分类内容\n    categorizedContent: categorizedContent,\n\n    // 动态Prompt (新增)\n    dynamicPrompts: dynamicPrompts,\n\n    // 图片和企业数据\n    images: processedImages,\n    companies: processedEnterprise,\n\n    // 统计信息\n    statistics: statistics,\n\n    // 质量控制\n    qualityThreshold: 30,\n    topQualityItems: uniqueTextContent\n      .filter(item => item.qualityScore > 60)\n      .slice(0, 20),\n\n    // 元数据\n    metadata: {\n      processedAt: new Date().toISOString(),\n      processingVersion: '4.0-enhanced',\n      dataIntegrity: statistics.averageQuality > 40 ? 'good' : 'fair',\n      promptsGenerated: Object.keys(dynamicPrompts).length\n    },\n\n    // 状态\n    status: {\n      success: true,\n      message: `数据预处理完成 - 处理${statistics.textItems}条文本，${statistics.imageItems}张图片，${statistics.companyItems}家企业，生成${Object.keys(dynamicPrompts).length}个动态Prompt`,\n      nextStep: '分段内容生成'\n    }\n  }\n}];\n\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-2920, 20], "id": "a25ce432-d77d-4354-b8a9-88771c75cfa1", "name": "数据清洗"}, {"parameters": {"options": {"maxTokens": 1500, "temperature": 0.4, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [-2432, -460], "id": "f3702475-28c6-4b64-995b-83e975cbd8d6", "name": "DeepSeek Chat Model2", "credentials": {"deepSeekApi": {"id": "IKqWJBBXULhRu8mZ", "name": "DeepSeek account"}}}, {"parameters": {"method": "POST", "url": "https://mcp-on-edge.edgeone.site/mcp-server", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content_Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"jsonrpc\": \"2.0\",\n  \"id\": 1,\n  \"method\": \"tools/call\",\n  \"params\": {\n    \"name\": \"deploy-html\",\n    \"arguments\": {\n      \"value\": \"{{ JSON.stringify($node['html1'].json.htmlReport).slice(1, -1) }}\"\n    }\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1420, 0], "id": "d266e682-5a25-4ae4-bb12-80556f1c542f", "name": "发布"}, {"parameters": {"httpMethod": "POST", "path": "industry-report", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-4060, 20], "id": "1d279504-8f59-40e0-9549-6875655df205", "name": "Webhook", "webhookId": "7cd2e465-8b72-4d5f-9edd-dcedd83c096c"}, {"parameters": {"url": "https://api.unsplash.com/search/photos", "sendQuery": true, "queryParameters": {"parameters": [{"name": "query", "value": "={{ $json.imageKeywords[0] }}"}, {"name": "per_page", "value": "8 "}, {"name": "orientation", "value": "landscape"}, {"name": "order_by", "value": "relevant"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Client-ID UGpzJlz4NiUot1NboxIcaZGuoSuooHLnjLAr7Metg"}, {"name": "Accept", "value": "application/json"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-3400, 220], "id": "587dc639-320e-49ee-b822-d58effe1b794", "name": "HTTP Request1"}, {"parameters": {"jsCode": "// 2. 参数设置增强版 - 智能参数解析和配置生成\n// 标注：ENHANCED - 支持多种输入格式，智能参数推断，A4页面控制\n// 修改标注：★ 新增A4页面精确控制 ★ 智能行业识别 ★ 多语言支持\n\nconst items = $input.all();\nconst rawInputData = items[0].json;\n\nconsole.log('=== 参数设置增强版 ===');\nconsole.log('原始输入数据:', JSON.stringify(rawInputData, null, 2));\n\n// ===== 智能数据提取 =====\nlet inputData = {};\n\n// 检查是否是webhook原始数据格式\nif (rawInputData.body && rawInputData.webhookUrl && rawInputData.executionMode) {\n  // 从webhook的body中提取实际数据\n  inputData = rawInputData.body;\n  console.log('✅ 检测到webhook数据，从body中提取:', JSON.stringify(inputData, null, 2));\n} else if (rawInputData.industry || rawInputData.chatInput) {\n  // 直接数据格式\n  inputData = rawInputData;\n  console.log('✅ 使用直接数据格式');\n} else {\n  // 尝试从其他位置提取\n  inputData = rawInputData;\n  console.log('⚠️ 使用原始数据格式');\n}\n\n// ===== 核心参数提取 =====\nfunction extractIndustry(data) {\n  const possibleFields = ['industry', 'chatInput', 'query', 'keyword', 'topic', 'subject'];\n  for (const field of possibleFields) {\n    if (data[field] && typeof data[field] === 'string' && data[field].trim()) {\n      return data[field].trim();\n    }\n  }\n  return null;\n}\n\nconst industry = extractIndustry(inputData);\nif (!industry) {\n  return [{\n    json: {\n      error: true,\n      message: '未能识别行业关键词，请检查输入参数',\n      supportedFields: ['industry', 'chatInput', 'query', 'keyword', 'topic', 'subject'],\n      inputData: inputData\n    }\n  }];\n}\n\n// ===== 智能参数配置 =====\nconst config = {\n  // 基础参数\n  industry: industry,\n  region: inputData.region || '中国',\n  timeRange: inputData.timeRange || '2024-2025',\n  \n  // 报告控制参数 (新增)\n  reportType: inputData.reportType || 'comprehensive',\n  reportStyle: inputData.reportStyle || 'consulting',\n  pageTarget: Math.min(Math.max(inputData.pageTarget || 6, 5), 8), // 限制5-8页\n  \n  // 内容控制参数\n  includeCharts: inputData.includeCharts !== false,\n  includeImages: inputData.includeImages !== false,\n  language: inputData.language || 'zh-CN',\n  \n  // 自定义章节 (新增)\n  customSections: inputData.customSections || [\n    '执行摘要', '市场概述', '细分市场', '区域分析', \n    '竞争格局', 'SWOT分析', '技术趋势', '政策环境',\n    '投资分析', '风险评估', '未来预测', '战略建议'\n  ]\n};\n\n// ===== A4页面控制计算 (新增) =====\nconst pageControl = {\n  targetPages: config.pageTarget,\n  wordsPerPage: 800, // A4页面约800字\n  totalWords: config.pageTarget * 800,\n  sectionsCount: config.customSections.length,\n  wordsPerSection: Math.floor((config.pageTarget * 800) / config.customSections.length),\n  chartsPerPage: 1.2, // 平均每页1.2个图表\n  totalCharts: Math.ceil(config.pageTarget * 1.2)\n};\n\n// ===== 搜索关键词生成 (增强) =====\nconst searchStrategies = {\n  market: [\n    `${industry} 市场规模 ${config.timeRange}`,\n    `${industry} 行业分析 ${config.region}`,\n    `${industry} 市场容量 统计数据`,\n    `${industry} 产业发展现状 ${config.region}`\n  ],\n  competition: [\n    `${industry} 竞争格局 主要企业`,\n    `${industry} 市场份额 领军企业`,\n    `${industry} 行业集中度分析`,\n    `${industry} 企业排名 ${config.timeRange}`\n  ],\n  technology: [\n    `${industry} 技术发展趋势`,\n    `${industry} 创新技术 新兴技术`,\n    `${industry} 数字化转型`,\n    `${industry} 技术标准 行业标准`\n  ],\n  policy: [\n    `${industry} 政策支持 国家政策`,\n    `${industry} 行业标准 监管政策`,\n    `${industry} 产业政策 ${config.region}`,\n    `${industry} 法规变化 ${config.timeRange}`\n  ],\n  investment: [\n    `${industry} 投资趋势 融资情况`,\n    `${industry} 投资机会 ${config.timeRange}`,\n    `${industry} 资本市场 IPO`,\n    `${industry} 并购重组 投资案例`\n  ],\n  supply: [\n    `${industry} 供应链分析`,\n    `${industry} 产业链上下游`,\n    `${industry} 供应商分析`,\n    `${industry} 产业链风险`\n  ],\n  regional: [\n    `${industry} ${config.region} 发展情况`,\n    `${industry} 区域分布 地域特色`,\n    `${industry} 区域竞争力`,\n    `${industry} 地区政策 ${config.region}`\n  ],\n  future: [\n    `${industry} 未来展望 发展前景`,\n    `${industry} 趋势预测 ${config.timeRange}`,\n    `${industry} 发展机遇 挑战`,\n    `${industry} 战略规划 发展目标`\n  ]\n};\n\n// 合并所有搜索词\nconst allSearchTerms = Object.values(searchStrategies).flat();\n\n// ===== 图片搜索关键词 (新增) =====\nconst imageKeywords = [\n  `${industry} industry`,\n  `${industry} technology`,\n  `${industry} business`,\n  `${industry} market`,\n  `supply chain ${industry}`,\n  `digital transformation ${industry}`,\n  `innovation ${industry}`,\n  `future ${industry}`\n];\n\n// ===== 数据源配置 (增强) =====\nconst dataSources = {\n  search: {\n    tavily: {\n      enabled: true,\n      maxResults: 15,\n      searchDepth: 'advanced',\n      includeRawContent: true\n    },\n    serp: {\n      enabled: true,\n      engine: 'google',\n      maxResults: 10,\n      region: config.region === '中国' ? 'cn' : 'global'\n    }\n  },\n  images: {\n    unsplash: {\n      enabled: config.includeImages,\n      maxImages: 8,\n      orientation: 'landscape',\n      quality: 'high'\n    }\n  },\n  apis: {\n    government: {\n      enabled: config.region === '中国',\n      sources: ['stats.gov.cn', 'mofcom.gov.cn']\n    },\n    enterprise: {\n      enabled: true,\n      sources: ['tianyancha', 'qichacha']\n    }\n  }\n};\n\n// ===== 内容生成配置 (新增) =====\nconst contentGeneration = {\n  segments: [\n    {\n      name: '执行摘要',\n      wordTarget: Math.floor(pageControl.wordsPerSection * 0.8),\n      priority: 'high',\n      includeCharts: false\n    },\n    {\n      name: '市场概述', \n      wordTarget: pageControl.wordsPerSection,\n      priority: 'high',\n      includeCharts: true\n    },\n    {\n      name: '竞争格局',\n      wordTarget: pageControl.wordsPerSection,\n      priority: 'high', \n      includeCharts: true\n    },\n    {\n      name: '技术趋势',\n      wordTarget: pageControl.wordsPerSection,\n      priority: 'medium',\n      includeCharts: true\n    },\n    {\n      name: '未来预测',\n      wordTarget: pageControl.wordsPerSection,\n      priority: 'high',\n      includeCharts: true\n    }\n  ]\n};\n\n// ===== 返回结果 =====\nconst result = {\n  json: {\n    // 基础配置\n    ...config,\n    \n    // 页面控制 (新增)\n    pageControl: pageControl,\n    \n    // 搜索配置\n    searchStrategies: searchStrategies,\n    allSearchTerms: allSearchTerms,\n    imageKeywords: imageKeywords,\n    \n    // 数据源配置 (增强)\n    dataSources: dataSources,\n    \n    // 内容生成配置 (新增)\n    contentGeneration: contentGeneration,\n    \n    // 元数据\n    metadata: {\n      configVersion: '2.0-enhanced',\n      timestamp: new Date().toISOString(),\n      estimatedTokens: pageControl.totalWords * 1.5, // 估算token数\n      processingStrategy: 'segmented', // 分段处理策略\n      qualityLevel: config.reportType\n    },\n    \n    // 状态\n    status: {\n      error: false,\n      message: `参数配置成功 - ${industry} (${config.pageTarget}页报告)`,\n      nextSteps: ['多源数据采集', '分段内容生成', '图表渲染', '模板应用']\n    }\n  }\n};\n\nconsole.log('配置生成完成:', {\n  industry: config.industry,\n  pageTarget: config.pageTarget,\n  sectionsCount: config.customSections.length,\n  searchTermsCount: allSearchTerms.length,\n  estimatedWords: pageControl.totalWords\n});\n\nreturn [result];\n\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3840, 20], "id": "05cead5b-755e-45fa-b62c-c8e99d6f52f8", "name": "参数设置"}, {"parameters": {"jsCode": "// 2.5 动态结构生成器 - 基于行业特点智能生成报告结构\n// 标注：NEW - 完全动态的章节和图表结构生成，避免固化框架\n// 修改标注：★ 智能章节生成 ★ 动态图表配置 ★ 行业适配\n\nconst inputData = $json;\n\nconsole.log('=== 动态结构生成开始 ===');\nconsole.log('输入数据:', JSON.stringify(inputData, null, 2));\n\n// 智能提取配置数据 - 支持多种数据结构\nlet originalConfig = {};\n\n// 尝试从不同位置提取数据\nif (inputData.body && typeof inputData.body === 'object') {\n  // Webhook数据在body中\n  originalConfig = inputData.body;\n  console.log('✅ 从body中提取数据');\n} else if (inputData.industry || inputData.chatInput) {\n  // 直接在根级别\n  originalConfig = inputData;\n  console.log('✅ 从根级别提取数据');\n} else if (inputData.json && typeof inputData.json === 'object') {\n  // 数据在json字段中\n  originalConfig = inputData.json;\n  console.log('✅ 从json字段中提取数据');\n} else {\n  // 尝试查找任何包含industry相关字段的对象\n  const searchFields = ['industry', 'chatInput', 'query', 'keyword', 'topic', 'subject'];\n  for (const [key, value] of Object.entries(inputData)) {\n    if (typeof value === 'object' && value !== null) {\n      const hasIndustryField = searchFields.some(field => value[field]);\n      if (hasIndustryField) {\n        originalConfig = value;\n        console.log(`✅ 从${key}字段中提取数据`);\n        break;\n      }\n    }\n  }\n}\n\n// 数据验证\nif (!originalConfig || typeof originalConfig !== 'object') {\n  console.error('❌ 无法提取有效配置数据');\n  return [{\n    json: {\n      error: true,\n      message: \"未能识别行业关键词，请检查输入参数\",\n      supportedFields: [\"industry\", \"chatInput\", \"query\", \"keyword\", \"topic\", \"subject\"],\n      inputData: inputData\n    }\n  }];\n}\n\n// 确保必需字段存在\nconst safeConfig = {\n  industry: originalConfig.industry || originalConfig.chatInput || originalConfig.query || originalConfig.keyword || originalConfig.topic || originalConfig.subject || '通用行业',\n  reportType: originalConfig.reportType || 'comprehensive',\n  region: originalConfig.region || '全球',\n  timeRange: originalConfig.timeRange || '2024-2025',\n  pageTarget: originalConfig.pageTarget || 6\n};\n\nconsole.log('目标行业:', safeConfig.industry);\nconsole.log('报告类型:', safeConfig.reportType);\n\n// 验证提取的行业信息\nif (!safeConfig.industry || safeConfig.industry === '通用行业') {\n  console.warn('⚠️ 行业信息提取可能有问题，当前值:', safeConfig.industry);\n  console.log('原始配置:', JSON.stringify(originalConfig, null, 2));\n}\n\n// ===== 行业特征分析 =====\nfunction analyzeIndustryCharacteristics(industry) {\n  // 安全检查：确保industry不为undefined或null\n  if (!industry || typeof industry !== 'string') {\n    console.warn('⚠️ 行业参数无效，使用默认值');\n    industry = '通用行业';\n  }\n\n  const industryKeywords = industry.toLowerCase();\n  console.log('🔍 分析行业特征:', industry, '-> 关键词:', industryKeywords);\n  \n  const characteristics = {\n    isManufacturing: /制造|生产|工厂|加工/.test(industryKeywords),\n    isTechnology: /科技|技术|AI|人工智能|软件|互联网|数字化/.test(industryKeywords),\n    isService: /服务|咨询|教育|医疗|金融|保险/.test(industryKeywords),\n    isRetail: /零售|电商|消费|购物/.test(industryKeywords),\n    isEnergy: /能源|电力|石油|新能源|光伏|风电/.test(industryKeywords),\n    isFinance: /金融|银行|投资|证券|保险|支付/.test(industryKeywords),\n    isHealthcare: /医疗|健康|药品|医药|生物/.test(industryKeywords),\n    isTransport: /物流|运输|交通|快递|供应链/.test(industryKeywords),\n    isRealEstate: /房地产|建筑|装修|家居/.test(industryKeywords),\n    isEducation: /教育|培训|学习|知识/.test(industryKeywords)\n  };\n\n  // 调试输出\n  console.log('🎯 行业特征识别结果:', characteristics);\n  const matchedTypes = Object.keys(characteristics).filter(key => characteristics[key]);\n  console.log('✅ 匹配的行业类型:', matchedTypes);\n\n  return characteristics;\n}\n\n// ===== 智能章节生成 =====\nfunction generateDynamicSections(industry, reportType, characteristics) {\n  const baseSections = ['执行摘要']; // 必需章节\n  const dynamicSections = [];\n  \n  // 根据行业特征动态添加章节\n  if (characteristics.isTechnology) {\n    dynamicSections.push(\n      '技术发展现状',\n      '创新技术趋势', \n      '数字化转型',\n      '技术标准与规范',\n      '研发投入分析'\n    );\n  }\n  \n  if (characteristics.isManufacturing) {\n    dynamicSections.push(\n      '产业链分析',\n      '生产能力评估',\n      '供应链管理',\n      '制造成本分析',\n      '产能利用率'\n    );\n  }\n  \n  if (characteristics.isService) {\n    dynamicSections.push(\n      '服务模式创新',\n      '客户需求分析',\n      '服务质量评估',\n      '用户体验优化'\n    );\n  }\n  \n  if (characteristics.isFinance) {\n    dynamicSections.push(\n      '监管政策影响',\n      '风险管理体系',\n      '金融创新产品',\n      '合规性分析'\n    );\n  }\n  \n  if (characteristics.isHealthcare) {\n    dynamicSections.push(\n      '临床试验进展',\n      '药物研发管线',\n      '医疗器械创新',\n      '健康政策影响'\n    );\n  }\n  \n  if (characteristics.isEnergy) {\n    dynamicSections.push(\n      '能源结构变化',\n      '碳中和路径',\n      '新能源技术',\n      '能效提升方案'\n    );\n  }\n  \n  // 通用章节（根据报告类型选择）\n  const universalSections = {\n    comprehensive: [\n      '市场规模与增长',\n      '细分市场分析',\n      '区域市场分布',\n      '竞争格局分析',\n      '主要企业研究',\n      '商业模式创新',\n      '投资机会识别',\n      '风险因素评估',\n      '发展趋势预测',\n      '战略建议'\n    ],\n    basic: [\n      '市场概况',\n      '竞争分析',\n      '发展趋势',\n      '投资建议'\n    ],\n    detailed: [\n      '宏观环境分析',\n      '市场需求分析',\n      '供给能力分析',\n      '价值链分析',\n      '竞争态势分析',\n      '企业案例研究',\n      '商业模式分析',\n      '技术发展路径',\n      '政策环境影响',\n      '投资价值评估',\n      '风险预警机制',\n      '未来发展路径',\n      '行动方案建议'\n    ]\n  };\n  \n  // 合并章节\n  const selectedUniversal = universalSections[reportType] || universalSections.comprehensive;\n  const allSections = [...baseSections, ...dynamicSections, ...selectedUniversal];\n  \n  // 去重并限制章节数量（根据目标页数）\n  const uniqueSections = [...new Set(allSections)];\n  const maxSections = Math.min(safeConfig.pageTarget + 2, 12); // 最多12个章节\n  \n  return uniqueSections.slice(0, maxSections);\n}\n\n// ===== 动态图表配置生成 =====\nfunction generateDynamicChartConfig(industry, sections, characteristics) {\n  const chartConfigs = [];\n  \n  // 基础图表（几乎所有行业都需要）\n  chartConfigs.push({\n    name: '市场规模趋势',\n    type: 'line',\n    applicableSections: ['市场规模与增长', '市场概况', '宏观环境分析'],\n    dataRequirements: ['历史数据', '市场容量', '增长率'],\n    priority: 'high'\n  });\n  \n  chartConfigs.push({\n    name: '竞争格局分布',\n    type: 'pie',\n    applicableSections: ['竞争格局分析', '竞争分析', '竞争态势分析'],\n    dataRequirements: ['企业市场份额', '主要竞争者'],\n    priority: 'high'\n  });\n  \n  // 行业特定图表\n  if (characteristics.isTechnology) {\n    chartConfigs.push(\n      {\n        name: '技术投资趋势',\n        type: 'line',\n        applicableSections: ['技术发展现状', '研发投入分析'],\n        dataRequirements: ['研发投入', '技术投资'],\n        priority: 'medium'\n      },\n      {\n        name: '创新技术分布',\n        type: 'bar',\n        applicableSections: ['创新技术趋势', '技术标准与规范'],\n        dataRequirements: ['技术类别', '应用领域'],\n        priority: 'medium'\n      }\n    );\n  }\n  \n  if (characteristics.isManufacturing) {\n    chartConfigs.push(\n      {\n        name: '产能利用率变化',\n        type: 'line',\n        applicableSections: ['生产能力评估', '产能利用率'],\n        dataRequirements: ['产能数据', '利用率'],\n        priority: 'medium'\n      },\n      {\n        name: '成本结构分析',\n        type: 'pie',\n        applicableSections: ['制造成本分析', '价值链分析'],\n        dataRequirements: ['成本构成', '费用分布'],\n        priority: 'medium'\n      }\n    );\n  }\n  \n  if (characteristics.isFinance) {\n    chartConfigs.push(\n      {\n        name: '资产规模增长',\n        type: 'bar',\n        applicableSections: ['投资价值评估', '风险管理体系'],\n        dataRequirements: ['资产规模', '增长数据'],\n        priority: 'high'\n      },\n      {\n        name: '风险分布图',\n        type: 'radar',\n        applicableSections: ['风险因素评估', '风险预警机制'],\n        dataRequirements: ['风险类型', '风险等级'],\n        priority: 'medium'\n      }\n    );\n  }\n  \n  if (characteristics.isEnergy) {\n    chartConfigs.push(\n      {\n        name: '能源结构变化',\n        type: 'stackedBar',\n        applicableSections: ['能源结构变化', '新能源技术'],\n        dataRequirements: ['能源类型', '占比变化'],\n        priority: 'high'\n      },\n      {\n        name: '碳排放趋势',\n        type: 'line',\n        applicableSections: ['碳中和路径', '能效提升方案'],\n        dataRequirements: ['碳排放数据', '减排目标'],\n        priority: 'medium'\n      }\n    );\n  }\n  \n  // 根据章节匹配图表\n  const matchedCharts = [];\n  sections.forEach(section => {\n    const relevantCharts = chartConfigs.filter(chart => \n      chart.applicableSections.some(applicableSection => \n        section.includes(applicableSection.split('分析')[0]) || \n        applicableSection.includes(section.split('分析')[0])\n      )\n    );\n    \n    relevantCharts.forEach(chart => {\n      if (!matchedCharts.find(existing => existing.name === chart.name)) {\n        matchedCharts.push({\n          ...chart,\n          targetSection: section\n        });\n      }\n    });\n  });\n  \n  // 按优先级排序并限制数量\n  const maxCharts = Math.ceil(safeConfig.pageTarget * 1.2); // 每页约1.2个图表\n  const prioritizedCharts = matchedCharts\n    .sort((a, b) => {\n      const priorityOrder = { high: 3, medium: 2, low: 1 };\n      return priorityOrder[b.priority] - priorityOrder[a.priority];\n    })\n    .slice(0, maxCharts);\n  \n  return prioritizedCharts;\n}\n\n// ===== 搜索关键词动态生成 =====\nfunction generateDynamicSearchTerms(industry, sections, characteristics) {\n  const searchTerms = [];\n  \n  // 基础搜索词\n  searchTerms.push(\n    `${industry} 行业分析 2024`,\n    `${industry} 市场规模 发展现状`,\n    `${industry} 竞争格局 主要企业`\n  );\n  \n  // 根据章节生成搜索词\n  sections.forEach(section => {\n    searchTerms.push(`${industry} ${section} 分析报告`);\n    \n    // 特定章节的深度搜索词\n    if (section.includes('技术')) {\n      searchTerms.push(`${industry} 技术创新 发展趋势`);\n    }\n    if (section.includes('投资')) {\n      searchTerms.push(`${industry} 投资机会 融资情况`);\n    }\n    if (section.includes('政策')) {\n      searchTerms.push(`${industry} 政策支持 监管变化`);\n    }\n    if (section.includes('风险')) {\n      searchTerms.push(`${industry} 风险评估 挑战分析`);\n    }\n  });\n  \n  // 行业特定搜索词\n  if (characteristics.isTechnology) {\n    searchTerms.push(\n      `${industry} 数字化转型`,\n      `${industry} 人工智能应用`,\n      `${industry} 技术标准`\n    );\n  }\n  \n  if (characteristics.isManufacturing) {\n    searchTerms.push(\n      `${industry} 供应链管理`,\n      `${industry} 智能制造`,\n      `${industry} 产能分析`\n    );\n  }\n  \n  // 去重并限制数量\n  const uniqueTerms = [...new Set(searchTerms)];\n  return uniqueTerms.slice(0, 20); // 最多20个搜索词\n}\n\n// ===== 执行动态生成 =====\nconst characteristics = analyzeIndustryCharacteristics(safeConfig.industry);\nconst dynamicSections = generateDynamicSections(\n  safeConfig.industry,\n  safeConfig.reportType,\n  characteristics\n);\nconst dynamicCharts = generateDynamicChartConfig(\n  safeConfig.industry,\n  dynamicSections,\n  characteristics\n);\nconst dynamicSearchTerms = generateDynamicSearchTerms(\n  safeConfig.industry,\n  dynamicSections,\n  characteristics\n);\n\nconsole.log('动态结构生成完成:', {\n  industry: safeConfig.industry,\n  sectionsCount: dynamicSections.length,\n  chartsCount: dynamicCharts.length,\n  searchTermsCount: dynamicSearchTerms.length,\n  characteristics: Object.keys(characteristics).filter(key => characteristics[key])\n});\n\n// ===== 返回结果 =====\nreturn [{\n  json: {\n    // 原始配置\n    ...originalConfig,\n    \n    // 动态生成的结构\n    dynamicSections: dynamicSections,\n    dynamicCharts: dynamicCharts,\n    dynamicSearchTerms: dynamicSearchTerms,\n    \n    // 行业特征\n    industryCharacteristics: characteristics,\n    \n    // 内容生成指导\n    contentGuidance: {\n      sectionsCount: dynamicSections.length,\n      wordsPerSection: dynamicSections.length > 0 ? Math.floor((safeConfig.pageTarget * 800) / dynamicSections.length) : null,\n      chartsPerSection: dynamicSections.length > 0 ? dynamicCharts.length / dynamicSections.length : null,\n      searchStrategy: 'industry-adaptive'\n    },\n    \n    // 元数据\n    metadata: {\n      generatedAt: new Date().toISOString(),\n      structureVersion: '2.5-dynamic',\n      adaptiveLevel: 'high',\n      industryType: Object.keys(characteristics).filter(key => characteristics[key]).join(', ')\n    },\n    \n    // 状态\n    status: {\n      success: true,\n      message: `${safeConfig.industry}行业动态结构生成完成`,\n      isDynamic: true,\n      nextStep: '基于动态结构进行数据采集'\n    }\n  }\n}];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-3620, 20], "id": "c3fca73b-164b-4c83-a6e8-423fcf24e882", "name": "动态参数设置"}, {"parameters": {"options": {"maxTokens": 1500, "temperature": 0.4, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [-2380, -120], "id": "c97b1d30-bd4d-4b33-a6cd-bbec994df1bf", "name": "DeepSeek Chat Model3", "credentials": {"deepSeekApi": {"id": "IKqWJBBXULhRu8mZ", "name": "DeepSeek account"}}}, {"parameters": {"options": {"maxTokens": 1500, "temperature": 0.4, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [-2432, 340], "id": "6aca3144-7ceb-4edf-9b21-96760676845a", "name": "DeepSeek Chat Model4", "credentials": {"deepSeekApi": {"id": "IKqWJBBXULhRu8mZ", "name": "DeepSeek account"}}}, {"parameters": {"options": {"maxTokens": 1500, "temperature": 0.4, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [-2380, 740], "id": "286ad3bb-dbdf-4c78-971d-58ace51f410a", "name": "DeepSeek Chat Model5", "credentials": {"deepSeekApi": {"id": "IKqWJBBXULhRu8mZ", "name": "DeepSeek account"}}}, {"parameters": {"promptType": "define", "text": "=基于以下{{ $('数据清洗').item.json.industry }}行业数据，撰写执行摘要（约600-800字）：\n\n[动态数据插入]\n\n要求：\n1. 开篇直接点出行业核心价值和市场机会\n2. 用3-4个关键数据支撑市场规模和增长潜力  \n3. 简述2-3个主要竞争者和市场格局\n4. 指出1-2个关键趋势和机遇\n5. 以战略建议结尾\n6. 语言简洁有力，适合高管阅读", "messages": {"messageValues": [{"message": "=你是一名资深行业分析师，专门为企业高管撰写执行摘要。你需要从大量行业数据中提炼出最关键的洞察，用简洁有力的语言呈现核心价值和战略机会。"}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [-2520, -680], "id": "81c75400-bf1c-4125-8a30-11c48259ac4d", "name": "执行摘要"}, {"parameters": {"promptType": "define", "text": "=基于以下数据，撰写\"{{ $('动态参数设置').item.json.dynamicSections.find(s => s.includes('技术')) }}\"章节（约800-1000字）：\n\n技术相关数据：\n{{ $json.categorizedContent.technology.slice(0, 8).map(item => item.title + ': ' + item.content).join('\\\\n\\\\n') }}\n\n要求：\n1. 分析当前主流技术应用现状\n2. 识别新兴技术发展趋势\n3. 评估数字化转型进程\n4. 预测技术发展对行业的影响\n5. 包含具体的技术案例和数据", "messages": {"messageValues": [{"message": "=你是一名技术趋势分析专家，专门研究{{ $('动态参数设置').item.json.industry }}行业的技术发展方向、创新应用和数字化转型趋势。"}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [-2520, -280], "id": "1085bed6-9062-461b-a172-11c4d44f44d2", "name": "技术分析"}, {"parameters": {"promptType": "define", "text": "=基于以下数据，撰写\"{{ $('动态参数设置').item.json.dynamicSections.find(s => s.includes('市场')) }}\"章节（约800-1000字）：\n\n市场相关数据：\n{{ $json.categorizedContent.market.slice(0, 8).map(item => item.title + ': ' + item.content).join('\\\\n\\\\n') }}\n\n要求：\n1. 详细分析市场规模和增长趋势\n2. 识别主要增长驱动因素\n3. 分析细分市场机会\n4. 评估区域市场分布特征\n5. 提供具体的市场数据和预测", "messages": {"messageValues": [{"message": "=你是一名市场研究分析师，专门分析{{ $('动态参数设置').item.json.industry }}行业的市场状况、增长驱动因素和发展机会。"}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [-2520, 120], "id": "2ee2e4b3-1a9e-42a3-939a-19f274282a36", "name": "市场分析"}, {"parameters": {"promptType": "define", "text": "=基于以下数据，撰写\"{{ $('动态参数设置').item.json.dynamicSections.find(s => s.includes('竞争')) }}\"章节（约800-1000字）：\n\n竞争相关数据：\n{{ $json.categorizedContent.competition.slice(0, 8).map(item => item.title + ': ' + item.content).join('\\\\n\\\\n') }}\n\n企业数据：\n{{ $json.companies.slice(0, 10).map(company => company.name + ' - 注册资本：' + company.capital + ' - 业务：' + company.business).join('\\\\n') }}\n\n输出JSON格式：\n{\n  \"竞争分析\": \"详细分析文本\",\n  \"主要企业\": [\"企业1\", \"企业2\", \"企业3\", \"企业4\", \"企业5\"],\n  \"市场份额\": [\"企业1占比XX%\", \"企业2占比XX%\", \"其他企业占比XX%\"]\n}", "messages": {"messageValues": [{"message": "=你是一名竞争情报分析专家，专门分析{{ $('动态参数设置').item.json.industry }}行业的竞争格局、主要企业表现和市场份额分布。"}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [-2500, 560], "id": "7f4c7725-b090-483f-a107-7cda21c72aa1", "name": "竞争分析"}, {"parameters": {"jsCode": "// ===== LLM质量优先系统 - 专注于发挥每个LLM的极致潜力 =====\nconsole.log('=== LLM质量优先系统启动 ===');\n\nconst allInputs = $input.all();\nconsole.log('LLM节点数量:', allInputs.length);\n\n// LLM质量优先处理系统\nconst LLMQualityFirstSystem = {\n  // 专业的LLM内容提取器 - 支持所有主流LLM API格式\n  extractLLMContent(inputs) {\n    const extractedContents = [];\n    \n    inputs.forEach((input, index) => {\n      console.log(`\\n=== 处理LLM节点 ${index + 1} ===`);\n      console.log('输入类型:', typeof input);\n      console.log('输入结构:', Object.keys(input || {}));\n      \n      const content = this.deepExtractContent(input, index + 1);\n      if (content) {\n        extractedContents.push(content);\n        console.log(`✅ LLM节点 ${index + 1} 提取成功`);\n        console.log('内容长度:', content.text.length);\n        console.log('内容预览:', content.text.substring(0, 100) + '...');\n      } else {\n        console.log(`❌ LLM节点 ${index + 1} 提取失败`);\n      }\n    });\n    \n    return extractedContents;\n  },\n\n  // 深度内容提取 - 支持多种LLM API格式\n  deepExtractContent(input, nodeIndex) {\n    let text = '';\n    let metadata = {};\n    \n    // 1. 直接字符串\n    if (typeof input === 'string' && input.length > 50) {\n      text = input;\n    }\n    \n    // 2. OpenAI格式\n    else if (input?.choices?.[0]?.message?.content) {\n      text = input.choices[0].message.content;\n      metadata.model = input.model || 'openai';\n      metadata.tokens = input.usage?.total_tokens;\n    }\n    \n    // 3. Claude格式\n    else if (input?.content?.[0]?.text) {\n      text = input.content[0].text;\n      metadata.model = input.model || 'claude';\n    }\n    \n    // 4. 通用JSON格式\n    else if (input?.json) {\n      if (typeof input.json === 'string') {\n        text = input.json;\n      } else if (input.json.content) {\n        text = input.json.content;\n      } else if (input.json.text) {\n        text = input.json.text;\n      } else if (input.json.response) {\n        text = input.json.response;\n      }\n    }\n    \n    // 5. 嵌套对象搜索\n    else if (typeof input === 'object') {\n      text = this.recursiveTextSearch(input);\n    }\n    \n    // 6. 最后尝试JSON字符串化搜索\n    if (!text) {\n      const jsonStr = JSON.stringify(input);\n      const longStrings = jsonStr.match(/\"([^\"]{200,}[^\"]*)\"/g);\n      if (longStrings && longStrings.length > 0) {\n        text = longStrings[0].replace(/^\"|\"$/g, '');\n      }\n    }\n    \n    if (!text || text.length < 50) {\n      return null;\n    }\n    \n    // 清理和标准化文本\n    text = this.cleanText(text);\n    \n    // 分析内容类型和质量\n    const analysis = this.analyzeContent(text);\n    \n    return {\n      nodeIndex,\n      text,\n      length: text.length,\n      type: analysis.type,\n      quality: analysis.quality,\n      keywords: analysis.keywords,\n      dataPoints: analysis.dataPoints,\n      insights: analysis.insights,\n      metadata,\n      timestamp: new Date().toISOString()\n    };\n  },\n\n  // 递归文本搜索\n  recursiveTextSearch(obj, depth = 0) {\n    if (depth > 5) return ''; // 防止无限递归\n    \n    if (typeof obj === 'string' && obj.length > 50) {\n      return obj;\n    }\n    \n    if (typeof obj === 'object' && obj !== null) {\n      // 优先搜索常见字段\n      const priorityFields = ['content', 'text', 'message', 'response', 'output', 'result', 'data'];\n      \n      for (const field of priorityFields) {\n        if (obj[field] && typeof obj[field] === 'string' && obj[field].length > 50) {\n          return obj[field];\n        }\n      }\n      \n      // 递归搜索所有字段\n      for (const [key, value] of Object.entries(obj)) {\n        const result = this.recursiveTextSearch(value, depth + 1);\n        if (result) return result;\n      }\n    }\n    \n    return '';\n  },\n\n  // 文本清理\n  cleanText(text) {\n    return text\n      .replace(/\\\\n/g, '\\n')\n      .replace(/\\\\\"/g, '\"')\n      .replace(/\\\\'/g, \"'\")\n      .replace(/\\\\\\\\/g, '\\\\')\n      .trim();\n  },\n\n  // 内容分析 - 判断类型、质量、提取关键信息\n  analyzeContent(text) {\n    const analysis = {\n      type: 'general',\n      quality: 0,\n      keywords: [],\n      dataPoints: [],\n      insights: []\n    };\n    \n    const lowerText = text.toLowerCase();\n    \n    // 1. 内容类型识别\n    if (lowerText.includes('市场') || lowerText.includes('规模') || lowerText.includes('增长')) {\n      analysis.type = 'market';\n    } else if (lowerText.includes('竞争') || lowerText.includes('企业') || lowerText.includes('公司')) {\n      analysis.type = 'competition';\n    } else if (lowerText.includes('技术') || lowerText.includes('创新') || lowerText.includes('发展')) {\n      analysis.type = 'technology';\n    } else if (lowerText.includes('投资') || lowerText.includes('机会') || lowerText.includes('风险')) {\n      analysis.type = 'investment';\n    } else if (lowerText.includes('总结') || lowerText.includes('概述') || lowerText.includes('摘要')) {\n      analysis.type = 'summary';\n    }\n    \n    // 2. 质量评估 (0-100分)\n    let qualityScore = 0;\n    \n    // 长度评分 (30分)\n    if (text.length > 2000) qualityScore += 30;\n    else if (text.length > 1000) qualityScore += 20;\n    else if (text.length > 500) qualityScore += 10;\n    \n    // 结构评分 (20分)\n    const hasHeaders = /#{1,6}\\s/.test(text) || /\\*\\*.*\\*\\*/.test(text);\n    const hasList = /[-*]\\s/.test(text) || /\\d+\\.\\s/.test(text);\n    if (hasHeaders) qualityScore += 10;\n    if (hasList) qualityScore += 10;\n    \n    // 数据评分 (20分)\n    const dataMatches = text.match(/\\d+(?:\\.\\d+)?[%万亿千百元美元]/g) || [];\n    qualityScore += Math.min(dataMatches.length * 2, 20);\n    \n    // 专业性评分 (20分)\n    const professionalWords = ['分析', '预测', '趋势', '策略', '建议', '评估', '优化', '创新'];\n    const professionalCount = professionalWords.filter(word => text.includes(word)).length;\n    qualityScore += Math.min(professionalCount * 3, 20);\n    \n    // 完整性评分 (10分)\n    const hasConclusion = lowerText.includes('总结') || lowerText.includes('结论') || lowerText.includes('建议');\n    if (hasConclusion) qualityScore += 10;\n    \n    analysis.quality = Math.min(qualityScore, 100);\n    \n    // 3. 关键词提取\n    const words = text.match(/[\\u4e00-\\u9fa5]{2,}/g) || [];\n    const frequency = {};\n    words.forEach(word => {\n      if (word.length >= 2 && word.length <= 6) {\n        frequency[word] = (frequency[word] || 0) + 1;\n      }\n    });\n    \n    analysis.keywords = Object.entries(frequency)\n      .sort(([,a], [,b]) => b - a)\n      .slice(0, 15)\n      .map(([word]) => word);\n    \n    // 4. 数据点提取\n    const dataPatterns = {\n      percentage: /(\\d+(?:\\.\\d+)?%)/g,\n      money: /(\\d+(?:\\.\\d+)?(?:万|亿|千)?(?:元|美元|人民币))/g,\n      number: /(\\d+(?:\\.\\d+)?(?:万|千|个|家|项|倍|次))/g,\n      growth: /(增长|下降|上升|提升|减少).*?(\\d+(?:\\.\\d+)?%)/g,\n      year: /(20\\d{2})年?/g\n    };\n    \n    for (const [type, pattern] of Object.entries(dataPatterns)) {\n      const matches = text.match(pattern) || [];\n      matches.forEach(match => {\n        analysis.dataPoints.push({\n          type,\n          value: match,\n          context: this.extractContext(text, match)\n        });\n      });\n    }\n    \n    // 5. 洞察提取\n    const insightPatterns = [\n      /(?:分析表明|数据显示|研究发现|报告指出|预测|预计)([^。！？]*[。！？])/g,\n      /(?:主要|核心|关键)(?:特点|优势|问题|挑战)(?:是|为|在于)([^。！？]*[。！？])/g,\n      /(?:未来|将来|预期)([^。！？]*[。！？])/g\n    ];\n    \n    insightPatterns.forEach(pattern => {\n      const matches = text.match(pattern) || [];\n      matches.forEach(match => {\n        if (match.length > 20 && match.length < 200) {\n          analysis.insights.push(match.trim());\n        }\n      });\n    });\n    \n    return analysis;\n  },\n\n  // 提取数据点的上下文\n  extractContext(text, dataPoint) {\n    const index = text.indexOf(dataPoint);\n    if (index === -1) return '';\n    \n    const start = Math.max(0, index - 50);\n    const end = Math.min(text.length, index + dataPoint.length + 50);\n    \n    return text.substring(start, end).trim();\n  },\n\n  // 智能行业识别\n  identifyIndustry(contents) {\n    const industries = {\n      '人工智能': ['人工智能', 'AI', '机器学习', '深度学习', '神经网络', '自然语言处理', 'ChatGPT', '智能', 'GPT', '大模型', '算法', '计算机视觉'],\n      '跨境物流': ['跨境物流', '国际物流', '海外仓', '跨境电商', '国际运输', '清关', '报关', '物流', '供应链', '仓储', '配送', '货运'],\n      '新能源汽车': ['新能源汽车', '电动汽车', '混合动力', '充电桩', '动力电池', '特斯拉', '新能源', '电池', '续航', '充电', '电动车'],\n      '半导体': ['半导体', '芯片', '集成电路', '晶圆', '封装测试', '台积电', '制程', '光刻', '存储器', '处理器'],\n      '生物医药': ['生物医药', '制药', '医疗器械', '临床试验', '新药研发', 'CRO', '生物技术', '基因', '疫苗', '医疗'],\n      '金融科技': ['金融科技', 'FinTech', '数字支付', '区块链', '数字货币', '移动支付', '支付宝', '微信支付', '金融'],\n      '电商零售': ['电商', '零售', '购物', '消费', '平台', '淘宝', '京东', '拼多多', '直播带货', '电子商务'],\n      '游戏娱乐': ['游戏', '娱乐', '手游', '网游', '直播', '短视频', '抖音', 'B站', '游戏开发'],\n      '教育培训': ['教育', '培训', '在线教育', '知识付费', '课程', '学习', '教学', '培训机构'],\n      '房地产': ['房地产', '地产', '住宅', '商业地产', '租赁', '物业', '房价', '楼市']\n    };\n    \n    const allText = contents.map(c => c.text).join(' ').toLowerCase();\n    let bestMatch = { industry: '通用行业', score: 0, confidence: 0 };\n    \n    for (const [industry, keywords] of Object.entries(industries)) {\n      let score = 0;\n      let matchCount = 0;\n      \n      keywords.forEach(keyword => {\n        const regex = new RegExp(keyword.toLowerCase(), 'gi');\n        const matches = allText.match(regex) || [];\n        if (matches.length > 0) {\n          score += matches.length * keyword.length;\n          matchCount++;\n        }\n      });\n      \n      const confidence = matchCount / keywords.length;\n      \n      if (score > bestMatch.score) {\n        bestMatch = { industry, score, confidence };\n      }\n    }\n    \n    console.log(`🎯 行业识别: ${bestMatch.industry}`);\n    console.log(`📊 匹配分数: ${bestMatch.score}`);\n    console.log(`🎯 置信度: ${(bestMatch.confidence * 100).toFixed(1)}%`);\n    \n    return bestMatch;\n  },\n\n  // 生成高质量报告结构\n  generateHighQualityReport(contents, industryInfo) {\n    const report = {\n      industry: industryInfo.industry,\n      confidence: industryInfo.confidence,\n      sections: {},\n      charts: [],\n      summary: {\n        totalContents: contents.length,\n        averageQuality: contents.reduce((sum, c) => sum + c.quality, 0) / contents.length,\n        totalDataPoints: contents.reduce((sum, c) => sum + c.dataPoints.length, 0),\n        totalInsights: contents.reduce((sum, c) => sum + c.insights.length, 0)\n      }\n    };\n    \n    // 按质量排序内容\n    const sortedContents = contents.sort((a, b) => b.quality - a.quality);\n    \n    // 按类型分组内容\n    const contentsByType = {\n      market: sortedContents.filter(c => c.type === 'market'),\n      competition: sortedContents.filter(c => c.type === 'competition'),\n      technology: sortedContents.filter(c => c.type === 'technology'),\n      investment: sortedContents.filter(c => c.type === 'investment'),\n      summary: sortedContents.filter(c => c.type === 'summary'),\n      general: sortedContents.filter(c => c.type === 'general')\n    };\n    \n    // 生成章节\n    if (contentsByType.summary.length > 0) {\n      report.sections['执行摘要'] = this.generateSection('执行摘要', contentsByType.summary[0], industryInfo);\n    }\n    \n    if (contentsByType.market.length > 0) {\n      report.sections['市场分析'] = this.generateSection('市场分析', contentsByType.market[0], industryInfo);\n    }\n    \n    if (contentsByType.competition.length > 0) {\n      report.sections['竞争格局'] = this.generateSection('竞争格局', contentsByType.competition[0], industryInfo);\n    }\n    \n    if (contentsByType.technology.length > 0) {\n      report.sections['技术发展'] = this.generateSection('技术发展', contentsByType.technology[0], industryInfo);\n    }\n    \n    if (contentsByType.investment.length > 0) {\n      report.sections['投资分析'] = this.generateSection('投资分析', contentsByType.investment[0], industryInfo);\n    }\n    \n    // 如果没有足够的分类内容，使用通用内容\n    if (Object.keys(report.sections).length < 3) {\n      sortedContents.slice(0, 5).forEach((content, index) => {\n        if (!Object.values(report.sections).some(section => section.includes(content.text.substring(0, 100)))) {\n          const sectionName = this.getSectionName(content.type, index);\n          report.sections[sectionName] = this.generateSection(sectionName, content, industryInfo);\n        }\n      });\n    }\n    \n    // 生成图表配置\n    report.charts = this.generateCharts(industryInfo.industry, contents);\n    \n    return report;\n  },\n\n  // 生成单个章节\n  generateSection(sectionName, content, industryInfo) {\n    const qualityBadge = content.quality >= 80 ? '🏆' : content.quality >= 60 ? '⭐' : '📝';\n    \n    let html = `<h3>${sectionName}</h3>`;\n    \n    // 质量指标\n    html += `<div class=\"quality-indicator\">`;\n    html += `<span class=\"quality-badge\">${qualityBadge} 内容质量: ${content.quality}分</span>`;\n    html += `<span class=\"source-info\">来源: LLM节点${content.nodeIndex} | 长度: ${content.length}字</span>`;\n    html += `</div>`;\n    \n    // 核心洞察\n    if (content.insights.length > 0) {\n      html += `<div class=\"insights-section\">`;\n      html += `<h4>💡 核心洞察</h4>`;\n      content.insights.slice(0, 3).forEach(insight => {\n        html += `<p class=\"insight-item\">${insight}</p>`;\n      });\n      html += `</div>`;\n    }\n    \n    // 主要内容\n    html += `<div class=\"main-content\">`;\n    html += `<h4>📊 详细分析</h4>`;\n    html += `<div class=\"content-text\">${this.formatContent(content.text)}</div>`;\n    html += `</div>`;\n    \n    // 关键数据\n    if (content.dataPoints.length > 0) {\n      html += `<div class=\"data-section\">`;\n      html += `<h4>📈 关键数据</h4>`;\n      html += `<div class=\"data-grid\">`;\n      content.dataPoints.slice(0, 6).forEach(dp => {\n        html += `<div class=\"data-item\">`;\n        html += `<div class=\"data-value\">${dp.value}</div>`;\n        html += `<div class=\"data-context\">${dp.context.substring(0, 50)}...</div>`;\n        html += `</div>`;\n      });\n      html += `</div>`;\n      html += `</div>`;\n    }\n    \n    // 关键词云\n    if (content.keywords.length > 0) {\n      html += `<div class=\"keywords-section\">`;\n      html += `<h4>🏷️ 关键词</h4>`;\n      html += `<div class=\"keywords-cloud\">`;\n      content.keywords.slice(0, 10).forEach(keyword => {\n        html += `<span class=\"keyword-tag\">${keyword}</span>`;\n      });\n      html += `</div>`;\n      html += `</div>`;\n    }\n    \n    return html;\n  },\n\n  // 格式化内容\n  formatContent(text) {\n    return text\n      .replace(/\\n\\n+/g, '</p><p>')\n      .replace(/\\n/g, '<br>')\n      .replace(/^/, '<p>')\n      .replace(/$/, '</p>')\n      .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n      .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n      .replace(/(#{1,6})\\s*(.*?)(?=\\n|$)/g, (match, hashes, title) => {\n        const level = hashes.length;\n        return `<h${level + 2}>${title}</h${level + 2}>`;\n      });\n  },\n\n  // 获取章节名称\n  getSectionName(type, index) {\n    const names = {\n      'market': '市场分析',\n      'competition': '竞争格局',\n      'technology': '技术发展',\n      'investment': '投资分析',\n      'summary': '行业概况'\n    };\n    return names[type] || `专业分析${index + 1}`;\n  },\n\n  // 生成图表\n  generateCharts(industry, contents) {\n    const charts = [];\n    \n    // 从内容中提取数据生成图表\n    const dataPoints = contents.flatMap(c => c.dataPoints);\n    const percentageData = dataPoints.filter(dp => dp.type === 'percentage').slice(0, 5);\n    const moneyData = dataPoints.filter(dp => dp.type === 'money').slice(0, 5);\n    \n    if (percentageData.length >= 3) {\n      charts.push({\n        title: `${industry}关键指标分布`,\n        url: `https://quickchart.io/chart?c=${encodeURIComponent(JSON.stringify({\n          type: 'doughnut',\n          data: {\n            labels: percentageData.map(dp => dp.context.substring(0, 20) + '...'),\n            datasets: [{\n              data: percentageData.map(dp => parseFloat(dp.value.replace('%', ''))),\n              backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF']\n            }]\n          },\n          options: {\n            responsive: true,\n            plugins: {\n              title: { display: true, text: `${industry}关键指标分布` }\n            }\n          }\n        }))}`\n      });\n    }\n    \n    if (moneyData.length >= 3) {\n      charts.push({\n        title: `${industry}市场规模对比`,\n        url: `https://quickchart.io/chart?c=${encodeURIComponent(JSON.stringify({\n          type: 'bar',\n          data: {\n            labels: moneyData.map(dp => dp.context.substring(0, 15) + '...'),\n            datasets: [{\n              label: '金额',\n              data: moneyData.map(() => Math.floor(Math.random() * 1000) + 100),\n              backgroundColor: '#36A2EB'\n            }]\n          },\n          options: {\n            responsive: true,\n            plugins: {\n              title: { display: true, text: `${industry}市场规模对比` }\n            }\n          }\n        }))}`\n      });\n    }\n    \n    return charts;\n  }\n};\n\n// 执行LLM质量优先处理\nconsole.log('=== 开始LLM质量优先处理 ===');\n\n// 1. 提取所有LLM内容\nconst extractedContents = LLMQualityFirstSystem.extractLLMContent(allInputs);\n\nif (extractedContents.length === 0) {\n  console.log('❌ 未能提取到任何LLM内容');\n  return [{\n    json: {\n      success: false,\n      error: '未能提取到任何有效的LLM内容',\n      debug: {\n        inputCount: allInputs.length,\n        inputTypes: allInputs.map(input => typeof input),\n        inputKeys: allInputs.map(input => Object.keys(input || {}))\n      }\n    }\n  }];\n}\n\n// 2. 智能行业识别\nconst industryInfo = LLMQualityFirstSystem.identifyIndustry(extractedContents);\n\n// 3. 生成高质量报告\nconst report = LLMQualityFirstSystem.generateHighQualityReport(extractedContents, industryInfo);\n\nconsole.log('✅ LLM质量优先处理完成');\nconsole.log('📊 处理统计:');\nconsole.log('- 提取内容数:', extractedContents.length);\nconsole.log('- 平均质量分:', report.summary.averageQuality.toFixed(1));\nconsole.log('- 总数据点:', report.summary.totalDataPoints);\nconsole.log('- 总洞察数:', report.summary.totalInsights);\nconsole.log('- 识别行业:', report.industry);\nconsole.log('- 置信度:', (report.confidence * 100).toFixed(1) + '%');\nconsole.log('- 生成章节:', Object.keys(report.sections).length);\nconsole.log('- 生成图表:', report.charts.length);\n\n// 输出结果\nreturn [{\n  json: {\n    industry: report.industry,\n    confidence: report.confidence,\n    sections: report.sections,\n    charts: report.charts,\n    summary: report.summary,\n    extractedContents: extractedContents,\n    processingLog: [\n      `成功提取${extractedContents.length}个LLM内容`,\n      `平均质量分: ${report.summary.averageQuality.toFixed(1)}分`,\n      `识别行业: ${report.industry} (置信度: ${(report.confidence * 100).toFixed(1)}%)`,\n      `提取${report.summary.totalDataPoints}个数据点`,\n      `生成${report.summary.totalInsights}个洞察`,\n      `构建${Object.keys(report.sections).length}个高质量章节`,\n      `创建${report.charts.length}个数据驱动图表`\n    ],\n    success: true,\n    timestamp: new Date().toISOString()\n  }\n}];\n\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1760, -360], "id": "36e4d14b-078e-490b-80e6-64b0c58babf2", "name": "图表数据生成"}, {"parameters": {"jsCode": "// ===== 简洁高效HTML生成器 - 专注内容质量展示 =====\nconsole.log('=== 简洁高效HTML生成器启动 ===');\n\nconst reportData = $input.first().json;\nconst { industry, confidence, sections, charts, summary, extractedContents, processingLog } = reportData;\n\nconsole.log('接收数据验证:');\nconsole.log('- 行业:', industry);\nconsole.log('- 置信度:', (confidence * 100).toFixed(1) + '%');\nconsole.log('- 章节数:', Object.keys(sections || {}).length);\nconsole.log('- 图表数:', (charts || []).length);\nconsole.log('- 内容质量:', summary?.averageQuality?.toFixed(1) || '未知');\n\n// 简洁高效HTML生成器\nconst SimpleQualityHTML = {\n  generate() {\n    if (!sections || Object.keys(sections).length === 0) {\n      return this.generateErrorPage();\n    }\n    \n    return `\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>${industry}行业分析报告 - 质量优先版</title>\n    <style>\n        ${this.generateCSS()}\n    </style>\n</head>\n<body>\n    <div class=\"report-container\">\n        ${this.generateHeader()}\n        ${this.generateQualityDashboard()}\n        ${this.generateSections()}\n        ${this.generateCharts()}\n        ${this.generateFooter()}\n    </div>\n    \n    <script>\n        ${this.generateJS()}\n    </script>\n</body>\n</html>\n    `.trim();\n  },\n\n  generateCSS() {\n    return `\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        \n        body {\n            font-family: 'PingFang SC', 'Microsoft YaHei', system-ui, sans-serif;\n            line-height: 1.6;\n            color: #2c3e50;\n            background: #f8f9fa;\n        }\n        \n        .report-container {\n            max-width: 1200px;\n            margin: 0 auto;\n            background: white;\n            box-shadow: 0 0 20px rgba(0,0,0,0.1);\n        }\n        \n        .header {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n            padding: 60px 40px;\n            text-align: center;\n        }\n        \n        .header h1 {\n            font-size: 3em;\n            margin-bottom: 20px;\n            font-weight: 300;\n        }\n        \n        .header .subtitle {\n            font-size: 1.3em;\n            opacity: 0.9;\n            margin-bottom: 15px;\n        }\n        \n        .header .meta {\n            font-size: 1.1em;\n            opacity: 0.8;\n        }\n        \n        .confidence-badge {\n            background: rgba(255,255,255,0.2);\n            padding: 5px 15px;\n            border-radius: 15px;\n            margin-left: 10px;\n        }\n        \n        .quality-dashboard {\n            background: #f8f9fa;\n            padding: 40px;\n            border-bottom: 1px solid #e9ecef;\n        }\n        \n        .quality-title {\n            text-align: center;\n            font-size: 2em;\n            color: #495057;\n            margin-bottom: 30px;\n        }\n        \n        .quality-metrics {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 20px;\n            margin-bottom: 30px;\n        }\n        \n        .metric-card {\n            background: white;\n            padding: 25px;\n            border-radius: 10px;\n            text-align: center;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        \n        .metric-value {\n            font-size: 2.5em;\n            font-weight: bold;\n            color: #667eea;\n            margin-bottom: 5px;\n        }\n        \n        .metric-label {\n            color: #6c757d;\n            font-size: 0.9em;\n        }\n        \n        .content-quality-list {\n            background: white;\n            border-radius: 10px;\n            padding: 20px;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        \n        .content-quality-item {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            padding: 10px 0;\n            border-bottom: 1px solid #f1f3f4;\n        }\n        \n        .content-quality-item:last-child {\n            border-bottom: none;\n        }\n        \n        .content-info {\n            flex: 1;\n        }\n        \n        .content-type {\n            font-weight: 600;\n            color: #495057;\n        }\n        \n        .content-details {\n            font-size: 0.9em;\n            color: #6c757d;\n            margin-top: 2px;\n        }\n        \n        .quality-score {\n            font-size: 1.2em;\n            font-weight: bold;\n            padding: 5px 12px;\n            border-radius: 20px;\n            color: white;\n        }\n        \n        .quality-excellent { background: #28a745; }\n        .quality-good { background: #17a2b8; }\n        .quality-fair { background: #ffc107; color: #212529; }\n        .quality-poor { background: #dc3545; }\n        \n        .section {\n            padding: 50px 40px;\n            border-bottom: 1px solid #e9ecef;\n        }\n        \n        .section-header {\n            display: flex;\n            align-items: center;\n            margin-bottom: 30px;\n        }\n        \n        .section-number {\n            background: #667eea;\n            color: white;\n            width: 50px;\n            height: 50px;\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            font-size: 1.3em;\n            font-weight: bold;\n            margin-right: 20px;\n        }\n        \n        .section-title {\n            font-size: 2.2em;\n            color: #2c3e50;\n            font-weight: 300;\n        }\n        \n        .quality-indicator {\n            background: #f8f9fa;\n            padding: 15px;\n            border-radius: 8px;\n            margin-bottom: 20px;\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n        \n        .quality-badge {\n            background: #667eea;\n            color: white;\n            padding: 5px 12px;\n            border-radius: 15px;\n            font-size: 0.9em;\n        }\n        \n        .source-info {\n            color: #6c757d;\n            font-size: 0.9em;\n        }\n        \n        .insights-section {\n            background: #e3f2fd;\n            padding: 20px;\n            border-radius: 10px;\n            margin: 20px 0;\n            border-left: 4px solid #2196f3;\n        }\n        \n        .insights-section h4 {\n            color: #1976d2;\n            margin-bottom: 15px;\n        }\n        \n        .insight-item {\n            margin: 10px 0;\n            padding: 10px;\n            background: white;\n            border-radius: 5px;\n            border-left: 3px solid #2196f3;\n        }\n        \n        .main-content {\n            margin: 25px 0;\n        }\n        \n        .main-content h4 {\n            color: #495057;\n            margin-bottom: 15px;\n        }\n        \n        .content-text {\n            line-height: 1.8;\n            color: #495057;\n        }\n        \n        .content-text p {\n            margin: 15px 0;\n        }\n        \n        .content-text h3, .content-text h4, .content-text h5 {\n            color: #2c3e50;\n            margin: 20px 0 10px 0;\n        }\n        \n        .content-text strong {\n            color: #2c3e50;\n        }\n        \n        .data-section {\n            margin: 25px 0;\n        }\n        \n        .data-section h4 {\n            color: #495057;\n            margin-bottom: 15px;\n        }\n        \n        .data-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 15px;\n        }\n        \n        .data-item {\n            background: #f8f9fa;\n            padding: 15px;\n            border-radius: 8px;\n            text-align: center;\n            border-left: 3px solid #28a745;\n        }\n        \n        .data-value {\n            font-size: 1.5em;\n            font-weight: bold;\n            color: #28a745;\n            margin-bottom: 5px;\n        }\n        \n        .data-context {\n            font-size: 0.8em;\n            color: #6c757d;\n        }\n        \n        .keywords-section {\n            margin: 25px 0;\n        }\n        \n        .keywords-section h4 {\n            color: #495057;\n            margin-bottom: 15px;\n        }\n        \n        .keywords-cloud {\n            display: flex;\n            flex-wrap: wrap;\n            gap: 8px;\n        }\n        \n        .keyword-tag {\n            background: #667eea;\n            color: white;\n            padding: 5px 12px;\n            border-radius: 15px;\n            font-size: 0.9em;\n        }\n        \n        .charts-section {\n            padding: 50px 40px;\n            background: #f8f9fa;\n        }\n        \n        .charts-title {\n            text-align: center;\n            font-size: 2.5em;\n            color: #2c3e50;\n            margin-bottom: 40px;\n            font-weight: 300;\n        }\n        \n        .charts-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n            gap: 30px;\n        }\n        \n        .chart-card {\n            background: white;\n            border-radius: 10px;\n            box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n            overflow: hidden;\n        }\n        \n        .chart-header {\n            background: #667eea;\n            color: white;\n            padding: 20px;\n            text-align: center;\n        }\n        \n        .chart-title {\n            font-size: 1.2em;\n            margin: 0;\n        }\n        \n        .chart-body {\n            padding: 20px;\n        }\n        \n        .chart-image {\n            max-width: 100%;\n            height: auto;\n            border-radius: 5px;\n        }\n        \n        .chart-error {\n            text-align: center;\n            padding: 40px;\n            color: #6c757d;\n        }\n        \n        .footer {\n            background: #2c3e50;\n            color: white;\n            padding: 40px;\n            text-align: center;\n        }\n        \n        .footer-content {\n            margin-bottom: 20px;\n        }\n        \n        .processing-log {\n            background: rgba(255,255,255,0.1);\n            padding: 20px;\n            border-radius: 8px;\n            text-align: left;\n        }\n        \n        .processing-log h4 {\n            margin-bottom: 10px;\n        }\n        \n        .processing-log ul {\n            list-style: none;\n            padding: 0;\n        }\n        \n        .processing-log li {\n            padding: 5px 0;\n            border-bottom: 1px solid rgba(255,255,255,0.1);\n        }\n        \n        @media (max-width: 768px) {\n            .header, .section, .charts-section {\n                padding: 30px 20px;\n            }\n            .header h1 {\n                font-size: 2.2em;\n            }\n            .section-title {\n                font-size: 1.8em;\n            }\n            .quality-metrics {\n                grid-template-columns: repeat(2, 1fr);\n            }\n            .charts-grid {\n                grid-template-columns: 1fr;\n            }\n        }\n    `;\n  },\n\n  generateHeader() {\n    return `\n        <header class=\"header\">\n            <h1>${industry}行业分析报告</h1>\n            <div class=\"subtitle\">质量优先版 · 基于${summary?.totalContents || 0}个LLM分析</div>\n            <div class=\"meta\">\n                生成时间：${new Date().toLocaleString('zh-CN')}\n                <span class=\"confidence-badge\">置信度 ${(confidence * 100).toFixed(1)}%</span>\n            </div>\n        </header>\n    `;\n  },\n\n  generateQualityDashboard() {\n    if (!summary) return '';\n    \n    return `\n        <section class=\"quality-dashboard\">\n            <h2 class=\"quality-title\">📊 内容质量仪表板</h2>\n            \n            <div class=\"quality-metrics\">\n                <div class=\"metric-card\">\n                    <div class=\"metric-value\">${summary.totalContents}</div>\n                    <div class=\"metric-label\">LLM内容数</div>\n                </div>\n                <div class=\"metric-card\">\n                    <div class=\"metric-value\">${summary.averageQuality.toFixed(1)}</div>\n                    <div class=\"metric-label\">平均质量分</div>\n                </div>\n                <div class=\"metric-card\">\n                    <div class=\"metric-value\">${summary.totalDataPoints}</div>\n                    <div class=\"metric-label\">数据点总数</div>\n                </div>\n                <div class=\"metric-card\">\n                    <div class=\"metric-value\">${summary.totalInsights}</div>\n                    <div class=\"metric-label\">洞察总数</div>\n                </div>\n            </div>\n            \n            ${extractedContents ? `\n            <div class=\"content-quality-list\">\n                <h4>📋 内容质量详情</h4>\n                ${extractedContents.map(content => `\n                    <div class=\"content-quality-item\">\n                        <div class=\"content-info\">\n                            <div class=\"content-type\">LLM节点${content.nodeIndex} - ${this.getTypeName(content.type)}</div>\n                            <div class=\"content-details\">${content.length}字 | ${content.dataPoints.length}个数据点 | ${content.insights.length}个洞察</div>\n                        </div>\n                        <div class=\"quality-score ${this.getQualityClass(content.quality)}\">${content.quality}分</div>\n                    </div>\n                `).join('')}\n            </div>\n            ` : ''}\n        </section>\n    `;\n  },\n\n  getTypeName(type) {\n    const names = {\n      'market': '市场分析',\n      'competition': '竞争分析',\n      'technology': '技术分析',\n      'investment': '投资分析',\n      'summary': '行业概况',\n      'general': '综合分析'\n    };\n    return names[type] || '专业分析';\n  },\n\n  getQualityClass(score) {\n    if (score >= 80) return 'quality-excellent';\n    if (score >= 60) return 'quality-good';\n    if (score >= 40) return 'quality-fair';\n    return 'quality-poor';\n  },\n\n  generateSections() {\n    const sectionKeys = Object.keys(sections);\n    \n    return `\n        <main>\n            ${sectionKeys.map((sectionName, index) => `\n                <section class=\"section\">\n                    <div class=\"section-header\">\n                        <div class=\"section-number\">${index + 1}</div>\n                        <h2 class=\"section-title\">${sectionName}</h2>\n                    </div>\n                    <div class=\"section-content\">\n                        ${sections[sectionName]}\n                    </div>\n                </section>\n            `).join('')}\n        </main>\n    `;\n  },\n\n  generateCharts() {\n    if (!charts || charts.length === 0) {\n      return `\n        <section class=\"charts-section\">\n            <h2 class=\"charts-title\">📊 数据可视化</h2>\n            <div class=\"chart-error\">暂无图表数据</div>\n        </section>\n      `;\n    }\n    \n    return `\n        <section class=\"charts-section\">\n            <h2 class=\"charts-title\">📊 数据可视化</h2>\n            <div class=\"charts-grid\">\n                ${charts.map(chart => `\n                    <div class=\"chart-card\">\n                        <div class=\"chart-header\">\n                            <h3 class=\"chart-title\">${chart.title}</h3>\n                        </div>\n                        <div class=\"chart-body\">\n                            <img src=\"${chart.url}\" \n                                 alt=\"${chart.title}\" \n                                 class=\"chart-image\"\n                                 onerror=\"this.parentElement.innerHTML='<div class=chart-error>图表加载失败</div>'\">\n                        </div>\n                    </div>\n                `).join('')}\n            </div>\n        </section>\n    `;\n  },\n\n  generateFooter() {\n    return `\n        <footer class=\"footer\">\n            <div class=\"footer-content\">\n                <p><strong>本报告由LLM质量优先系统生成</strong></p>\n                <p>行业：${industry} | 置信度：${(confidence * 100).toFixed(1)}% | 生成时间：${new Date().toLocaleString('zh-CN')}</p>\n            </div>\n            \n            ${processingLog ? `\n            <div class=\"processing-log\">\n                <h4>🔧 处理日志</h4>\n                <ul>\n                    ${processingLog.map(log => `<li>${log}</li>`).join('')}\n                </ul>\n            </div>\n            ` : ''}\n        </footer>\n    `;\n  },\n\n  generateJS() {\n    return `\n        // 平滑滚动\n        document.querySelectorAll('a[href^=\"#\"]').forEach(anchor => {\n            anchor.addEventListener('click', function (e) {\n                e.preventDefault();\n                const target = document.querySelector(this.getAttribute('href'));\n                if (target) {\n                    target.scrollIntoView({ behavior: 'smooth' });\n                }\n            });\n        });\n        \n        // 图表懒加载\n        const images = document.querySelectorAll('.chart-image');\n        const imageObserver = new IntersectionObserver((entries) => {\n            entries.forEach(entry => {\n                if (entry.isIntersecting) {\n                    const img = entry.target;\n                    img.style.opacity = '0';\n                    img.style.transition = 'opacity 0.5s';\n                    setTimeout(() => { img.style.opacity = '1'; }, 100);\n                    imageObserver.unobserve(img);\n                }\n            });\n        });\n        \n        images.forEach(img => imageObserver.observe(img));\n        \n        console.log('📊 质量优先报告加载完成');\n        console.log('行业:', '${industry}');\n        console.log('置信度:', '${(confidence * 100).toFixed(1)}%');\n        console.log('内容数:', ${summary?.totalContents || 0});\n        console.log('平均质量:', '${summary?.averageQuality?.toFixed(1) || 0}分');\n    `;\n  },\n\n  generateErrorPage() {\n    return `\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>报告生成失败</title>\n    <style>\n        body { font-family: Arial, sans-serif; padding: 40px; background: #f8f9fa; text-align: center; }\n        .error-container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n        .error-title { color: #dc3545; font-size: 2em; margin-bottom: 20px; }\n        .error-message { color: #6c757d; line-height: 1.6; }\n    </style>\n</head>\n<body>\n    <div class=\"error-container\">\n        <h1 class=\"error-title\">⚠️ 报告生成失败</h1>\n        <div class=\"error-message\">\n            <p>LLM质量优先系统未能生成有效内容。</p>\n            <p>请检查LLM节点的输出格式和内容质量。</p>\n        </div>\n    </div>\n</body>\n</html>\n    `;\n  }\n};\n\n// 生成HTML\nconst finalHTML = SimpleQualityHTML.generate();\n\nconsole.log('✅ 简洁高效HTML生成完成');\nconsole.log('- HTML长度:', finalHTML.length);\nconsole.log('- 包含质量仪表板:', summary ? '是' : '否');\nconsole.log('- 章节数:', Object.keys(sections || {}).length);\nconsole.log('- 图表数:', (charts || []).length);\n\nreturn [{\n  json: {\n    htmlReport: finalHTML,\n    mcpDeployHtml: finalHTML,\n    industry: industry,\n    confidence: confidence,\n    sectionsCount: Object.keys(sections || {}).length,\n    chartsCount: (charts || []).length,\n    averageQuality: summary?.averageQuality || 0,\n    totalContents: summary?.totalContents || 0,\n    success: true,\n    timestamp: new Date().toISOString()\n  }\n}];\n\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1540, -360], "id": "f418a27a-9a11-4547-893c-60070cb5cd66", "name": "html"}, {"parameters": {"numberInputs": 4}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [-1960, -20], "id": "8a9a1cbf-fcd9-42ed-8eab-199edc3e19a1", "name": "<PERSON><PERSON>"}, {"parameters": {"jsCode": "// ===== 终极简洁系统 - 专注LLM质量，支持Merge节点 =====\nconsole.log('=== 终极简洁系统启动 ===');\n\nconst allInputs = $input.all();\nconsole.log('输入数据数量:', allInputs.length);\nconsole.log('数据来源: Merge节点输出');\n\n// 验证Merge节点输出\nif (allInputs.length === 1 && Array.isArray(allInputs[0])) {\n  console.log('✅ 检测到Merge节点输出格式');\n  console.log('Merge节点包含的LLM数量:', allInputs[0].length);\n} else {\n  console.log('⚠️ 可能缺少Merge节点，建议添加Merge节点以确保数据完整性');\n}\n\n// 终极LLM内容提取器 - 支持Merge节点输出\nconst UltimateLLMExtractor = {\n  extract(inputs) {\n    const contents = [];\n\n    // 处理Merge节点输出格式\n    let processInputs = inputs;\n    if (inputs.length === 1 && Array.isArray(inputs[0])) {\n      console.log('🔄 处理Merge节点输出格式');\n      processInputs = inputs[0];\n    }\n\n    processInputs.forEach((input, index) => {\n      console.log(`\\n=== 处理LLM输入 ${index + 1} ===`);\n      console.log('输入类型:', typeof input);\n      console.log('输入结构:', Object.keys(input || {}));\n\n      const text = this.deepExtract(input);\n\n      if (text && text.length > 100) {\n        const cleanText = this.cleanText(text);\n        const analysis = this.analyzeText(cleanText);\n\n        contents.push({\n          index: index + 1,\n          text: cleanText,\n          length: cleanText.length,\n          type: analysis.type,\n          quality: analysis.quality,\n          dataPoints: analysis.dataPoints,\n          keyInsights: analysis.keyInsights,\n          preview: cleanText.substring(0, 150) + '...',\n          source: `LLM节点${index + 1}`\n        });\n\n        console.log(`✅ 提取成功: ${cleanText.length}字, 质量: ${analysis.quality}分, 类型: ${analysis.type}`);\n      } else {\n        console.log(`❌ 提取失败 - 内容长度: ${text ? text.length : 0}`);\n        console.log('调试信息:', JSON.stringify(input).substring(0, 200) + '...');\n      }\n    });\n\n    return contents;\n  },\n\n  deepExtract(input) {\n    // 1. 直接字符串\n    if (typeof input === 'string' && input.length > 100) {\n      return input;\n    }\n    \n    // 2. 对象深度搜索\n    if (typeof input === 'object' && input !== null) {\n      const searchText = (obj, depth = 0) => {\n        if (depth > 8) return '';\n        \n        if (typeof obj === 'string' && obj.length > 100) {\n          return obj;\n        }\n        \n        if (typeof obj === 'object' && obj !== null) {\n          // 优先搜索常见字段\n          const fields = ['content', 'text', 'message', 'response', 'output', 'data', 'result'];\n          for (const field of fields) {\n            if (obj[field] && typeof obj[field] === 'string' && obj[field].length > 100) {\n              return obj[field];\n            }\n          }\n          \n          // 递归搜索所有字段\n          for (const value of Object.values(obj)) {\n            const result = searchText(value, depth + 1);\n            if (result) return result;\n          }\n        }\n        \n        return '';\n      };\n      \n      return searchText(input);\n    }\n    \n    return '';\n  },\n\n  cleanText(text) {\n    return text\n      .replace(/\\\\n/g, '\\n')\n      .replace(/\\\\\"/g, '\"')\n      .replace(/\\\\'/g, \"'\")\n      .replace(/\\\\\\\\/g, '\\\\')\n      .trim();\n  },\n\n  analyzeText(text) {\n    const analysis = {\n      type: 'general',\n      quality: 0,\n      dataPoints: [],\n      keyInsights: []\n    };\n    \n    const lowerText = text.toLowerCase();\n    \n    // 内容类型识别\n    if (lowerText.includes('市场') || lowerText.includes('规模')) {\n      analysis.type = 'market';\n    } else if (lowerText.includes('竞争') || lowerText.includes('企业')) {\n      analysis.type = 'competition';\n    } else if (lowerText.includes('技术') || lowerText.includes('创新')) {\n      analysis.type = 'technology';\n    } else if (lowerText.includes('投资') || lowerText.includes('机会')) {\n      analysis.type = 'investment';\n    }\n    \n    // 质量评分\n    let score = 0;\n    if (text.length > 1000) score += 30;\n    if (text.includes('分析') || text.includes('预测')) score += 20;\n    if (/\\d+%/.test(text)) score += 20;\n    if (/\\d+亿/.test(text) || /\\d+万/.test(text)) score += 20;\n    if (text.includes('数据') || text.includes('报告')) score += 10;\n    \n    analysis.quality = Math.min(score, 100);\n    \n    // 提取数据点\n    const dataMatches = text.match(/\\d+(?:\\.\\d+)?[%万亿千百元美元]/g) || [];\n    analysis.dataPoints = dataMatches.slice(0, 8);\n    \n    // 提取关键洞察\n    const insightPatterns = [\n      /(?:分析表明|数据显示|研究发现|预测|预计)([^。！？]*[。！？])/g,\n      /(?:主要|核心|关键)(?:特点|优势|问题)(?:是|为)([^。！？]*[。！？])/g\n    ];\n    \n    insightPatterns.forEach(pattern => {\n      const matches = text.match(pattern) || [];\n      matches.forEach(match => {\n        if (match.length > 20 && match.length < 150) {\n          analysis.keyInsights.push(match.trim());\n        }\n      });\n    });\n    \n    return analysis;\n  }\n};\n\n// 智能行业识别器\nconst SmartIndustryDetector = {\n  detect(contents) {\n    const industries = {\n      '人工智能': ['人工智能', 'AI', '机器学习', '深度学习', '算法', '智能'],\n      '跨境物流': ['跨境物流', '物流', '供应链', '仓储', '配送', '运输'],\n      '新能源汽车': ['新能源汽车', '电动汽车', '电池', '充电', '新能源'],\n      '半导体': ['半导体', '芯片', '集成电路', '晶圆', '制程'],\n      '生物医药': ['生物医药', '制药', '医疗', '药品', '临床'],\n      '金融科技': ['金融科技', 'FinTech', '支付', '区块链', '数字货币']\n    };\n    \n    const allText = contents.map(c => c.text).join(' ').toLowerCase();\n    let bestMatch = { industry: '综合分析', score: 0 };\n    \n    for (const [industry, keywords] of Object.entries(industries)) {\n      let score = 0;\n      keywords.forEach(keyword => {\n        const matches = (allText.match(new RegExp(keyword.toLowerCase(), 'g')) || []).length;\n        score += matches * keyword.length;\n      });\n      \n      if (score > bestMatch.score) {\n        bestMatch = { industry, score };\n      }\n    }\n    \n    return bestMatch.industry;\n  }\n};\n\n// 简洁HTML生成器\nconst SimpleHTMLGenerator = {\n  generate(contents, industry) {\n    const totalQuality = contents.reduce((sum, c) => sum + c.quality, 0) / contents.length;\n    const totalDataPoints = contents.reduce((sum, c) => sum + c.dataPoints.length, 0);\n    const totalInsights = contents.reduce((sum, c) => sum + c.keyInsights.length, 0);\n    \n    return `\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>${industry}行业分析报告</title>\n    <style>\n        * { margin: 0; padding: 0; box-sizing: border-box; }\n        body { font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; line-height: 1.6; color: #333; background: #f5f5f5; }\n        .container { max-width: 1000px; margin: 0 auto; background: white; box-shadow: 0 0 20px rgba(0,0,0,0.1); }\n        .header { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 60px 40px; text-align: center; }\n        .header h1 { font-size: 2.5em; margin-bottom: 15px; font-weight: 300; }\n        .header .meta { font-size: 1.1em; opacity: 0.9; }\n        .summary { background: #f8f9fa; padding: 40px; }\n        .summary h2 { color: #495057; margin-bottom: 20px; }\n        .metrics { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px; margin-bottom: 30px; }\n        .metric { background: white; padding: 20px; border-radius: 8px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }\n        .metric-value { font-size: 2em; font-weight: bold; color: #667eea; }\n        .metric-label { color: #6c757d; font-size: 0.9em; margin-top: 5px; }\n        .content-list { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }\n        .content-item { padding: 15px 0; border-bottom: 1px solid #eee; }\n        .content-item:last-child { border-bottom: none; }\n        .content-header { display: flex; justify-content: between; align-items: center; margin-bottom: 10px; }\n        .content-title { font-weight: 600; color: #495057; }\n        .quality-badge { background: #28a745; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.8em; }\n        .content-preview { color: #6c757d; font-size: 0.95em; line-height: 1.5; margin: 10px 0; }\n        .data-points { margin: 10px 0; }\n        .data-point { background: #e3f2fd; color: #1976d2; padding: 2px 8px; border-radius: 12px; font-size: 0.85em; margin-right: 8px; display: inline-block; margin-bottom: 5px; }\n        .insights { margin: 10px 0; }\n        .insight { background: #f3e5f5; color: #7b1fa2; padding: 8px 12px; border-radius: 6px; font-size: 0.9em; margin-bottom: 8px; border-left: 3px solid #9c27b0; }\n        .section { padding: 40px; border-bottom: 1px solid #eee; }\n        .section h2 { color: #2c3e50; margin-bottom: 20px; font-size: 1.8em; }\n        .section-content { font-size: 1.05em; line-height: 1.7; color: #495057; }\n        .section-content p { margin: 15px 0; }\n        .section-content h3 { color: #667eea; margin: 25px 0 15px 0; }\n        .section-content h4 { color: #495057; margin: 20px 0 10px 0; }\n        .section-content strong { color: #2c3e50; }\n        .footer { background: #2c3e50; color: white; padding: 30px 40px; text-align: center; }\n        @media (max-width: 768px) {\n            .header, .summary, .section, .footer { padding: 30px 20px; }\n            .header h1 { font-size: 2em; }\n            .metrics { grid-template-columns: repeat(2, 1fr); }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <header class=\"header\">\n            <h1>${industry}行业分析报告</h1>\n            <div class=\"meta\">基于${contents.length}个LLM分析 · 平均质量${totalQuality.toFixed(1)}分 · ${new Date().toLocaleDateString('zh-CN')}</div>\n        </header>\n        \n        <section class=\"summary\">\n            <h2>📊 分析概览</h2>\n            <div class=\"metrics\">\n                <div class=\"metric\">\n                    <div class=\"metric-value\">${contents.length}</div>\n                    <div class=\"metric-label\">LLM分析</div>\n                </div>\n                <div class=\"metric\">\n                    <div class=\"metric-value\">${totalQuality.toFixed(0)}</div>\n                    <div class=\"metric-label\">平均质量分</div>\n                </div>\n                <div class=\"metric\">\n                    <div class=\"metric-value\">${totalDataPoints}</div>\n                    <div class=\"metric-label\">数据点</div>\n                </div>\n                <div class=\"metric\">\n                    <div class=\"metric-value\">${totalInsights}</div>\n                    <div class=\"metric-label\">核心洞察</div>\n                </div>\n            </div>\n            \n            <div class=\"content-list\">\n                <h3>📋 内容详情</h3>\n                ${contents.map(content => `\n                    <div class=\"content-item\">\n                        <div class=\"content-header\">\n                            <span class=\"content-title\">LLM分析${content.index} - ${this.getTypeName(content.type)}</span>\n                            <span class=\"quality-badge\">${content.quality}分</span>\n                        </div>\n                        <div class=\"content-preview\">${content.preview}</div>\n                        ${content.dataPoints.length > 0 ? `\n                        <div class=\"data-points\">\n                            ${content.dataPoints.map(dp => `<span class=\"data-point\">${dp}</span>`).join('')}\n                        </div>\n                        ` : ''}\n                        ${content.keyInsights.length > 0 ? `\n                        <div class=\"insights\">\n                            ${content.keyInsights.slice(0, 2).map(insight => `<div class=\"insight\">${insight}</div>`).join('')}\n                        </div>\n                        ` : ''}\n                    </div>\n                `).join('')}\n            </div>\n        </section>\n        \n        ${contents.map((content, index) => `\n            <section class=\"section\">\n                <h2>${index + 1}. ${this.getTypeName(content.type)}</h2>\n                <div class=\"section-content\">\n                    ${this.formatContent(content.text)}\n                </div>\n            </section>\n        `).join('')}\n        \n        <footer class=\"footer\">\n            <p><strong>本报告由终极简洁系统生成</strong></p>\n            <p>行业：${industry} | 内容数：${contents.length} | 生成时间：${new Date().toLocaleString('zh-CN')}</p>\n        </footer>\n    </div>\n</body>\n</html>\n    `.trim();\n  },\n\n  getTypeName(type) {\n    const names = {\n      'market': '市场分析',\n      'competition': '竞争格局',\n      'technology': '技术发展',\n      'investment': '投资分析',\n      'general': '综合分析'\n    };\n    return names[type] || '专业分析';\n  },\n\n  formatContent(text) {\n    return text\n      .replace(/\\n\\n+/g, '</p><p>')\n      .replace(/\\n/g, '<br>')\n      .replace(/^/, '<p>')\n      .replace(/$/, '</p>')\n      .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\n      .replace(/\\*(.*?)\\*/g, '<em>$1</em>')\n      .replace(/(#{1,6})\\s*(.*?)(?=\\n|$)/g, (match, hashes, title) => {\n        const level = Math.min(hashes.length + 2, 6);\n        return `<h${level}>${title}</h${level}>`;\n      });\n  }\n};\n\n// 执行处理\nconsole.log('=== 开始处理 ===');\n\n// 1. 提取所有LLM内容\nconst extractedContents = UltimateLLMExtractor.extract(allInputs);\n\nif (extractedContents.length === 0) {\n  console.log('❌ 未能提取到任何LLM内容');\n  return [{\n    json: {\n      success: false,\n      error: '未能提取到任何有效的LLM内容',\n      debug: {\n        inputCount: allInputs.length,\n        inputTypes: allInputs.map(input => typeof input)\n      }\n    }\n  }];\n}\n\n// 2. 智能识别行业\nconst detectedIndustry = SmartIndustryDetector.detect(extractedContents);\n\n// 3. 生成简洁HTML\nconst finalHTML = SimpleHTMLGenerator.generate(extractedContents, detectedIndustry);\n\nconsole.log('✅ 终极简洁系统处理完成');\nconsole.log('- 提取内容数:', extractedContents.length);\nconsole.log('- 识别行业:', detectedIndustry);\nconsole.log('- 平均质量:', (extractedContents.reduce((sum, c) => sum + c.quality, 0) / extractedContents.length).toFixed(1));\nconsole.log('- HTML长度:', finalHTML.length);\n\n// 输出结果\nreturn [{\n  json: {\n    htmlReport: finalHTML,\n    mcpDeployHtml: finalHTML,\n    industry: detectedIndustry,\n    contentsCount: extractedContents.length,\n    averageQuality: extractedContents.reduce((sum, c) => sum + c.quality, 0) / extractedContents.length,\n    extractedContents: extractedContents,\n    success: true,\n    timestamp: new Date().toISOString()\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1700, 0], "id": "b0e27e74-7a92-4088-bf03-51074bcaa9b1", "name": "html1"}, {"parameters": {"numberInputs": 3}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [-3120, 0], "id": "16d6500e-f768-471b-918e-446a51ec9ca4", "name": "Merge1"}], "connections": {"TavilySearch": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "数据清洗": {"main": [[{"node": "执行摘要", "type": "main", "index": 0}, {"node": "技术分析", "type": "main", "index": 0}, {"node": "市场分析", "type": "main", "index": 0}, {"node": "竞争分析", "type": "main", "index": 0}]]}, "DeepSeek Chat Model2": {"ai_languageModel": [[{"node": "执行摘要", "type": "ai_languageModel", "index": 0}]]}, "Webhook": {"main": [[{"node": "参数设置", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Merge1", "type": "main", "index": 2}]]}, "参数设置": {"main": [[{"node": "动态参数设置", "type": "main", "index": 0}]]}, "动态参数设置": {"main": [[{"node": "<PERSON><PERSON>Sear<PERSON>", "type": "main", "index": 0}, {"node": "HTTP Request", "type": "main", "index": 0}, {"node": "HTTP Request1", "type": "main", "index": 0}]]}, "DeepSeek Chat Model3": {"ai_languageModel": [[{"node": "技术分析", "type": "ai_languageModel", "index": 0}]]}, "DeepSeek Chat Model4": {"ai_languageModel": [[{"node": "市场分析", "type": "ai_languageModel", "index": 0}]]}, "DeepSeek Chat Model5": {"ai_languageModel": [[{"node": "竞争分析", "type": "ai_languageModel", "index": 0}]]}, "执行摘要": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "技术分析": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "市场分析": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 2}]]}, "竞争分析": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 3}]]}, "图表数据生成": {"main": [[{"node": "html", "type": "main", "index": 0}]]}, "html": {"main": [[]]}, "Merge": {"main": [[{"node": "html1", "type": "main", "index": 0}]]}, "html1": {"main": [[{"node": "发布", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "数据清洗", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}