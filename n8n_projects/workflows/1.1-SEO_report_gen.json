{"name": "1.1-SEO_report_gen", "nodes": [{"parameters": {"formTitle": "James工作流 SEO 关键词分析引擎", "formDescription": "报告生成快人一步， SEO 优化用人一筹。", "formFields": {"values": [{"fieldLabel": "主关键词", "requiredField": true}, {"fieldLabel": "公司品牌"}, {"fieldLabel": "公司介绍", "fieldType": "textarea"}, {"fieldLabel": "目标用户"}]}, "options": {"appendAttribution": false, "buttonLabel": "撰写报告", "path": "seo-report"}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-1980, 240], "id": "da414370-d30d-4e88-b0b4-f6e6a9b962c2", "name": "输入需求", "webhookId": "acf86b48-0b71-4f9c-a57e-ba58169fdfea"}, {"parameters": {"assignments": {"assignments": [{"id": "d701c3e2-c6a3-4a91-9f67-4f76d22c6eea", "name": "主关键词", "value": "={{ $json[\"主关键词\"] }}", "type": "string"}, {"id": "96bd6c06-54d8-40dc-9b4b-7beec9259fd6", "name": "公司品牌", "value": "={{ $json[\"公司品牌\"] }}", "type": "string"}, {"id": "32aebb5f-4687-405e-93f7-da9283ce97d8", "name": "公司介绍", "value": "={{ $json[\"公司介绍\"] }}", "type": "string"}, {"id": "d20f96de-44a9-4981-9b89-7cdb6c041511", "name": "目标用户", "value": "={{ $json[\"目标用户\"] }}", "type": "string"}, {"id": "aeb721fd-c6b2-4126-ab4b-5ab3f4df78d0", "name": "分支", "value": "表格", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1760, 240], "id": "34a0f306-aa4b-4fc0-9c2a-dea1eed7bf7a", "name": "设置参数-表格"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [-1540, 360], "id": "837a582a-e5f0-427a-8fed-a0ace388d76b", "name": "聚合分支"}, {"parameters": {"workflowInputs": {"values": [{"name": "主关键词"}, {"name": "公司品牌"}, {"name": "公司介绍"}, {"name": "目标用户"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-1980, 440], "id": "55ffdb4e-53ee-43b2-931f-d1378a9daf6e", "name": "When Executed by Another Workflow"}, {"parameters": {"assignments": {"assignments": [{"id": "d701c3e2-c6a3-4a91-9f67-4f76d22c6eea", "name": "主关键词", "value": "={{ $json[\"主关键词\"] }}", "type": "string"}, {"id": "96bd6c06-54d8-40dc-9b4b-7beec9259fd6", "name": "公司品牌", "value": "={{ $json[\"公司品牌\"] }}", "type": "string"}, {"id": "32aebb5f-4687-405e-93f7-da9283ce97d8", "name": "公司介绍", "value": "={{ $json[\"公司介绍\"] }}", "type": "string"}, {"id": "d20f96de-44a9-4981-9b89-7cdb6c041511", "name": "目标用户", "value": "={{ $json[\"目标用户\"] }}", "type": "string"}, {"id": "aeb721fd-c6b2-4126-ab4b-5ab3f4df78d0", "name": "分支", "value": "工作流", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1760, 440], "id": "4d659dbc-8d6f-46ed-9a13-17980a33bbce", "name": "设置参数-子工作流"}, {"parameters": {"assignments": {"assignments": [{"id": "d701c3e2-c6a3-4a91-9f67-4f76d22c6eea", "name": "主关键词", "value": "={{ $json[\"主关键词\"] }}", "type": "string"}, {"id": "96bd6c06-54d8-40dc-9b4b-7beec9259fd6", "name": "公司品牌", "value": "={{ $json[\"公司品牌\"] }}", "type": "string"}, {"id": "32aebb5f-4687-405e-93f7-da9283ce97d8", "name": "公司介绍", "value": "={{ $json[\"公司介绍\"] }}", "type": "string"}, {"id": "d20f96de-44a9-4981-9b89-7cdb6c041511", "name": "目标用户", "value": "={{ $json[\"目标用户\"] }}", "type": "string"}, {"id": "aeb721fd-c6b2-4126-ab4b-5ab3f4df78d0", "name": "分支", "value": "={{ $json[\"分支\"] }}", "type": "string"}, {"id": "0253fed5-354e-421a-879d-411383f079d4", "name": "302ai api key", "value": "sk-LMfpbQt8WPp71EXwFmW6fedhTJX9J4lag5znVNRReG5fWPSc", "type": "string"}, {"id": "2c4b5f86-705b-4138-aea9-8cd63ffe5aa7", "name": "jina api key", "value": "Bearer jina_29838aeeca404d859b2c52a0f90c352ceVP_1fga2siRtBiHzTmdAt7Y-8-Z", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1320, 360], "id": "e17ecfbc-9c64-4736-a78b-629afa688855", "name": "设置参数-综合"}, {"parameters": {"url": "https://api.302.ai/searchapi/search", "sendQuery": true, "queryParameters": {"parameters": [{"name": "api_key", "value": "={{ $json[\"302ai api key\"] }}"}, {"name": "engine", "value": "google"}, {"name": "q", "value": "={{ $json[\"主关键词\"] }}"}, {"name": "num", "value": "10"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1100, 360], "id": "1225e409-95e5-4bce-bee9-fa0eeb9950d2", "name": "HTTP Request"}, {"parameters": {"fieldToSplitOut": "organic_results", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-880, 360], "id": "279f293e-09c6-4637-ae0d-156b586684a7", "name": "遍历-网址"}, {"parameters": {"url": "=https://r.jina.ai/{{ $json.link }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "={{ $('设置参数-综合').item.json['jina api key'] }}"}, {"name": "X-Return_Format", "value": "html"}, {"name": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "value": "true"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-440, 360], "id": "fc597640-df53-4ca8-8499-867e0004b959", "name": "爬取-HTML", "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "return items.map(item => {\n    const html = item.json.html || '';\n\n    // 自动提取 canonical 作为 Competitor URL\n    function extractCompetitorUrl(html) {\n        const match = html.match(/<link\\s+rel=[\"']canonical[\"']\\s+href=[\"']([^\"']+)[\"']/i);\n        return match ? match[1] : '';\n    }\n    const competitorUrl = extractCompetitorUrl(html);\n\n    // Meta\n    function cleanText(text) {\n        return text.trim().replace(/&amp;/g, '&').replace(/&lt;/g, '<').replace(/&gt;/g, '>');\n    }\n    const metaTags = [\n        { name: 'og:image:type', regex: /<meta\\s+property=\"og:image:type\"\\s+content=\"([^\"]+)\"\\s*[\\/>]?>/i },\n        { name: 'twitter:card', regex: /<meta\\s+name=\"twitter:card\"\\s+content=\"([^\"]+)\"\\s*[\\/>]?>/i },\n        { name: 'twitter:title', regex: /<meta\\s+name=\"twitter:title\"\\s+content=\"([^\"]+)\"\\s*[\\/>]?>/i },\n        { name: 'twitter:site', regex: /<meta\\s+name=\"twitter:site\"\\s+content=\"([^\"]+)\"\\s*[\\/>]?>/i }\n    ];\n    const meta = {};\n    metaTags.forEach(tag => {\n        const match = html.match(tag.regex);\n        if (match && match[1]) {\n            meta[tag.name] = cleanText(match[1]);\n        }\n    });\n\n    // Ngrams\n    function extractNgrams(text, n = 2) {\n        const tokens = text.replace(/<[^>]+>/g, '').replace(/[^\\w\\u4e00-\\u9fa5]+/g, ' ').split(/\\s+/).filter(Boolean);\n        const ngrams = [];\n        for (let i = 0; i <= tokens.length - n; i++) {\n            ngrams.push(tokens.slice(i, i + n).join(' '));\n        }\n        return ngrams;\n    }\n    const bodyText = html.match(/<body[^>]*>([\\s\\S]*?)<\\/body>/i)?.[1] || '';\n    const ngrams = extractNgrams(bodyText, 2);\n\n    // Outline\n    function extractOutline(html) {\n        const outline = [];\n        const tagRegex = /<([a-zA-Z0-9]+)(\\s|>)/g;\n        let match;\n        while ((match = tagRegex.exec(html)) !== null) {\n            outline.push(match[1].toLowerCase());\n        }\n        return Array.from(new Set(outline));\n    }\n    const outline = extractOutline(html);\n\n    // 透传所有原始字段\n    return {\n        json: {\n            ...item.json, // 保留所有原始字段（如 position, title, link, source, domain, displayed link 等）\n            \"Competitor URL\": competitorUrl,\n            \"Outline\": outline,\n            \"Meta\": meta,\n            \"Ngrams\": ngrams\n        }\n    };\n});"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-220, 360], "id": "e6066482-0ee1-4adf-80dd-fa1d2299d12b", "name": "提取-HTML"}, {"parameters": {"assignments": {"assignments": [{"id": "543b689b-b78b-4297-81bf-3f4c31738c69", "name": "Competitor URL", "value": "={{ $('循环').item.json.link }}", "type": "string"}, {"id": "b10f15f6-9c17-4edb-8470-ee15bb271a06", "name": "Outline", "value": "={{ JSON.stringify($json.Outline) }}", "type": "string"}, {"id": "f078f4b9-9a3d-4250-8a1d-187f3cb62e74", "name": "Meta", "value": "={{ JSON.stringify($json.Meta) }}", "type": "string"}, {"id": "54706ad5-4879-4bef-b84c-52f2bc53e0e3", "name": "Ngrams", "value": "={{ JSON.stringify($json.Ngrams) }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [80, 440], "id": "3dc6687d-f29a-448c-ba33-dcb59300aa5d", "name": "设置参数-抓取"}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [-660, 360], "id": "585451ba-9a28-4b54-9794-2478a37dadf3", "name": "循环"}, {"parameters": {"jsCode": "let output = \"\";\n\nitems.forEach((data, index) => {\n    output += `<competitor${index + 1}>\\n`;\n    output += `<competitor_url>${data.json[\"Competitor URL\"] || data.json.url || ''}</competitor_url>\\n`;\n\n    // 透传常见SERP字段\n    if (data.json.position !== undefined) output += `<position>${data.json.position}</position>\\n`;\n    if (data.json.title) output += `<title>${data.json.title}</title>\\n`;\n    if (data.json.link) output += `<link>${data.json.link}</link>\\n`;\n    if (data.json.source) output += `<source>${data.json.source}</source>\\n`;\n    if (data.json.domain) output += `<domain>${data.json.domain}</domain>\\n`;\n    if (data.json[\"displayed link\"]) output += `<displayed_link>${data.json[\"displayed link\"]}</displayed_link>\\n`;\n\n    // Outline\n    output += `<outline>\\n${JSON.stringify(data.json.Outline || data.json.outline || [], null, 2)}\\n</outline>\\n`;\n    // Meta\n    output += `<meta>\\n${JSON.stringify(data.json.Meta || data.json.meta || {}, null, 2)}\\n</meta>\\n`;\n    // Ngrams\n    output += `<ngrams>\\n${JSON.stringify(data.json.Ngrams || data.json.ngrams || [], null, 2)}\\n</ngrams>\\n`;\n\n    output += `</competitor${index + 1}>\\n\\n`;\n});\n\nreturn [\n  {\n    json: {\n      competitors_data: output.trim(),\n      competitors_json: items.map(item => item.json)\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-440, 100], "id": "9952ecb1-29be-4fd2-8a8d-9551345738c2", "name": "聚合结果"}, {"parameters": {"assignments": {"assignments": [{"id": "ba0266bf-f264-4d16-90b3-e5844e6d0647", "name": "竞争者信息", "value": "={{ $json.competitors_data }}", "type": "string"}, {"id": "51e8e702-c02c-4a04-a909-4800f94d146f", "name": "", "value": "", "type": "string"}, {"id": "333bc6c7-f215-408a-8a5f-e078d800eaf3", "name": "", "value": "", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-220, 100], "id": "81d0303c-5dc3-412c-b265-b5434bd3bdf6", "name": "设置参数-竞争者信息"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {"maxOutputTokens": 2048, "temperature": 0.4, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [100, 220], "id": "0d829f92-74a2-44d9-911f-f543d2c88c96", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "cdwHRnakv6TIFeEn", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"promptType": "define", "text": "=<目标关键词>\n{{ $('设置参数-综合').first().json['主关键词'] }}\n</目标关键词>\n\n<竞争者信息>\n{{ $('设置参数-竞争者信息').item.json.竞争者信息 }}\n</竞争者信息>", "messages": {"messageValues": [{"message": "=角色：SEO和内容结构分析专家  你是一名专精于SEO和内容结构分析的高级数据分析师，拥有丰富的搜索引擎算法理解和数据挖掘经验。精通关键词密度分析、竞争者内容架构解析、SERP特征识别和用户意图匹配技术，擅长运用高级爬虫工具、自然语言处理和机器学习算法提取内容模式，可将复杂技术逻辑转化为可操作的内容策略建议，专业知识覆盖语义网络分析、内容层控词结构化优化，能精准预测搜索引擎内容理解模型，识别内容策略盲区，输出可落地内容优化方案 。  任务  请围绕以下数据展开分析： <目标关键词> <目标关键词> <目标关键词>  <竞争者信息> <竞争者信息> <竞争者信息>  输出格式  使用专业、规范的中文撰写报告，确保内容层次清晰、结构严谨、逻辑分明。 将完整报告以 markdown 格式呈现，并置于单一 XML 标签内：<competitor_report>...</competitor_report> 报告标题层级从二级标题(##)开始，标题定为“{{ $('设置参数-综合').first().json['主关键词'] }} 竞争者分析”  报告结构  1. 竞争者编码列表：构建规范化的竞争者名称，为每个竞争者分配专属编码标识（例如 C1，C2，C3 等），并在编码标识后附加其完整品牌名称。示例：“C1（肯德基），C2（麦当劳），C3（德克士）”。此编码体系将用于报告后续内容中的高频参考，务必确保编码及提及竞争者时均包含完整的编码标识与品牌全称。  2. Meta 标题和描述趋势：提取 BETA 标题与描述中的高频关键词、内容定位策略（如信息呈现顺序与层级关系）、中管控位策略（独特的价值向、地域聚焦型、效率强调型）以及转化优化元素、包括行动召唤语与吸引力表达。  3. 常见大额命题/主题：基于 H2/H3 标题结构，识别竞争者网页中重复出现的核心板块（做“概念定义”、“服务链路”、“价值与方案”、“客群与场景”、“常见问题解答”及特定领域专业定义、“服务与案例”），通过自然语言处理技术，提取并量化竞争者标题中最具显著性的二元、三元及四元词组，重点标记那些对主题相关性与搜索意图匹配具有决定性影响的语义单元。  4. 关键词组合模式（N - gram 分析）：通过自然语言处理技术，提取并量化竞争者标题中最具显著性的二元、三元及四元词组，重点标记那些对主题相关性与搜索意图匹配具有决定性影响的语义单元。  5. 页面结构与用户体验要素：系统化记录内容组织逻辑（如信息呈现顺序与层级关系）、功能性元素应用（如列表展现、数据表格、多媒体内容、互动表单、计算工具）以及内容深度与篇幅策略分析。  6. 竞争者总结与差异化分析：系统性评估每个竞争者的市场定位策略、核心优势提炼、内容差异化方向以及目标受众分层策略。"}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [0, 0], "id": "1972b893-ed54-4902-a762-352c8c2d5f71", "name": "竞争者分析"}, {"parameters": {"assignments": {"assignments": [{"id": "040c0e7c-b4e2-4e04-bef4-7b9a0b1adf54", "name": "竞争者分析", "value": "={{ $json.text.replace(/[\\s\\S]*<competitor_report>/, '').replace(/<\\?competitor_report>[\\s\\S]*/, '') }}", "type": "string"}, {"id": "c15c713c-5f79-4a79-ad01-0c3737a7e088", "name": "", "value": "", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [380, 100], "id": "a73e154c-7c24-4491-b393-815bc4227b88", "name": "设置参数-竞争者分析"}, {"parameters": {"promptType": "define", "text": "=<目标关键词>\n{{ $('设置参数-综合').first().json['主关键词'] }}\n</目标关键词>\n\n", "messages": {"messageValues": [{"message": "=角色：用户体验（UX）研究和搜索意图分析专家\n\n你是一名资深的用户体验（UX）研究员和搜索意图分析专家，具备精准拆解搜索查询背后的深层用户需求与心理期望，运用认知心理学和行为分析框架构建用户意图模型，独立于市场现有解决方案进行客观需求评估，以及将复杂的搜索行为转化为可操作的内容策略洞察等核心能力。\n\n一、任务目标\n\n请提供以下数据集用于分析：\n<目标关键词>\n<目标关键词>\n<目标关键词>\n\n二、输出格式\n\n使用专业、规范的中文撰写报告，确保内容层次清晰、结构严谨、逻辑分明。\n将完整报告以 markdown 格式呈现，并置于单一 XML 标签内：<user_intent_report>...</user_intent_report>\n报告标题层级从二级标题(##)开始，标题定为“{{ $('设置参数-综合').first().json['主关键词'] }} 用户意图分析”\n\n三、报告结构\n\n1. 主要用户意图：用户的主要目标是什么？（信息获取型、导航型、交易型、商业调研型）\n\n2. 次要意图：用户可能有哪些相关问题或需求？\n\n3. 隐含用户画像：描述可能的搜索者（例如，角色、痛点、背景、购买产品与获取服务需求）。\n\n4. 购买旅程阶段：（认知阶段、考虑阶段、决策阶段）。\n\n5. 预期服务/内容：用户在满足其意图的页面上逻辑上期望找到哪些特定服务或信息类型？\n\n6. 问题/解决方案框架：应如何阐述用户的核心问题，以及如何将服务定位为针对此特定查询的解决方案？"}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [600, 100], "id": "6d03b0fd-211e-4e5d-badb-3b7599bb64a8", "name": "用户意图分析"}, {"parameters": {"assignments": {"assignments": [{"id": "040c0e7c-b4e2-4e04-bef4-7b9a0b1adf54", "name": "用户意图分析", "value": "={{ $json.text.replace(/[\\s\\S]*<user_intent_report>/, '').replace(/<\\?user_intent_report>[\\s\\S]*/, '') }}", "type": "string"}, {"id": "c15c713c-5f79-4a79-ad01-0c3737a7e088", "name": "", "value": "", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [980, 100], "id": "76c2f66d-aac9-4808-a772-7cab7e65b8cd", "name": "设置参数-用户意图分析"}, {"parameters": {"promptType": "define", "text": "=<目标关键词>\n{{ $('设置参数-综合').first().json['主关键词'] }}\n</目标关键词>\n\n<竞争者信息>\n{{ $('设置参数-竞争者信息').item.json.竞争者信息 }}\n</竞争者信息>\n\n<用户意图分析>\n{{ $('设置参数-用户意图分析').item.json.用户意图分析 }}\n</用户意图分析>", "messages": {"messageValues": [{"message": "=角色：SEO 内容策略与用户体验（UX）专家\n\n你是一位资深的 SEO 内容策略与用户体验（UX）专家，需具备搜索引擎优化专业知识、内容差异化分析能力、用户体验设计专长和数据驱动决策能力，擅长长期市场空白、整合复杂数据源，并将分析转化为可执行的内容策略。\n\n任务目标\n\n请基于以下数据集开展分析：\n<目标关键词>\n<目标关键词>\n<目标关键词>\n\n<竞争者分析>\n<竞争者分析>\n<竞争者分析>\n\n<用户意图分析>\n<用户意图分析>\n<用户意图分析>\n\n输出格式\n\n使用专业、规范的中文撰写报告，确保内容层次清晰、结构严谨、逻辑分明。\n将完整报告以 markdown 格式呈现，嵌入单一 XML 标签：<opportunity_analysis>...</opportunity_analysis>\n报告标题从二级标题（##）起，格式为 {{ $('设置参数-综合').first().json['主关键词'] }} 机会识别与洞察分析 。\n\n报告结构\n\n1. 内容基石分析：结合用户意图分析与竞争者分析，识别用户搜索且具备高流量的核心主题、版块与信息点，从内容分布视角挖掘需覆盖的“行业标配”元素，作为后续差异化策略基础。\n\n2. 内容体验优化机会：挖掘用户意图（显性/隐藏需求），对比竞争者分析出其未有效满足或呈现差的关键触点，明确体验优化方向（如内容逻辑重组、CTA 优化等），构建体验优势。\n\n3. SEO 内容差异化机会：梳理用户意图词与竞争者内容的语义关联差异，识别高价值且竞争弱的关键词、概念与语义集群，预判潜在 SEO 内容突围点，指导页面架构与核心内容创作，抢占排名及流量。\n\n4. 用户体验与转化提升策略：综合分析并诊断竞争者在内容呈现、信息结构、导航及 CTA 等方面的问题与不足，制定体验优化路径与转化提升方案，推动流量变现 。"}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [1200, 100], "id": "4c0c4551-a9da-40a9-a475-0648de1d1c30", "name": "机会识别与洞察分析"}, {"parameters": {"promptType": "define", "text": "=<目标关键词>\n{{ $('设置参数-综合').first().json['主关键词'] }}\n</目标关键词>\n\n<机会识别与洞察分析>\n{$json['机会识别与洞察分析']\n</机会识别与洞察分析>\n\n<公司品牌>\n{{ $('设置参数-综合').first().json['公司品牌'] }}\n</公司品牌>\n\n<公司介绍>\n{{ $('设置参数-综合').first().json['公司介绍'] }}\n</公司介绍>\n\n<目标用户>\n{{ $('设置参数-综合').first().json['目标用户'] }}\n</目标用户>", "messages": {"messageValues": [{"message": "=角色：SEO 内容架构师、信息设计师和转化导向结构专家\n\n你是一名资深 SEO 内容架构师、信息设计专家和转化导向页面结构优化大师。\n\n任务目标\n\n根据提供的《机会识别与洞察分析》，为特定目标关键词设计最优的页面内容大纲，包括 H1、H2、H3 以及可能的 H4 标题。大纲需完美匹配用户搜索意图，整合竞争对手 SEO 最佳实践，打造逻辑清晰、用户体验流畅的结构，最终有效提高页面的访客转化率。\n\n信息输入\n\n请基于以下信息输出页面大纲：\n<目标关键词>\n{目标关键词}\n</目标关键词>\n\n<机会识别与洞察分析>\n{机会识别与洞察分析}\n</机会识别与洞察分析>\n\n<公司品牌>\n{公司品牌}\n</公司品牌>\n\n<公司介绍>\n{公司介绍}\n</公司介绍>\n\n输出格式要求\n\n以中文输出清晰易懂的页面大纲。\n页面大纲须采用以下 JSON 格式，其中顶层键为 content_outline：\n{\n    \"content_outline\": {\n        \"h1\": {\n            \"title\": \"H1标题：[精准覆盖目标关键词]，实现[用户核心利益点]\",\n            \"description\": \"此处填写H1标题的整体阐述，概括页面核心价值与用户主要关注点\",\n            \"seo_keywords_themes\": [\"核心关键词1\", \"核心关键词2\", \"用户利益主题\"]\n        },\n        \"sections_h2\": [\n            {\n                \"title\": \"H2：痛点洞察：您是否正面临[普遍用户痛点]？\",\n                \"description\": \"引导用户识别并共鸣其在特定场景下可能遇到的挑战或困扰\",\n                \"seo_keywords_themes\": [\"H2痛点洞察关键词\", \"用户挑战\", \"普遍问题\"],\n                \"subsections_h3\": [\n                    {\n                        \"title\": \"H3：关于[价格/投入/周期]的说明\",\n                        \"description\": \"针对用户关心的成本、效益或时间等问题提供清晰解答\",\n                        \"seo_keywords_themes\": [\"价格说明\", \"投入回报\", \"常见疑问\"]\n                    }\n                ]\n            },\n            {\n                \"title\": \"H2：即刻行动：开启您的[期望目标]之旅\",\n                \"description\": \"提供清晰、直接的行动号召，引导用户迈出下一步，并简化操作路径\",\n                \"seo_keywords_themes\": [\"H2行动号召关键词\", \"立即开始\", \"转化路径\"],\n                \"subsections_h3\": [\n                    {\n                        \"title\": \"H3：现在就[具体行动，如‘获取免费咨询’、‘申请试用’]\",\n                        \"description\": \"给出明确指令，告知用户具体可以做什么以及如何做\",\n                        \"seo_keywords_themes\": [\"行动号召关键词\", \"立即开始\", \"免费获取\"]\n                    },\n                    {\n                        \"title\": \"H3：联系我们/下一步指引\",\n                        \"description\": \"提供便捷的联系方式或清晰的下一步操作指南\",\n                        \"seo_keywords_themes\": [\"联系方式\", \"客户支持\", \"后续步骤\"]\n                    }\n                ]\n            },\n            {\n                \"title\": \"H2：权威信任：我们的专业实力与客户认可\",\n                \"description\": \"通过展示专业资质、成功案例、客户评价等，建立用户对我们品牌和能力的信任\",\n                \"seo_keywords_themes\": [\"H2权威信任关键词\", \"客户案例\", \"品牌信誉\"],\n                \"subsections_h3\": [\n                    {\n                        \"title\": \"H3：客户成功案例：[客户X]如何通过我们实现[显著成果]\",\n                        \"description\": \"通过真实案例展示服务的有效性和我们帮助客户成功的实力\",\n                        \"seo_keywords_themes\": [\"成功案例\", \"客户见证\", \"行业应用\"]\n                    },\n                    {\n                        \"title\": \"H3：[资质认证/合作伙伴/媒体报道]展示\",\n                        \"description\": \"借助第三方权威增强品牌可信度和专业形象\",\n                        \"seo_keywords_themes\": [\"权威认证\", \"合作伙伴\", \"品牌信誉\"]\n                    }\n                ]\n            },\n            {\n                \"title\": \"H2：疑虑解答：关于[服务/价格/流程]的常见问题\",\n                \"description\": \"主动解答用户在决策过程中可能存在的疑问，消除顾虑，使合作流程更透明\",\n                \"seo_keywords_themes\": [\"H2疑虑解答关键词\", \"常见问题\", \"流程透明\"],\n                \"subsections_h3\": [\n                    {\n                        \"title\": \"H3：我们的合作流程是怎样的？（详细步骤）\",\n                        \"description\": \"清晰、分步地解释合作或服务获取流程，降低用户未知感\",\n                        \"seo_keywords_themes\": [\"合作流程\", \"服务步骤\", \"透明化\"]\n                    },\n                    {\n                        \"title\": \"H3：关于[价格/投入/周期]的说明\",\n                        \"description\": \"针对用户关心的成本、效益或时间等问题提供清晰解答\",\n                        \"seo_keywords_themes\": [\"价格说明\", \"投入回报\", \"常见疑问\"]\n                    }\n                ]\n            },\n            {\n                \"title\": \"H3：您将获得的核心利益：[利益点1]、[利益点2]\",\n                \"description\": \"具体列举用户通过我们服务可直接获得的核心价值和积极改变\",\n                \"seo_keywords_themes\": [\"用户利益点\", \"预期成果关键词\"]\n            },\n            {\n                \"title\": \"H2：服务/产品优势：为何选择我们？\",\n                \"description\": \"详细介绍我们的服务或产品特性，并突出相较于其他方案的独特优势和用户具体获益\",\n                \"seo_keywords_themes\": [\"H2服务优势关键词\", \"产品特性\", \"竞争力\"],\n                \"subsections_h3\": [\n                    {\n                        \"title\": \"H3：特性一：[产品/服务特性A]及其带来的[具体用户获益A]\",\n                        \"description\": \"详细描述具体特性，并直接关联到用户能感知的实际好处或体验提升\",\n                        \"seo_keywords_themes\": [\"特性A关键词\", \"功能优势\", \"用户获益A\"]\n                    },\n                    {\n                        \"title\": \"H3：特性二：[产品/服务特性B]如何保障[具体用户获益B]\",\n                        \"description\": \"阐述另一关键特性，并强调其为用户带来的独特价值或保障\",\n                        \"seo_keywords_themes\": [\"特性B关键词\", \"独特卖点\", \"用户获益B\"]\n                    }\n                ]\n            }\n        ],\n        \"规划细节\": [\n            \"H1标题：精准覆盖目标关键词，明确用户核心利益点\",\n            \"H2章节：自然递进，引导用户从意识痛点、价值主张、服务优势，到信任构建、疑虑解决，最后明确行动路径\",\n            \"H3子章节：详细阐明产品特性、用户获益、具体步骤等；SEO关键词/主题自然融入，严格按照上述要求和结构，以提供易操作、强说服力、并优化转化的页面大纲\"\n        ]\n    }\n}\n通过上述结构化输出，助力打造契合 SEO 规则与用户需求的优质页面内容，提升转化效果 。"}]}, "batching": {}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [1800, 100], "id": "a84bfac7-1224-4808-804b-8dc0692befe4", "name": "内容大纲生成"}, {"parameters": {"assignments": {"assignments": [{"id": "040c0e7c-b4e2-4e04-bef4-7b9a0b1adf54", "name": "机会识别与洞察分析", "value": "={{ $json.text.replace(/[\\s\\S]*<opportunity_analysis>/, '').replace(/<\\?opportunity_analysis>[\\s\\S]*/, '') }}", "type": "string"}, {"id": "c15c713c-5f79-4a79-ad01-0c3737a7e088", "name": "", "value": "", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1580, 100], "id": "96f21ec0-70ea-461f-b779-99c95f410084", "name": "设置参数-机会识别与洞察分析"}, {"parameters": {"assignments": {"assignments": [{"id": "040c0e7c-b4e2-4e04-bef4-7b9a0b1adf54", "name": "内容大纲", "value": "={{ $json.text.replace(/[\\s\\S]*'''json/,'').replace(/''''[\\s\\S]*/, '').trim() }}", "type": "string"}, {"id": "c15c713c-5f79-4a79-ad01-0c3737a7e088", "name": "", "value": "", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2180, 100], "id": "eddb08c4-8552-4839-884b-e8827dab49e2", "name": "设置参数-内容大纲"}, {"parameters": {"assignments": {"assignments": [{"id": "9aefef72-ec8d-4cb5-bc18-a176e77c82d5", "name": "SEO", "value": "=# {{ $('设置参数-综合').first().json['主关键词'] }} SEO 分析报告\n\n本报告聚焦关键词{{ $('设置参数-综合').first().json['主关键词'] }}，面向{{ $('设置参数-综合').first().json['公司品牌'] }}开展系统化 SEO 深度剖析，涵盖板块如下：\n\n1. 竞争者信息\n\n2. 竞争分析\n\n3. 用户意图分析\n\n4. 机会识别与洞察分析\n\n5. 内容大纲\n\n旨在精准贴合{{ $('设置参数-综合').first().json['目标用户'] }}的需求，达成目标：\n{{ $('设置参数-综合').first().json['公司介绍'] }}\n\n竞争者信息\n\n\n{{ $('设置参数-竞争者信息').item.json['竞争者信息'] }}\n\n竞争分析\n\n{{ $('设置参数-竞争者分析').item.json['竞争者分析'] }}\n\n用户意图分析\n\n{{ $('设置参数-用户意图分析').item.json['用户意图分析'] }}\n\n机会识别与洞察分析\n\n{{ $('设置参数-机会识别与洞察分析').item.json['机会识别与洞察分析'] }}\n\n内容大纲\n、、、\n{{ $('设置参数-内容大纲').item.json['内容大纲'] }}\n\n、、、\n竞争者信息（补充展示）\n\n{{ $('设置参数-竞争者信息').item.json['竞争者信息'] }}\n\n、、、\n\n按照 “报告框架→核心板块→数据填充” 逻辑重构，保留原始参数调用与内容层级，优化排版让结构更清晰，便于理解各模块关联 。", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2400, 100], "id": "b71a999e-7a2e-4293-bf40-2e33a7eb2321", "name": "设置参数-组合"}, {"parameters": {"method": "POST", "url": "https://mcp-on-edge.edgeone.site/mcp-server", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content_Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"jsonrpc\": \"2.0\",\n  \"id\": 1,\n  \"method\": \"tools/call\",\n  \"params\": {\n    \"name\": \"deploy-html\",\n    \"arguments\": {\n      \"value\": \"{{ JSON.stringify($node['转化为HTML'].json.HTML).slice(1, -1) }}\"\n    }\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2840, 100], "id": "bef2f8fa-7c25-4776-87b2-7ea455fb553d", "name": "发布"}, {"parameters": {"mode": "markdownToHtml", "markdown": "={{ $json.SEO }}", "destinationKey": "HTML", "options": {"completeHTMLDocument": true}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [2600, 100], "id": "938c52c1-dfc4-4bd5-97e9-470bc0375a6a", "name": "转化为HTML"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "73120df7-eb9b-4347-8abe-5de12a4b2970", "leftValue": "={{ $('设置参数-综合').first().json['分支'] }}", "rightValue": "表格", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [3060, 100], "id": "b8dfe046-00b3-4c32-914a-4aa9dec49407", "name": "If"}, {"parameters": {"operation": "completion", "respondWith": "redirect", "redirectUrl": "={{ $json.result.content[0].text }}", "limitWaitTime": true, "resumeUnit": "minutes"}, "type": "n8n-nodes-base.form", "typeVersion": 1, "position": [3280, 40], "id": "fa3f0b9c-c0f7-4964-b24e-b9843d98c44a", "name": "返回结果", "webhookId": "e732a888-9c8e-4327-8af4-00de159c8074"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [3500, 100], "id": "7af70a08-b7d6-4b19-8c79-478c2f20531b", "name": "聚合条目"}, {"parameters": {"assignments": {"assignments": [{"id": "270eb53a-711b-4ac4-be55-2dd4a92cb97d", "name": "竞争者信息", "value": "={{ $('设置参数-竞争者信息').item.json['竞争者信息'] }}", "type": "string"}, {"id": "5e799f75-a7a4-49e5-90a9-e33f79869f93", "name": "竞争者分析", "value": "={{ $('设置参数-竞争者分析').item.json['竞争者分析'] }}", "type": "string"}, {"id": "0136f6dd-83b4-433b-9954-774be72cfb86", "name": "用户意图分析", "value": "={{ $('设置参数-用户意图分析').item.json['用户意图分析'] }}", "type": "string"}, {"id": "fc6ec7bd-0689-4aa0-ad5a-7ceea1d5a785", "name": "机会识别与洞察分析", "value": "={{ $('设置参数-机会识别与洞察分析').item.json['机会识别与洞察分析'] }}", "type": "string"}, {"id": "d8fa830a-1bb7-41cf-82ca-544984e4512f", "name": "内容大纲", "value": "={{ $('设置参数-内容大纲').item.json['内容大纲'] }}", "type": "string"}, {"id": "e640e584-0028-4c37-9af0-c48115779176", "name": "完整的 SEO 报告网址", "value": "={{ $('发布').item.json.result.content[0].text }}", "type": "string"}, {"id": "e751f69e-76f8-4243-bbb0-de9e9223a86a", "name": "SEO 分析报告", "value": "={{ $('设置参数-组合').item.json.SEO }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [3700, 100], "id": "1873d1c4-607a-4231-b7b1-2428ee83ad81", "name": "设置参数-汇总"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {"maxOutputTokens": 2048, "temperature": 0.4, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [660, 260], "id": "1b98a182-bea8-49dd-96bf-c379241bbb07", "name": "Google Gemini Chat Model4", "credentials": {"googlePalmApi": {"id": "cdwHRnakv6TIFeEn", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {"maxOutputTokens": 2048, "temperature": 0.4, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1120, 280], "id": "ca1a182f-b608-4e7b-a42d-54b8d5acd475", "name": "Google Gemini Chat Model5", "credentials": {"googlePalmApi": {"id": "cdwHRnakv6TIFeEn", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {"maxOutputTokens": 2048, "temperature": 0.4, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [1800, 320], "id": "3144df44-5680-4e28-bc7c-869c5b6b35fe", "name": "Google Gemini Chat Model6", "credentials": {"googlePalmApi": {"id": "cdwHRnakv6TIFeEn", "name": "Google Gemini(PaLM) Api account"}}}], "connections": {"输入需求": {"main": [[{"node": "设置参数-表格", "type": "main", "index": 0}]]}, "设置参数-表格": {"main": [[{"node": "聚合分支", "type": "main", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "设置参数-子工作流", "type": "main", "index": 0}]]}, "设置参数-子工作流": {"main": [[{"node": "聚合分支", "type": "main", "index": 1}]]}, "聚合分支": {"main": [[{"node": "设置参数-综合", "type": "main", "index": 0}]]}, "设置参数-综合": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "遍历-网址", "type": "main", "index": 0}]]}, "遍历-网址": {"main": [[{"node": "循环", "type": "main", "index": 0}]]}, "爬取-HTML": {"main": [[{"node": "提取-HTML", "type": "main", "index": 0}]]}, "提取-HTML": {"main": [[{"node": "设置参数-抓取", "type": "main", "index": 0}]]}, "设置参数-抓取": {"main": [[{"node": "循环", "type": "main", "index": 0}]]}, "循环": {"main": [[{"node": "聚合结果", "type": "main", "index": 0}], [{"node": "爬取-HTML", "type": "main", "index": 0}]]}, "聚合结果": {"main": [[{"node": "设置参数-竞争者信息", "type": "main", "index": 0}]]}, "设置参数-竞争者信息": {"main": [[{"node": "竞争者分析", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "竞争者分析", "type": "ai_languageModel", "index": 0}]]}, "竞争者分析": {"main": [[{"node": "设置参数-竞争者分析", "type": "main", "index": 0}]]}, "设置参数-竞争者分析": {"main": [[{"node": "用户意图分析", "type": "main", "index": 0}]]}, "用户意图分析": {"main": [[{"node": "设置参数-用户意图分析", "type": "main", "index": 0}]]}, "设置参数-用户意图分析": {"main": [[{"node": "机会识别与洞察分析", "type": "main", "index": 0}]]}, "机会识别与洞察分析": {"main": [[{"node": "设置参数-机会识别与洞察分析", "type": "main", "index": 0}]]}, "内容大纲生成": {"main": [[{"node": "设置参数-内容大纲", "type": "main", "index": 0}]]}, "设置参数-机会识别与洞察分析": {"main": [[{"node": "内容大纲生成", "type": "main", "index": 0}]]}, "设置参数-内容大纲": {"main": [[{"node": "设置参数-组合", "type": "main", "index": 0}]]}, "设置参数-组合": {"main": [[{"node": "转化为HTML", "type": "main", "index": 0}]]}, "转化为HTML": {"main": [[{"node": "发布", "type": "main", "index": 0}]]}, "发布": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "返回结果", "type": "main", "index": 0}], [{"node": "聚合条目", "type": "main", "index": 0}]]}, "返回结果": {"main": [[{"node": "聚合条目", "type": "main", "index": 0}]]}, "聚合条目": {"main": [[{"node": "设置参数-汇总", "type": "main", "index": 0}]]}, "Google Gemini Chat Model4": {"ai_languageModel": [[{"node": "用户意图分析", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model5": {"ai_languageModel": [[{"node": "机会识别与洞察分析", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model6": {"ai_languageModel": [[{"node": "内容大纲生成", "type": "ai_languageModel", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}