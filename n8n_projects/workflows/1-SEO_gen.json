{"name": "1-SEO_gen", "nodes": [{"parameters": {"formTitle": "James工作流 SEO 关键词分析引擎", "formDescription": "SEO洞察·聚集关键词，看得见的机遇，摸得着的价值！", "formFields": {"values": [{"fieldLabel": "主关键词", "requiredField": true}, {"fieldLabel": "公司品牌"}, {"fieldLabel": "公司介绍", "fieldType": "textarea"}, {"fieldLabel": "目标用户"}]}, "options": {"appendAttribution": false, "buttonLabel": "撰写报告", "path": "<PERSON><PERSON><PERSON><PERSON>"}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-880, -200], "id": "26c3706b-b174-4993-8e9f-7517d152391f", "name": "数据提交", "webhookId": "acf86b48-0b71-4f9c-a57e-ba58169fdfea"}, {"parameters": {"workflowId": {"__rl": true, "value": "QWjk6DZRYmEnlNZ4", "mode": "list", "cachedResultName": "1.1-SEO-workflow"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"主关键词": "={{ $json['主关键词'] }}", "公司品牌": "={{ $json['公司品牌'] }}", "公司介绍": "={{ $json['公司介绍'] }}", "目标用户": "={{ $json['目标用户'] }}"}, "matchingColumns": [], "schema": [{"id": "主关键词", "displayName": "主关键词", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "公司品牌", "displayName": "公司品牌", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "公司介绍", "displayName": "公司介绍", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "目标用户", "displayName": "目标用户", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {"waitForSubWorkflow": true}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [-660, -200], "id": "e67df2a1-7be8-45c8-9484-c3802be633fc", "name": "执行报告"}, {"parameters": {"workflowId": {"__rl": true, "value": "en57ZP93FOwVbzFz", "mode": "list", "cachedResultName": "1.2-SEO-workflow"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"可视化内容": "={{ $json['SEO 分析报告'] }}", "完成 SEO 报告网址": "={{ $json['完整 SEO 报告网址'] }}"}, "matchingColumns": [], "schema": [{"id": "可视化内容", "displayName": "可视化内容", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "完成 SEO 报告网址", "displayName": "完成 SEO 报告网址", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {"waitForSubWorkflow": true}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [-440, -200], "id": "545b8787-0b9e-4ab0-a6ae-13171e972c2b", "name": "可视化"}, {"parameters": {"operation": "completion", "respondWith": "redirect", "redirectUrl": "={{ $json['可视化网址'] }}", "limitWaitTime": true, "resumeUnit": "minutes"}, "type": "n8n-nodes-base.form", "typeVersion": 1, "position": [-220, -200], "id": "c12a797b-62b4-4eaf-967b-eeb7515d7af3", "name": "返回结果", "webhookId": "9b4c6b60-5e8f-4b82-a7ea-ff9638869346"}], "connections": {"数据提交": {"main": [[{"node": "执行报告", "type": "main", "index": 0}]]}, "执行报告": {"main": [[{"node": "可视化", "type": "main", "index": 0}]]}, "可视化": {"main": [[{"node": "返回结果", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}