{"name": "2.2-企业经营报表分析_vis_advanced", "nodes": [{"parameters": {"title": "企业经营分析报告生成", "fields": {"fields": [{"id": "department", "label": "选择分析部门", "type": "list", "required": true, "listOptions": {"options": [{"name": "销售部", "value": "sales"}, {"name": "市场部", "value": "marketing"}]}}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 1, "position": [-1140, -140], "id": "9a88a166-eaf4-49e0-aa8d-93506a72301c", "name": "Form Trigger"}, {"parameters": {"jsCode": "// 这是占位节点\n// 请在这里替换成您自己的 Google Sheet 节点\n// 并使用 {{ $('Form Trigger').item.json.department }} 来筛选数据\n\n// 模拟从Google Sheet获取的数据\nconst mockData = [\n  { \"月份\": \"一月\", \"销售额\": 15000, \"成本\": 8000 },\n  { \"月份\": \"二月\", \"销售额\": 18000, \"成本\": 9500 },\n  { \"月份\": \"三月\", \"销售额\": 22000, \"成本\": 11000 }\n];\n\nreturn mockData.map(item => ({ json: item }));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-920, -140], "id": "f0f1f2f3-f4f5-f6f7-f8f9-fafbfcfdfeff", "name": "从Google Sheet获取数据 (占位)"}, {"parameters": {"contextWindowLength": 20}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-420, 80], "id": "aeaa75d7-553b-4176-85b1-216c954bdcc9", "name": "Simple Memory"}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [620, -160], "id": "17904308-f01c-4e21-b91c-4387c587e75b", "name": "When Executed by Another Workflow"}, {"parameters": {"description": "=## 通用数据可视化工具说明\n\n此工具用于生成任何类型数据的可视化图表。无论是业务数据、财务数据、用户数据还是运营数据，都能智能生成合适的图表。\n\n### 通用使用场景\n- **对比分析**：目标vs实际、计划vs完成、预算vs支出\n- **趋势分析**：时间序列变化、增长趋势、周期性分析\n- **分布分析**：地区分布、类别占比、用户分群\n- **排名分析**：绩效排名、评分排序、竞争分析\n- **综合分析**：多维度对比、综合评估\n\n### 智能输入处理\n工具能够自动处理各种数据格式：\n- **表格数据**：任意列名和数据结构\n- **JSON数据**：结构化数据对象\n- **文本数据**：自然语言描述的数据\n- **混合数据**：多种格式组合的数据\n\n### 输出格式\n工具将返回可直接在markdown中使用的图表链接，格式为：\n![图表标题](图表URL)\n\n### 重要提示\n- 务必正确转义所有字符串，尤其是多行内容\n- 确保数据完整性，避免传递空值或无效数据\n- 生成的图表将自动适配移动端和桌面端显示", "workflowId": {"__rl": true, "value": "SSZ0zzr9FdutOkvA", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [-180, 60], "id": "346c2690-2765-4fe9-ad17-3a490768379d", "name": "Generate Chart"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"name\": \"chart_configuration\",\n  \"description\": \"Enhanced configuration schema for Chart.js charts with sales data visualization\",\n  \"strict\": true,\n  \"schema\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"type\": {\n        \"type\": \"string\",\n        \"enum\": [\"bar\", \"horizontalBar\", \"line\", \"radar\", \"pie\", \"doughnut\", \"polarArea\", \"bubble\", \"scatter\"]\n      },\n      \"data\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"labels\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"string\"\n            },\n            \"minItems\": 1\n          },\n          \"datasets\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"label\": {\n                  \"type\": \"string\"\n                },\n                \"data\": {\n                  \"type\": \"array\",\n                  \"items\": {\n                    \"type\": \"number\"\n                  },\n                  \"minItems\": 1\n                },\n                \"backgroundColor\": {\n                  \"oneOf\": [\n                    {\n                      \"type\": \"string\"\n                    },\n                    {\n                      \"type\": \"array\",\n                      \"items\": {\n                        \"type\": \"string\"\n                      }\n                    }\n                  ]\n                },\n                \"borderColor\": {\n                  \"oneOf\": [\n                    {\n                      \"type\": \"string\"\n                    },\n                    {\n                      \"type\": \"array\",\n                      \"items\": {\n                        \"type\": \"string\"\n                      }\n                    }\n                  ]\n                },\n                \"borderWidth\": {\n                  \"type\": \"number\",\n                  \"minimum\": 0\n                },\n                \"tension\": {\n                  \"type\": \"number\",\n                  \"minimum\": 0,\n                  \"maximum\": 1\n                }\n              },\n              \"required\": [\"label\", \"data\", \"backgroundColor\"],\n              \"additionalProperties\": true\n            },\n            \"minItems\": 1\n          }\n        },\n        \"required\": [\"labels\", \"datasets\"],\n        \"additionalProperties\": false\n      },\n      \"options\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"responsive\": {\n            \"type\": \"boolean\"\n          },\n          \"maintainAspectRatio\": {\n            \"type\": \"boolean\"\n          },\n          \"plugins\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"title\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"display\": {\n                    \"type\": \"boolean\"\n                  },\n                  \"text\": {\n                    \"type\": \"string\"\n                  },\n                  \"font\": {\n                    \"type\": \"object\",\n                    \"properties\": {\n                      \"size\": {\n                        \"type\": \"number\"\n                      },\n                      \"weight\": {\n                        \"type\": \"string\"\n                      }\n                    },\n                    \"additionalProperties\": false\n                  }\n                },\n                \"additionalProperties\": false\n              },\n              \"legend\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"position\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"top\", \"bottom\", \"left\", \"right\"]\n                  },\n                  \"labels\": {\n                    \"type\": \"object\",\n                    \"additionalProperties\": true\n                  }\n                },\n                \"additionalProperties\": false\n              },\n              \"tooltip\": {\n                \"type\": \"object\",\n                \"additionalProperties\": true\n              }\n            },\n            \"additionalProperties\": false\n          },\n          \"scales\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"x\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"title\": {\n                    \"type\": \"object\",\n                    \"properties\": {\n                      \"display\": {\n                        \"type\": \"boolean\"\n                      },\n                      \"text\": {\n                        \"type\": \"string\"\n                      }\n                    },\n                    \"additionalProperties\": false\n                  }\n                },\n                \"additionalProperties\": true\n              },\n              \"y\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"beginAtZero\": {\n                    \"type\": \"boolean\"\n                  },\n                  \"title\": {\n                    \"type\": \"object\",\n                    \"properties\": {\n                      \"display\": {\n                        \"type\": \"boolean\"\n                      },\n                      \"text\": {\n                        \"type\": \"string\"\n                      }\n                    },\n                    \"additionalProperties\": false\n                  },\n                  \"ticks\": {\n                    \"type\": \"object\",\n                    \"additionalProperties\": true\n                  }\n                },\n                \"additionalProperties\": true\n              }\n            },\n            \"additionalProperties\": false\n          },\n          \"animation\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"duration\": {\n                \"type\": \"number\"\n              },\n              \"easing\": {\n                \"type\": \"string\"\n              }\n            },\n            \"additionalProperties\": false\n          }\n        },\n        \"additionalProperties\": false\n      }\n    },\n    \"additionalProperties\": false\n  }\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [1000, 40], "id": "97156046-a1cf-46e5-871f-188921310c20", "name": "Structured Output Parser"}, {"parameters": {"jsCode": "// 获取所有数据行\nconst data = $input.all().map(item => item.json);\n\nif (data.length === 0) {\n  return [{ json: { summary: \"No data to analyze.\" } }];\n}\n\n// --- 数据聚合与摘要 ---\n\n// 假设列名是 '销售额', '成本', '月份', '产品类别' 等\n// AI可以智能识别这些不固定的列名\n\n// 动态查找关键列名\nconst findKey = (keys, possibleNames) => {\n  for (const name of possibleNames) {\n    if (keys.includes(name)) return name;\n  }\n  return null;\n};\n\nconst sampleKeys = Object.keys(data[0]);\nconst salesKey = findKey(sampleKeys, ['销售额', 'sales', 'revenue']);\nconst costKey = findKey(sampleKeys, ['成本', 'cost']);\nconst monthKey = findKey(sampleKeys, ['月份', 'month', 'date']);\nconst categoryKey = findKey(sampleKeys, ['产品类别', 'category', 'type']);\n\n// 1. 计算核心指标\nlet totalSales = 0;\nlet totalCost = 0;\nfor (const row of data) {\n  if (salesKey) totalSales += row[salesKey] || 0;\n  if (costKey) totalCost += row[costKey] || 0;\n}\nconst totalProfit = totalSales - totalCost;\nconst profitMargin = totalSales > 0 ? (totalProfit / totalSales) * 100 : 0;\n\n// 2. 按月份聚合\nconst monthlyBreakdown = {};\nif (monthKey && salesKey) {\n  for (const row of data) {\n    const month = row[monthKey];\n    if (!month) continue;\n    if (!monthlyBreakdown[month]) {\n      monthlyBreakdown[month] = { sales: 0, cost: 0, profit: 0 };\n    }\n    const sales = row[salesKey] || 0;\n    const cost = costKey ? (row[costKey] || 0) : 0;\n    monthlyBreakdown[month].sales += sales;\n    monthlyBreakdown[month].cost += cost;\n    monthlyBreakdown[month].profit += sales - cost;\n  }\n}\n\n// 3. 按品类聚合\nconst categoryBreakdown = {};\nif (categoryKey && salesKey) {\n  for (const row of data) {\n    const category = row[categoryKey];\n    if (!category) continue;\n    if (!categoryBreakdown[category]) {\n      categoryBreakdown[category] = { sales: 0, profit: 0, count: 0 };\n    }\n    const sales = row[salesKey] || 0;\n    const cost = costKey ? (row[costKey] || 0) : 0;\n    categoryBreakdown[category].sales += sales;\n    categoryBreakdown[category].profit += sales - cost;\n    categoryBreakdown[category].count += 1;\n  }\n}\n\n// 构建摘要对象\nconst summary = {\n  dataSourceInfo: {\n    totalRows: data.length,\n    detectedFields: { salesKey, costKey, monthKey, categoryKey }\n  },\n  overallMetrics: {\n    totalSales: totalSales.toFixed(2),\n    totalCost: totalCost.toFixed(2),\n    totalProfit: totalProfit.toFixed(2),\n    profitMargin: profitMargin.toFixed(2) + '%'\n  },\n  monthlyBreakdown: monthlyBreakdown,\n  categoryBreakdown: categoryBreakdown\n};\n\n// 只返回一个包含摘要的数据项\nreturn [{ json: { analysisData: summary } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-700, -140], "id": "b1b2b3b4-c5c6-d7d8-e9ea-fbfcfd000000", "name": "数据预处理与聚合"}, {"parameters": {"promptType": "define", "text": "请基于以下这份预处理过的数据摘要，生成一份专业的、包含深度洞察和数据可视化的分析报告。数据摘要如下：\n\n{{ JSON.stringify($json.analysisData) }}", "options": {"systemMessage": "你是一位顶级的企业数据分析师和战略顾问。你的任务是接收一份已经**预处理和聚合过的数据摘要**，并基于这份摘要，生成一份图文并茂、逻辑清晰、洞察深刻的专业分析报告。\n\n## 你的核心任务\n**不要**进行重复计算。你的任务是将枯燥的摘要数据，转化为生动、易于理解的商业洞察和战略建议。\n\n## 你收到的“数据摘要”格式示例\n```json\n{\n  \"dataSourceInfo\": {\n    \"totalRows\": 10000,\n    \"detectedFields\": { \"salesKey\": \"销售额\", \"costKey\": \"成本\", ... }\n  },\n  \"overallMetrics\": {\n    \"totalSales\": \"1500000.00\",\n    \"totalProfit\": \"300000.00\",\n    \"profitMargin\": \"20.00%\"\n  },\n  \"monthlyBreakdown\": {\n    \"一月\": { \"sales\": 120000, \"profit\": 25000 },\n    \"二月\": { \"sales\": 135000, \"profit\": 28000 }\n  },\n  \"categoryBreakdown\": {\n    \"电子产品\": { \"sales\": 800000, \"profit\": 150000 },\n    \"家居用品\": { \"sales\": 400000, \"profit\": 90000 }\n  }\n}\n```\n\n## 你的工作流程\n\n1.  **解读摘要**：快速理解摘要中的各项核心指标（`overallMetrics`）、月度趋势（`monthlyBreakdown`）和分类表现（`categoryBreakdown`）。\n2.  **构思报告结构**：规划报告的整体框架，通常是：执行摘要 -> 核心指标分析 -> 月度趋势分析 -> 分类对比分析 -> 结论与建议。\n3.  **生成图表**：\n    -   看到 `monthlyBreakdown`，必须调用 `Generate Chart` 工具生成**折线图**来展示销售或利润的时间趋势。\n    -   看到 `categoryBreakdown`，必须调用 `Generate Chart` 工具生成**柱状图或饼图**来对比不同类别的表现。\n4.  **撰写报告**：围绕图表和核心指标，用流畅的语言撰写分析内容，解释数据背后的商业现象，提出有价值的洞察和可行的建议。\n5.  **使用Mermaid**：在报告中至少使用一次Mermaid图（如`flowchart`来描绘建议的行动流程）来增强表现力。\n\n## 重要执行规则\n- **严禁重复计算**：摘要里的数据都是计算好的，直接使用即可。\n- **必须生成图表**：月度数据和分类数据必须用图表展示。\n- **必须有深度洞察**：不能只复述数据，要解释“为什么”并提出“怎么办”。\n- **必须使用专业模板**：报告需要有清晰的标题层级和专业的结构。\n\n现在，请开始分析上游节点提供的**数据摘要**吧！"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-460, -140], "id": "957b5a35-e607-4f47-8d83-0e4739bbfd16", "name": "可视化分析报告", "onError": "continueRegularOutput"}, {"parameters": {"promptType": "define", "text": "=input data: {{ $json.query }}\n\n", "hasOutputParser": true, "options": {"systemMessage": "=### 角色设定\n你是一位企业财务数据可视化专家，精通Chart.js和企业财务分析。你专门处理企业级财务数据，包括利润表、损益表、各种贡献度分析、成本费用分析等，能够生成专业的企业级图表。\n\n**核心使命**：为企业管理层提供专业、准确、美观的财务数据可视化图表。\n\n### 企业财务数据专业处理能力\n\n#### 1. 利润表/损益表数据\n- **柱状图**：展示收入、成本、利润对比\n- **堆叠柱状图**：多期利润表对比\n- **折线图**：利润趋势分析\n\n#### 2. 贡献度分析数据\n- **饼图/环形图**：客户/产品/地区贡献度占比\n- **柱状图**：贡献度排名分析\n- **水平条形图**：贡献度对比\n\n#### 3. 成本费用分析\n- **堆叠柱状图**：成本构成分析\n- **柱状图**：成本分类对比\n- **雷达图**：多维成本对比\n\n#### 4. 财务比率分析\n- **柱状图**：关键财务指标对比\n- **散点图**：比率相关性分析\n- **折线图**：比率趋势分析\n\n#### 5. 现金流分析\n- **柱状图**：现金流构成分析\n- **折线图**：现金流趋势\n- **堆叠柱状图**：三大现金流对比\n\n### Chart.js企业级配色方案\n```javascript\n// 专业企业配色（Chart.js格式）\nconst ENTERPRISE_COLORS = {\n  primary: '#1f4e79',      // 企业蓝\n  success: '#2d7d32',      // 盈利绿\n  warning: '#f57c00',      // 警示橙\n  danger: '#c62828',       // 亏损红\n  info: '#0288d1',         // 信息蓝\n  neutral: '#616161',      // 中性灰\n  \n  // 财务专用色彩\n  revenue: '#4caf50',      // 收入绿\n  cost: '#f44336',         // 成本红\n  profit: '#2196f3',       // 利润蓝\n  expense: '#ff9800',      // 费用橙\n  asset: '#9c27b0',        // 资产紫\n  liability: '#795548',    // 负债棕\n  equity: '#607d8b'        // 权益灰蓝\n};\n```\n\n### 智能数据识别算法\n\n#### 财务科目自动识别：\n1. **收入类**：营业收入、主营业务收入、其他业务收入 → 绿色系\n2. **成本类**：营业成本、主营业务成本、其他业务成本 → 红色系\n3. **费用类**：管理费用、销售费用、财务费用 → 橙色系\n4. **利润类**：毛利润、营业利润、净利润 → 蓝色系\n\n#### 业务场景智能判断：\n1. **profit_loss** → 柱状图展示利润构成\n2. **customer_contrib** → 饼图展示客户贡献\n3. **product_contrib** → 柱状图展示产品贡献\n4. **cost_analysis** → 堆叠柱状图展示成本构成\n5. **inventory_turnover** → 折线图展示周转率趋势\n6. **financial_ratios** → 雷达图展示财务健康度\n\n### 专业Chart.js配置示例\n\n#### 利润表柱状图：\n```json\n{\n  \"output\": {\n    \"type\": \"bar\",\n    \"data\": {\n      \"labels\": [\"营业收入\", \"营业成本\", \"毛利润\", \"管理费用\", \"销售费用\", \"净利润\"],\n      \"datasets\": [{\n        \"label\": \"金额(万元)\",\n        \"data\": [100, -60, 40, -15, -10, 15],\n        \"backgroundColor\": [\n          \"#4caf50\",  // 营业收入 - 绿色\n          \"#f44336\",  // 营业成本 - 红色\n          \"#2196f3\",  // 毛利润 - 蓝色\n          \"#ff9800\",  // 管理费用 - 橙色\n          \"#ff9800\",  // 销售费用 - 橙色\n          \"#4caf50\"   // 净利润 - 绿色\n        ],\n        \"borderColor\": [\n          \"#4caf50\", \"#f44336\", \"#2196f3\", \"#ff9800\", \"#ff9800\", \"#4caf50\"\n        ],\n        \"borderWidth\": 2\n      }]\n    },\n    \"options\": {\n      \"responsive\": true,\n      \"maintainAspectRatio\": true,\n      \"aspectRatio\": 1.6,\n      \"layout\": {\n        \"padding\": {\n          \"top\": 12,\n          \"bottom\": 12,\n          \"left\": 8,\n          \"right\": 8\n        }\n      },\n      \"plugins\": {\n        \"title\": {\n          \"display\": true,\n          \"text\": \"利润表分析\",\n          \"font\": {\n            \"size\": 15,\n            \"weight\": \"bold\",\n            \"family\": \"Arial, sans-serif\"\n          },\n          \"color\": \"#1f4e79\",\n          \"padding\": {\n            \"top\": 10,\n            \"bottom\": 15\n          }\n        },\n        \"legend\": {\n          \"display\": true,\n          \"position\": \"top\",\n          \"labels\": {\n            \"padding\": 12,\n            \"usePointStyle\": true,\n            \"font\": {\n              \"size\": 12\n            },\n            \"boxWidth\": 12\n          }\n        }\n      },\n      \"scales\": {\n        \"y\": {\n          \"beginAtZero\": true,\n          \"grid\": {\n            \"color\": \"rgba(0,0,0,0.1)\"\n          },\n          \"ticks\": {\n            \"font\": {\n              \"size\": 11\n            },\n            \"maxTicksLimit\": 6,\n            \"callback\": function(value) {\n              return value + '万';\n            }\n          }\n        },\n        \"x\": {\n          \"grid\": {\n            \"display\": false\n          },\n          \"ticks\": {\n            \"font\": {\n              \"size\": 11\n            },\n            \"maxRotation\": 0,\n            \"minRotation\": 0,\n            \"maxTicksLimit\": 8\n          }\n        }\n      }\n    }\n  }\n}\n```\n\n#### 客户贡献度饼图：\n```json\n{\n  \"output\": {\n    \"type\": \"pie\",\n    \"data\": {\n      \"labels\": [\"客户A\", \"客户B\", \"客户C\", \"客户D\", \"其他\"],\n      \"datasets\": [{\n        \"label\": \"贡献度\",\n        \"data\": [35, 25, 20, 15, 5],\n        \"backgroundColor\": [\n          \"#1f4e79\",  // 企业蓝\n          \"#2d7d32\",  // 盈利绿\n          \"#f57c00\",  // 警示橙\n          \"#0288d1\",  // 信息蓝\n          \"#616161\"   // 中性灰\n        ],\n        \"borderColor\": \"#ffffff\",\n        \"borderWidth\": 2\n      }]\n    },\n    \"options\": {\n      \"responsive\": true,\n      \"plugins\": {\n        \"title\": {\n          \"display\": true,\n          \"text\": \"客户贡献度分析\",\n          \"font\": {\n            \"size\": 18,\n            \"weight\": \"bold\"\n          },\n          \"color\": \"#1f4e79\"\n        },\n        \"legend\": {\n          \"position\": \"right\"\n        }\n      }\n    }\n  }\n}\n```\n\n#### 成本构成堆叠柱状图：\n```json\n{\n  \"output\": {\n    \"type\": \"bar\",\n    \"data\": {\n      \"labels\": [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n      \"datasets\": [\n        {\n          \"label\": \"直接成本\",\n          \"data\": [300, 320, 310, 330],\n          \"backgroundColor\": \"#f44336\",\n          \"stack\": \"成本\"\n        },\n        {\n          \"label\": \"间接成本\",\n          \"data\": [100, 110, 105, 115],\n          \"backgroundColor\": \"#ff9800\",\n          \"stack\": \"成本\"\n        },\n        {\n          \"label\": \"管理费用\",\n          \"data\": [80, 85, 82, 88],\n          \"backgroundColor\": \"#ff5722\",\n          \"stack\": \"成本\"\n        }\n      ]\n    },\n    \"options\": {\n      \"responsive\": true,\n      \"plugins\": {\n        \"title\": {\n          \"display\": true,\n          \"text\": \"季度成本构成分析\",\n          \"font\": {\n            \"size\": 18,\n            \"weight\": \"bold\"\n          },\n          \"color\": \"#1f4e79\"\n        }\n      },\n      \"scales\": {\n        \"x\": {\n          \"stacked\": true\n        },\n        \"y\": {\n          \"stacked\": true,\n          \"beginAtZero\": true,\n          \"ticks\": {\n            \"callback\": function(value) {\n              return value + '万';\n            }\n          }\n        }\n      }\n    }\n  }\n}\n```\n\n### 企业级数据处理规则\n\n#### 1. 数值格式化：\n- 金额单位自动转换（元→万元→亿元）\n- 百分比格式化（保留2位小数）\n- 负数显示优化（红色标注）\n\n#### 2. 颜色语义化：\n- 收入/利润 → 绿色系（正向指标）\n- 成本/费用 → 红色/橙色系（成本指标）\n- 资产 → 蓝色系（资源指标）\n- 负债 → 棕色系（债务指标）\n\n#### 3. 图表类型智能选择：\n- 对比分析 → 柱状图\n- 占比分析 → 饼图\n- 趋势分析 → 折线图\n- 构成分析 → 堆叠图\n- 多维分析 → 雷达图\n- **时间线分析 → Timeline图表**\n- **事件序列 → 甘特图**\n- **发展历程 → 时间轴图**\n\n### 重要输出要求\n\n**你必须输出一个包含\"output\"属性的JSON对象**，该属性包含完整的Chart.js配置：\n\n```json\n{\n  \"output\": {\n    \"type\": \"图表类型\",\n    \"data\": {\n      \"labels\": [\"标签数组\"],\n      \"datasets\": [数据集配置]\n    },\n    \"options\": {\n      \"responsive\": true,\n      \"plugins\": {\n        \"title\": {\n          \"display\": true,\n          \"text\": \"图表标题\"\n        }\n      }\n    }\n  }\n}\n```\n\n### Timeline图表配置模板\n\n当需要显示时间线或发展历程时，使用以下配置：\n\n```json\n{\n  \"output\": {\n    \"type\": \"line\",\n    \"data\": {\n      \"labels\": [\"2024-01\", \"2024-02\", \"2024-03\", \"2024-04\", \"2024-05\"],\n      \"datasets\": [{\n        \"label\": \"业务发展时间线\",\n        \"data\": [100, 150, 200, 180, 250],\n        \"borderColor\": \"#1f4e79\",\n        \"backgroundColor\": \"rgba(31, 78, 121, 0.1)\",\n        \"fill\": true,\n        \"tension\": 0.4,\n        \"pointRadius\": 8,\n        \"pointHoverRadius\": 12,\n        \"pointBackgroundColor\": \"#1f4e79\",\n        \"pointBorderColor\": \"#ffffff\",\n        \"pointBorderWidth\": 3\n      }]\n    },\n    \"options\": {\n      \"responsive\": true,\n      \"maintainAspectRatio\": true,\n      \"aspectRatio\": 2,\n      \"plugins\": {\n        \"title\": {\n          \"display\": true,\n          \"text\": \"企业发展时间线\",\n          \"font\": {\n            \"size\": 16,\n            \"weight\": \"bold\"\n          },\n          \"color\": \"#1f4e79\"\n        },\n        \"legend\": {\n          \"display\": true,\n          \"position\": \"top\"\n        },\n        \"tooltip\": {\n          \"mode\": \"index\",\n          \"intersect\": false,\n          \"callbacks\": {\n            \"label\": function(context) {\n              return context.dataset.label + ': ' + context.parsed.y + '万元';\n            }\n          }\n        }\n      },\n      \"scales\": {\n        \"y\": {\n          \"beginAtZero\": true,\n          \"title\": {\n            \"display\": true,\n            \"text\": \"金额(万元)\"\n          }\n        },\n        \"x\": {\n          \"title\": {\n            \"display\": true,\n            \"text\": \"时间\"\n          }\n        }\n      },\n      \"elements\": {\n        \"point\": {\n          \"hoverRadius\": 12\n        }\n      }\n    }\n  }\n}\n```\n\n### Mermaid时间线配置\n\n对于复杂的时间线，也可以使用Mermaid语法：\n\n```mermaid\ntimeline\n    title 企业发展历程\n\n    2024年1月 : 项目启动\n              : 团队组建\n\n    2024年2月 : 产品开发\n              : 市场调研\n\n    2024年3月 : 产品发布\n              : 用户增长\n\n    2024年4月 : 业务扩展\n              : 合作伙伴\n```\n\n**记住**：你的输出必须是专业的企业级Chart.js配置，能够准确反映财务数据的业务含义，为企业决策提供有价值的可视化支持。对于时间序列数据，优先使用Timeline配置。\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [840, -160], "id": "24b0ce39-3975-474f-bc6f-5e1423a29b03", "name": "图表生成", "onError": "continueRegularOutput"}, {"parameters": {"assignments": {"assignments": [{"id": "7a6da40c-4e76-4476-b3ca-d915363ec1e9", "name": "=chartUrl", "value": "={{ \"https://quickchart.io/chart?width=480&height=300&devicePixelRatio=2&backgroundColor=white&format=png&c=\" + encodeURIComponent(JSON.stringify($json.output)) }}", "type": "string"}, {"id": "48473c3b-9d32-42ad-8712-b5958c0f88e2", "name": "=chartMarkdown", "value": "={{ \"![数据分析图表](https://quickchart.io/chart?width=480&height=300&devicePixelRatio=2&backgroundColor=white&format=png&c=\" + encodeURIComponent(JSON.stringify($json.output)) + \")\" }}", "type": "string"}, {"id": "2b13537f-33d5-4d2b-9e20-20683258aa50", "name": "=chartConfig", "value": "={{ $json.output }}", "type": "object"}, {"id": "bc990bc5-224a-473d-8196-cf612e385f04", "name": "=timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}, {"id": "65d8f2e4-a9bb-4cfa-bae0-31256470e3cf", "name": "=success", "value": "={{ true }}", "type": "string"}, {"id": "d7cbc540-5aaf-4307-a317-e8ee3742d417", "name": "chartType", "value": "={{ \"chartjs\" }}", "type": "string"}, {"id": "8af5959c-65fe-49bd-bdef-cea0c9440f6f", "name": "=renderOptions", "value": "={{ {\n  \"width\": 480,\n  \"height\": 300,\n  \"devicePixelRatio\": 2,\n  \"backgroundColor\": \"white\",\n  \"format\": \"png\",\n  \"service\": \"quickchart.io\"\n} }}", "type": "string"}, {"id": "da5e2ab2-aa9a-401f-8f92-63bb0799290a", "name": "=debugInfo", "value": "={{ {   \"hasOutput\": $json.output ? true : false,   \"timestamp\": new Date().toISOString() } }}", "type": "string"}, {"id": "e4f01164-5eaf-42fc-b4a4-33e4597219a8", "name": "", "value": "", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1200, -160], "id": "07060ae0-f1b2-48ce-be41-e251343509a7", "name": "设置参数-图表生成"}, {"parameters": {"method": "POST", "url": "https://mcp-on-edge.edgeone.site/mcp-server", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content_Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"jsonrpc\": \"2.0\",\n  \"id\": 1,\n  \"method\": \"tools/call\",\n  \"params\": {\n    \"name\": \"deploy-html\",\n    \"arguments\": {\n      \"value\": \"{{ JSON.stringify($node['html'].json.htmlContent).slice(1, -1) }}\"\n    }\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [240, -140], "id": "f9245461-4117-45cd-903e-b0011daba8fa", "name": "发布"}, {"parameters": {"options": {"frequencyPenalty": 0, "maxTokens": 8192, "presencePenalty": 0, "temperature": 0.7, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [-540, 60], "id": "76bb1f92-2fc2-4312-b9bb-e523c0b03410", "name": "DeepSeek Chat Model", "credentials": {"deepSeekApi": {"id": "IKqWJBBXULhRu8mZ", "name": "DeepSeek account"}}}, {"parameters": {"options": {"frequencyPenalty": 0, "maxTokens": 8192, "presencePenalty": 0, "temperature": 0.3, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [820, 20], "id": "1053b04c-de2a-4af2-a6de-e5f3d5528571", "name": "DeepSeek Chat Model1", "credentials": {"deepSeekApi": {"id": "IKqWJBBXULhRu8mZ", "name": "DeepSeek account"}}}, {"parameters": {"jsCode": "// 响应式HTML包装器 - 兼容手机和电脑\nconst inputData = $input.all();\n\nconst processedItems = inputData.map(item => {\n  const content = item.json.output || item.json.content || item.json.text || '';\n\n  // 统计内容\n  const chartCount = (content.match(/!\\[([^\\]]*)\\]\\((https:\\/\\/quickchart\\.io\\/chart[^)]+)\\)/g) || []).length;\n  const mermaidCount = (content.match(/```mermaid[\\s\\S]*?```/g) || []).length;\n\n  // 生成响应式HTML\n  const fullHtml = `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, user-scalable=yes\">\n    <title>企业经营分析报告</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/marked/marked.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js\"></script>\n    <style>\n        /* 基础样式 */\n        * { box-sizing: border-box; }\n        body {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;\n            line-height: 1.6;\n            color: #333;\n            margin: 0;\n            padding: 10px;\n            background: #f5f7fa;\n            font-size: 16px;\n        }\n\n        /* 容器样式 */\n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n            background: white;\n            border-radius: 12px;\n            box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n            overflow: hidden;\n        }\n\n        /* 头部样式 */\n        .header {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n            padding: 20px;\n            text-align: center;\n        }\n        .header h1 {\n            margin: 0;\n            font-size: 1.8rem;\n            font-weight: 700;\n        }\n        .header p {\n            margin: 8px 0 0 0;\n            opacity: 0.9;\n            font-size: 0.9rem;\n        }\n\n        /* 内容区域 */\n        .content {\n            padding: 20px;\n        }\n\n        /* 调试信息 */\n        .debug {\n            background: rgba(0,0,0,0.8);\n            color: white;\n            padding: 8px 12px;\n            border-radius: 5px;\n            font-size: 11px;\n            margin-bottom: 15px;\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n\n        /* 标题样式 */\n        h1, h2, h3, h4, h5, h6 {\n            color: #2c3e50;\n            margin: 1.5em 0 0.8em 0;\n            line-height: 1.3;\n        }\n        h1 {\n            font-size: 1.8rem;\n            border-bottom: 2px solid #3498db;\n            padding-bottom: 0.5em;\n        }\n        h2 {\n            font-size: 1.5rem;\n            border-bottom: 1px solid #bdc3c7;\n            padding-bottom: 0.3em;\n        }\n        h3 {\n            font-size: 1.3rem;\n            border-left: 4px solid #3498db;\n            padding-left: 12px;\n        }\n\n        /* 表格样式 */\n        .table-wrapper {\n            overflow-x: auto;\n            margin: 1em 0;\n            border-radius: 8px;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n        }\n        table {\n            border-collapse: collapse;\n            width: 100%;\n            min-width: 300px;\n            background: white;\n        }\n        th, td {\n            border: 1px solid #e1e8ed;\n            padding: 12px 8px;\n            text-align: left;\n            font-size: 0.9rem;\n        }\n        th {\n            background: #3498db;\n            color: white;\n            font-weight: 600;\n            position: sticky;\n            top: 0;\n        }\n        tr:nth-child(even) {\n            background: #f8f9fa;\n        }\n        tr:hover {\n            background: #e3f2fd;\n        }\n\n        /* 图片和图表样式 */\n        img {\n            max-width: 100%;\n            height: auto;\n            display: block;\n            margin: 15px auto;\n            border-radius: 8px;\n            box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n        }\n\n        /* Mermaid样式 */\n        .mermaid {\n            text-align: center;\n            margin: 20px 0;\n            background: #f8f9fa;\n            padding: 15px;\n            border-radius: 8px;\n            overflow-x: auto;\n        }\n\n        /* 代码样式 */\n        pre {\n            background: #f8f9fa;\n            padding: 15px;\n            border-radius: 8px;\n            overflow-x: auto;\n            border-left: 4px solid #3498db;\n            margin: 1em 0;\n        }\n        code {\n            background: #f1f3f4;\n            padding: 2px 6px;\n            border-radius: 4px;\n            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;\n            font-size: 0.9em;\n        }\n\n        /* 文本样式 */\n        p {\n            margin: 1em 0;\n            line-height: 1.7;\n        }\n        strong {\n            color: #2c3e50;\n            font-weight: 600;\n        }\n        em {\n            color: #7f8c8d;\n        }\n\n        /* 列表样式 */\n        ul, ol {\n            padding-left: 1.5em;\n            margin: 1em 0;\n        }\n        li {\n            margin: 0.3em 0;\n            line-height: 1.6;\n        }\n\n        /* 引用样式 */\n        blockquote {\n            border-left: 4px solid #3498db;\n            margin: 1em 0;\n            padding: 0.5em 0 0.5em 1em;\n            background: #f8f9fa;\n            border-radius: 0 4px 4px 0;\n            color: #555;\n        }\n\n        /* 手机端优化 */\n        @media (max-width: 768px) {\n            body {\n                padding: 5px;\n                font-size: 15px;\n            }\n            .container {\n                border-radius: 8px;\n            }\n            .header {\n                padding: 15px;\n            }\n            .header h1 {\n                font-size: 1.5rem;\n            }\n            .content {\n                padding: 15px;\n            }\n            h1 {\n                font-size: 1.5rem;\n            }\n            h2 {\n                font-size: 1.3rem;\n            }\n            h3 {\n                font-size: 1.1rem;\n            }\n            th, td {\n                padding: 8px 6px;\n                font-size: 0.85rem;\n            }\n            .debug {\n                flex-direction: column;\n                align-items: flex-start;\n                gap: 5px;\n            }\n            pre {\n                padding: 10px;\n                font-size: 0.8rem;\n            }\n            code {\n                font-size: 0.8rem;\n            }\n        }\n\n        /* 超小屏幕优化 */\n        @media (max-width: 480px) {\n            body {\n                padding: 3px;\n                font-size: 14px;\n            }\n            .header {\n                padding: 12px;\n            }\n            .header h1 {\n                font-size: 1.3rem;\n            }\n            .content {\n                padding: 12px;\n            }\n            h1 {\n                font-size: 1.3rem;\n            }\n            h2 {\n                font-size: 1.1rem;\n            }\n            h3 {\n                font-size: 1rem;\n            }\n            th, td {\n                padding: 6px 4px;\n                font-size: 0.8rem;\n            }\n        }\n\n        /* 打印样式 */\n        @media print {\n            body {\n                background: white;\n                padding: 0;\n            }\n            .container {\n                box-shadow: none;\n                border-radius: 0;\n            }\n            .debug {\n                display: none;\n            }\n            .header {\n                background: #333 !important;\n                -webkit-print-color-adjust: exact;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <!-- 报告头部 -->\n        <div class=\"header\">\n            <h1>企业经营分析报告</h1>\n            <p>响应式版本 - 兼容手机和电脑</p>\n        </div>\n\n        <!-- 内容区域 -->\n        <div class=\"content\">\n            <!-- 调试信息 -->\n            <div class=\"debug\">\n                <div>📊 图表: ${chartCount} | 📈 Mermaid: ${mermaidCount}</div>\n                <div id=\"status\">加载中...</div>\n            </div>\n\n            <!-- 内容容器 -->\n            <div id=\"content-container\">\n                <!-- Markdown内容将在这里渲染 -->\n            </div>\n\n            <!-- 报告尾部 -->\n            <div style=\"margin-top: 30px; padding-top: 15px; border-top: 1px solid #e1e8ed; text-align: center; color: #6c757d; font-size: 0.9rem;\">\n                <p>📅 ${new Date().toLocaleString('zh-CN')} | 📱💻 响应式版本</p>\n            </div>\n        </div>\n    </div>\n\n    <!-- 原始Markdown内容（隐藏） -->\n    <script type=\"text/markdown\" id=\"markdown-content\">\n${content}\n    </script>\n\n    <script>\n        document.addEventListener('DOMContentLoaded', function() {\n            const status = document.getElementById('status');\n            status.textContent = '初始化中...';\n\n            // 获取Markdown内容\n            const markdownContent = document.getElementById('markdown-content').textContent;\n\n            // 渲染Markdown\n            if (typeof marked !== 'undefined') {\n                marked.setOptions({ breaks: true, gfm: true });\n\n                try {\n                    const htmlContent = marked.parse(markdownContent);\n\n                    // 处理表格包装\n                    const processedContent = htmlContent.replace(/<table>/g, '<div class=\"table-wrapper\"><table>').replace(/<\\\\/table>/g, '</table></div>');\n\n                    document.getElementById('content-container').innerHTML = processedContent;\n                    status.textContent = '✅ 已加载';\n\n                    // 图表加载监控\n                    document.querySelectorAll('img[src*=\"quickchart.io\"]').forEach((img, index) => {\n                        img.addEventListener('load', () => console.log(\\`图表 \\${index + 1} 加载成功\\`));\n                        img.addEventListener('error', () => console.log(\\`图表 \\${index + 1} 加载失败\\`));\n                    });\n\n                } catch (error) {\n                    document.getElementById('content-container').innerHTML = \\`\n                        <div style=\"background: #fee; border: 1px solid #fcc; padding: 15px; border-radius: 5px; color: #c33;\">\n                            <h3>渲染失败</h3>\n                            <p>错误: \\${error.message}</p>\n                            <details><summary>原始内容</summary><pre>\\${markdownContent}</pre></details>\n                        </div>\n                    \\`;\n                    status.textContent = '❌ 失败';\n                }\n            } else {\n                document.getElementById('content-container').innerHTML = \\`<pre>\\${markdownContent}</pre>\\`;\n                status.textContent = '⚠️ 库未加载';\n            }\n\n            // 初始化Mermaid\n            setTimeout(() => {\n                if (typeof mermaid !== 'undefined') {\n                    mermaid.initialize({ startOnLoad: true, theme: 'default', securityLevel: 'loose' });\n                    status.textContent = '🎉 完成';\n                }\n            }, 1500);\n        });\n    </script>\n</body>\n</html>`;\n\n  return {\n    json: {\n      ...item.json,\n      htmlContent: fullHtml,\n      processedAt: new Date().toISOString(),\n      version: 'minimal-wrapper-v1.0',\n      originalChartCount: chartCount,\n      originalMermaidCount: mermaidCount,\n      approach: 'client-side-rendering'\n    }\n  };\n});\n\nreturn processedItems;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [0, -140], "id": "3264248c-c24b-4ba5-b936-8d3d4851b1ec", "name": "html"}], "connections": {"Form Trigger": {"main": [[{"node": "从Google Sheet获取数据 (占位)", "type": "main", "index": 0}]]}, "从Google Sheet获取数据 (占位)": {"main": [[{"node": "数据预处理与聚合", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "可视化分析报告", "type": "ai_memory", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "图表生成", "type": "main", "index": 0}]]}, "Generate Chart": {"ai_tool": [[{"node": "可视化分析报告", "type": "ai_tool", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "图表生成", "type": "ai_outputParser", "index": 0}]]}, "数据预处理与聚合": {"main": [[{"node": "可视化分析报告", "type": "main", "index": 0}]]}, "图表生成": {"main": [[{"node": "设置参数-图表生成", "type": "main", "index": 0}]]}, "可视化分析报告": {"main": [[{"node": "html", "type": "main", "index": 0}]]}, "DeepSeek Chat Model": {"ai_languageModel": [[{"node": "可视化分析报告", "type": "ai_languageModel", "index": 0}]]}, "DeepSeek Chat Model1": {"ai_languageModel": [[{"node": "图表生成", "type": "ai_languageModel", "index": 0}]]}, "html": {"main": [[{"node": "发布", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}