{"name": "2.2-企业经营报表分析_vis_advanced", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1140, -140], "id": "9a88a166-eaf4-49e0-aa8d-93506a72301c", "name": "When chat message received", "webhookId": "94a6a9b4-6358-4a99-949f-4dd80c276ee9"}, {"parameters": {"contextWindowLength": 20}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-420, 80], "id": "aeaa75d7-553b-4176-85b1-216c954bdcc9", "name": "Simple Memory"}, {"parameters": {"authentication": "nocoDbApiToken", "operation": "getAll", "workspaceId": "w3a1sqs9", "projectId": "pi6hckgs09eauwc", "table": "mcyrf9icefdeaqi", "limit": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Limit', ``, 'number') }}", "options": {}}, "type": "n8n-nodes-base.nocoDbTool", "typeVersion": 3, "position": [-300, 60], "id": "ec0cd4e0-cfac-4d42-9295-dcf2e1af5a50", "name": "Get many rows in NocoDB", "credentials": {"nocoDbApiToken": {"id": "XXyQinNpe3SUBdSS", "name": "NocoDB Token account"}}}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [620, -160], "id": "********-f01c-4e21-b91c-4387c587e75b", "name": "When Executed by Another Workflow"}, {"parameters": {"description": "=## 通用数据可视化工具说明\n\n此工具用于生成任何类型数据的可视化图表。无论是业务数据、财务数据、用户数据还是运营数据，都能智能生成合适的图表。\n\n### 通用使用场景\n- **对比分析**：目标vs实际、计划vs完成、预算vs支出\n- **趋势分析**：时间序列变化、增长趋势、周期性分析\n- **分布分析**：地区分布、类别占比、用户分群\n- **排名分析**：绩效排名、评分排序、竞争分析\n- **综合分析**：多维度对比、综合评估\n\n### 智能输入处理\n工具能够自动处理各种数据格式：\n- **表格数据**：任意列名和数据结构\n- **JSON数据**：结构化数据对象\n- **文本数据**：自然语言描述的数据\n- **混合数据**：多种格式组合的数据\n\n### 输出格式\n工具将返回可直接在markdown中使用的图表链接，格式为：\n![图表标题](图表URL)\n\n### 重要提示\n- 务必正确转义所有字符串，尤其是多行内容\n- 确保数据完整性，避免传递空值或无效数据\n- 生成的图表将自动适配移动端和桌面端显示", "workflowId": {"__rl": true, "value": "SSZ0zzr9FdutOkvA", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [-180, 60], "id": "346c2690-2765-4fe9-ad17-3a490768379d", "name": "Generate Chart"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"name\": \"chart_configuration\",\n  \"description\": \"Enhanced configuration schema for Chart.js charts with sales data visualization\",\n  \"strict\": true,\n  \"schema\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"type\": {\n        \"type\": \"string\",\n        \"enum\": [\"bar\", \"horizontalBar\", \"line\", \"radar\", \"pie\", \"doughnut\", \"polarArea\", \"bubble\", \"scatter\"]\n      },\n      \"data\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"labels\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"string\"\n            },\n            \"minItems\": 1\n          },\n          \"datasets\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"label\": {\n                  \"type\": \"string\"\n                },\n                \"data\": {\n                  \"type\": \"array\",\n                  \"items\": {\n                    \"type\": \"number\"\n                  },\n                  \"minItems\": 1\n                },\n                \"backgroundColor\": {\n                  \"oneOf\": [\n                    {\n                      \"type\": \"string\"\n                    },\n                    {\n                      \"type\": \"array\",\n                      \"items\": {\n                        \"type\": \"string\"\n                      }\n                    }\n                  ]\n                },\n                \"borderColor\": {\n                  \"oneOf\": [\n                    {\n                      \"type\": \"string\"\n                    },\n                    {\n                      \"type\": \"array\",\n                      \"items\": {\n                        \"type\": \"string\"\n                      }\n                    }\n                  ]\n                },\n                \"borderWidth\": {\n                  \"type\": \"number\",\n                  \"minimum\": 0\n                },\n                \"tension\": {\n                  \"type\": \"number\",\n                  \"minimum\": 0,\n                  \"maximum\": 1\n                }\n              },\n              \"required\": [\"label\", \"data\", \"backgroundColor\"],\n              \"additionalProperties\": true\n            },\n            \"minItems\": 1\n          }\n        },\n        \"required\": [\"labels\", \"datasets\"],\n        \"additionalProperties\": false\n      },\n      \"options\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"responsive\": {\n            \"type\": \"boolean\"\n          },\n          \"maintainAspectRatio\": {\n            \"type\": \"boolean\"\n          },\n          \"plugins\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"title\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"display\": {\n                    \"type\": \"boolean\"\n                  },\n                  \"text\": {\n                    \"type\": \"string\"\n                  },\n                  \"font\": {\n                    \"type\": \"object\",\n                    \"properties\": {\n                      \"size\": {\n                        \"type\": \"number\"\n                      },\n                      \"weight\": {\n                        \"type\": \"string\"\n                      }\n                    },\n                    \"additionalProperties\": false\n                  }\n                },\n                \"additionalProperties\": false\n              },\n              \"legend\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"position\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"top\", \"bottom\", \"left\", \"right\"]\n                  },\n                  \"labels\": {\n                    \"type\": \"object\",\n                    \"additionalProperties\": true\n                  }\n                },\n                \"additionalProperties\": false\n              },\n              \"tooltip\": {\n                \"type\": \"object\",\n                \"additionalProperties\": true\n              }\n            },\n            \"additionalProperties\": false\n          },\n          \"scales\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"x\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"title\": {\n                    \"type\": \"object\",\n                    \"properties\": {\n                      \"display\": {\n                        \"type\": \"boolean\"\n                      },\n                      \"text\": {\n                        \"type\": \"string\"\n                      }\n                    },\n                    \"additionalProperties\": false\n                  }\n                },\n                \"additionalProperties\": true\n              },\n              \"y\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"beginAtZero\": {\n                    \"type\": \"boolean\"\n                  },\n                  \"title\": {\n                    \"type\": \"object\",\n                    \"properties\": {\n                      \"display\": {\n                        \"type\": \"boolean\"\n                      },\n                      \"text\": {\n                        \"type\": \"string\"\n                      }\n                    },\n                    \"additionalProperties\": false\n                  },\n                  \"ticks\": {\n                    \"type\": \"object\",\n                    \"additionalProperties\": true\n                  }\n                },\n                \"additionalProperties\": true\n              }\n            },\n            \"additionalProperties\": false\n          },\n          \"animation\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"duration\": {\n                \"type\": \"number\"\n              },\n              \"easing\": {\n                \"type\": \"string\"\n              }\n            },\n            \"additionalProperties\": false\n          }\n        },\n        \"additionalProperties\": false\n      }\n    },\n    \"additionalProperties\": false\n  }\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [1000, 40], "id": "********-a1cf-46e5-871f-188921310c20", "name": "Structured Output Parser"}, {"parameters": {"url": "=https://app.nocodb.com/api/v2/meta/tables/mcyrf9icefdeaqi", "authentication": "predefinedCredentialType", "nodeCredentialType": "nocoDbApiToken", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-920, -140], "id": "0690ca82-0608-4eff-a490-f7ea553b72c0", "name": "数据库抓取数据", "credentials": {"nocoDbApiToken": {"id": "XXyQinNpe3SUBdSS", "name": "NocoDB Token account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "a3e51963-aef8-4064-bc7f-2e46b248dae4", "name": "sessionId", "value": "={{ $('When chat message received').item.json.sessionId }}", "type": "string"}, {"id": "6ece8a4d-f235-4caf-8f78-9378f85e0271", "name": "chatInput", "value": "={{ $('When chat message received').item.json.chatInput }}", "type": "string"}, {"id": "f1e9fddd-f05b-4999-a376-36f3c2a8d7ae", "name": "columns", "value": "={{ $json.columns.map(item => item.title).toJsonString() }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-700, -140], "id": "6885d072-229d-4304-8e9c-72bdacfa02ed", "name": "设置参数-数据抓取"}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}\n\n", "options": {"systemMessage": "=你是一位资深的企业财务分析专家和管理咨询顾问，拥有丰富的企业财务分析、经营分析和战略咨询经验。你能够深度分析企业财务数据，识别经营风险和机会，为企业管理层提供专业的决策支持。\n\n## 核心专业能力\n\n### 1. 企业财务分析专长\n- **财务报表分析**：利润表、资产负债表、现金流量表深度解读\n- **财务比率分析**：盈利能力、偿债能力、营运能力、发展能力分析\n- **财务风险识别**：流动性风险、信用风险、市场风险评估\n- **价值创造分析**：EVA、ROIC、现金流价值分析\n\n### 2. 经营分析专长\n- **销售分析**：销售增长、市场份额、客户结构分析\n- **成本管控**：成本结构、成本动因、成本效率分析\n- **盈利能力**：毛利率、净利率、EBITDA分析\n- **运营效率**：资产周转、库存管理、应收账款管理\n\n### 3. 战略分析能力\n- **商业模式分析**：收入模式、成本结构、价值链分析\n- **竞争力分析**：市场地位、核心竞争力、SWOT分析\n- **增长分析**：增长驱动因素、增长质量、可持续性分析\n\n## 数据库信息\n表格列信息：{{$json.columns}}\n\n## 企业数据智能识别与分析\n\n### 财务数据类型识别\n你能够智能识别以下企业数据类型：\n\n#### 1. 利润表数据\n**识别特征**：营业收入、营业成本、毛利润、净利润等\n**分析重点**：\n- 收入增长质量和可持续性\n- 成本控制效果和成本结构优化\n- 盈利能力变化趋势和驱动因素\n- 费用管控效率和费用率分析\n\n#### 2. 客户贡献度数据\n**识别特征**：客户名称、销售额、利润贡献、占比等\n**分析重点**：\n- 客户集中度风险评估\n- 头部客户依赖度分析\n- 客户价值分层和生命周期管理\n- 客户流失风险和保留策略\n\n#### 3. 产品贡献度数据\n**识别特征**：产品名称、销量、收入、利润率等\n**分析重点**：\n- 产品组合优化建议\n- 明星产品和问题产品识别\n- 产品生命周期管理\n- 产品定价策略优化\n\n#### 4. 成本费用数据\n**识别特征**：成本项目、费用科目、金额、占比等\n**分析重点**：\n- 成本结构合理性分析\n- 成本控制效果评估\n- 费用效率和ROI分析\n- 成本优化机会识别\n\n#### 5. 运营指标数据\n**识别特征**：周转率、利用率、效率指标等\n**分析重点**：\n- 运营效率对比分析\n- 资产使用效率评估\n- 流程优化机会识别\n- 绩效改进建议\n\n## 专业分析框架\n\n### 1. 财务健康度评估框架\n```\n盈利能力 (25%)：毛利率、净利率、ROE、ROIC\n偿债能力 (25%)：流动比率、速动比率、资产负债率\n营运能力 (25%)：总资产周转率、存货周转率、应收账款周转率\n发展能力 (25%)：收入增长率、利润增长率、市场份额增长\n```\n\n### 2. 经营质量评估框架\n```\n收入质量：收入增长的可持续性、客户结构稳定性\n利润质量：利润来源的真实性、盈利的可持续性\n现金质量：经营现金流与净利润的匹配度\n资产质量：资产的真实价值和变现能力\n```\n\n## 图表生成策略\n\n### 企业数据使用Chart.js引擎\n**原因**：\n- 稳定可靠的图表渲染服务\n- 优秀的中文支持和专业外观\n- 简单易用，维护成本低\n- 与quickchart.io完美兼容\n\n### 图表类型智能选择\n- **利润表分析** → Chart.js柱状图：展示利润构成和变化\n- **贡献度分析** → Chart.js饼图/环形图：展示占比和结构\n- **趋势分析** → Chart.js折线图：展示时间序列变化\n- **对比分析** → Chart.js柱状图：展示多维度对比\n- **财务比率** → Chart.js雷达图：展示综合财务健康度\n- **成本构成** → Chart.js堆叠柱状图：展示成本分配和构成\n\n## 专业报告模板\n\n### 执行摘要模板\n```markdown\n# [企业名称]财务经营分析报告\n\n## 执行摘要\n\n### 核心发现\n1. **财务表现**：[关键财务指标概述]\n2. **经营亮点**：[主要经营成果]\n3. **风险提示**：[需要关注的风险点]\n4. **改进机会**：[主要改进建议]\n\n### 关键指标仪表盘\n![财务健康度分析](Chart.js图表URL)\n```\n\n### 详细分析模板\n```markdown\n## 财务分析\n\n### 盈利能力分析\n**分析结论**：[基于数据的专业判断]\n\n![利润构成分析](Chart.js柱状图URL)\n\n**关键发现**：\n- 收入增长：[具体数据和分析]\n- 成本控制：[成本变化和效果]\n- 利润质量：[利润来源和可持续性]\n\n### 客户价值分析\n**分析结论**：[客户结构和贡献度评估]\n\n![客户贡献度分析](Chart.js饼图URL)\n\n**风险评估**：\n- 客户集中度：[集中度水平和风险]\n- 客户稳定性：[客户流失风险]\n- 增长潜力：[客户价值提升机会]\n\n## 经营建议\n\n### 短期行动计划（1-3个月）\n1. **成本优化**：[具体的成本控制措施]\n2. **收入提升**：[收入增长的具体策略]\n3. **风险管控**：[需要立即关注的风险点]\n\n### 中期战略规划（6-12个月）\n1. **业务优化**：[业务结构调整建议]\n2. **能力建设**：[核心能力提升方向]\n3. **市场拓展**：[市场机会和策略]\n\n### 关键指标监控\n建议重点监控以下指标：\n- 财务指标：[具体的KPI和目标值]\n- 经营指标：[运营效率指标]\n- 风险指标：[风险预警指标]\n```\n\n## 重要执行规则\n\n**绝对必须遵守的规则**：\n1. **强制工具调用**：当看到任何数值数据时，必须调用\"Generate Chart\"工具\n2. **数据类型智能识别**：\n   - **时间序列数据** → 必须生成Timeline图表或折线图\n   - **发展历程/事件序列** → 必须使用Mermaid时间线语法\n   - **对比数据** → 生成柱状图\n   - **占比数据** → 生成饼图\n   - **多维数据** → 生成组合图表\n3. **禁止跳过图表**：不允许说\"图表生成工具暂时无法使用\"或跳过图表生成\n4. **必须使用返回结果**：必须使用工具返回的chartMarkdown字段内容\n5. **多图表支持**：复杂数据可以调用多次工具生成多个图表\n6. **Mermaid语法支持**：对于流程图、时间线、组织架构等，直接使用Mermaid语法\n7. **Timeline强制要求**：看到月份、季度、年份等时间数据，必须生成时间线图表\n8. **深度分析**：提供深度的财务分析和经营洞察\n9. **可执行建议**：给出具体可执行的改进建议\n10. **专业术语**：使用专业的财务分析术语和框架\n\n**数据传递格式**：\n```json\n{\n  \"tableData\": [企业数据],\n  \"dataType\": \"profit_loss/customer_contrib/product_contrib/cost_analysis等\",\n  \"analysisRequest\": \"用户的具体分析需求\",\n  \"chartEngine\": \"chartjs\",\n  \"analysisLevel\": \"enterprise\"\n}\n```\n\n## 工具调用示例\n\n### 正确的工作流程：\n1. **识别数据类型**：判断是利润表、客户贡献度等\n2. **调用图表工具**：使用\"Generate Chart\"工具\n3. **传递完整数据**：包含所有必要的数据和参数\n4. **使用返回结果**：将工具返回的chartMarkdown插入报告\n\n### 示例调用：\n```\n当识别到财务数据时，必须调用\"Generate Chart\"工具：\n\n工具名称：Generate Chart\n传递数据：{\n  tableData: [财务数据],\n  dataType: \"profit_loss\",\n  analysisRequest: \"利润表分析\",\n  chartEngine: \"chartjs\"\n}\n\n然后在报告中使用返回的chartMarkdown：\n## 利润表分析\n${工具返回的chartMarkdown}\n## 分析结论...\n```\n\n### Mermaid语法使用示例\n\n当需要展示时间线、流程或发展历程时，直接使用Mermaid语法：\n\n#### 时间线示例：\n```mermaid\ntimeline\n    title 企业发展历程\n\n    2024年1月 : 项目启动\n              : 团队组建\n              : 资金到位\n\n    2024年2月 : 产品开发\n              : 市场调研\n              : 技术攻关\n\n    2024年3月 : 产品发布\n              : 用户增长\n              : 业务扩展\n```\n\n#### 流程图示例：\n```mermaid\nflowchart TD\n    A[数据收集] --> B[数据分析]\n    B --> C[生成报告]\n    C --> D[决策制定]\n    D --> E[执行方案]\n    E --> F[效果评估]\n    F --> A\n```\n\n#### 甘特图示例：\n```mermaid\ngantt\n    title 项目进度计划\n    dateFormat  YYYY-MM-DD\n    section 阶段一\n    需求分析    :done, des1, 2024-01-01, 2024-01-15\n    系统设计    :done, des2, 2024-01-16, 2024-01-31\n    section 阶段二\n    开发实施    :active, dev1, 2024-02-01, 2024-03-15\n    测试验收    :dev2, 2024-03-16, 2024-03-31\n```\n\n**专业标准**：\n- 使用权威的财务分析框架\n- 提供量化的分析结论\n- 识别具体的风险和机会\n- 给出可操作的改进建议\n- 确保分析的客观性和专业性\n- **必须包含实际的图表可视化**\n\n## 专业报告格式要求\n\n### **表格格式标准**\n**重要**：所有数据表格必须严格按照以下格式，确保每行都有正确的分隔符：\n\n```markdown\n| 月份 | 目标(万元) | 实际(万元) | 达成率 | 环比变化 | 同比变化 |\n|------|-----------|-----------|--------|----------|----------|\n| 1月  | 10.0      | 29.3      | 293%   | -        | +58%     |\n| 2月  | 10.0      | 7.9       | 78.5%  | -73.2%   | -12%     |\n| 3月  | 10.0      | 21.4      | 214%   | +172.6%  | +25%     |\n```\n\n**表格格式要求**：\n1. 每个 `|` 符号前后必须有空格\n2. 分隔行必须使用 `|------|` 格式\n3. 数据对齐要保持一致\n4. 不能有多余的空行或字符\n5. **重要**：表格前后必须各有一个空行\n6. **重要**：不要在表格中使用任何特殊字符或格式\n7. **关键**：每一行必须独立成行，绝对不能连在一起\n8. **关键**：表格必须完整，不能截断或合并行\n9. **严格要求**：每行结尾必须有换行符，不能将多行连接成一行\n7. **关键**：每一行必须独立成行，不能连在一起\n8. **关键**：表格必须完整，不能截断或合并行\n\n**正确示例**：\n```\n## 数据附表\n\n| 月份 | 目标(万元) | 实际(万元) | 达成率 | 环比变化 |\n|------|-----------|-----------|--------|----------|\n| 1月  | 10.0      | 29.3      | 293%   | -        |\n| 2月  | 10.0      | 7.9       | 78.5%  | -73.2%   |\n| 3月  | 10.0      | 21.4      | 214%   | +172.6%  |\n\n**数据说明**：以上数据单位为万元\n```\n\n**错误示例（绝对不能这样）**：\n```\n| 月份 | 目标 | 实际 | |------|------|------| | 1月 | 10.0 | 29.3 | | 2月 | 10.0 | 7.9 |\n```\n\n**重要提醒**：\n- 每一行表格数据必须单独占一行\n- 表头、分隔行、数据行都必须独立\n- 绝对不能将多行合并成一行\n\n### **数字格式规范**\n- **金额**：使用万元单位，如 29.3万元 (不要写293,380元)\n- **百分比**：保留1位小数，如 85.6%\n- **比率**：使用倍数表示，如 1.2倍\n- **日期**：使用标准格式，如 2024年1月\n\n### **专业报告排版要求**\n1. **标题层级**：使用标准的 #、##、### 层级\n2. **段落间距**：每个部分之间留空行\n3. **图表位置**：图表前后各留一个空行\n4. **数据说明**：每个表格后必须有数据说明\n5. **视觉层次**：使用**粗体**突出关键信息\n\n### **专业报告结构要求**\n\n#### **标准报告模板**：\n```markdown\n# 企业经营数据分析报告\n\n## 一、执行摘要\n**关键发现**：[3-5个核心发现]\n**整体评价**：[总体表现评估]\n**紧急关注**：[需要立即关注的问题]\n\n## 二、数据可视化分析\n### 核心指标趋势图\n[图表markdown - 必须调用工具生成]\n\n**图表解读**：\n- 趋势特征：[描述]\n- 关键节点：[分析]\n- 异常情况：[说明]\n\n## 三、深度分析\n### [具体分析维度]\n[详细分析内容]\n\n## 四、数据附表\n[标准格式的数据表格]\n\n## 五、改进建议\n### 短期措施（1-3个月）\n### 中期规划（3-6个月）\n### 长期战略（6-12个月）\n```\n\n#### **图表与文字布局要求**：\n1. 图表前后各留一个空行\n2. 图表后必须有解读说明\n3. 使用合适的标题层级\n4. 保持内容逻辑清晰\n\n### **增强版报告模板**\n```markdown\n# 企业经营数据分析报告\n\n## 一、执行摘要\n\n**关键发现**：\n- 核心发现1\n- 核心发现2\n- 核心发现3\n\n**整体评价**：[总体表现评估]\n\n## 二、数据可视化分析\n\n### 2.1 核心指标趋势图\n\n[必须调用Generate Chart工具生成图表]\n\n**图表解读**：\n- **趋势特征**：[具体描述]\n- **关键节点**：[重要时间点分析]\n- **异常情况**：[问题识别]\n\n### 2.2 发展时间线\n\n```mermaid\ntimeline\n    title 业务发展历程\n\n    第一阶段 : 关键事件1\n             : 关键事件2\n\n    第二阶段 : 关键事件3\n             : 关键事件4\n```\n\n## 三、深度分析\n\n### 3.1 目标达成情况\n[具体分析内容，必须包含数据支撑]\n\n### 3.2 波动原因分析\n[深度分析，识别根本原因]\n\n### 3.3 对比分析\n\n[如有对比数据，必须调用Generate Chart工具生成对比图表]\n\n## 四、数据附表\n\n| 月份 | 目标(万元) | 实际(万元) | 达成率 | 环比变化 |\n|------|-----------|-----------|--------|----------|\n| 1月  | 10.0      | 29.3      | 293%   | -        |\n| 2月  | 10.0      | 7.9       | 78.5%  | -73.2%   |\n| 3月  | 10.0      | 21.4      | 214%   | +172.6%  |\n\n**数据说明**：金额单位为万元，达成率为实际/目标×100%\n\n## 五、业务流程分析\n\n```mermaid\nflowchart TD\n    A[数据收集] --> B[数据分析]\n    B --> C[问题识别]\n    C --> D[解决方案]\n    D --> E[执行监控]\n```\n\n## 六、改进建议\n\n### 短期措施（1-3个月）\n- **具体建议1**：[详细说明]\n- **具体建议2**：[详细说明]\n\n### 中长期规划（3-12个月）\n- **战略建议1**：[详细说明]\n- **战略建议2**：[详细说明]\n\n## 七、风险预警\n\n### 风险识别\n- 风险点1\n- 风险点2\n\n### 应对措施\n- 措施1\n- 措施2\n```\n\n**重要提醒**：\n1. **必须包含图表**：每个报告至少要有1-2个图表\n2. **必须使用Mermaid**：对于时间线、流程等，使用Mermaid语法\n3. **表格格式严格**：确保表格markdown格式正确\n4. **数据驱动**：所有分析必须基于实际数据\n\n## 最终执行检查清单\n\n在生成报告前，必须确认：\n\n### ✅ 图表生成检查\n- [ ] 识别到数值数据了吗？\n- [ ] 调用了\"Generate Chart\"工具了吗？\n- [ ] 使用了工具返回的chartMarkdown了吗？\n- [ ] 时间序列数据生成了Timeline图表吗？\n\n### ✅ Mermaid语法检查\n- [ ] 需要展示流程的地方使用了flowchart吗？\n- [ ] 需要展示时间线的地方使用了timeline吗？\n- [ ] 需要展示项目进度的地方使用了gantt吗？\n\n### ✅ 格式检查\n- [ ] 表格格式是否正确（每行独立）？\n- [ ] 标题层级是否清晰？\n- [ ] 段落格式是否规范？\n\n### ✅ 内容完整性检查\n- [ ] 包含了执行摘要吗？\n- [ ] 包含了数据可视化吗？\n- [ ] 包含了深度分析吗？\n- [ ] 包含了改进建议吗？\n\n## 强制执行规则 - 必须严格遵守\n\n### 🔥 图表生成强制规则\n1. **绝对不能跳过\"Generate Chart\"工具调用！**\n2. **必须使用工具返回的chartMarkdown！**\n3. **时间数据必须生成Timeline图表！**\n\n### 🔥 Mermaid语法强制规则\n4. **每个报告必须包含至少1个Mermaid图表！**\n5. **看到时间序列数据，必须添加Mermaid timeline！**\n6. **看到流程描述，必须添加Mermaid flowchart！**\n7. **看到项目进度，必须添加Mermaid gantt！**\n\n### 📋 Mermaid强制模板\n\n#### 必须使用的Timeline模板：\n```mermaid\ntimeline\n    title 业务发展时间线\n\n    第一阶段 : 关键事件1\n             : 关键事件2\n\n    第二阶段 : 关键事件3\n             : 关键事件4\n```\n\n#### 必须使用的流程图模板：\n```mermaid\nflowchart TD\n    A[数据分析] --> B[问题识别]\n    B --> C[解决方案]\n    C --> D[执行监控]\n```\n\n### ⚠️ 最终检查\n生成报告前必须确认：\n- [ ] 调用了Generate Chart工具？\n- [ ] 包含了至少1个Mermaid图表？\n- [ ] 时间数据生成了timeline？\n- [ ] 流程描述生成了flowchart？\n\n**如果以上任何一项为\"否\"，必须重新生成报告！**\n\n记住：**你是企业的财务顾问，必须提供专业、准确、有价值的分析和建议！每个报告都必须包含图表和Mermaid可视化内容！**\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-460, -140], "id": "957b5a35-e607-4f47-8d83-0e4739bbfd16", "name": "可视化分析报告", "onError": "continueRegularOutput"}, {"parameters": {"promptType": "define", "text": "=input data: {{ $json.query }}\n\n", "hasOutputParser": true, "options": {"systemMessage": "=### 角色设定\n你是一位企业财务数据可视化专家，精通Chart.js和企业财务分析。你专门处理企业级财务数据，包括利润表、损益表、各种贡献度分析、成本费用分析等，能够生成专业的企业级图表。\n\n**核心使命**：为企业管理层提供专业、准确、美观的财务数据可视化图表。\n\n### 企业财务数据专业处理能力\n\n#### 1. 利润表/损益表数据\n- **柱状图**：展示收入、成本、利润对比\n- **堆叠柱状图**：多期利润表对比\n- **折线图**：利润趋势分析\n\n#### 2. 贡献度分析数据\n- **饼图/环形图**：客户/产品/地区贡献度占比\n- **柱状图**：贡献度排名分析\n- **水平条形图**：贡献度对比\n\n#### 3. 成本费用分析\n- **堆叠柱状图**：成本构成分析\n- **柱状图**：成本分类对比\n- **雷达图**：多维成本对比\n\n#### 4. 财务比率分析\n- **柱状图**：关键财务指标对比\n- **散点图**：比率相关性分析\n- **折线图**：比率趋势分析\n\n#### 5. 现金流分析\n- **柱状图**：现金流构成分析\n- **折线图**：现金流趋势\n- **堆叠柱状图**：三大现金流对比\n\n### Chart.js企业级配色方案\n```javascript\n// 专业企业配色（Chart.js格式）\nconst ENTERPRISE_COLORS = {\n  primary: '#1f4e79',      // 企业蓝\n  success: '#2d7d32',      // 盈利绿\n  warning: '#f57c00',      // 警示橙\n  danger: '#c62828',       // 亏损红\n  info: '#0288d1',         // 信息蓝\n  neutral: '#616161',      // 中性灰\n  \n  // 财务专用色彩\n  revenue: '#4caf50',      // 收入绿\n  cost: '#f44336',         // 成本红\n  profit: '#2196f3',       // 利润蓝\n  expense: '#ff9800',      // 费用橙\n  asset: '#9c27b0',        // 资产紫\n  liability: '#795548',    // 负债棕\n  equity: '#607d8b'        // 权益灰蓝\n};\n```\n\n### 智能数据识别算法\n\n#### 财务科目自动识别：\n1. **收入类**：营业收入、主营业务收入、其他业务收入 → 绿色系\n2. **成本类**：营业成本、主营业务成本、其他业务成本 → 红色系\n3. **费用类**：管理费用、销售费用、财务费用 → 橙色系\n4. **利润类**：毛利润、营业利润、净利润 → 蓝色系\n\n#### 业务场景智能判断：\n1. **profit_loss** → 柱状图展示利润构成\n2. **customer_contrib** → 饼图展示客户贡献\n3. **product_contrib** → 柱状图展示产品贡献\n4. **cost_analysis** → 堆叠柱状图展示成本构成\n5. **inventory_turnover** → 折线图展示周转率趋势\n6. **financial_ratios** → 雷达图展示财务健康度\n\n### 专业Chart.js配置示例\n\n#### 利润表柱状图：\n```json\n{\n  \"output\": {\n    \"type\": \"bar\",\n    \"data\": {\n      \"labels\": [\"营业收入\", \"营业成本\", \"毛利润\", \"管理费用\", \"销售费用\", \"净利润\"],\n      \"datasets\": [{\n        \"label\": \"金额(万元)\",\n        \"data\": [100, -60, 40, -15, -10, 15],\n        \"backgroundColor\": [\n          \"#4caf50\",  // 营业收入 - 绿色\n          \"#f44336\",  // 营业成本 - 红色\n          \"#2196f3\",  // 毛利润 - 蓝色\n          \"#ff9800\",  // 管理费用 - 橙色\n          \"#ff9800\",  // 销售费用 - 橙色\n          \"#4caf50\"   // 净利润 - 绿色\n        ],\n        \"borderColor\": [\n          \"#4caf50\", \"#f44336\", \"#2196f3\", \"#ff9800\", \"#ff9800\", \"#4caf50\"\n        ],\n        \"borderWidth\": 2\n      }]\n    },\n    \"options\": {\n      \"responsive\": true,\n      \"maintainAspectRatio\": true,\n      \"aspectRatio\": 1.6,\n      \"layout\": {\n        \"padding\": {\n          \"top\": 12,\n          \"bottom\": 12,\n          \"left\": 8,\n          \"right\": 8\n        }\n      },\n      \"plugins\": {\n        \"title\": {\n          \"display\": true,\n          \"text\": \"利润表分析\",\n          \"font\": {\n            \"size\": 15,\n            \"weight\": \"bold\",\n            \"family\": \"Arial, sans-serif\"\n          },\n          \"color\": \"#1f4e79\",\n          \"padding\": {\n            \"top\": 10,\n            \"bottom\": 15\n          }\n        },\n        \"legend\": {\n          \"display\": true,\n          \"position\": \"top\",\n          \"labels\": {\n            \"padding\": 12,\n            \"usePointStyle\": true,\n            \"font\": {\n              \"size\": 12\n            },\n            \"boxWidth\": 12\n          }\n        }\n      },\n      \"scales\": {\n        \"y\": {\n          \"beginAtZero\": true,\n          \"grid\": {\n            \"color\": \"rgba(0,0,0,0.1)\"\n          },\n          \"ticks\": {\n            \"font\": {\n              \"size\": 11\n            },\n            \"maxTicksLimit\": 6,\n            \"callback\": function(value) {\n              return value + '万';\n            }\n          }\n        },\n        \"x\": {\n          \"grid\": {\n            \"display\": false\n          },\n          \"ticks\": {\n            \"font\": {\n              \"size\": 11\n            },\n            \"maxRotation\": 0,\n            \"minRotation\": 0,\n            \"maxTicksLimit\": 8\n          }\n        }\n      }\n    }\n  }\n}\n```\n\n#### 客户贡献度饼图：\n```json\n{\n  \"output\": {\n    \"type\": \"pie\",\n    \"data\": {\n      \"labels\": [\"客户A\", \"客户B\", \"客户C\", \"客户D\", \"其他\"],\n      \"datasets\": [{\n        \"label\": \"贡献度\",\n        \"data\": [35, 25, 20, 15, 5],\n        \"backgroundColor\": [\n          \"#1f4e79\",  // 企业蓝\n          \"#2d7d32\",  // 盈利绿\n          \"#f57c00\",  // 警示橙\n          \"#0288d1\",  // 信息蓝\n          \"#616161\"   // 中性灰\n        ],\n        \"borderColor\": \"#ffffff\",\n        \"borderWidth\": 2\n      }]\n    },\n    \"options\": {\n      \"responsive\": true,\n      \"plugins\": {\n        \"title\": {\n          \"display\": true,\n          \"text\": \"客户贡献度分析\",\n          \"font\": {\n            \"size\": 18,\n            \"weight\": \"bold\"\n          },\n          \"color\": \"#1f4e79\"\n        },\n        \"legend\": {\n          \"position\": \"right\"\n        }\n      }\n    }\n  }\n}\n```\n\n#### 成本构成堆叠柱状图：\n```json\n{\n  \"output\": {\n    \"type\": \"bar\",\n    \"data\": {\n      \"labels\": [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n      \"datasets\": [\n        {\n          \"label\": \"直接成本\",\n          \"data\": [300, 320, 310, 330],\n          \"backgroundColor\": \"#f44336\",\n          \"stack\": \"成本\"\n        },\n        {\n          \"label\": \"间接成本\",\n          \"data\": [100, 110, 105, 115],\n          \"backgroundColor\": \"#ff9800\",\n          \"stack\": \"成本\"\n        },\n        {\n          \"label\": \"管理费用\",\n          \"data\": [80, 85, 82, 88],\n          \"backgroundColor\": \"#ff5722\",\n          \"stack\": \"成本\"\n        }\n      ]\n    },\n    \"options\": {\n      \"responsive\": true,\n      \"plugins\": {\n        \"title\": {\n          \"display\": true,\n          \"text\": \"季度成本构成分析\",\n          \"font\": {\n            \"size\": 18,\n            \"weight\": \"bold\"\n          },\n          \"color\": \"#1f4e79\"\n        }\n      },\n      \"scales\": {\n        \"x\": {\n          \"stacked\": true\n        },\n        \"y\": {\n          \"stacked\": true,\n          \"beginAtZero\": true,\n          \"ticks\": {\n            \"callback\": function(value) {\n              return value + '万';\n            }\n          }\n        }\n      }\n    }\n  }\n}\n```\n\n### 企业级数据处理规则\n\n#### 1. 数值格式化：\n- 金额单位自动转换（元→万元→亿元）\n- 百分比格式化（保留2位小数）\n- 负数显示优化（红色标注）\n\n#### 2. 颜色语义化：\n- 收入/利润 → 绿色系（正向指标）\n- 成本/费用 → 红色/橙色系（成本指标）\n- 资产 → 蓝色系（资源指标）\n- 负债 → 棕色系（债务指标）\n\n#### 3. 图表类型智能选择：\n- 对比分析 → 柱状图\n- 占比分析 → 饼图\n- 趋势分析 → 折线图\n- 构成分析 → 堆叠图\n- 多维分析 → 雷达图\n- **时间线分析 → Timeline图表**\n- **事件序列 → 甘特图**\n- **发展历程 → 时间轴图**\n\n### 重要输出要求\n\n**你必须输出一个包含\"output\"属性的JSON对象**，该属性包含完整的Chart.js配置：\n\n```json\n{\n  \"output\": {\n    \"type\": \"图表类型\",\n    \"data\": {\n      \"labels\": [\"标签数组\"],\n      \"datasets\": [数据集配置]\n    },\n    \"options\": {\n      \"responsive\": true,\n      \"plugins\": {\n        \"title\": {\n          \"display\": true,\n          \"text\": \"图表标题\"\n        }\n      }\n    }\n  }\n}\n```\n\n### Timeline图表配置模板\n\n当需要显示时间线或发展历程时，使用以下配置：\n\n```json\n{\n  \"output\": {\n    \"type\": \"line\",\n    \"data\": {\n      \"labels\": [\"2024-01\", \"2024-02\", \"2024-03\", \"2024-04\", \"2024-05\"],\n      \"datasets\": [{\n        \"label\": \"业务发展时间线\",\n        \"data\": [100, 150, 200, 180, 250],\n        \"borderColor\": \"#1f4e79\",\n        \"backgroundColor\": \"rgba(31, 78, 121, 0.1)\",\n        \"fill\": true,\n        \"tension\": 0.4,\n        \"pointRadius\": 8,\n        \"pointHoverRadius\": 12,\n        \"pointBackgroundColor\": \"#1f4e79\",\n        \"pointBorderColor\": \"#ffffff\",\n        \"pointBorderWidth\": 3\n      }]\n    },\n    \"options\": {\n      \"responsive\": true,\n      \"maintainAspectRatio\": true,\n      \"aspectRatio\": 2,\n      \"plugins\": {\n        \"title\": {\n          \"display\": true,\n          \"text\": \"企业发展时间线\",\n          \"font\": {\n            \"size\": 16,\n            \"weight\": \"bold\"\n          },\n          \"color\": \"#1f4e79\"\n        },\n        \"legend\": {\n          \"display\": true,\n          \"position\": \"top\"\n        },\n        \"tooltip\": {\n          \"mode\": \"index\",\n          \"intersect\": false,\n          \"callbacks\": {\n            \"label\": function(context) {\n              return context.dataset.label + ': ' + context.parsed.y + '万元';\n            }\n          }\n        }\n      },\n      \"scales\": {\n        \"y\": {\n          \"beginAtZero\": true,\n          \"title\": {\n            \"display\": true,\n            \"text\": \"金额(万元)\"\n          }\n        },\n        \"x\": {\n          \"title\": {\n            \"display\": true,\n            \"text\": \"时间\"\n          }\n        }\n      },\n      \"elements\": {\n        \"point\": {\n          \"hoverRadius\": 12\n        }\n      }\n    }\n  }\n}\n```\n\n### Mermaid时间线配置\n\n对于复杂的时间线，也可以使用Mermaid语法：\n\n```mermaid\ntimeline\n    title 企业发展历程\n\n    2024年1月 : 项目启动\n              : 团队组建\n\n    2024年2月 : 产品开发\n              : 市场调研\n\n    2024年3月 : 产品发布\n              : 用户增长\n\n    2024年4月 : 业务扩展\n              : 合作伙伴\n```\n\n**记住**：你的输出必须是专业的企业级Chart.js配置，能够准确反映财务数据的业务含义，为企业决策提供有价值的可视化支持。对于时间序列数据，优先使用Timeline配置。\n\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [840, -160], "id": "24b0ce39-3975-474f-bc6f-5e1423a29b03", "name": "图表生成", "onError": "continueRegularOutput"}, {"parameters": {"assignments": {"assignments": [{"id": "7a6da40c-4e76-4476-b3ca-d915363ec1e9", "name": "=chartUrl", "value": "={{ \"https://quickchart.io/chart?width=480&height=300&devicePixelRatio=2&backgroundColor=white&format=png&c=\" + encodeURIComponent(JSON.stringify($json.output)) }}", "type": "string"}, {"id": "48473c3b-9d32-42ad-8712-b5958c0f88e2", "name": "=chartMarkdown", "value": "={{ \"![数据分析图表](https://quickchart.io/chart?width=480&height=300&devicePixelRatio=2&backgroundColor=white&format=png&c=\" + encodeURIComponent(JSON.stringify($json.output)) + \")\" }}", "type": "string"}, {"id": "2b13537f-33d5-4d2b-9e20-20683258aa50", "name": "=chartConfig", "value": "={{ $json.output }}", "type": "object"}, {"id": "bc990bc5-224a-473d-8196-cf612e385f04", "name": "=timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}, {"id": "65d8f2e4-a9bb-4cfa-bae0-31256470e3cf", "name": "=success", "value": "={{ true }}", "type": "string"}, {"id": "d7cbc540-5aaf-4307-a317-e8ee3742d417", "name": "chartType", "value": "={{ \"chartjs\" }}", "type": "string"}, {"id": "8af5959c-65fe-49bd-bdef-cea0c9440f6f", "name": "=renderOptions", "value": "={{ {\n  \"width\": 480,\n  \"height\": 300,\n  \"devicePixelRatio\": 2,\n  \"backgroundColor\": \"white\",\n  \"format\": \"png\",\n  \"service\": \"quickchart.io\"\n} }}", "type": "string"}, {"id": "da5e2ab2-aa9a-401f-8f92-63bb0799290a", "name": "=debugInfo", "value": "={{ {   \"hasOutput\": $json.output ? true : false,   \"timestamp\": new Date().toISOString() } }}", "type": "string"}, {"id": "e4f01164-5eaf-42fc-b4a4-33e4597219a8", "name": "", "value": "", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1200, -160], "id": "07060ae0-f1b2-48ce-be41-e251343509a7", "name": "设置参数-图表生成"}, {"parameters": {"method": "POST", "url": "https://mcp-on-edge.edgeone.site/mcp-server", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content_Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"jsonrpc\": \"2.0\",\n  \"id\": 1,\n  \"method\": \"tools/call\",\n  \"params\": {\n    \"name\": \"deploy-html\",\n    \"arguments\": {\n      \"value\": \"{{ JSON.stringify($node['html'].json.htmlContent).slice(1, -1) }}\"\n    }\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [240, -140], "id": "f9245461-4117-45cd-903e-b0011daba8fa", "name": "发布"}, {"parameters": {"options": {"frequencyPenalty": 0, "maxTokens": 8192, "presencePenalty": 0, "temperature": 0.7, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [-540, 60], "id": "76bb1f92-2fc2-4312-b9bb-e523c0b03410", "name": "DeepSeek Chat Model", "credentials": {"deepSeekApi": {"id": "IKqWJBBXULhRu8mZ", "name": "DeepSeek account"}}}, {"parameters": {"options": {"frequencyPenalty": 0, "maxTokens": 8192, "presencePenalty": 0, "temperature": 0.3, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [820, 20], "id": "1053b04c-de2a-4af2-a6de-e5f3d5528571", "name": "DeepSeek Chat Model1", "credentials": {"deepSeekApi": {"id": "IKqWJBBXULhRu8mZ", "name": "DeepSeek account"}}}, {"parameters": {"jsCode": "// 响应式HTML包装器 - 兼容手机和电脑\nconst inputData = $input.all();\n\nconst processedItems = inputData.map(item => {\n  const content = item.json.output || item.json.content || item.json.text || '';\n\n  // 统计内容\n  const chartCount = (content.match(/!\\[([^\\]]*)\\]\\((https:\\/\\/quickchart\\.io\\/chart[^)]+)\\)/g) || []).length;\n  const mermaidCount = (content.match(/```mermaid[\\s\\S]*?```/g) || []).length;\n\n  // 生成响应式HTML\n  const fullHtml = `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, user-scalable=yes\">\n    <title>企业经营分析报告</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/marked/marked.min.js\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js\"></script>\n    <style>\n        /* 基础样式 */\n        * { box-sizing: border-box; }\n        body {\n            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;\n            line-height: 1.6;\n            color: #333;\n            margin: 0;\n            padding: 10px;\n            background: #f5f7fa;\n            font-size: 16px;\n        }\n\n        /* 容器样式 */\n        .container {\n            max-width: 1200px;\n            margin: 0 auto;\n            background: white;\n            border-radius: 12px;\n            box-shadow: 0 4px 20px rgba(0,0,0,0.1);\n            overflow: hidden;\n        }\n\n        /* 头部样式 */\n        .header {\n            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n            color: white;\n            padding: 20px;\n            text-align: center;\n        }\n        .header h1 {\n            margin: 0;\n            font-size: 1.8rem;\n            font-weight: 700;\n        }\n        .header p {\n            margin: 8px 0 0 0;\n            opacity: 0.9;\n            font-size: 0.9rem;\n        }\n\n        /* 内容区域 */\n        .content {\n            padding: 20px;\n        }\n\n        /* 调试信息 */\n        .debug {\n            background: rgba(0,0,0,0.8);\n            color: white;\n            padding: 8px 12px;\n            border-radius: 5px;\n            font-size: 11px;\n            margin-bottom: 15px;\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n\n        /* 标题样式 */\n        h1, h2, h3, h4, h5, h6 {\n            color: #2c3e50;\n            margin: 1.5em 0 0.8em 0;\n            line-height: 1.3;\n        }\n        h1 {\n            font-size: 1.8rem;\n            border-bottom: 2px solid #3498db;\n            padding-bottom: 0.5em;\n        }\n        h2 {\n            font-size: 1.5rem;\n            border-bottom: 1px solid #bdc3c7;\n            padding-bottom: 0.3em;\n        }\n        h3 {\n            font-size: 1.3rem;\n            border-left: 4px solid #3498db;\n            padding-left: 12px;\n        }\n\n        /* 表格样式 */\n        .table-wrapper {\n            overflow-x: auto;\n            margin: 1em 0;\n            border-radius: 8px;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n        }\n        table {\n            border-collapse: collapse;\n            width: 100%;\n            min-width: 300px;\n            background: white;\n        }\n        th, td {\n            border: 1px solid #e1e8ed;\n            padding: 12px 8px;\n            text-align: left;\n            font-size: 0.9rem;\n        }\n        th {\n            background: #3498db;\n            color: white;\n            font-weight: 600;\n            position: sticky;\n            top: 0;\n        }\n        tr:nth-child(even) {\n            background: #f8f9fa;\n        }\n        tr:hover {\n            background: #e3f2fd;\n        }\n\n        /* 图片和图表样式 */\n        img {\n            max-width: 100%;\n            height: auto;\n            display: block;\n            margin: 15px auto;\n            border-radius: 8px;\n            box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n        }\n\n        /* Mermaid样式 */\n        .mermaid {\n            text-align: center;\n            margin: 20px 0;\n            background: #f8f9fa;\n            padding: 15px;\n            border-radius: 8px;\n            overflow-x: auto;\n        }\n\n        /* 代码样式 */\n        pre {\n            background: #f8f9fa;\n            padding: 15px;\n            border-radius: 8px;\n            overflow-x: auto;\n            border-left: 4px solid #3498db;\n            margin: 1em 0;\n        }\n        code {\n            background: #f1f3f4;\n            padding: 2px 6px;\n            border-radius: 4px;\n            font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;\n            font-size: 0.9em;\n        }\n\n        /* 文本样式 */\n        p {\n            margin: 1em 0;\n            line-height: 1.7;\n        }\n        strong {\n            color: #2c3e50;\n            font-weight: 600;\n        }\n        em {\n            color: #7f8c8d;\n        }\n\n        /* 列表样式 */\n        ul, ol {\n            padding-left: 1.5em;\n            margin: 1em 0;\n        }\n        li {\n            margin: 0.3em 0;\n            line-height: 1.6;\n        }\n\n        /* 引用样式 */\n        blockquote {\n            border-left: 4px solid #3498db;\n            margin: 1em 0;\n            padding: 0.5em 0 0.5em 1em;\n            background: #f8f9fa;\n            border-radius: 0 4px 4px 0;\n            color: #555;\n        }\n\n        /* 手机端优化 */\n        @media (max-width: 768px) {\n            body {\n                padding: 5px;\n                font-size: 15px;\n            }\n            .container {\n                border-radius: 8px;\n            }\n            .header {\n                padding: 15px;\n            }\n            .header h1 {\n                font-size: 1.5rem;\n            }\n            .content {\n                padding: 15px;\n            }\n            h1 {\n                font-size: 1.5rem;\n            }\n            h2 {\n                font-size: 1.3rem;\n            }\n            h3 {\n                font-size: 1.1rem;\n            }\n            th, td {\n                padding: 8px 6px;\n                font-size: 0.85rem;\n            }\n            .debug {\n                flex-direction: column;\n                align-items: flex-start;\n                gap: 5px;\n            }\n            pre {\n                padding: 10px;\n                font-size: 0.8rem;\n            }\n            code {\n                font-size: 0.8rem;\n            }\n        }\n\n        /* 超小屏幕优化 */\n        @media (max-width: 480px) {\n            body {\n                padding: 3px;\n                font-size: 14px;\n            }\n            .header {\n                padding: 12px;\n            }\n            .header h1 {\n                font-size: 1.3rem;\n            }\n            .content {\n                padding: 12px;\n            }\n            h1 {\n                font-size: 1.3rem;\n            }\n            h2 {\n                font-size: 1.1rem;\n            }\n            h3 {\n                font-size: 1rem;\n            }\n            th, td {\n                padding: 6px 4px;\n                font-size: 0.8rem;\n            }\n        }\n\n        /* 打印样式 */\n        @media print {\n            body {\n                background: white;\n                padding: 0;\n            }\n            .container {\n                box-shadow: none;\n                border-radius: 0;\n            }\n            .debug {\n                display: none;\n            }\n            .header {\n                background: #333 !important;\n                -webkit-print-color-adjust: exact;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <!-- 报告头部 -->\n        <div class=\"header\">\n            <h1>企业经营分析报告</h1>\n            <p>响应式版本 - 兼容手机和电脑</p>\n        </div>\n\n        <!-- 内容区域 -->\n        <div class=\"content\">\n            <!-- 调试信息 -->\n            <div class=\"debug\">\n                <div>📊 图表: ${chartCount} | 📈 Mermaid: ${mermaidCount}</div>\n                <div id=\"status\">加载中...</div>\n            </div>\n\n            <!-- 内容容器 -->\n            <div id=\"content-container\">\n                <!-- Markdown内容将在这里渲染 -->\n            </div>\n\n            <!-- 报告尾部 -->\n            <div style=\"margin-top: 30px; padding-top: 15px; border-top: 1px solid #e1e8ed; text-align: center; color: #6c757d; font-size: 0.9rem;\">\n                <p>📅 ${new Date().toLocaleString('zh-CN')} | 📱💻 响应式版本</p>\n            </div>\n        </div>\n    </div>\n\n    <!-- 原始Markdown内容（隐藏） -->\n    <script type=\"text/markdown\" id=\"markdown-content\">\n${content}\n    </script>\n\n    <script>\n        document.addEventListener('DOMContentLoaded', function() {\n            const status = document.getElementById('status');\n            status.textContent = '初始化中...';\n\n            // 获取Markdown内容\n            const markdownContent = document.getElementById('markdown-content').textContent;\n\n            // 渲染Markdown\n            if (typeof marked !== 'undefined') {\n                marked.setOptions({ breaks: true, gfm: true });\n\n                try {\n                    const htmlContent = marked.parse(markdownContent);\n\n                    // 处理表格包装\n                    const processedContent = htmlContent.replace(/<table>/g, '<div class=\"table-wrapper\"><table>').replace(/<\\\\/table>/g, '</table></div>');\n\n                    document.getElementById('content-container').innerHTML = processedContent;\n                    status.textContent = '✅ 已加载';\n\n                    // 图表加载监控\n                    document.querySelectorAll('img[src*=\"quickchart.io\"]').forEach((img, index) => {\n                        img.addEventListener('load', () => console.log(\\`图表 \\${index + 1} 加载成功\\`));\n                        img.addEventListener('error', () => console.log(\\`图表 \\${index + 1} 加载失败\\`));\n                    });\n\n                } catch (error) {\n                    document.getElementById('content-container').innerHTML = \\`\n                        <div style=\"background: #fee; border: 1px solid #fcc; padding: 15px; border-radius: 5px; color: #c33;\">\n                            <h3>渲染失败</h3>\n                            <p>错误: \\${error.message}</p>\n                            <details><summary>原始内容</summary><pre>\\${markdownContent}</pre></details>\n                        </div>\n                    \\`;\n                    status.textContent = '❌ 失败';\n                }\n            } else {\n                document.getElementById('content-container').innerHTML = \\`<pre>\\${markdownContent}</pre>\\`;\n                status.textContent = '⚠️ 库未加载';\n            }\n\n            // 初始化Mermaid\n            setTimeout(() => {\n                if (typeof mermaid !== 'undefined') {\n                    mermaid.initialize({ startOnLoad: true, theme: 'default', securityLevel: 'loose' });\n                    status.textContent = '🎉 完成';\n                }\n            }, 1500);\n        });\n    </script>\n</body>\n</html>`;\n\n  return {\n    json: {\n      ...item.json,\n      htmlContent: fullHtml,\n      processedAt: new Date().toISOString(),\n      version: 'minimal-wrapper-v1.0',\n      originalChartCount: chartCount,\n      originalMermaidCount: mermaidCount,\n      approach: 'client-side-rendering'\n    }\n  };\n});\n\nreturn processedItems;"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [0, -140], "id": "3264248c-c24b-4ba5-b936-8d3d4851b1ec", "name": "html"}], "connections": {"When chat message received": {"main": [[{"node": "数据库抓取数据", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "可视化分析报告", "type": "ai_memory", "index": 0}]]}, "Get many rows in NocoDB": {"ai_tool": [[{"node": "可视化分析报告", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "图表生成", "type": "main", "index": 0}]]}, "Generate Chart": {"ai_tool": [[{"node": "可视化分析报告", "type": "ai_tool", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "图表生成", "type": "ai_outputParser", "index": 0}]]}, "数据库抓取数据": {"main": [[{"node": "设置参数-数据抓取", "type": "main", "index": 0}]]}, "设置参数-数据抓取": {"main": [[{"node": "可视化分析报告", "type": "main", "index": 0}]]}, "图表生成": {"main": [[{"node": "设置参数-图表生成", "type": "main", "index": 0}]]}, "可视化分析报告": {"main": [[{"node": "html", "type": "main", "index": 0}]]}, "DeepSeek Chat Model": {"ai_languageModel": [[{"node": "可视化分析报告", "type": "ai_languageModel", "index": 0}]]}, "DeepSeek Chat Model1": {"ai_languageModel": [[{"node": "图表生成", "type": "ai_languageModel", "index": 0}]]}, "html": {"main": [[{"node": "发布", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}