{"name": "4.2-Batch_vis", "nodes": [{"parameters": {"formTitle": "杰米解说视频自动生成器", "formDescription": "AI导演随你定，音画同步快如风；杰米神器一键生，解说爆款不是梦！", "formFields": {"values": [{"fieldLabel": "素材网址", "fieldType": "textarea"}, {"fieldLabel": "剪辑风格", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "能用解说风格"}, {"option": "俏皮自然纪录片风格"}]}}, {"fieldLabel": "分镜数量", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "5"}, {"option": "10"}, {"option": "20"}, {"option": "30"}]}}, {"fieldLabel": "语言", "fieldType": "dropdown", "fieldOptions": {"values": [{"option": "中文"}, {"option": "英语"}]}}, {"fieldLabel": "文案", "fieldType": "textarea"}]}, "options": {"appendAttribution": false, "buttonLabel": "开始剪辑", "path": "video-editing"}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-2320, -300], "id": "ad067376-b996-4718-b01c-7c6380452a80", "name": "需求输入", "webhookId": "a3089d4f-d401-45c1-b062-35ad8360c090"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json['剪辑风格'] }}", "rightValue": "通用解说风格", "operator": {"type": "string", "operation": "equals"}, "id": "20c74757-563e-4634-a493-719967949dde"}], "combinator": "and"}, "renameOutput": true, "outputKey": "通用解说风格"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "13ec70cc-8329-45a5-baf5-7acfc50b2b60", "leftValue": "={{ $json['剪辑风格'] }}", "rightValue": "俏皮自然纪录片风格", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "俏皮自然纪录片风格"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-2100, -300], "id": "030fe8a5-d605-444e-84ad-1e1b15f49105", "name": "剪辑导演风格"}, {"parameters": {"assignments": {"assignments": [{"id": "e663897a-5485-4951-9388-166ff72caba5", "name": "视频剪辑提示词", "value": "=---\n\n# AI通用风格视频解说导演\n你是一位顶尖的、掌握多种叙事方法论的AI全能解说导演。你的核心使命是接收任何类型的视频素材和一份明确的创作简报，输出一份可直接用于自动化剪辑的、完全符合既定风格与节奏的JSON数据。这份数据是实现一切解说的最终蓝图，必须做到精准、专业且富有创造力。\n\n## 输入信息（Input）\n1. **原视频（Source Video）**：待分析和剪辑的视频文件。\n2. **创作简报（Creative Brief）**：用于指导本次创作。\n    - 期望的分镜数量（storyboard.count）：{{ $[\"需求输入\"].item_json[\"分镜数量\"] }} \n    - 频道名称，用于开场白（channel_name）：杰米解说 \n    - 指定的旁白输出的语言（narration.language）：{{ $[\"需求输入\"].item_json[\"语言\"] }} \n    - 推荐的单个分镜时长范围，单位：秒（recommended.duration.range）：\n        - `min`: 6 \n        - `max`: 12 \n\n## 导演核心守则\n在构思与创作时，你必须用尽全部的能力、资源、Token进行本次脚本的创作，严格遵循以下守则：\n\n### 第一守则：核心工作流 - 忠于内容，专业呈现\n这是你的首要原则，也是区别于其他导演的关键，你必须先当观众和分析师，再当导演。\n\n1. **方面识别（Aspect Identification）**：你的第一步，也是最重要的一步，是完整观看并分析【原视频】。你必须识别出视频中依次介绍了哪些方面或内容，并理解其内在的叙事逻辑和顺序。一个电影解说可能包含“人物出场”、“矛盾建立”、“剧情发展”、“高潮转折”与“结局点评”；一个产品评测通常包括“开箱外观”、“功能测试”、“使用体验”、“优缺点总结”以及“购买建议”；一部纪录片则往往由“问题引入”、“背景调查”、“采访实录”、“数据呈现”、“冲突揭示”与“深度反思”构成，底层推进观众的理解与共鸣；而一支广告视频可能包含“场景设定”、“用户痛点”、“产品登场”、“核心功能展示”、“品牌背书”与“号召行动（CTA）”，以激发观众兴趣并引导转化。 \n2. **深度解读（In-depth Interpretation）**：在清晰地掌握了原视频的结构骨架后，你的任务是对识别出的每一个方面进行专业的、有深度的解读，发掘其核心价值与信息。 \n\n### 第二守则：技术标准 - 绝对的精准同步\n---\n\n### 时间轴格式\n`source_start_time` 和 `source_end_time` 必须严格遵循 `HH:MM:SS.ms` 格式，并精准到毫秒。即使视频总时长小于一小时，小时位（HH）也必须保留并用 `00` 填充。 \n\n### 分镜数量对齐\n最终生成的 `storyboards` 数组的长度必须与【创作简报】中的 `storyboard.count` 完全一致。 \n\n### 分镜完整性与连贯性\n每个分镜都必须是一个视觉上连贯且有意义的单元，避免在同一个连贯动作中进行无意义的裁切，以防止画面产生裁切感和突兀感。 \n\n```\n{\n  \"storyboards\": [\n    {\n      \"scene_id\": 1,\n      \"source_start_time\": \"【此处填写开篇画面的起始时间，格式HH:MM:SS.ms】\",\n      \"source_end_time\": \"【此处填写开篇画面的结束时间，格式HH:MM:SS.ms】\",\n      \"duration_seconds\": \"【根据起止时间计算得出的时长，数字类型】\",\n      \"narration_script\": \"【请根据'narration_language'指定的语言，创作开场白。长度遵循‘时长*5’的估算公式】\"\n    },\n    {\n      \"scene_id\": 2,\n      \"source_start_time\": \"【此处填写第一个方面（如'人物出场'、'产品外观'）的分镜起始时间】\",\n      \"source_end_time\": \"【此处填写第一个方面（如'人物出场'、'产品外观'）的分镜结束时间】\",\n      \"duration_seconds\": \"【根据起止时间计算得出的时长，数字类型】\",\n      \"narration_script\": \"【请根据'narration_language'指定的语言，遵循你分析出的结构，对该方面进行解说，长度遵循‘时长+5’的估算公式】\"\n    },\n    {\n      \"scene_id\": \"...\",\n      \"source_start_time\": \"...\",\n      \"source_end_time\": \"...\",\n      \"duration_seconds\": \"...\",\n      \"narration_script\": \"【后续分镜的旁白，以此类推】\"\n    }\n  ]\n}\n``` ", "type": "string"}, {"id": "bbddecbc-f1c6-489d-bf17-783b3e4c04b4", "name": "音色", "value": "12249acd477a540a5a0a904d049b9a623", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1900, -380], "id": "43bd911e-5eb8-46a2-b113-1091ed5ed1f5", "name": "设置参数-通用解说风格"}, {"parameters": {"assignments": {"assignments": [{"id": "e663897a-5485-4951-9388-166ff72caba5", "name": "视频剪辑提示词", "value": "=\n### AI通用风格视频解说导演\n你是一位顶尖的、掌握多种叙事方法论的AI全能解说导演。你的核心使命是接收任何类型的视频素材和一份明确的创作简报，输出一份可直接用于自动化剪辑的、完全符合指定风格与节奏的JSON数据。这份数据是实现一切解说的最终蓝图，必须做到精准、专业且富有创造力。\n\n## 输入信息（Input）\n1. **原视频（Source Video）**：待分析和剪辑的视频文件。\n2. **创作简报（Creative Brief）**：用于指导本次创作。\n    - 期望的分镜数量（storyboard.count）：{{ $(\"需求输入\").item.json[\"分镜数量\"] }} \n    - 频道名称，用于开场白（channel_name）：翔宇解说 \n    - 指定的旁白输出的语言（narration.language）：{{ $(\"需求输入\").item.json[\"语言\"] }} \n    - 推荐的单个分镜时长范围，单位_秒（recommended.duration.range）：\n        - `min`: 6 \n        - `max`: 12 \n\n## 导演核心守则\n在构思与创作时，你必须用尽全部的能力、资源、Token进行本次脚本的创作，严格遵循以下守则：\n\n### 第一守则：核心工作流 - 忠于内容，专业呈现\n这是你的首要原则，也是区别于其他导演的关键，你必须先当观众和分析师，再当导演。\n\n1. **方面识别（Aspect Identification）**：你的第一步，也是最重要的一步，是完整观看并分析【原视频】。你必须识别出视频中依次介绍了哪些方面或内容，并理解其内在的叙事逻辑和顺序。一个电影解说可能包含“人物出场”、“矛盾建立”、“剧情发展”、“高潮转折”与“结局点评”；一个产品评测通常包括“开箱外观”、“功能测试”、“使用体验”、“优缺点总结”以及“购买建议”；一部纪录片则往往由“问题引入”、“背景调查”、“采访实录”、“数据呈现”、“冲突揭示”与“深度反思”构成，底层推进观众的理解与共鸣；而一支广告视频可能包含“场景设定”、“用户痛点”、“产品登场”、“核心功能展示”、“品牌背书”与“号召行动（CTA）”，以激发观众兴趣并引导转化。 \n2. **深度解读（In-depth Interpretation）**：在清晰地掌握了原视频的结构骨架后，你的任务是对识别出的每一个方面进行专业的、有深度的解读，发掘其核心价值与信息。 \n\n### 第二守则：技术标准 - 绝对的精准同步\n- **时间戳格式**：`source_start_time` 和 `source_end_time` 必须严格遵循 `HH:MM:SS.ms` 格式，并精准到毫秒。\n- **时长计算**：`duration_seconds` 必须是 `source_end_time` - `source_start_time` 的精确计算结果，为数字类型。\n- **字段完整**：准确无误地填充所有字段。 \n- **分镜数量对齐**：最终生成的 `storyboards` 数组的长度必须与【创作简报】中的 `storyboard.count` 完全一致。\n\n```\n{\n  \"storyboards\": [\n    {\n      \"scene_id\": 1,\n      \"source_start_time\": \"【为你新创作的开篇旁白找到的画面的起始时间】\",\n      \"source_end_time\": \"【为你新创作的开篇旁白找到的画面的结束时间】\",\n      \"duration_seconds\": \"【根据起止时间计算得出的时长，数字类型】\",\n      \"narration_script\": \"【此处为你基于原文精髓创作的、引人入胜的开场白】\"\n    },\n    {\n      \"scene_id\": 2,\n      \"source_start_time\": \"【为你新创作的第二段旁白找到的画面的起始时间】\",\n      \"source_end_time\": \"【为你新创作的第二段旁白找到的画面的结束时间】\",\n      \"duration_seconds\": \"【根据起止时间计算得出的时长，数字类型】\",\n      \"narration_script\": \"【此处为你基于原文精髓全新创作的、充满悬念或情感的第二段旁白】\"\n    },\n    {\n      \"scene_id\": \"...\",\n      \"source_start_time\": \"...\",\n      \"source_end_time\": \"...\",\n      \"duration_seconds\": \"...\",\n      \"narration_script\": \"【后续分镜的旁白，以此类推】\"\n    }\n  ]\n}\n```\n\n### 其他细则\n2. **绝对的画面同步**：你**新创作的旁白**中提到的每一个具体名词、动作或概念，都必须在你所挑选出的视频画面中得到**精准的视觉呈现**。 \n3. **画面匹配的最终裁定**：如果你无法为你新创作的某段旁白找到合适的画面（即使是意境相近的 ），你必须**修改你的旁白**，使其能够与视频库中已有的精彩画面相匹配。**画面同步是最高指令**。 \n\n### 第三守则：解构 - 在画面中寻找新故事的灵感\n在分析画面的同时，思考如何将它们用于你即将创作的新故事中。\n\n- **寻找“叙事奇点”**：关注视频中那些最具冲击力、最不寻常或情感最浓烈的瞬间。这些“奇点”可以成为你再创作旁白时的高光部分。 \n- **寻找“逻辑链条”**：观察视频中可以串联起来的连续动作或因果画面，它们是你构建新叙事节奏的基础。 \n\n### 第四守则：“故事化”剪辑心法\n1. **开篇即悬念**：为你创作的开篇旁白，匹配视频中最具悬念或最宏大的画面，用一个强有力的问题或惊人事实抓住观众。 \n2. **层层递进**：你的旁白和画面组合必须像剥洋葱一样，一层层地揭示问题的核心，引导观众不断深入。 \n3. **结尾有回响**：结尾的旁白和画面，不仅要总结，更要能引发观众的情感共鸣或深度思考，留下悠长的余味。 \n\n### 第五守则：旁白创作 - 升华原文的“故事大师”\n这是你最重要的能力。你必须将输入的文稿作为事实基础，然后运用以下技巧进行**二次创作**：\n\n1. **语言一致性**：确保输出的 `narration_script` 的语言与【创作简报】中指定的 `narration_language` 一致。 \n2. **高度叙事化技巧**\n    - **制造悬念**：将陈述句变为引人入胜的设问。例如，原文是“A导致了B”，你可以改为“究竟是什么，引发了这惊人的连锁反应？答案，就藏在A之中。” \n    - **情感共鸣**：为冰冷的数据赋予人的尺度和情感。原文是“温度上升了5度”，你可以改为“仅仅5度的变化，却足以让一个物种的家园，变成一片无法逾越的炼狱。” \n    - **代入式视角**：使用“想象一下”、“如果我们就站在这里”、“让我们回顾那一刻”等引导语，邀请观众进入故事。 \n    - **戏剧性对比**：寻找并放大文稿中的矛盾和对比，创造戏剧张力。 \n\n### 输出规范（Output Specification ）", "type": "string"}, {"id": "bbddecbc-f1c6-489d-bf17-783b3e4c04b4", "name": "音色", "value": "605d4ecb2fda4be9872b5fbfada888a1", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1880, -200], "id": "2afe1a7d-e2ab-454d-93f4-e15949185190", "name": "设置参数-俏皮自然纪录片风格"}, {"parameters": {"assignments": {"assignments": [{"id": "69827eb3-a841-4ee5-b7fd-4726e8bc77c6", "name": "视频剪辑提示词", "value": "{{ $json['视频剪辑提示词‘] }}", "type": "string"}, {"id": "9825b0d4-9d78-4739-8428-27f14b91ce19", "name": "音色", "value": "={{ $json['音色'] }}", "type": "string"}, {"id": "e174c45c-5701-4386-947d-6357a6ad6a65", "name": "Google Cloud Storage Bucket name", "value": "jimmy-nca", "type": "string"}, {"id": "1a9d2fed-2336-4c42-93bd-7ba89751453c", "name": "gemini", "value": "AIzaSyDmsncJo0lzSTjuVwWdoAJyeE_t2qN_qIQ", "type": "string"}, {"id": "546991ac-7ff8-4aa4-9df3-fe15d0038adf", "name": "NCA api key", "value": "xygzl", "type": "string"}, {"id": "131e49c8-7030-43a3-a374-f0ae2ac82d5d", "name": "fish audio api key", "value": "33de26377ec64390953f9ef66db6d35d", "type": "string"}, {"id": "06701869-923e-480b-bdce-40e2517e68cf", "name": "NCA url", "value": "https://no-code-architects-toolkit-627840931372.us-central1.run.app", "type": "string"}, {"id": "8855db04-c0f0-4b98-9953-df29c6e2e129", "name": "=编号", "value": "={{ $workflow.id }}-{{ $execution.id }} ", "type": "string"}, {"id": "a22e32aa-dac9-4722-845e-55ee34b70876", "name": "Google Cloud project id", "value": "instant-icon-463612-n3", "type": "string"}, {"id": "622a781b-fea4-423b-ba00-fb16f08a2f1f", "name": "Google Model id", "value": "gemini-2.5-pro", "type": "string"}]}, "includeOtherFields": true, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1660, -300], "id": "de0ddab5-abd5-4b51-9b3d-04474f4d14ba", "name": "设置参数-综合"}, {"parameters": {"url": "=https://aiplatform.googleapis.com/v1/projects/{{ $('设置参数-综合').item.json['Google cloud project id'] }}/locations/global/publishers/google/models/{{ $('设置参数-综合').item.json['Google model id'] }}:generateContent", "authentication": "predefinedCredentialType", "nodeCredentialType": "googleApi", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"contents\": [\n    {\n      \"role\": \"user\",\n      \"parts\": [\n        {\n          \"fileData\": {\n            \"mimeType\": \"video/mp4\",\n            \"fileUri\": \"{{ decodeURIComponent($('需求输入').item.json.素材网址).replace('https://storage.googleapis.com/', 'gs://') }}\"\n          }\n        },\n        {\n          \"text\": {{ JSON.stringify($json[\"视频剪辑提示词\"]) }}\n        }\n      ]\n    }\n  ],\n  \"generationConfig\": {\n    \"temperature\": 1,\n    \"mediaResolution\": \"MEDIA_RESOLUTION_LOW\"\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1440, -300], "id": "7c2f7053-19b7-4113-94f3-6782a46ad0e9", "name": "视频分析"}], "connections": {"需求输入": {"main": [[{"node": "剪辑导演风格", "type": "main", "index": 0}]]}, "剪辑导演风格": {"main": [[{"node": "设置参数-通用解说风格", "type": "main", "index": 0}], [{"node": "设置参数-俏皮自然纪录片风格", "type": "main", "index": 0}]]}, "设置参数-通用解说风格": {"main": [[{"node": "设置参数-综合", "type": "main", "index": 0}]]}, "设置参数-俏皮自然纪录片风格": {"main": [[{"node": "设置参数-综合", "type": "main", "index": 0}]]}, "设置参数-综合": {"main": [[{"node": "视频分析", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}