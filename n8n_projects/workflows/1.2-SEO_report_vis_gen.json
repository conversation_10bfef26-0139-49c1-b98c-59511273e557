{"name": "1.2-SEO_report_vis_gen", "nodes": [{"parameters": {"assignments": {"assignments": [{"id": "d701c3e2-c6a3-4a91-9f67-4f76d22c6eea", "name": "可视化内容", "value": "={{ $json['可视化内容'] }}", "type": "string"}, {"id": "aeb721fd-c6b2-4126-ab4b-5ab3f4df78d0", "name": "分支", "value": "表格", "type": "string"}, {"id": "4d50cdd8-8725-45f0-b29f-40d90f7ccddf", "name": "完整 SEO 报告网址", "value": "", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1640, 40], "id": "6bbc2243-7e2e-430d-89d5-2ac546911f11", "name": "设置参数-表格"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.2, "position": [-1420, 140], "id": "44c16e78-f586-4736-8619-671ab7f66d5c", "name": "聚合分支"}, {"parameters": {"assignments": {"assignments": [{"id": "d701c3e2-c6a3-4a91-9f67-4f76d22c6eea", "name": "可视化内容", "value": "={{ $json['可视化内容'] }}", "type": "string"}, {"id": "aeb721fd-c6b2-4126-ab4b-5ab3f4df78d0", "name": "分支", "value": "工作流", "type": "string"}, {"id": "ed01f691-f80f-4451-ae7c-0c4b2b3542b5", "name": "完整 SEO 报告网址", "value": "=完整 SEO 报告链接：{{ $json['完成 SEO 报告网址'] }} 请将些链接显示在可视化网页的页眉区域，确保链接地址准确无误且可正常访问。", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1640, 240], "id": "c03a0e46-1153-45b5-a6f6-09cc5cf4e193", "name": "设置参数-子工作流"}, {"parameters": {"assignments": {"assignments": [{"id": "d701c3e2-c6a3-4a91-9f67-4f76d22c6eea", "name": "可视化", "value": "={{ $json['可视化内容'] }}", "type": "string"}, {"id": "aeb721fd-c6b2-4126-ab4b-5ab3f4df78d0", "name": "分支", "value": "={{ $json[\"分支\"] }}", "type": "string"}, {"id": "0253fed5-354e-421a-879d-411383f079d4", "name": "完整的 SEO 报告网址", "value": "={{ $json['完整 SEO 报告网址'] }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1200, 140], "id": "4fd1efdd-eed7-4ead-8344-c58096100b71", "name": "设置参数-综合"}, {"parameters": {"formTitle": "<PERSON>工作流可视化报告生成器", "formDescription": "看得见的报告，看得透的洞察。", "formFields": {"values": [{"fieldLabel": "可视化内容", "fieldType": "textarea", "requiredField": true}]}, "options": {"appendAttribution": false, "buttonLabel": "开始生成", "path": "report-visualization"}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-1860, 40], "id": "838ba413-a6bc-45f5-b8f6-c47d066f7457", "name": "可视化提交", "webhookId": "acf86b48-0b71-4f9c-a57e-ba58169fdfea"}, {"parameters": {"workflowInputs": {"values": [{"name": "可视化内容"}, {"name": "完成 SEO 报告网址"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-1860, 240], "id": "ccb53074-6a7a-44f1-bc62-53a75dff9077", "name": "报告生成"}, {"parameters": {"promptType": "define", "text": "={{ $json['可视化'] }}", "options": {"systemMessage": "角色：长文报告信息提取与总结专家\n\n你是一位专业的信息提取和内容总结专家，具备深厚的长篇文档分析、精准语义理解和信息浓缩能力。你擅长快速解析复杂长文报告的结构、主题和关键内容，并通过系统化的分析精准提炼核心信息，包括所有关键术语、数据和结论。你的特长还包括对报告内容的深度挖掘，能够发现隐藏在文本深处的关联性和隐含价值，提供深入且富有洞察力的内容解读。\n\n目标\n\n你的任务是对所提供的长篇文档进行系统的深入分析和梳理，撰写一份长度约3000字的高度结构化的专业总结。该总结需要涵盖报告的所有核心要点、关键数据、术语定义和重要结论，确保在语言精炼的同时信息全面且逻辑清晰。生成的总结不仅要精准提炼原文信息，更要体现报告的核心逻辑框架，为读者快速获取关键信息提供便利，也为后续的大型分析和可视化提供高质量的文本基础。\n\n任务具体要求\n\n1. 深入分析长篇报告\n\n• 仔细阅读并快速把握报告整体结构、主要章节、核心主题和子主题。\n\n• 明确识别报告的核心论点、关键数据点、图表与统计信息。\n\n2. 综合精炼与结构化提炼\n\n• 提取所有关键术语，定义和核心观点，确保无遗漏地覆盖所有关键词和关键信息。\n\n• 梳理总结报告的论证逻辑，包括论点的提出、论据的支撑以及结论的推导过程。\n\n• 按照逻辑顺序组织信息，形成逻辑严谨、结构清晰、层次分明的文本架构。\n\n3. 撰写高度信息密度的专业总结\n\n• 将梳理的信息凝练成约3000字的专业总结。\n\n• 确保总结的内容详实，建议使用清晰的Markdown格式（包括标题、子标题、列表、加粗等）突出重点内容。\n\n• 明确划分内容层次，避免专业术语有误解可保留原文并在其后方括号中提供通俗的中文翻译，确保信息准确传达 。"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-980, 140], "id": "7f483e78-2bd6-4659-8593-3eb3ce1d51e1", "name": "信息提取"}, {"parameters": {"promptType": "define", "text": "=请根据以下分析报告内容进行可视化设计：\n<seo-report>\n{{ $json.output }}\n/<seo-report>", "options": {"systemMessage": "=角色：网页设计师和前端开发专家\n\n你是一名专业的网页设计师和前端开发专家，对现代 web 设计趋势和最佳实践有深入理解，尤其擅长创造具有极高审美价值的分析洞察和数据驱动的报告可视化网页。你的设计作品不仅功能完备，而且视觉表现令人惊艳，能够直观清晰地展现复杂分析内容，给用户带来强烈的“Aha - moment”体验。\n\n任务\n\n请根据稍后提供的内容，设计一个美观、现代、易读、专业的分析报告“中文”可视化网页。请充分发挥你的专业判断，选择最能体现报告分析与洞察精髓的设计风格、配色方案、排版和布局。网页应按照报告的各个部分依次呈现，确保内容逻辑清晰，层次分明，让用户能够按照自然阅读顺序轻松理解报告中的每个章节和要点。\n\n设计目标\n\n• 视觉与可读性：创造视觉上令人印象深刻且文本清晰易读的网页，能够立即吸引用户注意力并提供舒适的阅读体验，激发用户深入了解报告中的分析结果和洞察。\n\n• 信息传达：用一种既美观又高效的方式呈现报告各个维度的深入分析。\n\n• 用户感知：通过设计传递清晰性、可信度、专业性的数据与分析，帮助用户快速把握报告内容，并基于报告进一步提升。\n\n设计指导\n\n• 整体风格：采用 Bento Grid 风格的视觉设计，或者其他你认为合适的现代 web 设计风格。目标是创造一个既有丰富信息量，又能清晰展示报告数据和洞察的页面，就像一份精心设计的交互式商业智能报告。\n\n• Hero 模块：设计一个引人注目的 Hero 模块。它可以包含报告标题、作者、摘要，以及一张高质量的背景图片或插图。\n\n• 排版：\n\n◦ 精心选择字体组合（衬线和无衬线），以提升中文阅读体验。\n\n◦ 强调超大字体或数字突出核心要点，画面中可以有超大视觉元素强调重点，与小元素的比例形成反差。\n\n◦ 可以考虑使用一些精致的排版细节（如下沉字、悬挂标点）来提升整体质感。\n\n◦ Font - Awesome 中有很多图标，选合适的点缀增加趣味性和信息指引性。\n\n◦ 页面宽度：桌面端最大宽度控制在 1280px，确保在大屏幕上有良好的阅读体验。\n\n◦ 移动端采用全宽设计，但保持适当的左右边距（16x）。\n\n◦ 确保关键内容在不同设备上都能保持良好的可读性和视觉平衡。\n\n• 配色方案：\n\n◦ 选择一套低饱和度、视觉舒适的配色方案。\n\n◦ 考虑使用高对比度的颜色突出重要信息。\n\n◦ 确保可视化设计既美观又有洞察性，帮助用户更直观地理解整体框架。\n\n◦ 可以考虑使用层级与关系类、聚类与主题分析、分布与对比类、时间与趋势类等合适的展示方式。\n\n◦ 注意专业术语或专有名词可保留英文原文并在其后的括号中提供准确的中文翻译，确保术语表达既专业又易于理解。\n\n◦ 禁止使用 Mermaid 生成交互式图表。\n\n技术规范\n\n• 实现完整的响应式，必须在所有设备上（手机、平板、桌面）完美展示。\n\n• 使用 HTML5、Font Awesome、Tailwind CSS 和必要的 JavaScript。\n\n◦ Font Awesome：[https://lf6 - cdn - tos. bytedn. com/cdn/expire - 100 - M/font - awesome/6.0.0/css/all.min.css](https://lf6 - cdn - tos. bytedn. com/cdn/expire - 100 - M/font - awesome/6.0.0/css/all.min.css)、[https://lf6 - cdn - tos. bytedn. com/cdn/expire - 100 - N/font - awesome/6.0.0/css/all.min.css](https://lf6 - cdn - tos. bytedn. com/cdn/expire - 100 - N/font - awesome/6.0.0/css/all.min.css)\n\n◦ Tailwind CSS：[https://lf3 - cdn - tos. bytedn. com/cdn/expire - 1 - M/tailwindcss/2.2.19/tailwind.min.css](https://lf3 - cdn - tos. bytedn. com/cdn/expire - 1 - M/tailwindcss/2.2.19/tailwind.min.css)、[https://lf3 - cdn - tos. bytedn. com/cdn/expire - 1 - M/tailwindcss/2.2.19/tailwind.min.css](https://lf3 - cdn - tos. bytedn. com/cdn/expire - 1 - M/tailwindcss/2.2.19/tailwind.min.css)\n\n◦ 非中文字体：[https://fonts.googleapis.com/css2?family = Noto + Serif + SC:wght@300;400;500;700&display = swap](https://fonts.googleapis.com/css2?family = Noto + Serif + SC:wght@300;400;500;700&display = swap)、[https://fonts.googleapis.com/css2?family = Noto + Sans + SC:wght@300;400;500;700&display = swap](https://fonts.googleapis.com/css2?family = Noto + Sans + SC:wght@300;400;500;700&display = swap)\n\n◦ fallback - family：Tahoma, Arial, Roboto, \"Droid Sans\", \"Helvetica Neue\", \"Droid Sans Fallback\", \"Heiti SC\", \"Hiragino Sans GB\", Simsun, sans - serif;\n\n• 在生成的 HTML 网页中，可根据需要引用翔宇工作流作者头像/Logo：https://xiangyugongzuoliu - 1309206323. cos. ap - beijing. myqcloud. com/2025 - 05 - 14 - logo. jpg\n\n• 禁止使用任何外部图片，改用 Font Awesome 图标或纯 CSS 绘制图片占位。\n\n额外加分项\n\n• 微交互：添加微妙而有意义的微交互效果来提升用户体验（例如，按钮悬停效果、卡片悬停效果、页面滚动效果）。\n\n• 作者信息：本报告由翔宇制作，采用翔宇工作流设计的 n8n 自动化工作流生成，报告分析、识别机遇、创造价值！欢迎访问翔宇工作流 https://xiangyugongzuoliu. com/ 专注于 AI 与自动化的分享与实践。\n\n◦ 网页页脚：© 2025 翔宇工作流 | 专注于 AI 与自动化技术的分享与实践！\n\n◦ 图表可链接到翔宇工作流作者的 YouTube 频道：https://www.youtube.com/\n\n（注：实际使用时，需补充具体要基于设计的报告内容相关信息 ）"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-600, 140], "id": "8d728d31-0103-47ae-9f9e-08886153434c", "name": "HTML 生成"}, {"parameters": {"assignments": {"assignments": [{"id": "6b1e4312-a97a-4d39-b52f-0293a61c5f47", "name": "HTML", "value": "={{ $json.output.replace(/[\\s\\S]*<visual_report>/, '').replace(/<\\visual_report>[\\s\\S]*/, '') }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-240, 140], "id": "5b3fe5fb-92ca-4227-a68d-f993b84545c1", "name": "设置参数-提取HTML"}, {"parameters": {"method": "POST", "url": "https://mcp-on-edge.edgeone.site/mcp-server", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content_Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"jsonrpc\": \"2.0\",\n  \"id\": 1,\n  \"method\": \"tools/call\",\n  \"params\": {\n    \"name\": \"deploy-html\",\n    \"arguments\": {\n      \"value\": \"{{ JSON.stringify($node['设置参数-提取HTML'].json.HTML).slice(1, -1) }}\"\n    }\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-20, 140], "id": "d4793364-ebc1-45c8-b2d1-b751e6ca3e15", "name": "发布"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "73120df7-eb9b-4347-8abe-5de12a4b2970", "leftValue": "={{ $('设置参数-综合').first().json['分支'] }}", "rightValue": "表格", "operator": {"type": "string", "operation": "contains"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [220, 140], "id": "eaeb3ac5-26e7-4894-a11d-8c0d34760e12", "name": "If"}, {"parameters": {"operation": "completion", "respondWith": "redirect", "redirectUrl": "={{ $json.result.content[0].text }}", "limitWaitTime": true, "resumeUnit": "minutes"}, "type": "n8n-nodes-base.form", "typeVersion": 1, "position": [440, 60], "id": "ee9368f0-2f2f-477e-b3c8-069543cb9814", "name": "返回结果", "webhookId": "e732a888-9c8e-4327-8af4-00de159c8074"}, {"parameters": {"fieldsToAggregate": {"fieldToAggregate": [{}]}, "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [660, 140], "id": "c7ad1543-9364-465e-ad4a-a48ae7fb2be5", "name": "聚合条目"}, {"parameters": {"assignments": {"assignments": [{"id": "270eb53a-711b-4ac4-be55-2dd4a92cb97d", "name": "可视化网址", "value": "={{ $('发布').item.json.result.content[0].text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [880, 140], "id": "188ae808-5404-4b8f-a4f3-e95f06a0a8d5", "name": "设置参数-汇总"}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {"maxOutputTokens": 2048, "temperature": 0.4, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-900, 440], "id": "7bb8ac46-c9fd-4ae9-965d-24c72947330f", "name": "Google Gemini Chat Model2", "credentials": {"googlePalmApi": {"id": "cdwHRnakv6TIFeEn", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"modelName": "models/gemini-2.5-flash-preview-04-17", "options": {"maxOutputTokens": 2048, "temperature": 0.4, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-280, 400], "id": "029bbd06-4a4e-425f-bc03-f6ef10e387d7", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "cdwHRnakv6TIFeEn", "name": "Google Gemini(PaLM) Api account"}}}], "connections": {"设置参数-表格": {"main": [[{"node": "聚合分支", "type": "main", "index": 0}]]}, "聚合分支": {"main": [[{"node": "设置参数-综合", "type": "main", "index": 0}]]}, "设置参数-子工作流": {"main": [[{"node": "聚合分支", "type": "main", "index": 1}]]}, "可视化提交": {"main": [[{"node": "设置参数-表格", "type": "main", "index": 0}]]}, "报告生成": {"main": [[{"node": "设置参数-子工作流", "type": "main", "index": 0}]]}, "设置参数-综合": {"main": [[{"node": "信息提取", "type": "main", "index": 0}]]}, "信息提取": {"main": [[{"node": "HTML 生成", "type": "main", "index": 0}]]}, "HTML 生成": {"main": [[{"node": "设置参数-提取HTML", "type": "main", "index": 0}]]}, "设置参数-提取HTML": {"main": [[{"node": "发布", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "返回结果", "type": "main", "index": 0}], [{"node": "聚合条目", "type": "main", "index": 0}]]}, "返回结果": {"main": [[{"node": "聚合条目", "type": "main", "index": 0}]]}, "聚合条目": {"main": [[{"node": "设置参数-汇总", "type": "main", "index": 0}]]}, "发布": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Google Gemini Chat Model2": {"ai_languageModel": [[{"node": "信息提取", "type": "ai_languageModel", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "HTML 生成", "type": "ai_languageModel", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}