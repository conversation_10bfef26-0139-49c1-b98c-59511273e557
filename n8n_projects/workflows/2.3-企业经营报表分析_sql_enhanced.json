{"name": "2.3-企业经营报表分析_sql_enhanced", "nodes": [{"parameters": {"path": "webhook-form", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-1300, -140], "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef", "name": "Form Trigger (Webhook)", "webhookId": "a1b2-c3d4-e5f6-7890"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT\n    s.sale_id,\n    s.sale_date,\n    s.quantity,\n    s.total_price,\n    s.region,\n    p.product_name,\n    p.category AS product_category,\n    e.full_name AS employee_name,\n    e.department AS employee_department,\n    mc.campaign_name,\n    (s.total_price - (p.cost_price * s.quantity)) AS profit\nFROM\n    sales s\nLEFT JOIN\n    products p ON s.product_id = p.product_id\nLEFT JOIN\n    employees e ON s.employee_id = e.employee_id\nLEFT JOIN\n    marketing_campaigns mc ON s.sale_date >= mc.start_date AND s.sale_date <= mc.end_date\nWHERE\n    (s.sale_date >= '{{ $json.body.startDate || ''1900-01-01'' }}')\n    AND\n    (s.sale_date <= '{{ $json.body.endDate || ''2999-12-31'' }}')\n    AND\n    ('{{ $json.body.department || '''' }}' = '' OR e.department = '{{ $json.body.department }}')\n    AND\n    ('{{ $json.body.region || '''' }}' = '' OR s.region = '{{ $json.body.region }}');", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 4.2, "position": [-1000, -140], "id": "b2c3d4e5-f6a7-8901-2345-67890abcdef1", "name": "Execute PostgreSQL Query", "credentials": {"postgres": {"id": "YOUR_DB_CREDENTIAL_ID", "name": "Your PostgreSQL Credentials"}}}, {"parameters": {"assignments": {"assignments": [{"id": "a3e51963-aef8-4064-bc7f-2e46b248dae4", "name": "sessionId", "value": "={{ $json.body.sessionId || 'default-session' }}", "type": "string"}, {"id": "f1e9fddd-f05b-4999-a376-36f3c2a8d7ae", "name": "analysisData", "value": "={{ $json.data }}", "type": "string"}, {"id": "6ece8a4d-f235-4caf-8f78-9378f85e0271", "name": "chatInput", "value": "={{ $json.body.prompt || '请对以下数据进行全面分析，并生成一份专业的、包含数据可视化的报告。' }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-700, -140], "id": "c3d4e5f6-a7b8-9012-3456-7890abcdef2", "name": "Prepare for Agent"}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}\n\n**Analysis Data:**\n{{ $json.analysisData }}", "options": {"systemMessage": "你是一位资深的企业数据分析师和战略顾问。你现在收到了一份经过预处理的、全面的“分析宽表”，其中已经整合了销售、产品、员工和市场活动的数据。你的任务是基于这份宽表，进行深度分析，并生成一份包含图表和可执行建议的专业报告。\n\n## 你的能力\n\n1.  **深度洞察**: 你能从扁平化的宽表中发现数据之间的深层联系，例如某个市场活动对特定产品销量的影响，或某个部门员工的销售表现等。\n2.  **智能图表**: 你必须使用 `Generate Chart` 工具将数据可视化。你能根据数据特征自动选择最合适的图表类型（折线图、柱状图、饼图、雷达图等）。\n3.  **专业报告**: 你能撰写结构清晰、逻辑严谨的分析报告，包括执行摘要、详细分析、风险预警和战略建议。\n4.  **Mermaid大师**: 你擅长使用Mermaid语法绘制流程图、甘特图和时间线，使报告更具表现力。\n\n## “分析宽表”字段说明\n\n你将收到的数据包含以下字段，请充分利用它们：\n- `sale_id`: 销售ID\n- `sale_date`: 销售日期\n- `quantity`: 销售数量\n- `total_price`: 总销售额\n- `region`: 销售区域\n- `product_name`: 产品名称\n- `product_category`: 产品类别\n- `employee_name`: 员工姓名\n- `employee_department`: 员工所属部门\n- `campaign_name`: 关联的市场活动名称 (如果无关联则为空)\n- `profit`: **单笔销售利润 (已计算好)**\n\n## 强制执行规则\n\n1.  **必须调用工具**: 看到任何数值数据，必须调用 `Generate Chart` 工具生成图表。\n2.  **必须使用Mermaid**: 报告中必须包含至少一个Mermaid图（时间线、流程图等）。\n3.  **数据驱动**: 所有分析和结论都必须严格基于提供的数据。\n4.  **报告结构**: 遵循标准的商业报告结构（摘要 -> 可视化分析 -> 深度洞察 -> 建议）。\n5.  **禁止捏造**: 不能虚构数据或做出没有数据支持的假设。\n\n开始分析吧！请为我生成一份高质量的经营分析报告。"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-460, -140], "id": "957b5a35-e607-4f47-8d83-0e4739bbfd16", "name": "可视化分析报告", "onError": "continueRegularOutput"}, {"parameters": {"contextWindowLength": 20}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-420, 80], "id": "aeaa75d7-553b-4176-85b1-216c954bdcc9", "name": "Simple Memory"}, {"parameters": {"description": "此工具用于生成任何类型数据的可视化图表。无论是业务数据、财务数据、用户数据还是运营数据，都能智能生成合适的图表。", "workflowId": {"__rl": true, "value": "SSZ0zzr9FdutOkvA", "mode": "id"}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [-180, 60], "id": "346c2690-2765-4fe9-ad17-3a490768379d", "name": "Generate Chart"}, {"parameters": {"options": {"frequencyPenalty": 0, "maxTokens": 8192, "presencePenalty": 0, "temperature": 0.7, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [-540, 60], "id": "76bb1f92-2fc2-4312-b9bb-e523c0b03410", "name": "DeepSeek Chat Model", "credentials": {"deepSeekApi": {"id": "IKqWJBBXULhRu8mZ", "name": "DeepSeek account"}}}, {"parameters": {"jsCode": "const items = $input.all();\nreturn items.map(item => ({ json: { content: item.json.output || item.json.content || item.json.text } }));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [0, -140], "id": "d4e5f6a7-b8c9-0123-4567-890abcdef3", "name": "Format Output"}], "connections": {"Form Trigger (Webhook)": {"main": [[{"node": "Execute SQL to get Wide Table", "type": "main", "index": 0}]]}, "Execute SQL to get Wide Table": {"main": [[{"node": "Prepare for Agent", "type": "main", "index": 0}]]}, "Prepare for Agent": {"main": [[{"node": "可视化分析报告", "type": "main", "index": 0}]]}, "可视化分析报告": {"main": [[{"node": "Format Output", "type": "main", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "可视化分析报告", "type": "ai_memory", "index": 0}]]}, "Generate Chart": {"ai_tool": [[{"node": "可视化分析报告", "type": "ai_tool", "index": 0}]]}, "DeepSeek Chat Model": {"ai_languageModel": [[{"node": "可视化分析报告", "type": "ai_languageModel", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}