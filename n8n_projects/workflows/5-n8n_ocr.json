{"name": "5-n8n_ocr", "nodes": [{"parameters": {"httpMethod": "POST", "path": "convert-image-to-excel", "responseMode": "responseNode", "options": {"allowedOrigins": "*"}}, "id": "dc059710-ba26-4509-8fb0-d9d518909cac", "name": "Image Upload Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-1860, 20], "webhookId": "b2a13ea1-7927-4031-8e70-a17bad0ea5e7"}, {"parameters": {"values": {"string": [{"name": "image_data", "value": "={{ $json.body.image_data }}"}, {"name": "filename", "value": "={{ $json.body.filename || 'uploaded_image.jpg' }}"}, {"name": "output_filename", "value": "={{ $json.body.output_filename || 'converted_data.xlsx' }}"}]}, "options": {}}, "id": "c57937c8-b753-4d7e-b79b-1bf16df96b61", "name": "Set Image Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [-1640, 20]}, {"parameters": {"jsCode": "// Save base64 image data to temporary file\nconst fs = require('fs');\nconst path = require('path');\nconst os = require('os');\n\nconst imageData = $json.image_data;\nconst filename = $json.filename;\n\n// Create temp directory if it doesn't exist\nconst tempDir = path.join(os.tmpdir(), 'n8n_ocr');\nif (!fs.existsSync(tempDir)) {\n  fs.mkdirSync(tempDir, { recursive: true });\n}\n\n// Generate unique filename\nconst timestamp = Date.now();\nconst tempImagePath = path.join(tempDir, `${timestamp}_${filename}`);\n\n// Remove data URL prefix if present\nlet base64Data = imageData;\nif (imageData.startsWith('data:')) {\n  base64Data = imageData.split(',')[1];\n}\n\n// Save image file\nfs.writeFileSync(tempImagePath, base64Data, 'base64');\n\nreturn [{\n  json: {\n    temp_image_path: tempImagePath,\n    filename: filename,\n    output_filename: $json.output_filename,\n    temp_dir: tempDir\n  }\n}];"}, "id": "918caccc-79e1-44dd-b18b-f25d196e10c8", "name": "Save Temp Image", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [-1420, 20]}, {"parameters": {"command": "python3 /path/to/image_to_excel_converter.py \"{{ $json.temp_image_path }}\" -o \"{{ $json.temp_dir }}/{{ $json.output_filename }}\""}, "id": "6b4c2b69-fdda-4b3e-b94d-8be9f334e1ec", "name": "Run OCR Conversion", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-1200, 20]}, {"parameters": {"conditions": {"number": [{"value1": "={{ $json.exitCode }}", "operation": "equal"}]}}, "id": "6d2a8710-998d-4588-8deb-bf0181d362bc", "name": "Check Conversion Success", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-980, 20]}, {"parameters": {"jsCode": "// Read the generated Excel file and convert to base64\nconst fs = require('fs');\nconst path = require('path');\n\nconst tempDir = $('Save Temp Image').first().json.temp_dir;\nconst outputFilename = $('Save Temp Image').first().json.output_filename;\nconst excelFilePath = path.join(tempDir, outputFilename);\n\nif (fs.existsSync(excelFilePath)) {\n  // Read Excel file as base64\n  const excelData = fs.readFileSync(excelFilePath, 'base64');\n  \n  // Clean up temp files\n  const tempImagePath = $('Save Temp Image').first().json.temp_image_path;\n  try {\n    fs.unlinkSync(tempImagePath);\n    fs.unlinkSync(excelFilePath);\n  } catch (e) {\n    console.log('Cleanup warning:', e.message);\n  }\n  \n  return [{\n    json: {\n      success: true,\n      excel_data: excelData,\n      filename: outputFilename,\n      message: 'Image successfully converted to Excel',\n      conversion_log: $json.stdout\n    }\n  }];\n} else {\n  return [{\n    json: {\n      success: false,\n      error: 'Excel file was not created',\n      conversion_log: $json.stdout,\n      error_log: $json.stderr\n    }\n  }];\n}"}, "id": "99603390-953a-4f68-bca9-ee691f398680", "name": "Prepare Excel Response", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [-760, -80]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"{{ $json.message }}\",\n  \"excel_file\": {\n    \"filename\": \"{{ $json.filename }}\",\n    \"data\": \"{{ $json.excel_data }}\",\n    \"encoding\": \"base64\"\n  },\n  \"conversion_details\": {\n    \"original_filename\": \"{{ $('Save Temp Image').first().json.filename }}\",\n    \"processed_at\": \"{{ new Date().toISOString() }}\"\n  }\n}", "options": {"responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}, {"name": "Access-Control-Allow-Origin", "value": "*"}]}}}, "id": "8d951121-e2c9-460d-9f7d-682b67fe1f81", "name": "Response Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [-540, -80]}, {"parameters": {"jsCode": "// Clean up temp files on error\nconst fs = require('fs');\n\nconst tempImagePath = $('Save Temp Image').first().json.temp_image_path;\ntry {\n  if (fs.existsSync(tempImagePath)) {\n    fs.unlinkSync(tempImagePath);\n  }\n} catch (e) {\n  console.log('Cleanup warning:', e.message);\n}\n\nreturn [{\n  json: {\n    success: false,\n    error: 'OCR conversion failed',\n    details: {\n      exit_code: $json.exitCode,\n      stdout: $json.stdout,\n      stderr: $json.stderr\n    }\n  }\n}];"}, "id": "4716d5f9-dcd9-44c6-be83-52096ca103e4", "name": "Prepare Error Response", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [-760, 120]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"error\": \"{{ $json.error }}\",\n  \"details\": {{ JSON.stringify($json.details) }}\n}", "options": {"responseCode": 500, "responseHeaders": {"entries": [{"name": "Content-Type", "value": "application/json"}, {"name": "Access-Control-Allow-Origin", "value": "*"}]}}}, "id": "e92be55e-e8bf-458a-8301-1656f4e1786e", "name": "Response Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [-540, 120]}, {"parameters": {"path": "ocr-status", "responseMode": "responseNode", "options": {}}, "id": "352ca3db-5c1c-480d-a0c9-42a207427498", "name": "Status Check Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-1860, 320], "webhookId": "ac6b8bf3-4841-40f2-9022-491ef8c66449"}, {"parameters": {"command": "python3 -c \"import pytesseract; print('Tesseract version:', pytesseract.get_tesseract_version())\""}, "id": "5380e2ca-e4f5-4735-b636-13e76d5feb92", "name": "Check Tesseract Status", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [-1640, 320]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"service\": \"Image to Excel OCR Converter\",\n  \"status\": \"{{ $json.exitCode === 0 ? 'healthy' : 'error' }}\",\n  \"tesseract_info\": \"{{ $json.stdout }}\",\n  \"timestamp\": \"{{ new Date().toISOString() }}\",\n  \"endpoints\": {\n    \"convert\": \"POST /webhook/convert-image-to-excel\",\n    \"status\": \"GET /webhook/ocr-status\"\n  },\n  \"usage\": {\n    \"convert_endpoint\": \"Send POST request with JSON body containing 'image_data' (base64) and optional 'filename', 'output_filename'\",\n    \"response_format\": \"Returns JSON with success status and base64 encoded Excel file\"\n  }\n}", "options": {}}, "id": "29347a18-07fe-462b-8349-154156f6ccc7", "name": "Response Status", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [-1420, 320]}], "connections": {"Image Upload Webhook": {"main": [[{"node": "Set Image Data", "type": "main", "index": 0}]]}, "Set Image Data": {"main": [[{"node": "Save Temp Image", "type": "main", "index": 0}]]}, "Save Temp Image": {"main": [[{"node": "Run OCR Conversion", "type": "main", "index": 0}]]}, "Run OCR Conversion": {"main": [[{"node": "Check Conversion Success", "type": "main", "index": 0}]]}, "Check Conversion Success": {"main": [[{"node": "Prepare Excel Response", "type": "main", "index": 0}], [{"node": "Prepare Error Response", "type": "main", "index": 0}]]}, "Prepare Excel Response": {"main": [[{"node": "Response Success", "type": "main", "index": 0}]]}, "Prepare Error Response": {"main": [[{"node": "Response Error", "type": "main", "index": 0}]]}, "Status Check Webhook": {"main": [[{"node": "Check Tesseract Status", "type": "main", "index": 0}]]}, "Check Tesseract Status": {"main": [[{"node": "Response Status", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}