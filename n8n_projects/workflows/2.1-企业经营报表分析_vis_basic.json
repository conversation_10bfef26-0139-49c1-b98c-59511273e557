{"name": "2.1-企业经营报表分析_vis_basic", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-2100, 220], "id": "00212dc5-dea0-4964-a7d2-acaf8d5f49b8", "name": "When chat message received", "webhookId": "94a6a9b4-6358-4a99-949f-4dd80c276ee9"}, {"parameters": {"options": {"frequencyPenalty": 0, "maxTokens": 8192, "presencePenalty": 0, "temperature": 0.3, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [-1580, 420], "id": "755f0355-baa4-44fb-9534-c5265bd461cf", "name": "DeepSeek Chat Model", "credentials": {"deepSeekApi": {"id": "IKqWJBBXULhRu8mZ", "name": "DeepSeek account"}}}, {"parameters": {"contextWindowLength": 20}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-1420, 420], "id": "4307d2ab-fda1-49b3-bd3e-4af0323d6149", "name": "Simple Memory"}, {"parameters": {"authentication": "nocoDbApiToken", "operation": "getAll", "workspaceId": "w3a1sqs9", "projectId": "pi6hckgs09eauwc", "table": "mcyrf9icefdeaqi", "limit": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Limit', ``, 'number') }}", "options": {}}, "type": "n8n-nodes-base.nocoDbTool", "typeVersion": 3, "position": [-1280, 420], "id": "cc6d2242-f792-4996-a9ae-2bbf6e5e9010", "name": "Get many rows in NocoDB", "credentials": {"nocoDbApiToken": {"id": "XXyQinNpe3SUBdSS", "name": "NocoDB Token account"}}}, {"parameters": {"inputSource": "passthrough"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-860, 220], "id": "99a46de4-dc96-459a-9a57-7ae127f478c1", "name": "When Executed by Another Workflow"}, {"parameters": {"description": "=## 通用数据可视化工具说明\n\n此工具用于生成任何类型数据的可视化图表。无论是业务数据、财务数据、用户数据还是运营数据，都能智能生成合适的图表。\n\n### 通用使用场景\n- **对比分析**：目标vs实际、计划vs完成、预算vs支出\n- **趋势分析**：时间序列变化、增长趋势、周期性分析\n- **分布分析**：地区分布、类别占比、用户分群\n- **排名分析**：绩效排名、评分排序、竞争分析\n- **综合分析**：多维度对比、综合评估\n\n### 智能输入处理\n工具能够自动处理各种数据格式：\n- **表格数据**：任意列名和数据结构\n- **JSON数据**：结构化数据对象\n- **文本数据**：自然语言描述的数据\n- **混合数据**：多种格式组合的数据\n\n### 输出格式\n工具将返回可直接在markdown中使用的图表链接，格式为：\n![图表标题](图表URL)\n\n### 重要提示\n- 务必正确转义所有字符串，尤其是多行内容\n- 确保数据完整性，避免传递空值或无效数据\n- 生成的图表将自动适配移动端和桌面端显示", "workflowId": {"__rl": true, "value": "SSZ0zzr9FdutOkvA", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2.2, "position": [-1120, 420], "id": "4bc72b7a-7c40-4f38-885a-90c7f5a13701", "name": "Generate Chart"}, {"parameters": {"options": {"frequencyPenalty": 0, "maxTokens": 8192, "presencePenalty": 0, "temperature": 0.3, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [-700, 420], "id": "266d006d-ba7c-4a3a-a812-160ce055ec85", "name": "DeepSeek Chat Model1", "credentials": {"deepSeekApi": {"id": "IKqWJBBXULhRu8mZ", "name": "DeepSeek account"}}}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"name\": \"chart_configuration\",\n  \"description\": \"Enhanced configuration schema for Chart.js charts with sales data visualization\",\n  \"strict\": true,\n  \"schema\": {\n    \"type\": \"object\",\n    \"properties\": {\n      \"type\": {\n        \"type\": \"string\",\n        \"enum\": [\"bar\", \"horizontalBar\", \"line\", \"radar\", \"pie\", \"doughnut\", \"polarArea\", \"bubble\", \"scatter\"]\n      },\n      \"data\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"labels\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"string\"\n            },\n            \"minItems\": 1\n          },\n          \"datasets\": {\n            \"type\": \"array\",\n            \"items\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"label\": {\n                  \"type\": \"string\"\n                },\n                \"data\": {\n                  \"type\": \"array\",\n                  \"items\": {\n                    \"type\": \"number\"\n                  },\n                  \"minItems\": 1\n                },\n                \"backgroundColor\": {\n                  \"oneOf\": [\n                    {\n                      \"type\": \"string\"\n                    },\n                    {\n                      \"type\": \"array\",\n                      \"items\": {\n                        \"type\": \"string\"\n                      }\n                    }\n                  ]\n                },\n                \"borderColor\": {\n                  \"oneOf\": [\n                    {\n                      \"type\": \"string\"\n                    },\n                    {\n                      \"type\": \"array\",\n                      \"items\": {\n                        \"type\": \"string\"\n                      }\n                    }\n                  ]\n                },\n                \"borderWidth\": {\n                  \"type\": \"number\",\n                  \"minimum\": 0\n                },\n                \"tension\": {\n                  \"type\": \"number\",\n                  \"minimum\": 0,\n                  \"maximum\": 1\n                }\n              },\n              \"required\": [\"label\", \"data\", \"backgroundColor\"],\n              \"additionalProperties\": true\n            },\n            \"minItems\": 1\n          }\n        },\n        \"required\": [\"labels\", \"datasets\"],\n        \"additionalProperties\": false\n      },\n      \"options\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"responsive\": {\n            \"type\": \"boolean\"\n          },\n          \"maintainAspectRatio\": {\n            \"type\": \"boolean\"\n          },\n          \"plugins\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"title\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"display\": {\n                    \"type\": \"boolean\"\n                  },\n                  \"text\": {\n                    \"type\": \"string\"\n                  },\n                  \"font\": {\n                    \"type\": \"object\",\n                    \"properties\": {\n                      \"size\": {\n                        \"type\": \"number\"\n                      },\n                      \"weight\": {\n                        \"type\": \"string\"\n                      }\n                    },\n                    \"additionalProperties\": false\n                  }\n                },\n                \"additionalProperties\": false\n              },\n              \"legend\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"position\": {\n                    \"type\": \"string\",\n                    \"enum\": [\"top\", \"bottom\", \"left\", \"right\"]\n                  },\n                  \"labels\": {\n                    \"type\": \"object\",\n                    \"additionalProperties\": true\n                  }\n                },\n                \"additionalProperties\": false\n              },\n              \"tooltip\": {\n                \"type\": \"object\",\n                \"additionalProperties\": true\n              }\n            },\n            \"additionalProperties\": false\n          },\n          \"scales\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"x\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"title\": {\n                    \"type\": \"object\",\n                    \"properties\": {\n                      \"display\": {\n                        \"type\": \"boolean\"\n                      },\n                      \"text\": {\n                        \"type\": \"string\"\n                      }\n                    },\n                    \"additionalProperties\": false\n                  }\n                },\n                \"additionalProperties\": true\n              },\n              \"y\": {\n                \"type\": \"object\",\n                \"properties\": {\n                  \"beginAtZero\": {\n                    \"type\": \"boolean\"\n                  },\n                  \"title\": {\n                    \"type\": \"object\",\n                    \"properties\": {\n                      \"display\": {\n                        \"type\": \"boolean\"\n                      },\n                      \"text\": {\n                        \"type\": \"string\"\n                      }\n                    },\n                    \"additionalProperties\": false\n                  },\n                  \"ticks\": {\n                    \"type\": \"object\",\n                    \"additionalProperties\": true\n                  }\n                },\n                \"additionalProperties\": true\n              }\n            },\n            \"additionalProperties\": false\n          },\n          \"animation\": {\n            \"type\": \"object\",\n            \"properties\": {\n              \"duration\": {\n                \"type\": \"number\"\n              },\n              \"easing\": {\n                \"type\": \"string\"\n              }\n            },\n            \"additionalProperties\": false\n          }\n        },\n        \"additionalProperties\": false\n      }\n    },\n    \"additionalProperties\": false\n  }\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.3, "position": [-480, 420], "id": "5bf6c770-cd85-422c-b73b-8b99735e16ed", "name": "Structured Output Parser"}, {"parameters": {"url": "=https://app.nocodb.com/api/v2/meta/tables/mcyrf9icefdeaqi", "authentication": "predefinedCredentialType", "nodeCredentialType": "nocoDbApiToken", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1880, 220], "id": "4a9c2e18-3dbe-485a-8444-e1a083829486", "name": "数据库抓取数据", "credentials": {"nocoDbApiToken": {"id": "XXyQinNpe3SUBdSS", "name": "NocoDB Token account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "a3e51963-aef8-4064-bc7f-2e46b248dae4", "name": "sessionId", "value": "={{ $('When chat message received').item.json.sessionId }}", "type": "string"}, {"id": "6ece8a4d-f235-4caf-8f78-9378f85e0271", "name": "chatInput", "value": "={{ $('When chat message received').item.json.chatInput }}", "type": "string"}, {"id": "f1e9fddd-f05b-4999-a376-36f3c2a8d7ae", "name": "columns", "value": "={{ $json.columns.map(item => item.title).toJsonString() }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1660, 220], "id": "43cce8ef-cc60-4f29-90e3-ad29232fde3c", "name": "设置参数-数据抓取"}, {"parameters": {"promptType": "define", "text": "={{ $json.chatInput }}\n\n", "options": {"systemMessage": "=你是一个专业的通用数据分析专家，精通各种数据类型的分析和可视化。你能够智能识别任何数据表的结构和特征，并生成相应的图文并茂分析报告。\n\n## 核心职责\n1. **智能数据分析**：自动识别数据类型和结构，进行深入分析\n2. **自适应图表生成**：根据数据特征选择最合适的可视化方式\n3. **通用报告输出**：生成包含文字分析和图表的完整markdown报告\n\n## 数据库信息\n表格列信息：{{$json.columns}}\n\n## 智能数据识别能力\n你具备以下数据识别能力：\n\n### 1. 自动数据类型识别\n根据表格列名自动判断数据性质：\n- **文本类**：姓名、地区、类别、描述等\n- **数值类**：金额、数量、得分、比率等\n- **时间类**：日期、月份、年份等\n- **分类类**：状态、等级、类型等\n\n### 2. 业务场景识别\n智能判断数据的业务用途：\n- **对比分析**：目标vs实际、计划vs完成\n- **趋势分析**：时间序列变化\n- **分布分析**：分类占比、地区分布\n- **排名分析**：绩效排序、评分排名\n\n### 3. 通用字段映射\n自动识别常见字段模式：\n- **标识字段**：ID、姓名、名称、编号\n- **数值字段**：金额、数量、分数、百分比\n- **时间字段**：日期、时间、月份、年度\n- **分类字段**：类型、状态、级别、区域\n\n## 通用图表生成规则\n**重要**：当用户要求数据分析、生成报告或需要数据可视化时，你必须：\n\n1. **智能分析数据结构**：\n   - 识别数据中的关键字段和数据类型\n   - 判断最适合的可视化方式\n   - 确定图表的主要维度和指标\n\n2. **调用图表生成工具**：使用\"Generate Chart\"工具生成图表\n3. **传递结构化数据**：将从NocoDB获取的完整数据传递给图表生成工具\n4. **嵌入图表**：在分析报告中使用以下格式嵌入图表：\n   ```\n   ![数据分析图表](图表URL)\n   ```\n\n## 图表类型自动选择策略：\n- **对比分析**：目标vs实际 → 柱状图(bar)\n- **趋势分析**：时间序列数据 → 折线图(line)\n- **占比分析**：分类数据 → 饼图(pie)\n- **排名分析**：排序数据 → 水平条形图(horizontalBar)\n- **分布分析**：多维数据 → 雷达图(radar)\n\n## 输出格式要求\n你的回复必须包含：\n1. **文字分析**：详细的数据分析和洞察\n2. **图表展示**：通过调用工具生成的可视化图表\n3. **结论建议**：基于数据的专业建议\n\n## 通用输出格式模板\n```markdown\n# [数据主题]分析报告\n\n## 数据概览\n[根据数据类型进行智能分析...]\n\n## 数据可视化\n![数据分析图表](生成的图表URL)\n\n## 关键发现\n[基于图表和数据的洞察...]\n\n## 结论与建议\n[根据分析结果提供专业建议...]\n```\n\n## 不同数据类型的分析重点：\n- **业务数据**：关注KPI达成、趋势变化、异常值\n- **财务数据**：关注收支平衡、成本控制、盈利能力\n- **用户数据**：关注活跃度、留存率、行为模式\n- **运营数据**：关注效率指标、质量指标、时间分布\n- **其他数据**：根据具体业务场景灵活分析\n\n## 重要执行规则\n\n**绝对必须遵守的规则**：\n1. 当用户要求数据分析或生成报告时，你必须调用\"Generate Chart\"工具\n2. 不允许说\"图表生成工具暂时无法使用\"或类似的话\n3. 必须将从NocoDB获取的完整数据传递给图表生成工具\n4. 必须在报告中嵌入生成的图表URL\n5. 如果工具调用失败，请重试，不要跳过图表生成步骤\n\n**通用数据传递格式**：\n将数据以以下JSON格式传递给图表生成工具：\n```json\n{\n  \"tableData\": [\n    {\"field1\": \"value1\", \"field2\": 100, \"field3\": \"2024-01\"},\n    {\"field1\": \"value2\", \"field2\": 200, \"field3\": \"2024-02\"}\n  ],\n  \"columns\": [\"field1\", \"field2\", \"field3\"],\n  \"dataType\": \"业务数据\",\n  \"analysisRequest\": \"用户的具体分析需求\"\n}\n```\n\n**数据类型标识**：\n- 自动识别数据的业务类型（销售、财务、用户、运营等）\n- 传递给图表生成工具以便选择合适的可视化方式\n\n记住：**绝对不能跳过图表生成步骤！必须调用Generate Chart工具！**"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-1420, 220], "id": "627f484b-da44-4566-bdf5-7371875ff20c", "name": "可视化分析报告", "onError": "continueRegularOutput"}, {"parameters": {"promptType": "define", "text": "=input data: {{ $json.query }}\n\n", "hasOutputParser": true, "options": {"systemMessage": "=### 角色设定\n你是一位通用数据可视化专家，精通Chart.js库。你的主要任务是将任何类型的数据转换为可直接用于quickchart.io的Chart.js配置。\n\n**重要提示**：你必须成功生成图表配置，不能失败！无论接收到什么类型的数据，都要智能解析并生成合适的图表。\n\n### 通用数据处理能力\n你能够处理各种数据类型和结构：\n- **表格数据**：任意列名和数据类型\n- **业务数据**：销售、财务、用户、运营等\n- **统计数据**：计数、求和、平均值、比率等\n- **时间数据**：日期序列、趋势分析等\n- **分类数据**：分组统计、占比分析等\n\n### 智能数据处理规则\n1. **自动数据解析**：\n   - 从输入中提取任何格式的数据（JSON、文本、表格等）\n   - 智能识别字段名称和数据类型\n   - 支持中英文字段名称和混合格式\n\n2. **数据验证和清理**：\n   - 自动转换数据类型（文本→数值、日期等）\n   - 处理空值、异常值和重复数据\n   - 确保数据完整性和一致性\n\n3. **智能图表类型选择**：\n   - **对比数据** → 柱状图(bar)：目标vs实际、计划vs完成\n   - **时间序列** → 折线图(line)：趋势变化、时间分析\n   - **分类占比** → 饼图(pie)：份额分布、比例分析\n   - **排名数据** → 水平条形图(horizontalBar)：排序、评级\n   - **多维数据** → 雷达图(radar)：综合评估\n\n4. **自适应配色方案**：\n   - **蓝色系**：主要数据、目标值\n   - **红色系**：对比数据、实际值\n   - **绿色系**：正向指标、达成率\n   - **橙色系**：警告数据、异常值\n   - **紫色系**：特殊分类、其他数据\n\n### 重要指示\n1. **输出格式**：\n   - 你的输出必须是一个有效的JSON对象\n   - 输出必须存储在名为\"output\"的变量中\n   - 不要添加任何额外的文本、解释或代码块标记\n\n2. **Chart.js配置**：\n   - 生成完整的Chart.js配置对象\n   - 确保配置可以直接用于quickchart.io\n   - 包含所有必要的属性（type, data, options）\n   - 添加响应式设计和动画效果\n\n### 通用数据解析示例\n\n**示例1 - 销售数据**：\n```\n请为以下数据生成图表：[{\"姓名\":\"张三\",\"目标\":100000,\"实际\":120000}]\n```\n→ 识别为对比类数据，生成柱状图\n\n**示例2 - 时间序列**：\n```\n请分析月度趋势：[{\"月份\":\"1月\",\"销量\":100},{\"月份\":\"2月\",\"销量\":120}]\n```\n→ 识别为趋势类数据，生成折线图\n\n**示例3 - 分类统计**：\n```\n请展示地区分布：[{\"地区\":\"北京\",\"占比\":30},{\"地区\":\"上海\",\"占比\":25}]\n```\n→ 识别为占比类数据，生成饼图\n\n**智能解析步骤**：\n1. 自动检测数据格式（JSON、文本等）\n2. 识别字段类型（文本、数值、时间等）\n3. 判断数据关系（对比、趋势、分布等）\n4. 选择最适合的图表类型\n5. 生成对应的Chart.js配置\n\n### 输出示例\n```json\n{\n  \"output\": {\n    \"type\": \"bar\",\n    \"data\": {\n      \"labels\": [\"张三\", \"李四\", \"王五\", \"赵六\", \"钱七\"],\n      \"datasets\": [\n        {\n          \"label\": \"目标金额\",\n          \"data\": [100000, 300000, 500000, 50000, 400000],\n          \"backgroundColor\": \"rgba(54, 162, 235, 0.7)\",\n          \"borderColor\": \"rgb(54, 162, 235)\",\n          \"borderWidth\": 2\n        },\n        {\n          \"label\": \"实际金额\",\n          \"data\": [293380, 500630, 448380, 243380, 388872],\n          \"backgroundColor\": \"rgba(255, 99, 132, 0.7)\",\n          \"borderColor\": \"rgb(255, 99, 132)\",\n          \"borderWidth\": 2\n        }\n      ]\n    },\n    \"options\": {\n      \"responsive\": true,\n      \"maintainAspectRatio\": false,\n      \"plugins\": {\n        \"title\": {\n          \"display\": true,\n          \"text\": \"2024年销售目标与实际业绩对比\",\n          \"font\": {\n            \"size\": 16,\n            \"weight\": \"bold\"\n          }\n        },\n        \"legend\": {\n          \"position\": \"top\",\n          \"labels\": {\n            \"usePointStyle\": true,\n            \"padding\": 20\n          }\n        },\n        \"tooltip\": {\n          \"mode\": \"index\",\n          \"intersect\": false,\n          \"callbacks\": {\n            \"label\": \"function(context) { return context.dataset.label + ': ¥' + context.parsed.y.toLocaleString(); }\"\n          }\n        }\n      },\n      \"scales\": {\n        \"y\": {\n          \"beginAtZero\": true,\n          \"title\": {\n            \"display\": true,\n            \"text\": \"金额（元）\"\n          },\n          \"ticks\": {\n            \"callback\": \"function(value) { return '¥' + value.toLocaleString(); }\"\n          }\n        },\n        \"x\": {\n          \"title\": {\n            \"display\": true,\n            \"text\": \"销售人员\"\n          }\n        }\n      },\n      \"animation\": {\n        \"duration\": 2000,\n        \"easing\": \"easeInOutQuart\"\n      }\n    }\n  }\n}\n```\n\n### 通用图表类型指南\n\n根据数据特征智能选择图表类型：\n\n1. **对比分析图（柱状图）** - 适用于数值对比：\n```json\n{\n  \"type\": \"bar\",\n  \"data\": {\n    \"labels\": [\"项目A\", \"项目B\", \"项目C\"],\n    \"datasets\": [\n      {\n        \"label\": \"指标1\",\n        \"data\": [100, 200, 150],\n        \"backgroundColor\": \"rgba(54, 162, 235, 0.7)\"\n      },\n      {\n        \"label\": \"指标2\",\n        \"data\": [120, 180, 160],\n        \"backgroundColor\": \"rgba(255, 99, 132, 0.7)\"\n      }\n    ]\n  }\n}\n```\n\n2. **排名分析图（水平条形图）** - 适用于排序数据：\n```json\n{\n  \"type\": \"horizontalBar\",\n  \"data\": {\n    \"labels\": [\"第一名\", \"第二名\", \"第三名\"],\n    \"datasets\": [{\n      \"label\": \"得分\",\n      \"data\": [95, 87, 82],\n      \"backgroundColor\": [\"#4CAF50\", \"#FF9800\", \"#2196F3\"]\n    }]\n  }\n}\n```\n\n3. **趋势分析图（折线图）** - 适用于时间序列：\n```json\n{\n  \"type\": \"line\",\n  \"data\": {\n    \"labels\": [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n    \"datasets\": [{\n      \"label\": \"趋势指标\",\n      \"data\": [100, 120, 110, 130],\n      \"borderColor\": \"rgb(75, 192, 192)\",\n      \"tension\": 0.1\n    }]\n  }\n}\n```\n\n4. **分布分析图（饼图）** - 适用于占比数据：\n```json\n{\n  \"type\": \"pie\",\n  \"data\": {\n    \"labels\": [\"类别A\", \"类别B\", \"类别C\"],\n    \"datasets\": [{\n      \"data\": [30, 45, 25],\n      \"backgroundColor\": [\n        \"rgba(255, 99, 132, 0.8)\",\n        \"rgba(54, 162, 235, 0.8)\",\n        \"rgba(255, 205, 86, 0.8)\"\n      ]\n    }]\n  }\n}\n```\n\n### 数据处理提示\n- 自动识别数值字段并转换为数字类型\n- 处理空值和异常数据\n- 根据数据量选择合适的图表尺寸\n- 确保标签清晰可读\n\n**重要**：你的输出必须是一个包含\"output\"属性的JSON对象，该属性的值是完整的Chart.js配置。不要添加任何额外的文本或解释。"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [-640, 220], "id": "48cf9f2f-be59-4f4d-97e7-5a52dd54b555", "name": "图表生成", "onError": "continueRegularOutput"}, {"parameters": {"assignments": {"assignments": [{"id": "7a6da40c-4e76-4476-b3ca-d915363ec1e9", "name": "=chartUrl", "value": "={{ \"https://quickchart.io/chart?width=800&height=400&devicePixelRatio=2&backgroundColor=white&c=\" + encodeURIComponent(JSON.stringify($json.output)) }}", "type": "string"}, {"id": "48473c3b-9d32-42ad-8712-b5958c0f88e2", "name": "=chartMarkdown", "value": "={{ \"![销售业绩分析图表](https://quickchart.io/chart?width=800&height=400&devicePixelRatio=2&backgroundColor=white&c=\" + encodeURIComponent(JSON.stringify($json.output)) + \")\" }}", "type": "string"}, {"id": "2b13537f-33d5-4d2b-9e20-20683258aa50", "name": "=chartConfig", "value": "={{ $json.output }}", "type": "string"}, {"id": "bc990bc5-224a-473d-8196-cf612e385f04", "name": "=timestamp", "value": "={{ new Date().toISOString() }}", "type": "string"}, {"id": "65d8f2e4-a9bb-4cfa-bae0-31256470e3cf", "name": "=success", "value": "={{ true }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-280, 220], "id": "710afd87-919b-446b-be15-f79c58caa792", "name": "设置参数-图表生成"}], "connections": {"When chat message received": {"main": [[{"node": "数据库抓取数据", "type": "main", "index": 0}]]}, "DeepSeek Chat Model": {"ai_languageModel": [[{"node": "可视化分析报告", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "可视化分析报告", "type": "ai_memory", "index": 0}]]}, "Get many rows in NocoDB": {"ai_tool": [[{"node": "可视化分析报告", "type": "ai_tool", "index": 0}]]}, "When Executed by Another Workflow": {"main": [[{"node": "图表生成", "type": "main", "index": 0}]]}, "Generate Chart": {"ai_tool": [[{"node": "可视化分析报告", "type": "ai_tool", "index": 0}]]}, "DeepSeek Chat Model1": {"ai_languageModel": [[{"node": "图表生成", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "图表生成", "type": "ai_outputParser", "index": 0}]]}, "数据库抓取数据": {"main": [[{"node": "设置参数-数据抓取", "type": "main", "index": 0}]]}, "设置参数-数据抓取": {"main": [[{"node": "可视化分析报告", "type": "main", "index": 0}]]}, "可视化分析报告": {"main": [[]]}, "图表生成": {"main": [[{"node": "设置参数-图表生成", "type": "main", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}