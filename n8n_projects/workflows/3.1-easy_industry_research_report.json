{"name": "3.1-easy_industry_research_report", "nodes": [{"parameters": {"jsCode": "// 改进的代码节点HTML生成器\n// 节点顺序：Set → Tavily Search → OpenAI → Code (此节点)\n// 这个代码节点会生成完整的HTML报告并返回给下游节点\n\n// 获取上游节点数据\nconst setData = $('Set').item.json;\nconst searchResults = $('Tavily').item.json;\nconst aiResponse = $('OpenAI').item.json;\n\n// 增强的AI响应解析函数\nfunction parseAIResponse(aiResponse) {\n    console.log('开始解析AI响应...');\n    \n    let aiContent = null;\n    \n    // 步骤1: 提取AI内容 - 支持DeepSeek和OpenAI格式\n    try {\n        // DeepSeek格式: 直接返回结构化数据在output字段中\n        if (aiResponse?.output && typeof aiResponse.output === 'object') {\n            console.log('使用output字段提取成功');\n            return validateAndNormalizeData(aiResponse.output);\n        }\n        // OpenAI格式: 从choices中提取\n        else if (aiResponse?.choices?.[0]?.message?.content) {\n            aiContent = aiResponse.choices[0].message.content;\n        } else if (aiResponse?.content) {\n            aiContent = aiResponse.content;\n        } else if (aiResponse?.message?.content) {\n            aiContent = aiResponse.message.content;\n        } else if (typeof aiResponse === 'string') {\n            aiContent = aiResponse;\n        } else {\n            throw new Error('无法从AI响应中提取内容');\n        }\n        \n        console.log('AI内容提取成功，长度:', aiContent.length);\n    } catch (error) {\n        console.error('AI内容提取失败:', error.message);\n        return getDefaultAnalysisData();\n    }\n    \n    // 步骤2: 清理和预处理内容\n    try {\n        // 移除markdown代码块标记\n        aiContent = aiContent\n            .replace(/```(?:json|javascript|js)?\\s*/g, '')\n            .replace(/```\\s*$/g, '')\n            .trim();\n        \n        // 尝试提取JSON对象\n        const jsonMatch = aiContent.match(/\\{[\\s\\S]*\\}/);\n        if (jsonMatch) {\n            aiContent = jsonMatch[0];\n        }\n        \n        // 处理可能的转义字符\n        if (aiContent.startsWith('\"') && aiContent.endsWith('\"')) {\n            try {\n                const unescaped = JSON.parse(aiContent);\n                if (typeof unescaped === 'string' && unescaped.startsWith('{')) {\n                    aiContent = unescaped;\n                }\n            } catch (e) {\n                // 忽略转义处理错误\n            }\n        }\n        \n        console.log('内容清理完成');\n    } catch (error) {\n        console.error('内容清理失败:', error.message);\n        return getDefaultAnalysisData();\n    }\n    \n    // 步骤3: 尝试JSON解析\n    try {\n        const parsedData = JSON.parse(aiContent);\n        console.log('JSON解析成功');\n        return validateAndNormalizeData(parsedData);\n    } catch (jsonError) {\n        console.log('JSON解析失败，尝试文本提取:', jsonError.message);\n        return extractFromText(aiContent);\n    }\n}\n\n// 从文本中提取结构化数据\nfunction extractFromText(content) {\n    console.log('开始文本提取...');\n    \n    const extractField = (patterns) => {\n        for (const pattern of patterns) {\n            const match = content.match(pattern);\n            if (match && match[1]) {\n                return match[1].trim().replace(/^[\"']|[\"']$/g, '');\n            }\n        }\n        return null;\n    };\n    \n    const extractList = (patterns) => {\n        for (const pattern of patterns) {\n            const match = content.match(pattern);\n            if (match && match[1]) {\n                return match[1]\n                    .split(/[\\n\\r]+/)\n                    .map(item => item.replace(/^[-*•]\\s*/, '').trim())\n                    .filter(item => item.length > 0 && !item.match(/^[\\d.]+$/));\n            }\n        }\n        return [];\n    };\n    \n    const result = {\n        executive_summary: extractField([\n            /执行摘要[：:]([\\s\\S]*?)(?=市场规模|发展趋势|主要参与者|面临挑战|$)/i,\n            /executive_summary[：:]\\s*([^\\n]+)/i,\n            /摘要[：:]([\\s\\S]*?)(?=规模|趋势|参与者|挑战|$)/i\n        ]) || \"基于AI分析生成的行业报告摘要\",\n        \n        market_size: extractField([\n            /市场规模[：:]([\\s\\S]*?)(?=发展趋势|主要参与者|面临挑战|增长率|$)/i,\n            /market_size[：:]\\s*([^\\n]+)/i,\n            /规模[：:]([\\s\\S]*?)(?=趋势|参与者|挑战|$)/i\n        ]) || \"市场规模数据分析中\",\n        \n        trends: extractList([\n            /发展趋势[：:]([\\s\\S]*?)(?=主要参与者|面临挑战|发展机遇|增长率|$)/i,\n            /trends[：:]([\\s\\S]*?)(?=key_players|challenges|opportunities|$)/i,\n            /趋势[：:]([\\s\\S]*?)(?=参与者|挑战|机遇|$)/i\n        ]),\n        \n        key_players: extractList([\n            /主要参与者[：:]([\\s\\S]*?)(?=面临挑战|发展机遇|增长率|未来预测|$)/i,\n            /key_players[：:]([\\s\\S]*?)(?=challenges|opportunities|growth|$)/i,\n            /参与者[：:]([\\s\\S]*?)(?=挑战|机遇|增长|$)/i\n        ]),\n        \n        challenges: extractList([\n            /面临挑战[：:]([\\s\\S]*?)(?=发展机遇|增长率|未来预测|$)/i,\n            /challenges[：:]([\\s\\S]*?)(?=opportunities|growth|forecast|$)/i,\n            /挑战[：:]([\\s\\S]*?)(?=机遇|增长|预测|$)/i\n        ]),\n        \n        opportunities: extractList([\n            /发展机遇[：:]([\\s\\S]*?)(?=增长率|未来预测|数据来源|$)/i,\n            /opportunities[：:]([\\s\\S]*?)(?=growth|forecast|source|$)/i,\n            /机遇[：:]([\\s\\S]*?)(?=增长|预测|来源|$)/i\n        ]),\n        \n        growth_rate: extractField([\n            /增长率[：:]([\\s\\S]*?)(?=未来预测|数据来源|$)/i,\n            /growth_rate[：:]\\s*([^\\n]+)/i,\n            /增长[：:]([\\s\\S]*?)(?=预测|来源|$)/i\n        ]) || \"增长率数据分析中\",\n        \n        future_forecast: extractField([\n            /未来预测[：:]([\\s\\S]*?)(?=数据来源|$)/i,\n            /future_forecast[：:]\\s*([\\s\\S]*?)(?=source|$)/i,\n            /预测[：:]([\\s\\S]*?)(?=来源|$)/i\n        ]) || \"未来预测分析中\"\n    };\n    \n    console.log('文本提取完成');\n    return validateAndNormalizeData(result);\n}\n\n// 验证和标准化数据\nfunction validateAndNormalizeData(data) {\n    const normalized = {\n        executive_summary: data.executive_summary || data.summary || \"行业分析摘要\",\n        market_size: data.market_size || data.marketSize || \"市场规模信息\",\n        trends: Array.isArray(data.trends) ? data.trends : \n                (typeof data.trends === 'string' ? [data.trends] : [\"发展趋势分析中\"]),\n        key_players: Array.isArray(data.key_players) ? data.key_players :\n                     Array.isArray(data.keyPlayers) ? data.keyPlayers :\n                     (typeof data.key_players === 'string' ? [data.key_players] : [\"主要参与者分析中\"]),\n        challenges: Array.isArray(data.challenges) ? data.challenges :\n                    (typeof data.challenges === 'string' ? [data.challenges] : [\"挑战分析中\"]),\n        opportunities: Array.isArray(data.opportunities) ? data.opportunities :\n                       (typeof data.opportunities === 'string' ? [data.opportunities] : [\"机遇分析中\"]),\n        growth_rate: data.growth_rate || data.growthRate || \"增长率分析中\",\n        future_forecast: data.future_forecast || data.futureForecast || data.forecast || \"未来预测分析中\",\n        chart_data: data.chart_data || data.chartData || getDefaultChartData()\n    };\n    \n    console.log('数据标准化完成:', Object.keys(normalized));\n    return normalized;\n}\n\n// 获取默认分析数据\nfunction getDefaultAnalysisData() {\n    return {\n        executive_summary: \"本报告基于最新的市场数据和AI分析，为您提供全面的行业洞察。报告涵盖了市场规模、发展趋势、主要参与者、面临挑战和发展机遇等关键信息。\",\n        market_size: \"根据最新数据显示，该行业市场规模持续增长，预计在未来几年将保持稳定的增长态势。\",\n        trends: [\n            \"数字化转型加速推进，技术创新成为重要驱动力\",\n            \"可持续发展理念深入人心，绿色环保成为新标准\",\n            \"消费者需求日益多样化，个性化服务需求增长\",\n            \"国际合作与竞争并存，全球化布局成为发展趋势\"\n        ],\n        key_players: [\"行业领军企业A\", \"创新科技公司B\", \"传统巨头企业C\", \"新兴独角兽D\"],\n        challenges: [\n            \"技术更新换代速度加快，需要持续投入研发\",\n            \"市场竞争日趋激烈，利润空间受到压缩\",\n            \"监管政策不断完善，合规成本上升\"\n        ],\n        opportunities: [\n            \"新兴市场需求旺盛，提供广阔发展空间\",\n            \"技术创新带来新的商业模式和盈利点\",\n            \"政策支持力度加大，为发展提供有力保障\"\n        ],\n        growth_rate: \"预计年均增长率将保持在15-25%之间\",\n        future_forecast: \"未来3-5年，该行业将继续保持快速发展态势，建议相关企业加强技术研发，提升服务质量。\",\n        chart_data: getDefaultChartData()\n    };\n}\n\n// 获取默认图表数据\nfunction getDefaultChartData() {\n    return {\n        market_size_trend: [\n            {year: 2022, value: 1200},\n            {year: 2023, value: 1450},\n            {year: 2024, value: 1680},\n            {year: 2025, value: 1950}\n        ],\n        growth_rate_trend: [\n            {year: 2022, value: 18},\n            {year: 2023, value: 21},\n            {year: 2024, value: 16},\n             {year: 2025, value: 16}\n        ],\n        // DeepSeek格式兼容\n        market_trend: {\n            years: [\"2022\", \"2023\", \"2024\", \"2025\"],\n            values: [1.2, 1.5, 1.8, 2.2],\n            unit: \"万亿元\"\n        },\n        growth_rate: {\n            years: [\"2022\", \"2023\", \"2024\", \"2025\"],\n            values: [8, 10, 12, 10],\n            unit: \"%\"\n        }\n    };\n}\n\n// 生成HTML报告\nfunction generateHTMLReport(analysisData, setData) {\n    const reportTitle = `${setData.industry}行业分析报告`;\n    const currentDate = new Date();\n    const dateString = currentDate.toLocaleDateString('zh-CN');\n    const timeString = currentDate.toLocaleString('zh-CN');\n    \n    return `<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>${reportTitle}</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js\"></script>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <style>\n        .chart-container { width: 100%; height: 400px; }\n        .section { margin-bottom: 2rem; }\n        body { font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; }\n        .fade-in { animation: fadeIn 0.5s ease-in; }\n        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }\n    </style>\n</head>\n<body class=\"bg-gray-50 p-6\">\n    <div class=\"max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-8 fade-in\">\n        <!-- 报告标题 -->\n        <header class=\"text-center mb-8\">\n            <h1 class=\"text-3xl font-bold text-gray-800 mb-2\">${reportTitle}</h1>\n            <p class=\"text-gray-600\">生成时间：${dateString}</p>\n            <p class=\"text-sm text-gray-500\">地区：${setData.region || '全球'} | 时间范围：${setData.timeRange || '2024-2025'}</p>\n        </header>\n\n        <!-- 执行摘要 -->\n        <section class=\"section\">\n            <h2 class=\"text-2xl font-semibold text-blue-600 mb-4 border-b-2 border-blue-200 pb-2\">执行摘要</h2>\n            <div class=\"bg-blue-50 p-4 rounded-lg\">\n                <p class=\"text-gray-700 leading-relaxed\">${analysisData.executive_summary}</p>\n            </div>\n        </section>\n\n        <!-- 市场规模 -->\n        <section class=\"section\">\n            <h2 class=\"text-2xl font-semibold text-green-600 mb-4 border-b-2 border-green-200 pb-2\">市场规模</h2>\n            <div class=\"bg-green-50 p-4 rounded-lg mb-4\">\n                <p class=\"text-gray-700\">${analysisData.market_size}</p>\n            </div>\n            <div id=\"marketChart\" class=\"chart-container bg-white rounded border shadow\"></div>\n        </section>\n\n        <!-- 发展趋势 -->\n        <section class=\"section\">\n            <h2 class=\"text-2xl font-semibold text-purple-600 mb-4 border-b-2 border-purple-200 pb-2\">发展趋势</h2>\n            <div class=\"grid md:grid-cols-2 gap-4\">\n                ${analysisData.trends.map((trend, index) => `\n                    <div class=\"bg-purple-50 p-4 rounded-lg\">\n                        <h3 class=\"font-semibold text-purple-800 mb-2\">趋势 ${index + 1}</h3>\n                        <p class=\"text-gray-700\">${trend}</p>\n                    </div>\n                `).join('')}\n            </div>\n        </section>\n\n        <!-- 主要参与者 -->\n        <section class=\"section\">\n            <h2 class=\"text-2xl font-semibold text-orange-600 mb-4 border-b-2 border-orange-200 pb-2\">主要参与者</h2>\n            <div class=\"grid md:grid-cols-3 gap-4\">\n                ${analysisData.key_players.map(player => `\n                    <div class=\"bg-orange-50 p-4 rounded-lg text-center\">\n                        <h3 class=\"font-semibold text-orange-800\">${player}</h3>\n                    </div>\n                `).join('')}\n            </div>\n        </section>\n\n        <!-- 挑战与机遇 -->\n        <section class=\"section\">\n            <div class=\"grid md:grid-cols-2 gap-6\">\n                <!-- 挑战 -->\n                <div>\n                    <h2 class=\"text-2xl font-semibold text-red-600 mb-4 border-b-2 border-red-200 pb-2\">面临挑战</h2>\n                    ${analysisData.challenges.map(challenge => `\n                        <div class=\"bg-red-50 p-3 rounded-lg mb-3\">\n                            <p class=\"text-gray-700\">${challenge}</p>\n                        </div>\n                    `).join('')}\n                </div>\n                \n                <!-- 机遇 -->\n                <div>\n                    <h2 class=\"text-2xl font-semibold text-teal-600 mb-4 border-b-2 border-teal-200 pb-2\">发展机遇</h2>\n                    ${analysisData.opportunities.map(opportunity => `\n                        <div class=\"bg-teal-50 p-3 rounded-lg mb-3\">\n                            <p class=\"text-gray-700\">${opportunity}</p>\n                        </div>\n                    `).join('')}\n                </div>\n            </div>\n        </section>\n\n        <!-- 增长率图表 -->\n        <section class=\"section\">\n            <h2 class=\"text-2xl font-semibold text-indigo-600 mb-4 border-b-2 border-indigo-200 pb-2\">增长率趋势</h2>\n            <div id=\"growthChart\" class=\"chart-container bg-white rounded border shadow\"></div>\n        </section>\n\n        <!-- 未来预测 -->\n        <section class=\"section\">\n            <h2 class=\"text-2xl font-semibold text-gray-600 mb-4 border-b-2 border-gray-200 pb-2\">未来预测</h2>\n            <div class=\"bg-gray-50 p-4 rounded-lg\">\n                <p class=\"text-gray-700 leading-relaxed\">${analysisData.future_forecast}</p>\n            </div>\n        </section>\n\n        <!-- 数据来源 -->\n        <section class=\"section\">\n            <h2 class=\"text-2xl font-semibold text-gray-600 mb-4 border-b-2 border-gray-200 pb-2\">数据来源</h2>\n            <div class=\"bg-gray-50 p-4 rounded-lg\">\n                <p class=\"text-sm text-gray-600 mb-2\">本报告基于以下数据源生成：</p>\n                <ul class=\"text-sm text-gray-600 list-disc ml-4\">\n                    <li>Tavily搜索引擎实时数据</li>\n                    <li>AI智能分析处理</li>\n                    <li>生成时间：${timeString}</li>\n                </ul>\n            </div>\n        </section>\n    </div>\n\n    <script>\n        document.addEventListener('DOMContentLoaded', function() {\n            const chartData = ${JSON.stringify(analysisData.chart_data)};\n            \n            // 市场规模趋势图\n            try {\n                const marketChart = echarts.init(document.getElementById('marketChart'));\n                \n                // 兼容DeepSeek和原有格式\n                let marketData, marketYears, marketValues, marketUnit;\n                if (chartData.market_trend && chartData.market_trend.years) {\n                    // DeepSeek格式\n                    marketYears = chartData.market_trend.years;\n                    marketValues = chartData.market_trend.values;\n                    marketUnit = chartData.market_trend.unit || '亿元';\n                } else if (chartData.market_size_trend) {\n                    // 原有格式\n                    marketYears = chartData.market_size_trend.map(item => item.year);\n                    marketValues = chartData.market_size_trend.map(item => item.value);\n                    marketUnit = '亿元';\n                } else {\n                    // 默认数据\n                    marketYears = ['2022', '2023', '2024', '2025'];\n                    marketValues = [1200, 1450, 1680, 1950];\n                    marketUnit = '亿元';\n                }\n                \n                const marketOption = {\n                    title: {\n                        text: '市场规模趋势',\n                        left: 'center',\n                        textStyle: { fontSize: 16, fontWeight: 'bold' }\n                    },\n                    tooltip: {\n                        trigger: 'axis',\n                        formatter: function(params) {\n                            return params[0].name + ': ' + params[0].value + ' ' + marketUnit;\n                        }\n                    },\n                    xAxis: {\n                        type: 'category',\n                        data: marketYears\n                    },\n                    yAxis: {\n                        type: 'value',\n                        name: marketUnit\n                    },\n                    series: [{\n                        data: marketValues,\n                        type: 'line',\n                        smooth: true,\n                        lineStyle: { color: '#10B981', width: 3 },\n                        itemStyle: { color: '#10B981' },\n                        areaStyle: {\n                            color: {\n                                type: 'linear',\n                                x: 0, y: 0, x2: 0, y2: 1,\n                                colorStops: [\n                                    { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },\n                                    { offset: 1, color: 'rgba(16, 185, 129, 0.1)' }\n                                ]\n                            }\n                        }\n                    }]\n                };\n                marketChart.setOption(marketOption);\n            } catch (error) {\n                console.error('市场规模图表渲染失败:', error);\n                document.getElementById('marketChart').innerHTML = '<p class=\"text-center text-gray-500 py-8\">图表数据加载失败</p>';\n            }\n\n            // 增长率图表\n            try {\n                const growthChart = echarts.init(document.getElementById('growthChart'));\n                \n                // 兼容DeepSeek和原有格式\n                let growthYears, growthValues, growthUnit;\n                if (chartData.growth_rate && chartData.growth_rate.years) {\n                    // DeepSeek格式\n                    growthYears = chartData.growth_rate.years;\n                    growthValues = chartData.growth_rate.values;\n                    growthUnit = chartData.growth_rate.unit || '%';\n                } else if (chartData.growth_rate_trend) {\n                    // 原有格式\n                    growthYears = chartData.growth_rate_trend.map(item => item.year);\n                    growthValues = chartData.growth_rate_trend.map(item => item.value);\n                    growthUnit = '%';\n                } else {\n                    // 默认数据\n                    growthYears = ['2022', '2023', '2024', '2025'];\n                    growthValues = [18, 21, 16, 16];\n                    growthUnit = '%';\n                }\n                \n                const growthOption = {\n                    title: {\n                        text: '年增长率',\n                        left: 'center',\n                        textStyle: { fontSize: 16, fontWeight: 'bold' }\n                    },\n                    tooltip: {\n                        trigger: 'axis',\n                        formatter: function(params) {\n                            return params[0].name + ': ' + params[0].value + growthUnit;\n                        }\n                    },\n                    xAxis: {\n                        type: 'category',\n                        data: growthYears\n                    },\n                    yAxis: {\n                        type: 'value',\n                        name: '增长率 (' + growthUnit + ')'\n                    },\n                    series: [{\n                        data: growthValues,\n                        type: 'bar',\n                        itemStyle: {\n                            color: {\n                                type: 'linear',\n                                x: 0, y: 0, x2: 0, y2: 1,\n                                colorStops: [\n                                    { offset: 0, color: '#6366F1' },\n                                    { offset: 1, color: '#8B5CF6' }\n                                ]\n                            }\n                        }\n                    }]\n                };\n                growthChart.setOption(growthOption);\n            } catch (error) {\n                console.error('增长率图表渲染失败:', error);\n                document.getElementById('growthChart').innerHTML = '<p class=\"text-center text-gray-500 py-8\">图表数据加载失败</p>';\n            }\n\n            // 响应式图表\n            window.addEventListener('resize', function() {\n                try {\n                    echarts.getInstanceByDom(document.getElementById('marketChart'))?.resize();\n                    echarts.getInstanceByDom(document.getElementById('growthChart'))?.resize();\n                } catch (error) {\n                    console.error('图表resize失败:', error);\n                }\n            });\n        });\n    </script>\n</body>\n</html>`;\n}\n\n// 主执行逻辑\ntry {\n    console.log('开始处理数据...');\n    \n    // 解析AI响应\n    const analysisData = parseAIResponse(aiResponse);\n    console.log('数据解析完成:', Object.keys(analysisData));\n    \n    // 生成HTML报告\n    const htmlReport = generateHTMLReport(analysisData, setData);\n    console.log('HTML报告生成完成，长度:', htmlReport.length);\n    \n    // 返回结果\n    return {\n        html: htmlReport,\n        filename: `${setData.industry}_行业分析报告_${new Date().toISOString().split('T')[0]}.html`,\n        analysis_data: analysisData,\n        metadata: {\n            industry: setData.industry,\n            region: setData.region,\n            timeRange: setData.timeRange,\n            generated_at: new Date().toISOString(),\n            workflow_version: \"improved_v2.0\",\n            parsing_method: \"enhanced_robust_parsing\"\n        },\n        success: true,\n        message: \"HTML报告生成成功\"\n    };\n    \n} catch (error) {\n    console.error('处理过程中发生错误:', error);\n    \n    // 错误情况下返回默认报告\n    const defaultData = getDefaultAnalysisData();\n    const errorReport = generateHTMLReport(defaultData, setData);\n    \n    return {\n        html: errorReport,\n        filename: `${setData.industry}_行业分析报告_${new Date().toISOString().split('T')[0]}_error.html`,\n        analysis_data: defaultData,\n        metadata: {\n            industry: setData.industry,\n            region: setData.region,\n            timeRange: setData.timeRange,\n            generated_at: new Date().toISOString(),\n            workflow_version: \"improved_v2.0\",\n            parsing_method: \"error_fallback\"\n        },\n        success: false,\n        error: error.message,\n        message: \"使用默认数据生成报告\"\n    };\n  }\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1936, 260], "id": "9a8cf52b-f840-4ccb-9e77-f94092ce04b8", "name": "html"}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"type\": \"object\",\n  \"properties\": {\n    \"title\": {\n      \"type\": \"string\",\n      \"description\": \"报告标题，包含行业名称\"\n    },\n    \"summary\": {\n      \"type\": \"string\",\n      \"description\": \"行业分析的执行摘要\"\n    },\n    \"market_size\": {\n      \"type\": \"string\",\n      \"description\": \"当前市场规模，包含单位\"\n    },\n    \"trends\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\": \"行业关键趋势\"\n    },\n    \"key_players\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\": \"行业主要参与者和公司\"\n    },\n    \"challenges\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\": \"行业面临的挑战和障碍\"\n    },\n    \"opportunities\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"string\"\n      },\n      \"description\": \"市场机遇和潜力\"\n    },\n    \"forecast\": {\n      \"type\": \"string\",\n      \"description\": \"未来展望和预测\"\n    },\n    \"chart_data\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"market_trend\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"years\": {\n              \"type\": \"array\",\n              \"items\": {\n                \"type\": \"string\"\n              },\n              \"description\": \"年份数组\"\n            },\n            \"values\": {\n              \"type\": \"array\",\n              \"items\": {\n                \"type\": \"number\"\n              },\n              \"description\": \"市场规模数值\"\n            },\n            \"unit\": {\n              \"type\": \"string\",\n              \"description\": \"数值单位，如亿元、万亿等\"\n            }\n          },\n          \"required\": [\"years\", \"values\", \"unit\"]\n        },\n        \"growth_rate\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"years\": {\n              \"type\": \"array\",\n              \"items\": {\n                \"type\": \"string\"\n              },\n              \"description\": \"年份数组\"\n            },\n            \"values\": {\n              \"type\": \"array\",\n              \"items\": {\n                \"type\": \"number\"\n              },\n              \"description\": \"增长率数值\"\n            },\n            \"unit\": {\n              \"type\": \"string\",\n              \"description\": \"增长率单位，通常为%\"\n            }\n          },\n          \"required\": [\"years\", \"values\", \"unit\"]\n        }\n      },\n      \"required\": [\"market_trend\", \"growth_rate\"]\n    }\n  },\n  \"required\": [\"title\", \"summary\", \"market_size\", \"trends\", \"key_players\", \"challenges\", \"opportunities\", \"forecast\", \"chart_data\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1740, 460], "id": "54bcc2a3-a4b9-4d79-82a9-0b23e654164f", "name": "Structured Output Parser"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [680, 260], "id": "8b7c2559-a753-45ce-8016-e3177c9212de", "name": "When chat message received", "webhookId": "d869a8cf-1af0-4da7-a731-440ecbc18e78"}, {"parameters": {"jsCode": "// Get industry keyword from chat input\nconst chatInput = $input.item.json.chatInput || \"\";\n\n// Clean and normalize the keyword\nconst rawIndustry = chatInput.trim();\n\n// Basic input validation\nif (!rawIndustry) {\n  throw new Error(\"Please provide a valid industry keyword\");\n}\n\n// Sanitize input: remove special characters and normalize spaces\nconst industry = rawIndustry\n  .replace(/[^\\w\\s\\u4e00-\\u9fff]/g, '') // Keep only word chars, spaces, and Chinese characters\n  .replace(/\\s+/g, ' ') // Normalize spaces\n  .trim();\n\n// Validate sanitized input\nif (industry.length < 2) {\n  throw new Error(\"Industry keyword must be at least 2 characters long\");\n}\n\n// Generate comprehensive search variants\nconst searchVariants = [\n  // Market analysis variants\n  `${industry} 市场规模`,\n  `${industry} 行业分析报告`,\n  `${industry} 产业分析`,\n  `${industry} 市场分析`,\n  \n  // Competition analysis variants\n  `${industry} 竞争格局`,\n  `${industry} 竞争分析`,\n  `${industry} 主要企业`,\n  `${industry} 市场份额`,\n  \n  // Development trend variants\n  `${industry} 发展趋势`,\n  `${industry} 未来展望`,\n  `${industry} 前景分析`,\n  `${industry} 最新动态`,\n  \n  // Data and statistics variants\n  `${industry} 最新数据`,\n  `${industry} 统计数据`,\n  `${industry} 行业数据`,\n  `${industry} 市场容量`\n];\n\n// Add English variants if the input contains English characters\nif (/[a-zA-Z]/.test(industry)) {\n  const englishVariants = [\n    `${industry} market size`,\n    `${industry} industry analysis`,\n    `${industry} market analysis`,\n    `${industry} competitive landscape`,\n    `${industry} market share`,\n    `${industry} industry trends`,\n    `${industry} future outlook`,\n    `${industry} statistics`,\n    `${industry} market data`\n  ];\n  searchVariants.push(...englishVariants);\n}\n\n// Return the processed data\nreturn [{\n  json: {\n    industry: industry,\n    searchVariants: searchVariants,\n    timestamp: new Date().toISOString(),\n    metadata: {\n      originalInput: rawIndustry,\n      sanitized: industry !== rawIndustry,\n      hasEnglish: /[a-zA-Z]/.test(industry),\n      variantCount: searchVariants.length\n    }\n  }\n}];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [900, 260], "id": "********-6788-4a66-9162-cc1eff0ced7c", "name": "Set"}, {"parameters": {"options": {"maxTokens": 4000, "responseFormat": "json_object", "temperature": 0.4, "maxRetries": 3}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [1520, 460], "id": "25396da9-f6c7-44cd-abf7-e932e2b3153e", "name": "DeepSeek Chat Model", "credentials": {"deepSeekApi": {"id": "IKqWJBBXULhRu8mZ", "name": "DeepSeek account"}}}, {"parameters": {"promptType": "define", "text": "=请基于以下搜索结果，分析{{$('Set').item.json.industry}}行业在{{ $('Set').item.json.timestamp }}时间的发展情况：\n\n搜索结果：\n{{$('Tavily').item.json.results}}\n\n请严格按照JSON格式输出分析报告。", "hasOutputParser": true, "options": {"systemMessage": "=你是一个专业的行业分析师，擅长撰写详细的行业研究报告。你的回答应该准确、专业、结构化，并严格按照提供的JSON格式输出。\n\n要求：\n\n1. 输出格式必须是有效的JSON，不包含任何其他文本\n2. 所有内容必须使用中文\n3. 包含以下字段：\n\n   - title: 报告标题（包含行业名称）\n   - summary: 执行摘要（150-200字）\n   - market_size: 当前市场规模数据（包含具体数值和单位）\n   - trends: 发展趋势（3-5个要点，每个要点简洁明了）\n   - key_players: 主要参与者（3-5家公司，包含简短描述）\n   - challenges: 面临挑战（3个要点）\n   - opportunities: 发展机遇（3个要点）\n   - forecast: 未来3-5年预测\n   - chart_data: 图表数据（必须包含真实可用的数值）\n4. chart_data格式要求：\n\n   ```\n   {\n   \"market_trend\": {\n   \"years\": [\"2022\", \"2023\", \"2024\", \"2025\"],\n   \"values\": [实际数值数组],\n   \"unit\": \"亿元\"\n   },\n   \"growth_rate\": {\n   \"years\": [\"2022\", \"2023\", \"2024\", \"2025\"],\n   \"values\": [实际增长率数组],\n   \"unit\": \"%\"\n   }\n   }\n   ```\n\n请确保所有数据基于真实市场信息，分析客观深入，避免过度乐观或悲观的预测。"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.9, "position": [1560, 260], "id": "cc5cd44a-4b07-4812-a3d2-a8e1add08908", "name": "OpenAI"}, {"parameters": {"fieldToSplitOut": "searchVariants", "include": "allOtherFields", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1120, 260], "id": "8f42cec1-1ddd-48e0-ae55-d3ee42bfb661", "name": "Split Out"}, {"parameters": {"url": "={{ $json.result.content[0].text }}", "operation": "getScreenshot", "imageType": "webp", "fullPage": true, "options": {"executablePath": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"}}, "type": "n8n-nodes-puppeteer.puppeteer", "typeVersion": 1, "position": [2280, 260], "id": "c6b765fe-7038-42ef-b96c-63427e38459b", "name": "<PERSON><PERSON><PERSON><PERSON>"}, {"parameters": {"query": "={{ $json.searchVariants }}", "options": {"search_depth": "advanced", "max_results": 10, "include_raw_content": true, "include_images": true}}, "type": "@tavily/n8n-nodes-tavily.tavily", "typeVersion": 1, "position": [1340, 260], "id": "bf90ca6b-d510-4612-acae-6d5eb8b1d82c", "name": "<PERSON><PERSON>", "credentials": {"tavilyApi": {"id": "MYBbUyqdPTUvZuXR", "name": "Tavily account"}}}, {"parameters": {"method": "POST", "url": "https://mcp-on-edge.edgeone.site/mcp-server", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content_Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"jsonrpc\": \"2.0\",\n  \"id\": 1,\n  \"method\": \"tools/call\",\n  \"params\": {\n    \"name\": \"deploy-html\",\n    \"arguments\": {\n      \"value\": \"{{ JSON.stringify($node['html'].json.html).slice(1, -1) }}\"\n    }\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2120, 260], "id": "64f29cb0-ca25-4159-919d-4045906dc863", "name": "发布"}], "connections": {"html": {"main": [[]]}, "When chat message received": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "DeepSeek Chat Model": {"ai_languageModel": [[{"node": "OpenAI", "type": "ai_languageModel", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "OpenAI", "type": "ai_outputParser", "index": 0}]]}, "OpenAI": {"main": [[{"node": "html", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Tavily": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "发布": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Puppeteer": {"main": [[]]}}, "settings": {"executionOrder": "v1"}}