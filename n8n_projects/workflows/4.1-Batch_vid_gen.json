{"name": "4.1-Batch_vid_gen", "nodes": [{"parameters": {"formTitle": "视频话题", "formDescription": "输入视频话题，自动生成短视频。", "formFields": {"values": [{"fieldLabel": "topic"}]}, "options": {}}, "type": "n8n-nodes-base.formTrigger", "typeVersion": 2.2, "position": [-1168, -96], "id": "a1718929-bc7c-4c35-9883-a73457eafe81", "name": "On form submission", "webhookId": "7172535b-d65b-44d1-a72d-85e3f4487c84"}, {"parameters": {"jsonSchemaExample": "{\n\"title\": \"在此填入视频标题\",\n\"content\": \"在此填入文案内容\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [-752, 80], "id": "0d01f755-bdee-4862-84ac-883a0a525fb8", "name": "Structured Output Parser"}, {"parameters": {"method": "POST", "url": "http://host.docker.internal:8080/api/v1/videos", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"video_subject\": \"{{ $json.output.title }} \",\n  \"video_script\": \"{{ $json.output.content }}\",\n  \"video_terms\": \"string\",\n  \"video_aspect\": \"9:16\",\n  \"video_concat_mode\": \"random\",\n  \"video_transition_mode\": \"None\",\n  \"video_clip_duration\": 5,\n  \"video_count\": 1,\n  \"video_source\": \"pexels\",\n  \"video_materials\": [\n    {\n      \"provider\": \"pexels\",\n      \"url\": \"\",\n      \"duration\": 0\n    }\n  ],\n  \"video_language\": \"\",\n  \"voice_name\": \"\",\n  \"voice_volume\": 1,\n  \"voice_rate\": 1,\n  \"bgm_type\": \"random\",\n  \"bgm_file\": \"\",\n  \"bgm_volume\": 0.2,\n  \"subtitle_enabled\": true,\n  \"subtitle_position\": \"bottom\",\n  \"custom_position\": 70,\n  \"font_name\": \"STHeitiMedium.ttc\",\n  \"text_fore_color\": \"#FFFFFF\",\n  \"text_background_color\": true,\n  \"font_size\": 60,\n  \"stroke_color\": \"#000000\",\n  \"stroke_width\": 1.5,\n  \"n_threads\": 2,\n  \"paragraph_number\": 1\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-544, -80], "id": "5e6911c9-871e-432f-89fb-b16f54562231", "name": "生成视频"}, {"parameters": {}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-320, -96], "id": "c4c3faa0-fac4-4175-acc9-3b7ec6fa4499", "name": "Wait", "webhookId": "d133f1da-3b50-4384-89be-cbc6273202eb"}, {"parameters": {"url": "=http://host.docker.internal:8080/api/v1/tasks/{{ $json.data.task_id }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-96, -160], "id": "225fdb83-f1d6-4b0e-bd6b-f875fa5bc290", "name": "查询进度"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "c00c71fb-fadf-4347-87fb-dab064c90493", "leftValue": "={{ $json.data.progress }}", "rightValue": 100, "operator": {"type": "number", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [128, -96], "id": "ca7c7745-15d9-4228-9aa8-e0433b267ac7", "name": "If"}, {"parameters": {"url": "={{ $json.data.video[0] }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [352, -96], "id": "be654f6e-913a-4bde-943d-b3376f43a668", "name": "HTTP Request"}, {"parameters": {"promptType": "define", "text": "=按照要求生成内容。", "hasOutputParser": true, "options": {"systemMessage": "=你是一名资深自媒体运营专家兼文案策划师，对 抖音 平台的调性和用户心理有深入了解。\n任务：围绕 “{{$json.topic}}” 主题，创作一条 100 - 120 字的短视频文案，同时拟定一个不超过 10 字的标题。\n要求：\n文案结构需遵循：\n【钩子】在 3 秒内抓住受众注意力\n【核心要点】提炼 3 个关键卖点或能给用户带来的利益点\n【CTA】包含一句行动号召\n\n严格以 JSON 格式输出，且仅包含 title（标题 ）和 content（文案内容 ）两个字段。不要在 JSON 前后添加任何其他文本或解释。格式如下：\n{\n\"title\": \"在此填入视频标题\",\n\"content\": \"在此填入文案内容\"\n}"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.1, "position": [-944, -96], "id": "9dd28b9a-beee-4180-b112-73a56dfc6810", "name": "AI Agent1"}, {"parameters": {"options": {"maxOutputTokens": 2048, "temperature": 0.4, "topP": 1}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [-928, 64], "id": "f7b0d504-e6ce-4da3-9a80-92fd990f1053", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "cdwHRnakv6TIFeEn", "name": "Google Gemini(PaLM) Api account"}}}], "connections": {"On form submission": {"main": [[{"node": "AI Agent1", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "AI Agent1", "type": "ai_outputParser", "index": 0}]]}, "生成视频": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "查询进度", "type": "main", "index": 0}]]}, "查询进度": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}, "AI Agent1": {"main": [[{"node": "生成视频", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent1", "type": "ai_languageModel", "index": 0}]]}}, "settings": {"executionOrder": "v1"}}