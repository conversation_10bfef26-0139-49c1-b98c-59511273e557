services:
  n8n:
    image: n8nio/n8n
    restart: always
    ports:
      - "5678:5678"
    volumes:
      - docker_n8n_data:/home/<USER>/.n8n
      - ../comfyui_docker/output:/home/<USER>/shared_output
    environment:
      - GENERIC_TIMEZONE=Asia/Shanghai
      - LANG=en_US.UTF-8
    depends_on:
      - postgres

  postgres:
    image: postgres:13
    restart: always
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: n8nuser
      POSTGRES_PASSWORD: Me249500_xs!!
      POSTGRES_DB: n8n_analytics
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  docker_n8n_data:
    external: true
  postgres_data: {}
