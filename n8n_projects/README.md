# n8n 工作流项目

此目录用于存放所有与 n8n 相关的工作流、脚本和配置文件，以实现集中化管理。

## 项目概览

- **行业报告生成器**:
  - **用途**: 自动化生成行业报告的工作流。
  - **包含**:
    - `easy_n8n_workflow`: 简化版工作流。
    - `enhanced_n8n_workflow`: 增强版工作流。
    - `enhanced_n8n_workflow_v2`: 第二版增强型工作流。

- **image_to_excel_ocr**:
  - **用途**: 从图片中提取文本并将其转换为 Excel 表格。
  - **包含**:
    - `n8n_ocr_workflow.json`: n8n 工作流文件。
    - `image_to_excel_converter.py`: Python 转换脚本。
    - `requirements.txt`: Python 依赖项。

- **Industry_report_agent**:
  - **用途**: 一个更复杂的、基于代理的行业报告生成系统。
  - **包含**:
    - `index.js`: Node.js 入口文件。
    - `config/`: 配置文件目录。
    - `templates/`: 报告模板。
    - `n8n-visualization/`: n8n 可视化相关文件。

- **performance_appraisal_system**:
  - **用途**: 用于管理绩效评估流程的工作流集合。
  - **包含**:
    - `workflows/`: 包含多个 n8n 工作流文件（.json），分别对应绩效评估的不同阶段。
    - `1_system_architecture.md`: 系统架构文档。
    - `2_setup_guide.md`: 安装与设置指南。

## 标准化项目结构建议

为了保持项目清晰并易于维护，建议所有新的 n8n 项目遵循以下目录结构：

```
/n8n_projects
|-- /your_project_name
|   |-- /workflows
|   |   |-- main_workflow.json
|   |   |-- sub_workflow_1.json
|   |-- /scripts
|   |   |-- data_processing.py
|   |   |-- api_integration.js
|   |-- /docs
|   |   |-- README.md
|   |   |-- setup_guide.md
|   |-- /assets
|   |   |-- email_template.html
|   |   |-- report_logo.png
|   |-- .env.example
|   |-- package.json
```

- **/workflows**: 存放所有 n8n 工作流的 `.json` 文件。
- **/scripts**: 存放工作流中执行的自定义脚本（如 Python, JavaScript）。
- **/docs**: 存放项目文档，如 `README.md`、设置指南和架构图。
- **/assets**: 存放静态资源，如邮件模板、图片等。
- **.env.example**: 环境变量示例文件，用于说明项目所需的环境变量。
- **package.json**: 如果项目包含 Node.js 脚本，用于管理依赖。

遵循此结构将使您的 n8n 项目更易于理解、扩展和协作。
