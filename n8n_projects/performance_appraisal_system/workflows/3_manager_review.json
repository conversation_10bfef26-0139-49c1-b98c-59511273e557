{"name": "Performance Appraisal - Manager Review", "nodes": [{"parameters": {"httpMethod": "POST", "path": "manager-review", "responseMode": "responseNode", "options": {}}, "id": "webhook-manager-review", "name": "Manager Review Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "employee_id", "value": "={{ $json.body.employee_id }}"}, {"name": "manager_id", "value": "={{ $json.body.manager_id }}"}, {"name": "cycle_id", "value": "={{ $json.body.cycle_id }}"}, {"name": "strengths", "value": "={{ $json.body.strengths }}"}, {"name": "areas_for_improvement", "value": "={{ $json.body.areas_for_improvement }}"}, {"name": "promotion_readiness", "value": "={{ $json.body.promotion_readiness }}"}, {"name": "salary_recommendation", "value": "={{ $json.body.salary_recommendation }}"}, {"name": "comments", "value": "={{ $json.body.comments }}"}], "number": [{"name": "performance_rating", "value": "={{ $json.body.performance_rating }}"}, {"name": "goal_achievement_rating", "value": "={{ $json.body.goal_achievement_rating }}"}], "object": [{"name": "competency_ratings", "value": "={{ $json.body.competency_ratings }}"}]}}, "id": "set-review-data", "name": "Set Review Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.employee_id }}", "operation": "isNotEmpty"}, {"value1": "={{ $json.manager_id }}", "operation": "isNotEmpty"}, {"value1": "={{ $json.cycle_id }}", "operation": "isNotEmpty"}]}}, "id": "validate-required-fields", "name": "Validate Required Fields", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM manager_reviews WHERE employee_id = '{{ $json.employee_id }}' AND manager_id = '{{ $json.manager_id }}' AND cycle_id = {{ $json.cycle_id }}"}, "id": "check-existing-review", "name": "Check Existing Review", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [900, 200], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"conditions": {"number": [{"value1": "={{ $json.length }}", "operation": "equal", "value2": 0}]}}, "id": "check-is-new-review", "name": "Check Is New Review", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"operation": "insert", "table": "manager_reviews", "columns": "employee_id, manager_id, cycle_id, performance_rating, competency_ratings, strengths, areas_for_improvement, goal_achievement_rating, promotion_readiness, salary_recommendation, comments, status, submitted_at", "additionalFields": {"mode": "independently"}}, "id": "insert-new-review", "name": "Insert New Review", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [1340, 100], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"operation": "update", "table": "manager_reviews", "updateKey": "id", "columns": "performance_rating, competency_ratings, strengths, areas_for_improvement, goal_achievement_rating, promotion_readiness, salary_recommendation, comments, submitted_at", "additionalFields": {"mode": "independently"}}, "id": "update-existing-review", "name": "Update Existing Review", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [1340, 300], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT e.first_name, e.last_name, e.email FROM employees e WHERE e.employee_id = '{{ $('Set Review Data').first().json.employee_id }}'"}, "id": "get-employee-info", "name": "Get Employee Info", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [1560, 200], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "requestMethod": "POST", "url": "={{ $json.webhook_url || 'http://localhost:5678/webhook/notify-employee-review-complete' }}", "jsonParameters": true, "bodyParametersJson": "={\n  \"employee_email\": \"{{ $json.email }}\",\n  \"employee_name\": \"{{ $json.first_name }} {{ $json.last_name }}\",\n  \"employee_id\": \"{{ $('Set Review Data').first().json.employee_id }}\",\n  \"manager_id\": \"{{ $('Set Review Data').first().json.manager_id }}\",\n  \"cycle_id\": \"{{ $('Set Review Data').first().json.cycle_id }}\",\n  \"performance_rating\": \"{{ $('Set Review Data').first().json.performance_rating }}\",\n  \"notification_type\": \"manager_review_complete\"\n}", "options": {}}, "id": "notify-employee-review-complete", "name": "Notify Employee Review Complete", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1780, 200]}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "requestMethod": "POST", "url": "={{ $json.webhook_url || 'http://localhost:5678/webhook/notify-hr-review-complete' }}", "jsonParameters": true, "bodyParametersJson": "={\n  \"employee_id\": \"{{ $('Set Review Data').first().json.employee_id }}\",\n  \"manager_id\": \"{{ $('Set Review Data').first().json.manager_id }}\",\n  \"cycle_id\": \"{{ $('Set Review Data').first().json.cycle_id }}\",\n  \"performance_rating\": \"{{ $('Set Review Data').first().json.performance_rating }}\",\n  \"salary_recommendation\": \"{{ $('Set Review Data').first().json.salary_recommendation }}\",\n  \"promotion_readiness\": \"{{ $('Set Review Data').first().json.promotion_readiness }}\",\n  \"notification_type\": \"hr_review_notification\"\n}", "options": {}}, "id": "notify-hr-review-complete", "name": "Notify HR Review Complete", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [2000, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"Manager review submitted successfully\",\n  \"employee_id\": \"{{ $('Set Review Data').first().json.employee_id }}\",\n  \"manager_id\": \"{{ $('Set Review Data').first().json.manager_id }}\",\n  \"cycle_id\": \"{{ $('Set Review Data').first().json.cycle_id }}\",\n  \"performance_rating\": \"{{ $('Set Review Data').first().json.performance_rating }}\",\n  \"submitted_at\": \"{{ new Date().toISOString() }}\"\n}"}, "id": "response-success", "name": "Response Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2220, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"error\": \"Missing required fields: employee_id, manager_id, and cycle_id are required\"\n}", "options": {"responseCode": 400}}, "id": "response-validation-error", "name": "Response Validation Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"httpMethod": "GET", "path": "manager-review/{{ $parameter.manager_id }}/{{ $parameter.cycle_id }}", "responseMode": "responseNode", "options": {}}, "id": "webhook-get-team-reviews", "name": "Get Team Reviews Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 600]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT mr.*, e.first_name, e.last_name, e.position, ac.cycle_name, sa.overall_satisfaction FROM manager_reviews mr JOIN employees e ON mr.employee_id = e.employee_id JOIN appraisal_cycles ac ON mr.cycle_id = ac.id LEFT JOIN self_assessments sa ON mr.employee_id = sa.employee_id AND mr.cycle_id = sa.cycle_id WHERE mr.manager_id = '{{ $parameter.manager_id }}' AND mr.cycle_id = {{ $parameter.cycle_id }} ORDER BY e.last_name, e.first_name"}, "id": "get-team-reviews", "name": "Get Team Reviews", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [460, 600], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"team_reviews\": {{ JSON.stringify($json) }}\n}"}, "id": "response-team-reviews", "name": "Response Team Reviews", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 600]}], "connections": {"Manager Review Webhook": {"main": [[{"node": "Set Review Data", "type": "main", "index": 0}]]}, "Set Review Data": {"main": [[{"node": "Validate Required Fields", "type": "main", "index": 0}]]}, "Validate Required Fields": {"main": [[{"node": "Check Existing Review", "type": "main", "index": 0}], [{"node": "Response Validation Error", "type": "main", "index": 0}]]}, "Check Existing Review": {"main": [[{"node": "Check Is New Review", "type": "main", "index": 0}]]}, "Check Is New Review": {"main": [[{"node": "Insert New Review", "type": "main", "index": 0}], [{"node": "Update Existing Review", "type": "main", "index": 0}]]}, "Insert New Review": {"main": [[{"node": "Get Employee Info", "type": "main", "index": 0}]]}, "Update Existing Review": {"main": [[{"node": "Get Employee Info", "type": "main", "index": 0}]]}, "Get Employee Info": {"main": [[{"node": "Notify Employee Review Complete", "type": "main", "index": 0}]]}, "Notify Employee Review Complete": {"main": [[{"node": "Notify HR Review Complete", "type": "main", "index": 0}]]}, "Notify HR Review Complete": {"main": [[{"node": "Response Success", "type": "main", "index": 0}]]}, "Get Team Reviews Webhook": {"main": [[{"node": "Get Team Reviews", "type": "main", "index": 0}]]}, "Get Team Reviews": {"main": [[{"node": "Response Team Reviews", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "1"}