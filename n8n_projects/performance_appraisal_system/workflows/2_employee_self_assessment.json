{"name": "Performance Appraisal - Employee Self Assessment", "nodes": [{"parameters": {"httpMethod": "POST", "path": "self-assessment", "responseMode": "responseNode", "options": {}}, "id": "webhook-self-assessment", "name": "Self Assessment Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "employee_id", "value": "={{ $json.body.employee_id }}"}, {"name": "cycle_id", "value": "={{ $json.body.cycle_id }}"}, {"name": "achievements", "value": "={{ $json.body.achievements }}"}, {"name": "challenges", "value": "={{ $json.body.challenges }}"}, {"name": "career_aspirations", "value": "={{ $json.body.career_aspirations }}"}, {"name": "training_needs", "value": "={{ $json.body.training_needs }}"}, {"name": "overall_satisfaction", "value": "={{ $json.body.overall_satisfaction }}"}], "object": [{"name": "goals_met", "value": "={{ $json.body.goals_met }}"}, {"name": "skills_rating", "value": "={{ $json.body.skills_rating }}"}]}}, "id": "set-assessment-data", "name": "Set Assessment Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.employee_id }}", "operation": "isNotEmpty"}, {"value1": "={{ $json.cycle_id }}", "operation": "isNotEmpty"}]}}, "id": "validate-required-fields", "name": "Validate Required Fields", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM self_assessments WHERE employee_id = '{{ $json.employee_id }}' AND cycle_id = {{ $json.cycle_id }}"}, "id": "check-existing-assessment", "name": "Check Existing Assessment", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [900, 200], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"conditions": {"number": [{"value1": "={{ $json.length }}", "operation": "equal", "value2": 0}]}}, "id": "check-is-new-assessment", "name": "Check Is New Assessment", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"operation": "insert", "table": "self_assessments", "columns": "employee_id, cycle_id, achievements, challenges, goals_met, skills_rating, career_aspirations, training_needs, overall_satisfaction, status, submitted_at", "additionalFields": {"mode": "independently"}}, "id": "insert-new-assessment", "name": "Insert New Assessment", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [1340, 100], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"operation": "update", "table": "self_assessments", "updateKey": "id", "columns": "achievements, challenges, goals_met, skills_rating, career_aspirations, training_needs, overall_satisfaction, submitted_at", "additionalFields": {"mode": "independently"}}, "id": "update-existing-assessment", "name": "Update Existing Assessment", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [1340, 300], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT e.first_name, e.last_name, e.email, e.manager_id, m.email as manager_email FROM employees e LEFT JOIN employees m ON e.manager_id = m.employee_id WHERE e.employee_id = '{{ $('Set Assessment Data').first().json.employee_id }}'"}, "id": "get-employee-manager-info", "name": "Get Employee & Manager Info", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [1560, 200], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "requestMethod": "POST", "url": "={{ $json.webhook_url || 'http://localhost:5678/webhook/notify-manager-assessment-complete' }}", "jsonParameters": true, "bodyParametersJson": "={\n  \"manager_email\": \"{{ $json.manager_email }}\",\n  \"employee_name\": \"{{ $json.first_name }} {{ $json.last_name }}\",\n  \"employee_id\": \"{{ $('Set Assessment Data').first().json.employee_id }}\",\n  \"cycle_id\": \"{{ $('Set Assessment Data').first().json.cycle_id }}\",\n  \"notification_type\": \"self_assessment_complete\"\n}", "options": {}}, "id": "notify-manager-assessment-complete", "name": "Notify Manager Assessment Complete", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1780, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"Self assessment submitted successfully\",\n  \"employee_id\": \"{{ $('Set Assessment Data').first().json.employee_id }}\",\n  \"cycle_id\": \"{{ $('Set Assessment Data').first().json.cycle_id }}\",\n  \"submitted_at\": \"{{ new Date().toISOString() }}\"\n}"}, "id": "response-success", "name": "Response Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2000, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"error\": \"Missing required fields: employee_id and cycle_id are required\"\n}", "options": {"responseCode": 400}}, "id": "response-validation-error", "name": "Response Validation Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"httpMethod": "GET", "path": "self-assessment/{{ $parameter.employee_id }}/{{ $parameter.cycle_id }}", "responseMode": "responseNode", "options": {}}, "id": "webhook-get-assessment", "name": "Get Assessment Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 600]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT sa.*, ac.cycle_name, e.first_name, e.last_name FROM self_assessments sa JOIN appraisal_cycles ac ON sa.cycle_id = ac.id JOIN employees e ON sa.employee_id = e.employee_id WHERE sa.employee_id = '{{ $parameter.employee_id }}' AND sa.cycle_id = {{ $parameter.cycle_id }}"}, "id": "get-assessment-details", "name": "Get Assessment Details", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [460, 600], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"assessment\": {{ JSON.stringify($json[0] || null) }}\n}"}, "id": "response-assessment-details", "name": "Response Assessment Details", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 600]}], "connections": {"Self Assessment Webhook": {"main": [[{"node": "Set Assessment Data", "type": "main", "index": 0}]]}, "Set Assessment Data": {"main": [[{"node": "Validate Required Fields", "type": "main", "index": 0}]]}, "Validate Required Fields": {"main": [[{"node": "Check Existing Assessment", "type": "main", "index": 0}], [{"node": "Response Validation Error", "type": "main", "index": 0}]]}, "Check Existing Assessment": {"main": [[{"node": "Check Is New Assessment", "type": "main", "index": 0}]]}, "Check Is New Assessment": {"main": [[{"node": "Insert New Assessment", "type": "main", "index": 0}], [{"node": "Update Existing Assessment", "type": "main", "index": 0}]]}, "Insert New Assessment": {"main": [[{"node": "Get Employee & Manager Info", "type": "main", "index": 0}]]}, "Update Existing Assessment": {"main": [[{"node": "Get Employee & Manager Info", "type": "main", "index": 0}]]}, "Get Employee & Manager Info": {"main": [[{"node": "Notify Manager Assessment Complete", "type": "main", "index": 0}]]}, "Notify Manager Assessment Complete": {"main": [[{"node": "Response Success", "type": "main", "index": 0}]]}, "Get Assessment Webhook": {"main": [[{"node": "Get Assessment Details", "type": "main", "index": 0}]]}, "Get Assessment Details": {"main": [[{"node": "Response Assessment Details", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "1"}