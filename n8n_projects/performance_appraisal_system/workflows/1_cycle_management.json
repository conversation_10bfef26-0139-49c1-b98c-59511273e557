{"name": "Performance Appraisal - Cycle Management", "nodes": [{"parameters": {"httpMethod": "POST", "path": "create-cycle", "responseMode": "responseNode", "options": {}}, "id": "webhook-create-cycle", "name": "Create Cycle Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "cycle_name", "value": "={{ $json.body.cycle_name }}"}, {"name": "cycle_type", "value": "={{ $json.body.cycle_type }}"}, {"name": "start_date", "value": "={{ $json.body.start_date }}"}, {"name": "end_date", "value": "={{ $json.body.end_date }}"}, {"name": "self_assessment_deadline", "value": "={{ $json.body.self_assessment_deadline }}"}, {"name": "manager_review_deadline", "value": "={{ $json.body.manager_review_deadline }}"}]}}, "id": "set-cycle-data", "name": "Set Cycle Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"operation": "insert", "table": "appraisal_cycles", "columns": "cycle_name, cycle_type, start_date, end_date, self_assessment_deadline, manager_review_deadline, status", "additionalFields": {"mode": "independently"}}, "id": "insert-cycle", "name": "Insert Cycle", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [680, 300], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM employees WHERE status = 'active'"}, "id": "get-active-employees", "name": "Get Active Employees", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [900, 300], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"batchSize": 1, "options": {}}, "id": "split-employees", "name": "Split Employees", "type": "n8n-nodes-base.splitInBatches", "typeVersion": 1, "position": [1120, 300]}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "requestMethod": "POST", "url": "={{ $json.webhook_url || 'http://localhost:5678/webhook/notify-employee' }}", "jsonParameters": true, "bodyParametersJson": "={\n  \"employee_id\": \"{{ $json.employee_id }}\",\n  \"email\": \"{{ $json.email }}\",\n  \"first_name\": \"{{ $json.first_name }}\",\n  \"cycle_name\": \"{{ $('Set Cycle Data').first().json.cycle_name }}\",\n  \"self_assessment_deadline\": \"{{ $('Set Cycle Data').first().json.self_assessment_deadline }}\",\n  \"notification_type\": \"cycle_start\"\n}", "options": {}}, "id": "notify-employee", "name": "Notify Employee", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1340, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.manager_id }}", "operation": "isNotEmpty"}]}}, "id": "check-has-manager", "name": "Check Has Manager", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "requestMethod": "POST", "url": "={{ $json.webhook_url || 'http://localhost:5678/webhook/notify-manager' }}", "jsonParameters": true, "bodyParametersJson": "={\n  \"manager_id\": \"{{ $json.manager_id }}\",\n  \"employee_id\": \"{{ $json.employee_id }}\",\n  \"employee_name\": \"{{ $json.first_name }} {{ $json.last_name }}\",\n  \"cycle_name\": \"{{ $('Set Cycle Data').first().json.cycle_name }}\",\n  \"manager_review_deadline\": \"{{ $('Set Cycle Data').first().json.manager_review_deadline }}\",\n  \"notification_type\": \"cycle_start_manager\"\n}", "options": {}}, "id": "notify-manager", "name": "Notify Manager", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1780, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"Performance appraisal cycle created successfully\",\n  \"cycle_id\": {{ $('Insert Cycle').first().json.insertId }},\n  \"cycle_name\": \"{{ $('Set Cycle Data').first().json.cycle_name }}\",\n  \"employees_notified\": {{ $('Split Employees').itemMatching(0).json.total }}\n}"}, "id": "response-success", "name": "Response Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [2000, 300]}, {"parameters": {"httpMethod": "GET", "path": "cycles", "responseMode": "responseNode", "options": {}}, "id": "webhook-get-cycles", "name": "Get Cycles Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 500]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT * FROM appraisal_cycles ORDER BY created_at DESC"}, "id": "get-all-cycles", "name": "Get All Cycles", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [460, 500], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"cycles\": {{ JSON.stringify($json) }}\n}"}, "id": "response-cycles", "name": "Response Cycles", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 500]}], "connections": {"Create Cycle Webhook": {"main": [[{"node": "Set Cycle Data", "type": "main", "index": 0}]]}, "Set Cycle Data": {"main": [[{"node": "Insert Cycle", "type": "main", "index": 0}]]}, "Insert Cycle": {"main": [[{"node": "Get Active Employees", "type": "main", "index": 0}]]}, "Get Active Employees": {"main": [[{"node": "Split Employees", "type": "main", "index": 0}]]}, "Split Employees": {"main": [[{"node": "Notify Employee", "type": "main", "index": 0}]]}, "Notify Employee": {"main": [[{"node": "Check Has Manager", "type": "main", "index": 0}]]}, "Check Has Manager": {"main": [[{"node": "Notify Manager", "type": "main", "index": 0}], [{"node": "Response Success", "type": "main", "index": 0}]]}, "Notify Manager": {"main": [[{"node": "Response Success", "type": "main", "index": 0}]]}, "Get Cycles Webhook": {"main": [[{"node": "Get All Cycles", "type": "main", "index": 0}]]}, "Get All Cycles": {"main": [[{"node": "Response Cycles", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "1"}