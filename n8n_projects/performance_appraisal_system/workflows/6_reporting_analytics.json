{"name": "Performance Appraisal - Reporting & Analytics", "nodes": [{"parameters": {"httpMethod": "GET", "path": "reports/performance/{{ $parameter.cycle_id }}", "responseMode": "responseNode", "options": {}}, "id": "webhook-performance-report", "name": "Performance Report Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  ac.cycle_name,\n  ac.cycle_type,\n  ac.start_date,\n  ac.end_date,\n  COUNT(DISTINCT e.employee_id) as total_employees,\n  COUNT(DISTINCT sa.employee_id) as self_assessments_completed,\n  COUNT(DISTINCT mr.employee_id) as manager_reviews_completed,\n  ROUND((COUNT(DISTINCT sa.employee_id) / COUNT(DISTINCT e.employee_id)) * 100, 2) as self_assessment_completion_rate,\n  ROUND((COUNT(DISTINCT mr.employee_id) / COUNT(DISTINCT e.employee_id)) * 100, 2) as manager_review_completion_rate\nFROM appraisal_cycles ac\nLEFT JOIN employees e ON e.status = 'active'\nLEFT JOIN self_assessments sa ON sa.cycle_id = ac.id AND sa.employee_id = e.employee_id AND sa.status = 'submitted'\nLEFT JOIN manager_reviews mr ON mr.cycle_id = ac.id AND mr.employee_id = e.employee_id AND mr.status = 'submitted'\nWHERE ac.id = {{ $parameter.cycle_id }}\nGROUP BY ac.id"}, "id": "get-cycle-overview", "name": "Get Cycle Overview", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [460, 300], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  e.department,\n  COUNT(DISTINCT e.employee_id) as total_employees,\n  COUNT(DISTINCT sa.employee_id) as self_assessments_completed,\n  COUNT(DISTINCT mr.employee_id) as manager_reviews_completed,\n  AVG(mr.performance_rating) as avg_performance_rating,\n  AVG(sa.overall_satisfaction) as avg_satisfaction\nFROM employees e\nLEFT JOIN self_assessments sa ON sa.employee_id = e.employee_id AND sa.cycle_id = {{ $parameter.cycle_id }}\nLEFT JOIN manager_reviews mr ON mr.employee_id = e.employee_id AND mr.cycle_id = {{ $parameter.cycle_id }}\nWHERE e.status = 'active'\nGROUP BY e.department\nORDER BY e.department"}, "id": "get-department-breakdown", "name": "Get Department Breakdown", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [680, 300], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  mr.performance_rating,\n  COUNT(*) as count,\n  ROUND((COUNT(*) / (SELECT COUNT(*) FROM manager_reviews WHERE cycle_id = {{ $parameter.cycle_id }} AND status = 'submitted')) * 100, 2) as percentage\nFROM manager_reviews mr\nWHERE mr.cycle_id = {{ $parameter.cycle_id }} AND mr.status = 'submitted'\nGROUP BY mr.performance_rating\nORDER BY mr.performance_rating"}, "id": "get-performance-distribution", "name": "Get Performance Distribution", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [900, 300], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  g.status,\n  COUNT(*) as count,\n  AVG(g.achievement_percentage) as avg_achievement\nFROM goals g\nWHERE g.cycle_id = {{ $parameter.cycle_id }}\nGROUP BY g.status\nORDER BY \n  CASE g.status \n    WHEN 'completed' THEN 1\n    WHEN 'in_progress' THEN 2\n    WHEN 'not_started' THEN 3\n    WHEN 'cancelled' THEN 4\n  END"}, "id": "get-goal-statistics", "name": "Get Goal Statistics", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [1120, 300], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  mr.promotion_readiness,\n  COUNT(*) as count\nFROM manager_reviews mr\nWHERE mr.cycle_id = {{ $parameter.cycle_id }} AND mr.status = 'submitted'\nGROUP BY mr.promotion_readiness\nORDER BY \n  CASE mr.promotion_readiness\n    WHEN 'ready' THEN 1\n    WHEN 'developing' THEN 2\n    WHEN 'not_ready' THEN 3\n  END"}, "id": "get-promotion-readiness", "name": "Get Promotion Readiness", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [1340, 300], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"jsCode": "// Combine all report data\nconst cycleOverview = $('Get Cycle Overview').first().json;\nconst departmentBreakdown = $('Get Department Breakdown').all();\nconst performanceDistribution = $('Get Performance Distribution').all();\nconst goalStatistics = $('Get Goal Statistics').all();\nconst promotionReadiness = $('Get Promotion Readiness').all();\n\n// Calculate additional metrics\nconst totalGoals = goalStatistics.reduce((sum, stat) => sum + stat.count, 0);\nconst completedGoals = goalStatistics.find(stat => stat.status === 'completed')?.count || 0;\nconst goalCompletionRate = totalGoals > 0 ? Math.round((completedGoals / totalGoals) * 100) : 0;\n\n// Calculate average performance rating\nconst avgPerformanceRating = performanceDistribution.reduce((sum, dist) => {\n  return sum + (dist.performance_rating * dist.count);\n}, 0) / performanceDistribution.reduce((sum, dist) => sum + dist.count, 0);\n\n// Generate insights\nconst insights = [];\n\nif (cycleOverview.self_assessment_completion_rate < 80) {\n  insights.push({\n    type: 'warning',\n    message: `Self-assessment completion rate is ${cycleOverview.self_assessment_completion_rate}%. Consider sending reminders.`\n  });\n}\n\nif (cycleOverview.manager_review_completion_rate < 70) {\n  insights.push({\n    type: 'warning', \n    message: `Manager review completion rate is ${cycleOverview.manager_review_completion_rate}%. Follow up with managers.`\n  });\n}\n\nif (goalCompletionRate > 85) {\n  insights.push({\n    type: 'success',\n    message: `Excellent goal completion rate of ${goalCompletionRate}%!`\n  });\n} else if (goalCompletionRate < 60) {\n  insights.push({\n    type: 'warning',\n    message: `Goal completion rate is ${goalCompletionRate}%. Review goal setting process.`\n  });\n}\n\nconst highPerformers = promotionReadiness.find(pr => pr.promotion_readiness === 'ready')?.count || 0;\nif (highPerformers > 0) {\n  insights.push({\n    type: 'info',\n    message: `${highPerformers} employees are ready for promotion. Consider succession planning.`\n  });\n}\n\nreturn [{\n  json: {\n    success: true,\n    report: {\n      cycle_overview: cycleOverview,\n      department_breakdown: departmentBreakdown,\n      performance_distribution: performanceDistribution,\n      goal_statistics: goalStatistics,\n      promotion_readiness: promotionReadiness,\n      key_metrics: {\n        goal_completion_rate: goalCompletionRate,\n        average_performance_rating: Math.round(avgPerformanceRating * 10) / 10,\n        total_goals: totalGoals,\n        high_performers: highPerformers\n      },\n      insights: insights,\n      generated_at: new Date().toISOString()\n    }\n  }\n}];"}, "id": "compile-performance-report", "name": "Compile Performance Report", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [1560, 300]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json.report, null, 2) }}"}, "id": "response-performance-report", "name": "Response Performance Report", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1780, 300]}, {"parameters": {"httpMethod": "GET", "path": "reports/employee/{{ $parameter.employee_id }}/{{ $parameter.cycle_id }}", "responseMode": "responseNode", "options": {}}, "id": "webhook-employee-report", "name": "Employee Report Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 600]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  e.employee_id,\n  e.first_name,\n  e.last_name,\n  e.email,\n  e.department,\n  e.position,\n  e.hire_date,\n  sa.achievements,\n  sa.challenges,\n  sa.career_aspirations,\n  sa.training_needs,\n  sa.overall_satisfaction,\n  sa.submitted_at as self_assessment_date,\n  mr.performance_rating,\n  mr.strengths,\n  mr.areas_for_improvement,\n  mr.goal_achievement_rating,\n  mr.promotion_readiness,\n  mr.salary_recommendation,\n  mr.comments as manager_comments,\n  mr.submitted_at as manager_review_date\nFROM employees e\nLEFT JOIN self_assessments sa ON sa.employee_id = e.employee_id AND sa.cycle_id = {{ $parameter.cycle_id }}\nLEFT JOIN manager_reviews mr ON mr.employee_id = e.employee_id AND mr.cycle_id = {{ $parameter.cycle_id }}\nWHERE e.employee_id = '{{ $parameter.employee_id }}'"}, "id": "get-employee-details", "name": "Get Employee Details", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [460, 600], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  goal_title,\n  goal_description,\n  target_date,\n  status,\n  achievement_percentage,\n  manager_rating,\n  employee_rating,\n  comments\nFROM goals\nWHERE employee_id = '{{ $parameter.employee_id }}' AND cycle_id = {{ $parameter.cycle_id }}\nORDER BY created_at"}, "id": "get-employee-goals", "name": "Get Employee Goals", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [680, 600], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  development_area,\n  action_items,\n  timeline,\n  resources_needed,\n  success_metrics,\n  manager_support,\n  status\nFROM development_plans\nWHERE employee_id = '{{ $parameter.employee_id }}' AND cycle_id = {{ $parameter.cycle_id }}\nORDER BY created_at"}, "id": "get-development-plans", "name": "Get Development Plans", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [900, 600], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"jsCode": "// Compile individual employee report\nconst employeeDetails = $('Get Employee Details').first().json;\nconst employeeGoals = $('Get Employee Goals').all();\nconst developmentPlans = $('Get Development Plans').all();\n\n// Calculate goal metrics\nconst totalGoals = employeeGoals.length;\nconst completedGoals = employeeGoals.filter(goal => goal.status === 'completed').length;\nconst avgAchievement = employeeGoals.reduce((sum, goal) => sum + goal.achievement_percentage, 0) / totalGoals;\n\n// Generate recommendations\nconst recommendations = [];\n\nif (employeeDetails.overall_satisfaction < 7) {\n  recommendations.push({\n    area: 'Job Satisfaction',\n    recommendation: 'Schedule one-on-one to discuss satisfaction concerns and potential improvements.'\n  });\n}\n\nif (employeeDetails.performance_rating >= 4) {\n  recommendations.push({\n    area: 'High Performance',\n    recommendation: 'Consider for stretch assignments, mentoring opportunities, or leadership development.'\n  });\n}\n\nif (completedGoals / totalGoals < 0.7) {\n  recommendations.push({\n    area: 'Goal Achievement',\n    recommendation: 'Review goal setting process and provide additional support for goal completion.'\n  });\n}\n\nif (employeeDetails.promotion_readiness === 'ready') {\n  recommendations.push({\n    area: 'Career Development',\n    recommendation: 'Employee is ready for promotion. Consider available opportunities and succession planning.'\n  });\n}\n\nreturn [{\n  json: {\n    success: true,\n    employee_report: {\n      employee_info: employeeDetails,\n      goals: employeeGoals,\n      development_plans: developmentPlans,\n      summary: {\n        total_goals: totalGoals,\n        completed_goals: completedGoals,\n        goal_completion_rate: Math.round((completedGoals / totalGoals) * 100),\n        average_achievement: Math.round(avgAchievement),\n        performance_rating: employeeDetails.performance_rating,\n        satisfaction_score: employeeDetails.overall_satisfaction\n      },\n      recommendations: recommendations,\n      generated_at: new Date().toISOString()\n    }\n  }\n}];"}, "id": "compile-employee-report", "name": "Compile Employee Report", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [1120, 600]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json.employee_report, null, 2) }}"}, "id": "response-employee-report", "name": "Response Employee Report", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 600]}], "connections": {"Performance Report Webhook": {"main": [[{"node": "Get Cycle Overview", "type": "main", "index": 0}]]}, "Get Cycle Overview": {"main": [[{"node": "Get Department Breakdown", "type": "main", "index": 0}]]}, "Get Department Breakdown": {"main": [[{"node": "Get Performance Distribution", "type": "main", "index": 0}]]}, "Get Performance Distribution": {"main": [[{"node": "Get Goal Statistics", "type": "main", "index": 0}]]}, "Get Goal Statistics": {"main": [[{"node": "Get Promotion Readiness", "type": "main", "index": 0}]]}, "Get Promotion Readiness": {"main": [[{"node": "Compile Performance Report", "type": "main", "index": 0}]]}, "Compile Performance Report": {"main": [[{"node": "Response Performance Report", "type": "main", "index": 0}]]}, "Employee Report Webhook": {"main": [[{"node": "Get Employee Details", "type": "main", "index": 0}]]}, "Get Employee Details": {"main": [[{"node": "Get Employee Goals", "type": "main", "index": 0}]]}, "Get Employee Goals": {"main": [[{"node": "Get Development Plans", "type": "main", "index": 0}]]}, "Get Development Plans": {"main": [[{"node": "Compile Employee Report", "type": "main", "index": 0}]]}, "Compile Employee Report": {"main": [[{"node": "Response Employee Report", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "1"}