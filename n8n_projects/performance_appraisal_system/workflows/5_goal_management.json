{"name": "Performance Appraisal - Goal Management", "nodes": [{"parameters": {"httpMethod": "POST", "path": "goals", "responseMode": "responseNode", "options": {}}, "id": "webhook-create-goal", "name": "Create Goal Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "employee_id", "value": "={{ $json.body.employee_id }}"}, {"name": "cycle_id", "value": "={{ $json.body.cycle_id }}"}, {"name": "goal_title", "value": "={{ $json.body.goal_title }}"}, {"name": "goal_description", "value": "={{ $json.body.goal_description }}"}, {"name": "target_date", "value": "={{ $json.body.target_date }}"}, {"name": "status", "value": "={{ $json.body.status || 'not_started' }}"}, {"name": "comments", "value": "={{ $json.body.comments }}"}], "number": [{"name": "weight", "value": "={{ $json.body.weight || 1.0 }}"}, {"name": "achievement_percentage", "value": "={{ $json.body.achievement_percentage || 0 }}"}, {"name": "manager_rating", "value": "={{ $json.body.manager_rating }}"}, {"name": "employee_rating", "value": "={{ $json.body.employee_rating }}"}]}}, "id": "set-goal-data", "name": "Set Goal Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.employee_id }}", "operation": "isNotEmpty"}, {"value1": "={{ $json.cycle_id }}", "operation": "isNotEmpty"}, {"value1": "={{ $json.goal_title }}", "operation": "isNotEmpty"}]}}, "id": "validate-goal-data", "name": "Validate Goal Data", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"operation": "insert", "table": "goals", "columns": "employee_id, cycle_id, goal_title, goal_description, target_date, weight, status, achievement_percentage, manager_rating, employee_rating, comments", "additionalFields": {"mode": "independently"}}, "id": "insert-goal", "name": "Insert Goal", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [900, 200], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"Goal created successfully\",\n  \"goal_id\": {{ $json.insertId }},\n  \"employee_id\": \"{{ $('Set Goal Data').first().json.employee_id }}\",\n  \"goal_title\": \"{{ $('Set Goal Data').first().json.goal_title }}\"\n}"}, "id": "response-goal-created", "name": "Response Goal Created", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"error\": \"Missing required fields: employee_id, cycle_id, and goal_title are required\"\n}", "options": {"responseCode": 400}}, "id": "response-validation-error", "name": "Response Validation Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"httpMethod": "GET", "path": "goals/{{ $parameter.employee_id }}/{{ $parameter.cycle_id }}", "responseMode": "responseNode", "options": {}}, "id": "webhook-get-goals", "name": "Get Goals Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 600]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT g.*, ac.cycle_name, e.first_name, e.last_name FROM goals g JOIN appraisal_cycles ac ON g.cycle_id = ac.id JOIN employees e ON g.employee_id = e.employee_id WHERE g.employee_id = '{{ $parameter.employee_id }}' AND g.cycle_id = {{ $parameter.cycle_id }} ORDER BY g.created_at DESC"}, "id": "get-employee-goals", "name": "Get Employee Goals", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [460, 600], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"goals\": {{ JSON.stringify($json) }}\n}"}, "id": "response-employee-goals", "name": "Response Employee Goals", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 600]}, {"parameters": {"httpMethod": "PUT", "path": "goals/{{ $parameter.goal_id }}", "responseMode": "responseNode", "options": {}}, "id": "webhook-update-goal", "name": "Update Goal Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 900]}, {"parameters": {"values": {"string": [{"name": "goal_id", "value": "={{ $parameter.goal_id }}"}, {"name": "goal_title", "value": "={{ $json.body.goal_title }}"}, {"name": "goal_description", "value": "={{ $json.body.goal_description }}"}, {"name": "target_date", "value": "={{ $json.body.target_date }}"}, {"name": "status", "value": "={{ $json.body.status }}"}, {"name": "comments", "value": "={{ $json.body.comments }}"}], "number": [{"name": "weight", "value": "={{ $json.body.weight }}"}, {"name": "achievement_percentage", "value": "={{ $json.body.achievement_percentage }}"}, {"name": "manager_rating", "value": "={{ $json.body.manager_rating }}"}, {"name": "employee_rating", "value": "={{ $json.body.employee_rating }}"}]}}, "id": "set-update-goal-data", "name": "Set Update Goal Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 900]}, {"parameters": {"operation": "update", "table": "goals", "updateKey": "id", "columns": "goal_title, goal_description, target_date, weight, status, achievement_percentage, manager_rating, employee_rating, comments", "additionalFields": {"mode": "independently"}}, "id": "update-goal", "name": "Update Goal", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [680, 900], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"Goal updated successfully\",\n  \"goal_id\": \"{{ $('Set Update Goal Data').first().json.goal_id }}\",\n  \"updated_fields\": {{ $json.affectedRows }}\n}"}, "id": "response-goal-updated", "name": "Response Goal Updated", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 900]}, {"parameters": {"httpMethod": "GET", "path": "goals/progress/{{ $parameter.employee_id }}/{{ $parameter.cycle_id }}", "responseMode": "responseNode", "options": {}}, "id": "webhook-goal-progress", "name": "Goal Progress Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 1200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT \n  COUNT(*) as total_goals,\n  COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_goals,\n  COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_goals,\n  COUNT(CASE WHEN status = 'not_started' THEN 1 END) as not_started_goals,\n  AVG(achievement_percentage) as avg_achievement,\n  AVG(CASE WHEN manager_rating IS NOT NULL THEN manager_rating END) as avg_manager_rating,\n  AVG(CASE WHEN employee_rating IS NOT NULL THEN employee_rating END) as avg_employee_rating\nFROM goals \nWHERE employee_id = '{{ $parameter.employee_id }}' AND cycle_id = {{ $parameter.cycle_id }}"}, "id": "calculate-goal-progress", "name": "Calculate Goal Progress", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [460, 1200], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"progress_summary\": {\n    \"total_goals\": {{ $json[0].total_goals }},\n    \"completed_goals\": {{ $json[0].completed_goals }},\n    \"in_progress_goals\": {{ $json[0].in_progress_goals }},\n    \"not_started_goals\": {{ $json[0].not_started_goals }},\n    \"completion_rate\": {{ Math.round(($json[0].completed_goals / $json[0].total_goals) * 100) }}%,\n    \"average_achievement\": {{ Math.round($json[0].avg_achievement || 0) }}%,\n    \"average_manager_rating\": {{ Math.round(($json[0].avg_manager_rating || 0) * 10) / 10 }},\n    \"average_employee_rating\": {{ Math.round(($json[0].avg_employee_rating || 0) * 10) / 10 }}\n  }\n}"}, "id": "response-goal-progress", "name": "Response Goal Progress", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 1200]}], "connections": {"Create Goal Webhook": {"main": [[{"node": "Set Goal Data", "type": "main", "index": 0}]]}, "Set Goal Data": {"main": [[{"node": "Validate Goal Data", "type": "main", "index": 0}]]}, "Validate Goal Data": {"main": [[{"node": "Insert Goal", "type": "main", "index": 0}], [{"node": "Response Validation Error", "type": "main", "index": 0}]]}, "Insert Goal": {"main": [[{"node": "Response Goal Created", "type": "main", "index": 0}]]}, "Get Goals Webhook": {"main": [[{"node": "Get Employee Goals", "type": "main", "index": 0}]]}, "Get Employee Goals": {"main": [[{"node": "Response Employee Goals", "type": "main", "index": 0}]]}, "Update Goal Webhook": {"main": [[{"node": "Set Update Goal Data", "type": "main", "index": 0}]]}, "Set Update Goal Data": {"main": [[{"node": "Update Goal", "type": "main", "index": 0}]]}, "Update Goal": {"main": [[{"node": "Response Goal Updated", "type": "main", "index": 0}]]}, "Goal Progress Webhook": {"main": [[{"node": "Calculate Goal Progress", "type": "main", "index": 0}]]}, "Calculate Goal Progress": {"main": [[{"node": "Response Goal Progress", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "1"}