{"name": "Performance Appraisal - Development Planning", "nodes": [{"parameters": {"httpMethod": "POST", "path": "development-plan", "responseMode": "responseNode", "options": {}}, "id": "webhook-create-development-plan", "name": "Create Development Plan Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "employee_id", "value": "={{ $json.body.employee_id }}"}, {"name": "cycle_id", "value": "={{ $json.body.cycle_id }}"}, {"name": "development_area", "value": "={{ $json.body.development_area }}"}, {"name": "timeline", "value": "={{ $json.body.timeline }}"}, {"name": "resources_needed", "value": "={{ $json.body.resources_needed }}"}, {"name": "success_metrics", "value": "={{ $json.body.success_metrics }}"}, {"name": "manager_support", "value": "={{ $json.body.manager_support }}"}, {"name": "status", "value": "={{ $json.body.status || 'planned' }}"}], "object": [{"name": "action_items", "value": "={{ $json.body.action_items }}"}]}}, "id": "set-development-plan-data", "name": "Set Development Plan Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.employee_id }}", "operation": "isNotEmpty"}, {"value1": "={{ $json.cycle_id }}", "operation": "isNotEmpty"}, {"value1": "={{ $json.development_area }}", "operation": "isNotEmpty"}]}}, "id": "validate-development-plan-data", "name": "Validate Development Plan Data", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"operation": "insert", "table": "development_plans", "columns": "employee_id, cycle_id, development_area, action_items, timeline, resources_needed, success_metrics, manager_support, status", "additionalFields": {"mode": "independently"}}, "id": "insert-development-plan", "name": "Insert Development Plan", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [900, 200], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT e.first_name, e.last_name, e.email, m.email as manager_email FROM employees e LEFT JOIN employees m ON e.manager_id = m.employee_id WHERE e.employee_id = '{{ $('Set Development Plan Data').first().json.employee_id }}'"}, "id": "get-employee-and-manager-info", "name": "Get Employee and Manager Info", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [1120, 200], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "requestMethod": "POST", "url": "={{ $json.webhook_url || 'http://localhost:5678/webhook/notify-employee' }}", "jsonParameters": true, "bodyParametersJson": "={\n  \"employee_email\": \"{{ $json.email }}\",\n  \"employee_name\": \"{{ $json.first_name }} {{ $json.last_name }}\",\n  \"employee_id\": \"{{ $('Set Development Plan Data').first().json.employee_id }}\",\n  \"development_area\": \"{{ $('Set Development Plan Data').first().json.development_area }}\",\n  \"timeline\": \"{{ $('Set Development Plan Data').first().json.timeline }}\",\n  \"notification_type\": \"development_plan_created\"\n}", "options": {}}, "id": "notify-employee-development-plan", "name": "Notify Employee Development Plan", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1340, 200]}, {"parameters": {"authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "requestMethod": "POST", "url": "={{ $json.webhook_url || 'http://localhost:5678/webhook/notify-manager' }}", "jsonParameters": true, "bodyParametersJson": "={\n  \"manager_email\": \"{{ $('Get Employee and Manager Info').first().json.manager_email }}\",\n  \"employee_name\": \"{{ $('Get Employee and Manager Info').first().json.first_name }} {{ $('Get Employee and Manager Info').first().json.last_name }}\",\n  \"employee_id\": \"{{ $('Set Development Plan Data').first().json.employee_id }}\",\n  \"development_area\": \"{{ $('Set Development Plan Data').first().json.development_area }}\",\n  \"manager_support\": \"{{ $('Set Development Plan Data').first().json.manager_support }}\",\n  \"notification_type\": \"development_plan_manager_action\"\n}", "options": {}}, "id": "notify-manager-development-plan", "name": "Notify Manager Development Plan", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "position": [1560, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"Development plan created successfully\",\n  \"plan_id\": {{ $('Insert Development Plan').first().json.insertId }},\n  \"employee_id\": \"{{ $('Set Development Plan Data').first().json.employee_id }}\",\n  \"development_area\": \"{{ $('Set Development Plan Data').first().json.development_area }}\"\n}"}, "id": "response-development-plan-created", "name": "Response Development Plan Created", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1780, 200]}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": false,\n  \"error\": \"Missing required fields: employee_id, cycle_id, and development_area are required\"\n}", "options": {"responseCode": 400}}, "id": "response-validation-error", "name": "Response Validation Error", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 400]}, {"parameters": {"httpMethod": "GET", "path": "development-plan/{{ $parameter.employee_id }}/{{ $parameter.cycle_id }}", "responseMode": "responseNode", "options": {}}, "id": "webhook-get-development-plans", "name": "Get Development Plans Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 600]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT dp.*, ac.cycle_name, e.first_name, e.last_name FROM development_plans dp JOIN appraisal_cycles ac ON dp.cycle_id = ac.id JOIN employees e ON dp.employee_id = e.employee_id WHERE dp.employee_id = '{{ $parameter.employee_id }}' AND dp.cycle_id = {{ $parameter.cycle_id }} ORDER BY dp.created_at DESC"}, "id": "get-employee-development-plans", "name": "Get Employee Development Plans", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [460, 600], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"development_plans\": {{ JSON.stringify($json) }}\n}"}, "id": "response-development-plans", "name": "Response Development Plans", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 600]}, {"parameters": {"httpMethod": "PUT", "path": "development-plan/{{ $parameter.plan_id }}/progress", "responseMode": "responseNode", "options": {}}, "id": "webhook-update-development-progress", "name": "Update Development Progress Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 900]}, {"parameters": {"values": {"string": [{"name": "plan_id", "value": "={{ $parameter.plan_id }}"}, {"name": "status", "value": "={{ $json.body.status }}"}, {"name": "progress_notes", "value": "={{ $json.body.progress_notes }}"}, {"name": "completion_date", "value": "={{ $json.body.completion_date }}"}], "object": [{"name": "updated_action_items", "value": "={{ $json.body.updated_action_items }}"}]}}, "id": "set-progress-update-data", "name": "Set Progress Update Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 900]}, {"parameters": {"operation": "update", "table": "development_plans", "updateKey": "id", "columns": "status, action_items", "additionalFields": {"mode": "independently"}}, "id": "update-development-progress", "name": "Update Development Progress", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [680, 900], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"operation": "insert", "table": "development_progress_log", "columns": "plan_id, progress_notes, updated_by, created_at", "additionalFields": {"mode": "independently"}}, "id": "log-progress-update", "name": "Log Progress Update", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [900, 900], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"Development plan progress updated successfully\",\n  \"plan_id\": \"{{ $('Set Progress Update Data').first().json.plan_id }}\",\n  \"new_status\": \"{{ $('Set Progress Update Data').first().json.status }}\"\n}"}, "id": "response-progress-updated", "name": "Response Progress Updated", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1120, 900]}, {"parameters": {"httpMethod": "GET", "path": "development-plan/templates", "responseMode": "responseNode", "options": {}}, "id": "webhook-get-development-templates", "name": "Get Development Templates Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 1200]}, {"parameters": {"jsCode": "// Development plan templates for common areas\nconst templates = {\n  \"Leadership Development\": {\n    \"action_items\": [\n      \"Complete leadership assessment\",\n      \"Attend leadership training program\",\n      \"Shadow senior leader for 2 weeks\",\n      \"Lead cross-functional project\",\n      \"Receive 360-degree feedback\"\n    ],\n    \"timeline\": \"6-12 months\",\n    \"resources_needed\": \"Leadership training budget, mentorship program, project assignment\",\n    \"success_metrics\": \"360 feedback improvement, successful project delivery, promotion readiness assessment\",\n    \"manager_support\": \"Provide mentorship, assign stretch projects, regular check-ins\"\n  },\n  \"Technical Skills\": {\n    \"action_items\": [\n      \"Identify specific technical skills gap\",\n      \"Enroll in relevant courses/certifications\",\n      \"Practice through hands-on projects\",\n      \"Join technical communities\",\n      \"Present learnings to team\"\n    ],\n    \"timeline\": \"3-6 months\",\n    \"resources_needed\": \"Training budget, time allocation, practice projects\",\n    \"success_metrics\": \"Certification completion, project application, peer recognition\",\n    \"manager_support\": \"Approve training time, provide practice opportunities, regular progress reviews\"\n  },\n  \"Communication Skills\": {\n    \"action_items\": [\n      \"Take communication skills assessment\",\n      \"Join public speaking group (Toastmasters)\",\n      \"Practice presentation skills\",\n      \"Seek feedback on communication style\",\n      \"Lead team meetings\"\n    ],\n    \"timeline\": \"4-8 months\",\n    \"resources_needed\": \"Communication training, presentation opportunities, feedback sessions\",\n    \"success_metrics\": \"Improved presentation ratings, increased meeting leadership, positive feedback\",\n    \"manager_support\": \"Provide presentation opportunities, give constructive feedback, encourage participation\"\n  },\n  \"Project Management\": {\n    \"action_items\": [\n      \"Complete PM certification (PMP/Agile)\",\n      \"Shadow experienced project manager\",\n      \"Lead small project independently\",\n      \"Learn PM tools and methodologies\",\n      \"Build stakeholder management skills\"\n    ],\n    \"timeline\": \"6-9 months\",\n    \"resources_needed\": \"Certification fees, project assignments, PM tools access\",\n    \"success_metrics\": \"Certification achievement, successful project delivery, stakeholder satisfaction\",\n    \"manager_support\": \"Assign appropriate projects, provide PM mentorship, support certification pursuit\"\n  },\n  \"Customer Focus\": {\n    \"action_items\": [\n      \"Spend time with customer-facing teams\",\n      \"Attend customer meetings/calls\",\n      \"Analyze customer feedback data\",\n      \"Develop customer empathy skills\",\n      \"Implement customer-focused improvements\"\n    ],\n    \"timeline\": \"3-6 months\",\n    \"resources_needed\": \"Customer interaction opportunities, feedback data access, improvement project budget\",\n    \"success_metrics\": \"Customer satisfaction scores, implemented improvements, customer feedback\",\n    \"manager_support\": \"Facilitate customer interactions, support improvement initiatives, share customer insights\"\n  }\n};\n\nreturn [{\n  json: {\n    success: true,\n    templates: templates,\n    available_areas: Object.keys(templates)\n  }\n}];"}, "id": "generate-development-templates", "name": "Generate Development Templates", "type": "n8n-nodes-base.code", "typeVersion": 1, "position": [460, 1200]}, {"parameters": {"respondWith": "json", "responseBody": "={{ JSON.stringify($json, null, 2) }}"}, "id": "response-development-templates", "name": "Response Development Templates", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 1200]}], "connections": {"Create Development Plan Webhook": {"main": [[{"node": "Set Development Plan Data", "type": "main", "index": 0}]]}, "Set Development Plan Data": {"main": [[{"node": "Validate Development Plan Data", "type": "main", "index": 0}]]}, "Validate Development Plan Data": {"main": [[{"node": "Insert Development Plan", "type": "main", "index": 0}], [{"node": "Response Validation Error", "type": "main", "index": 0}]]}, "Insert Development Plan": {"main": [[{"node": "Get Employee and Manager Info", "type": "main", "index": 0}]]}, "Get Employee and Manager Info": {"main": [[{"node": "Notify Employee Development Plan", "type": "main", "index": 0}]]}, "Notify Employee Development Plan": {"main": [[{"node": "Notify Manager Development Plan", "type": "main", "index": 0}]]}, "Notify Manager Development Plan": {"main": [[{"node": "Response Development Plan Created", "type": "main", "index": 0}]]}, "Get Development Plans Webhook": {"main": [[{"node": "Get Employee Development Plans", "type": "main", "index": 0}]]}, "Get Employee Development Plans": {"main": [[{"node": "Response Development Plans", "type": "main", "index": 0}]]}, "Update Development Progress Webhook": {"main": [[{"node": "Set Progress Update Data", "type": "main", "index": 0}]]}, "Set Progress Update Data": {"main": [[{"node": "Update Development Progress", "type": "main", "index": 0}]]}, "Update Development Progress": {"main": [[{"node": "Log Progress Update", "type": "main", "index": 0}]]}, "Log Progress Update": {"main": [[{"node": "Response Progress Updated", "type": "main", "index": 0}]]}, "Get Development Templates Webhook": {"main": [[{"node": "Generate Development Templates", "type": "main", "index": 0}]]}, "Generate Development Templates": {"main": [[{"node": "Response Development Templates", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "1"}