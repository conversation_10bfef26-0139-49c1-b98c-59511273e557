{"name": "Performance Appraisal - Notification System", "nodes": [{"parameters": {"httpMethod": "POST", "path": "notify-employee", "responseMode": "responseNode", "options": {}}, "id": "webhook-notify-employee", "name": "Notify Employee Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "notification_type", "value": "={{ $json.body.notification_type }}"}, {"name": "employee_id", "value": "={{ $json.body.employee_id }}"}, {"name": "email", "value": "={{ $json.body.email }}"}, {"name": "first_name", "value": "={{ $json.body.first_name }}"}, {"name": "cycle_name", "value": "={{ $json.body.cycle_name }}"}, {"name": "self_assessment_deadline", "value": "={{ $json.body.self_assessment_deadline }}"}]}}, "id": "set-employee-notification-data", "name": "Set Employee Notification Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 300]}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.notification_type }}", "operation": "equal", "value2": "cycle_start"}]}}, "id": "check-notification-type", "name": "Check Notification Type", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [680, 300]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.email }}", "subject": "Performance Appraisal Cycle Started - {{ $json.cycle_name }}", "emailFormat": "html", "html": "<!DOCTYPE html>\n<html>\n<head>\n    <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background-color: #4CAF50; color: white; padding: 20px; text-align: center; }\n        .content { padding: 20px; background-color: #f9f9f9; }\n        .button { background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 0; }\n        .deadline { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin: 10px 0; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>Performance Appraisal Started</h1>\n        </div>\n        <div class=\"content\">\n            <p>Dear {{ $json.first_name }},</p>\n            \n            <p>Your performance appraisal cycle <strong>\"{{ $json.cycle_name }}\"</strong> has started.</p>\n            \n            <div class=\"deadline\">\n                <h3>📅 Important Dates:</h3>\n                <ul>\n                    <li><strong>Self-Assessment Deadline:</strong> {{ $json.self_assessment_deadline }}</li>\n                </ul>\n            </div>\n            \n            <h3>📝 Next Steps:</h3>\n            <ol>\n                <li>Complete your self-assessment</li>\n                <li>Review your goals and achievements</li>\n                <li>Prepare for your manager discussion</li>\n            </ol>\n            \n            <p><a href=\"{{ $json.self_assessment_url || 'http://localhost:5678/self-assessment' }}\" class=\"button\">Start Self-Assessment</a></p>\n            \n            <p>If you have any questions, please contact HR.</p>\n            \n            <p>Best regards,<br>HR Team</p>\n        </div>\n    </div>\n</body>\n</html>", "options": {}}, "id": "send-cycle-start-email", "name": "Send Cycle Start Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [900, 200], "credentials": {"smtp": {"id": "company-smtp", "name": "Company SMTP"}}}, {"parameters": {"httpMethod": "POST", "path": "notify-manager", "responseMode": "responseNode", "options": {}}, "id": "webhook-notify-manager", "name": "Notify Manager Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 600]}, {"parameters": {"values": {"string": [{"name": "notification_type", "value": "={{ $json.body.notification_type }}"}, {"name": "manager_id", "value": "={{ $json.body.manager_id }}"}, {"name": "employee_id", "value": "={{ $json.body.employee_id }}"}, {"name": "employee_name", "value": "={{ $json.body.employee_name }}"}, {"name": "cycle_name", "value": "={{ $json.body.cycle_name }}"}, {"name": "manager_review_deadline", "value": "={{ $json.body.manager_review_deadline }}"}]}}, "id": "set-manager-notification-data", "name": "Set Manager Notification Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [460, 600]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT email, first_name FROM employees WHERE employee_id = '{{ $json.manager_id }}'"}, "id": "get-manager-email", "name": "Get Manager Email", "type": "n8n-nodes-base.mysql", "typeVersion": 1, "position": [680, 600], "credentials": {"mysql": {"id": "mysql-hr-db", "name": "HR Database"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{ $('Set Manager Notification Data').first().json.notification_type }}", "operation": "equal", "value2": "self_assessment_complete"}]}}, "id": "check-manager-notification-type", "name": "Check Manager Notification Type", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [900, 600]}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.email }}", "subject": "Self-Assessment Completed - {{ $('Set Manager Notification Data').first().json.employee_name }}", "emailFormat": "html", "html": "<!DOCTYPE html>\n<html>\n<head>\n    <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background-color: #2196F3; color: white; padding: 20px; text-align: center; }\n        .content { padding: 20px; background-color: #f9f9f9; }\n        .button { background-color: #2196F3; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 0; }\n        .info-box { background-color: #e3f2fd; border: 1px solid #2196F3; padding: 15px; border-radius: 4px; margin: 15px 0; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>Self-Assessment Completed</h1>\n        </div>\n        <div class=\"content\">\n            <p>Dear {{ $json.first_name }},</p>\n            \n            <p><strong>{{ $('Set Manager Notification Data').first().json.employee_name }}</strong> has completed their self-assessment for <strong>{{ $('Set Manager Notification Data').first().json.cycle_name }}</strong>.</p>\n            \n            <div class=\"info-box\">\n                <h3>📋 Review Details:</h3>\n                <ul>\n                    <li><strong>Employee:</strong> {{ $('Set Manager Notification Data').first().json.employee_name }}</li>\n                    <li><strong>Submitted:</strong> {{ new Date().toLocaleDateString() }}</li>\n                    <li><strong>Cycle:</strong> {{ $('Set Manager Notification Data').first().json.cycle_name }}</li>\n                </ul>\n            </div>\n            \n            <p><strong>⚡ Action Required:</strong> Please complete your manager review by <strong>{{ $('Set Manager Notification Data').first().json.manager_review_deadline }}</strong></p>\n            \n            <p><a href=\"{{ $json.manager_review_url || 'http://localhost:5678/manager-review' }}\" class=\"button\">Complete Manager Review</a></p>\n            \n            <p>Best regards,<br>HR System</p>\n        </div>\n    </div>\n</body>\n</html>", "options": {}}, "id": "send-assessment-complete-email", "name": "Send Assessment Complete Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [1120, 500], "credentials": {"smtp": {"id": "company-smtp", "name": "Company SMTP"}}}, {"parameters": {"fromEmail": "<EMAIL>", "toEmail": "={{ $json.email }}", "subject": "Team Performance Reviews Started - {{ $('Set Manager Notification Data').first().json.cycle_name }}", "emailFormat": "html", "html": "<!DOCTYPE html>\n<html>\n<head>\n    <style>\n        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }\n        .container { max-width: 600px; margin: 0 auto; padding: 20px; }\n        .header { background-color: #FF9800; color: white; padding: 20px; text-align: center; }\n        .content { padding: 20px; background-color: #f9f9f9; }\n        .button { background-color: #FF9800; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 0; }\n        .deadline { background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin: 10px 0; }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>Team Performance Reviews</h1>\n        </div>\n        <div class=\"content\">\n            <p>Dear {{ $json.first_name }},</p>\n            \n            <p>The performance appraisal cycle <strong>\"{{ $('Set Manager Notification Data').first().json.cycle_name }}\"</strong> has started for your team.</p>\n            \n            <div class=\"deadline\">\n                <h3>📅 Important Deadline:</h3>\n                <p><strong>Manager Review Deadline:</strong> {{ $('Set Manager Notification Data').first().json.manager_review_deadline }}</p>\n            </div>\n            \n            <h3>👥 Your Responsibilities:</h3>\n            <ol>\n                <li>Review team member self-assessments</li>\n                <li>Complete manager reviews for each team member</li>\n                <li>Schedule one-on-one discussions</li>\n                <li>Provide constructive feedback and development plans</li>\n            </ol>\n            \n            <p><a href=\"{{ $json.manager_dashboard_url || 'http://localhost:5678/manager-dashboard' }}\" class=\"button\">Access Manager Dashboard</a></p>\n            \n            <p>Best regards,<br>HR Team</p>\n        </div>\n    </div>\n</body>\n</html>", "options": {}}, "id": "send-manager-cycle-start-email", "name": "Send Manager Cycle Start Email", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [1120, 700], "credentials": {"smtp": {"id": "company-smtp", "name": "Company SMTP"}}}, {"parameters": {"respondWith": "json", "responseBody": "={\n  \"success\": true,\n  \"message\": \"Notification sent successfully\",\n  \"notification_type\": \"{{ $('Set Employee Notification Data').first().json.notification_type || $('Set Manager Notification Data').first().json.notification_type }}\",\n  \"recipient\": \"{{ $('Set Employee Notification Data').first().json.email || $json.email }}\"\n}"}, "id": "response-notification-success", "name": "Response Notification Success", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [1340, 400]}], "connections": {"Notify Employee Webhook": {"main": [[{"node": "Set Employee Notification Data", "type": "main", "index": 0}]]}, "Set Employee Notification Data": {"main": [[{"node": "Check Notification Type", "type": "main", "index": 0}]]}, "Check Notification Type": {"main": [[{"node": "Send Cycle Start Email", "type": "main", "index": 0}]]}, "Send Cycle Start Email": {"main": [[{"node": "Response Notification Success", "type": "main", "index": 0}]]}, "Notify Manager Webhook": {"main": [[{"node": "Set Manager Notification Data", "type": "main", "index": 0}]]}, "Set Manager Notification Data": {"main": [[{"node": "Get Manager Email", "type": "main", "index": 0}]]}, "Get Manager Email": {"main": [[{"node": "Check Manager Notification Type", "type": "main", "index": 0}]]}, "Check Manager Notification Type": {"main": [[{"node": "Send Assessment Complete Email", "type": "main", "index": 0}], [{"node": "Send Manager Cycle Start Email", "type": "main", "index": 0}]]}, "Send Assessment Complete Email": {"main": [[{"node": "Response Notification Success", "type": "main", "index": 0}]]}, "Send Manager Cycle Start Email": {"main": [[{"node": "Response Notification Success", "type": "main", "index": 0}]]}}, "active": true, "settings": {}, "versionId": "1"}