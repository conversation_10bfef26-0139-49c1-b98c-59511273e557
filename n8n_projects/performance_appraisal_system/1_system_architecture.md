# 🏢 Performance Appraisal System - n8n Implementation

## 📊 **System Overview**

### **Core Components**
1. **Employee Self-Assessment Portal**
2. **Manager Review Dashboard** 
3. **HR Analytics & Reporting**
4. **Goal Setting & Tracking**
5. **Development Planning**
6. **Automated Notifications**

## 🗄️ **Database Schema**

### **1. employees**
```sql
CREATE TABLE employees (
  id INT PRIMARY KEY AUTO_INCREMENT,
  employee_id VARCHAR(50) UNIQUE,
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  email VARCHAR(255),
  department VARCHAR(100),
  position VARCHAR(100),
  manager_id VARCHAR(50),
  hire_date DATE,
  status ENUM('active', 'inactive'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **2. appraisal_cycles**
```sql
CREATE TABLE appraisal_cycles (
  id INT PRIMARY KEY AUTO_INCREMENT,
  cycle_name VA<PERSON>HA<PERSON>(100),
  cycle_type ENUM('annual', 'quarterly', 'mid_year'),
  start_date DATE,
  end_date DATE,
  self_assessment_deadline DATE,
  manager_review_deadline DATE,
  status ENUM('planning', 'active', 'review', 'completed'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **3. self_assessments**
```sql
CREATE TABLE self_assessments (
  id INT PRIMARY KEY AUTO_INCREMENT,
  employee_id VARCHAR(50),
  cycle_id INT,
  achievements TEXT,
  challenges TEXT,
  goals_met JSON,
  skills_rating JSON,
  career_aspirations TEXT,
  training_needs TEXT,
  overall_satisfaction INT,
  submitted_at TIMESTAMP,
  status ENUM('draft', 'submitted', 'reviewed'),
  FOREIGN KEY (cycle_id) REFERENCES appraisal_cycles(id)
);
```

### **4. manager_reviews**
```sql
CREATE TABLE manager_reviews (
  id INT PRIMARY KEY AUTO_INCREMENT,
  employee_id VARCHAR(50),
  manager_id VARCHAR(50),
  cycle_id INT,
  performance_rating INT,
  competency_ratings JSON,
  strengths TEXT,
  areas_for_improvement TEXT,
  goal_achievement_rating INT,
  promotion_readiness ENUM('ready', 'developing', 'not_ready'),
  salary_recommendation ENUM('increase', 'maintain', 'review'),
  comments TEXT,
  submitted_at TIMESTAMP,
  status ENUM('draft', 'submitted', 'approved'),
  FOREIGN KEY (cycle_id) REFERENCES appraisal_cycles(id)
);
```

### **5. goals**
```sql
CREATE TABLE goals (
  id INT PRIMARY KEY AUTO_INCREMENT,
  employee_id VARCHAR(50),
  cycle_id INT,
  goal_title VARCHAR(255),
  goal_description TEXT,
  target_date DATE,
  weight DECIMAL(3,2),
  status ENUM('not_started', 'in_progress', 'completed', 'cancelled'),
  achievement_percentage INT DEFAULT 0,
  manager_rating INT,
  employee_rating INT,
  comments TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **6. development_plans**
```sql
CREATE TABLE development_plans (
  id INT PRIMARY KEY AUTO_INCREMENT,
  employee_id VARCHAR(50),
  cycle_id INT,
  development_area VARCHAR(255),
  action_items JSON,
  timeline VARCHAR(100),
  resources_needed TEXT,
  success_metrics TEXT,
  manager_support TEXT,
  status ENUM('planned', 'in_progress', 'completed'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔄 **Workflow Architecture**

### **Main Workflows**
1. **Cycle Management** - Create and manage appraisal cycles
2. **Employee Self-Assessment** - Guided self-evaluation process
3. **Manager Review Process** - Manager evaluation and feedback
4. **Goal Setting & Tracking** - SMART goals management
5. **Notification System** - Automated reminders and alerts
6. **Reporting & Analytics** - Performance insights and reports
7. **Development Planning** - Career development workflows

### **Integration Points**
- **Email System** (SMTP/Gmail/Outlook)
- **HR Information System** (HRIS)
- **Calendar Systems** (Google Calendar/Outlook)
- **Document Storage** (Google Drive/SharePoint)
- **Slack/Teams** for notifications
- **Database** (MySQL/PostgreSQL/MongoDB)

## 📈 **Key Features**

### **For Employees**
- Self-assessment forms with guided questions
- Goal setting and progress tracking
- Career development planning
- Feedback history and trends
- Performance dashboard

### **For Managers**
- Team performance overview
- Individual review workflows
- Goal approval and monitoring
- Development plan collaboration
- Performance analytics

### **For HR**
- Cycle management dashboard
- Organization-wide analytics
- Compliance tracking
- Report generation
- Calibration support

## 🎯 **Success Metrics**
- **Completion Rate**: % of assessments completed on time
- **Engagement Score**: Employee participation and feedback quality
- **Goal Achievement**: % of goals met across organization
- **Development Progress**: Training completion and skill improvement
- **Manager Effectiveness**: Quality of feedback and development support

## 🚀 **Implementation Timeline**
- **Week 1-2**: Database setup and core workflows
- **Week 3-4**: Employee and manager interfaces
- **Week 5-6**: Notification and reporting systems
- **Week 7-8**: Testing and refinement
- **Week 9**: Training and rollout
- **Week 10**: Go-live and support

## 📧 **Email Templates**

### **Cycle Start Notification (Employee)**
```html
Subject: Performance Appraisal Cycle Started - {{ cycle_name }}

Dear {{ first_name }},

Your performance appraisal cycle "{{ cycle_name }}" has started.

**Important Dates:**
- Self-Assessment Deadline: {{ self_assessment_deadline }}
- Manager Review Deadline: {{ manager_review_deadline }}

**Next Steps:**
1. Complete your self-assessment
2. Review your goals and achievements
3. Prepare for your manager discussion

**Self-Assessment Link:** {{ self_assessment_url }}

Best regards,
HR Team
```

### **Manager Notification**
```html
Subject: Team Performance Reviews - {{ cycle_name }}

Dear Manager,

The performance appraisal cycle "{{ cycle_name }}" has started for your team.

**Your Team Members:**
{{ team_member_list }}

**Manager Review Deadline:** {{ manager_review_deadline }}

**Manager Dashboard:** {{ manager_dashboard_url }}

Best regards,
HR Team
```

### **Assessment Complete Notification**
```html
Subject: Self-Assessment Completed - {{ employee_name }}

Dear Manager,

{{ employee_name }} has completed their self-assessment for {{ cycle_name }}.

**Review Details:**
- Employee: {{ employee_name }}
- Submitted: {{ submitted_date }}
- Overall Satisfaction: {{ overall_satisfaction }}/10

**Action Required:** Please complete your manager review by {{ deadline }}

**Review Link:** {{ manager_review_url }}

Best regards,
HR System
```

## 🔧 **Technical Requirements**
- **n8n Instance** (self-hosted or cloud)
- **Database Server** (MySQL/PostgreSQL)
- **Email Service** (SMTP/API)
- **Web Forms** (Typeform/Google Forms/Custom)
- **File Storage** (Cloud storage service)
- **Optional**: Slack/Teams integration

## 🎯 **API Endpoints**

### **Core Endpoints**
- `POST /webhook/create-cycle` - Create new appraisal cycle
- `GET /webhook/cycles` - Get all cycles
- `POST /webhook/self-assessment` - Submit self-assessment
- `GET /webhook/self-assessment/{employee_id}/{cycle_id}` - Get assessment
- `POST /webhook/manager-review` - Submit manager review
- `GET /webhook/manager-review/{manager_id}/{cycle_id}` - Get team reviews
- `POST /webhook/goals` - Create/update goals
- `GET /webhook/goals/{employee_id}/{cycle_id}` - Get employee goals
- `POST /webhook/development-plan` - Create development plan
- `GET /webhook/reports/performance/{cycle_id}` - Performance reports
