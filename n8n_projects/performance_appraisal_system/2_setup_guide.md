# 🚀 Performance Appraisal System - Setup Guide

## 📋 **Prerequisites**

### **Required Software**
- **n8n** (version 1.0+)
- **MySQL** or **PostgreSQL** database
- **SMTP Email Service** (Gmail, Outlook, or custom SMTP)
- **Web Server** (optional, for custom forms)

### **Required Credentials**
- Database connection credentials
- SMTP email credentials
- Optional: Slack/Teams webhook URLs

## 🗄️ **Database Setup**

### **Step 1: Create Database**
```sql
CREATE DATABASE performance_appraisal;
USE performance_appraisal;
```

### **Step 2: Create Tables**
Run the following SQL scripts in order:

```sql
-- 1. Employees table
CREATE TABLE employees (
  id INT PRIMARY KEY AUTO_INCREMENT,
  employee_id VARCHAR(50) UNIQUE NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL,
  department VARCHAR(100),
  position VARCHAR(100),
  manager_id VARCHAR(50),
  hire_date DATE,
  status ENUM('active', 'inactive') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_employee_id (employee_id),
  INDEX idx_manager_id (manager_id),
  INDEX idx_department (department)
);

-- 2. Appraisal cycles table
CREATE TABLE appraisal_cycles (
  id INT PRIMARY KEY AUTO_INCREMENT,
  cycle_name VARCHAR(100) NOT NULL,
  cycle_type ENUM('annual', 'quarterly', 'mid_year') DEFAULT 'annual',
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  self_assessment_deadline DATE NOT NULL,
  manager_review_deadline DATE NOT NULL,
  status ENUM('planning', 'active', 'review', 'completed') DEFAULT 'planning',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_status (status),
  INDEX idx_dates (start_date, end_date)
);

-- 3. Self assessments table
CREATE TABLE self_assessments (
  id INT PRIMARY KEY AUTO_INCREMENT,
  employee_id VARCHAR(50) NOT NULL,
  cycle_id INT NOT NULL,
  achievements TEXT,
  challenges TEXT,
  goals_met JSON,
  skills_rating JSON,
  career_aspirations TEXT,
  training_needs TEXT,
  overall_satisfaction INT CHECK (overall_satisfaction BETWEEN 1 AND 10),
  submitted_at TIMESTAMP NULL,
  status ENUM('draft', 'submitted', 'reviewed') DEFAULT 'draft',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (cycle_id) REFERENCES appraisal_cycles(id),
  UNIQUE KEY unique_employee_cycle (employee_id, cycle_id),
  INDEX idx_employee_cycle (employee_id, cycle_id),
  INDEX idx_status (status)
);

-- 4. Manager reviews table
CREATE TABLE manager_reviews (
  id INT PRIMARY KEY AUTO_INCREMENT,
  employee_id VARCHAR(50) NOT NULL,
  manager_id VARCHAR(50) NOT NULL,
  cycle_id INT NOT NULL,
  performance_rating INT CHECK (performance_rating BETWEEN 1 AND 5),
  competency_ratings JSON,
  strengths TEXT,
  areas_for_improvement TEXT,
  goal_achievement_rating INT CHECK (goal_achievement_rating BETWEEN 1 AND 5),
  promotion_readiness ENUM('ready', 'developing', 'not_ready'),
  salary_recommendation ENUM('increase', 'maintain', 'review'),
  comments TEXT,
  submitted_at TIMESTAMP NULL,
  status ENUM('draft', 'submitted', 'approved') DEFAULT 'draft',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (cycle_id) REFERENCES appraisal_cycles(id),
  UNIQUE KEY unique_employee_manager_cycle (employee_id, manager_id, cycle_id),
  INDEX idx_employee_cycle (employee_id, cycle_id),
  INDEX idx_manager_cycle (manager_id, cycle_id)
);

-- 5. Goals table
CREATE TABLE goals (
  id INT PRIMARY KEY AUTO_INCREMENT,
  employee_id VARCHAR(50) NOT NULL,
  cycle_id INT NOT NULL,
  goal_title VARCHAR(255) NOT NULL,
  goal_description TEXT,
  target_date DATE,
  weight DECIMAL(3,2) DEFAULT 1.00,
  status ENUM('not_started', 'in_progress', 'completed', 'cancelled') DEFAULT 'not_started',
  achievement_percentage INT DEFAULT 0 CHECK (achievement_percentage BETWEEN 0 AND 100),
  manager_rating INT CHECK (manager_rating BETWEEN 1 AND 5),
  employee_rating INT CHECK (employee_rating BETWEEN 1 AND 5),
  comments TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (cycle_id) REFERENCES appraisal_cycles(id),
  INDEX idx_employee_cycle (employee_id, cycle_id),
  INDEX idx_status (status)
);

-- 6. Development plans table
CREATE TABLE development_plans (
  id INT PRIMARY KEY AUTO_INCREMENT,
  employee_id VARCHAR(50) NOT NULL,
  cycle_id INT NOT NULL,
  development_area VARCHAR(255) NOT NULL,
  action_items JSON,
  timeline VARCHAR(100),
  resources_needed TEXT,
  success_metrics TEXT,
  manager_support TEXT,
  status ENUM('planned', 'in_progress', 'completed') DEFAULT 'planned',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (cycle_id) REFERENCES appraisal_cycles(id),
  INDEX idx_employee_cycle (employee_id, cycle_id)
);

-- 7. Notifications log table (optional)
CREATE TABLE notifications (
  id INT PRIMARY KEY AUTO_INCREMENT,
  recipient_email VARCHAR(255) NOT NULL,
  notification_type VARCHAR(100) NOT NULL,
  subject VARCHAR(255),
  content TEXT,
  sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status ENUM('sent', 'failed', 'pending') DEFAULT 'pending',
  INDEX idx_recipient (recipient_email),
  INDEX idx_type (notification_type),
  INDEX idx_sent_at (sent_at)
);
```

## 🔧 **n8n Configuration**

### **Step 1: Import Workflows**
1. Open n8n interface
2. Go to **Workflows** → **Import from File**
3. Import each workflow file in this order:
   - `1_cycle_management.json`
   - `2_employee_self_assessment.json`
   - `3_manager_review.json`
   - `4_notification_system.json`
   - `5_goal_management.json`
   - `6_reporting_analytics.json`

### **Step 2: Configure Database Credentials**
1. Go to **Settings** → **Credentials**
2. Create new **MySQL** credential named `mysql-hr-db`
3. Enter your database connection details:
   - **Host**: Your database host
   - **Database**: `performance_appraisal`
   - **Username**: Your database username
   - **Password**: Your database password

### **Step 3: Configure Email Credentials**
1. Create new **SMTP** credential named `company-smtp`
2. Enter your email service details:
   - **Host**: smtp.gmail.com (for Gmail)
   - **Port**: 587
   - **Username**: <EMAIL>
   - **Password**: your-app-password

### **Step 4: Activate Workflows**
1. Open each imported workflow
2. Click **Active** toggle to enable
3. Test webhook endpoints

## 📊 **Sample Data**

### **Insert Sample Employees**
```sql
INSERT INTO employees (employee_id, first_name, last_name, email, department, position, manager_id, hire_date) VALUES
('EMP001', 'John', 'Smith', '<EMAIL>', 'Engineering', 'Senior Developer', 'MGR001', '2022-01-15'),
('EMP002', 'Sarah', 'Johnson', '<EMAIL>', 'Marketing', 'Marketing Specialist', 'MGR002', '2022-03-20'),
('EMP003', 'Mike', 'Davis', '<EMAIL>', 'Sales', 'Sales Representative', 'MGR003', '2021-11-10'),
('MGR001', 'Alice', 'Wilson', '<EMAIL>', 'Engineering', 'Engineering Manager', NULL, '2020-05-01'),
('MGR002', 'Bob', 'Brown', '<EMAIL>', 'Marketing', 'Marketing Manager', NULL, '2019-08-15'),
('MGR003', 'Carol', 'Taylor', '<EMAIL>', 'Sales', 'Sales Manager', NULL, '2020-02-01');

-- Create sample appraisal cycle
INSERT INTO appraisal_cycles (cycle_name, cycle_type, start_date, end_date, self_assessment_deadline, manager_review_deadline, status) VALUES
('2024 Annual Review', 'annual', '2024-01-01', '2024-12-31', '2024-11-15', '2024-12-15', 'active');
```

## 🌐 **API Endpoints**

After setup, your system will have these endpoints:

### **Cycle Management**
- `POST /webhook/create-cycle` - Create new cycle
- `GET /webhook/cycles` - Get all cycles

### **Self Assessment**
- `POST /webhook/self-assessment` - Submit assessment
- `GET /webhook/self-assessment/{employee_id}/{cycle_id}` - Get assessment

### **Manager Review**
- `POST /webhook/manager-review` - Submit review
- `GET /webhook/manager-review/{manager_id}/{cycle_id}` - Get team reviews

### **Goal Management**
- `POST /webhook/goals` - Create goal
- `GET /webhook/goals/{employee_id}/{cycle_id}` - Get goals
- `PUT /webhook/goals/{goal_id}` - Update goal

### **Reporting**
- `GET /webhook/reports/performance/{cycle_id}` - Performance report
- `GET /webhook/reports/employee/{employee_id}/{cycle_id}` - Employee report

## 🧪 **Testing**

### **Test Cycle Creation**
```bash
curl -X POST http://localhost:5678/webhook/create-cycle \
  -H "Content-Type: application/json" \
  -d '{
    "cycle_name": "Q1 2024 Review",
    "cycle_type": "quarterly",
    "start_date": "2024-01-01",
    "end_date": "2024-03-31",
    "self_assessment_deadline": "2024-03-15",
    "manager_review_deadline": "2024-03-30"
  }'
```

### **Test Self Assessment**
```bash
curl -X POST http://localhost:5678/webhook/self-assessment \
  -H "Content-Type: application/json" \
  -d '{
    "employee_id": "EMP001",
    "cycle_id": 1,
    "achievements": "Completed major project ahead of schedule",
    "challenges": "Learning new technology stack",
    "overall_satisfaction": 8
  }'
```

## 🔒 **Security Considerations**

1. **Database Security**
   - Use strong passwords
   - Limit database user permissions
   - Enable SSL connections

2. **API Security**
   - Implement authentication for webhooks
   - Use HTTPS in production
   - Rate limiting for API endpoints

3. **Data Privacy**
   - Encrypt sensitive data
   - Regular backups
   - Access logging

## 📈 **Next Steps**

1. **Customize Forms**: Create web forms for better user experience
2. **Add Integrations**: Connect with HRIS, Slack, or Teams
3. **Enhanced Reporting**: Add charts and visualizations
4. **Mobile Support**: Create mobile-friendly interfaces
5. **Automation**: Add reminder schedules and escalations
