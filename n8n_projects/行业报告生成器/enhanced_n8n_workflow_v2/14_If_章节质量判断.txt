{
  "conditions": {
    "options": {
      "caseSensitive": true,
      "leftValue": "",
      "typeValidation": "strict",
      "version": 2
    },
    "conditions": [
      {
        "id": "ed152898-78a9-4ac3-b76f-87d61874b607",
        "leftValue": "={{ $json.qualityAssessment.score}}",
        "rightValue": 80,
        "operator": {
          "type": "number",
          "operation": "gte"
        }
      },
      {
        "id": "retry-limit-check",
        "leftValue": "={{ $json.retryCount}}",
        "rightValue": 3,
        "operator": {
          "type": "number",
          "operation": "gte"
        }
      }
    ],
    "combinator": "or"
  },
  "options": {}
}
// 说明：
// 该节点为If条件判断节点，判断以下条件之一：
// 1. 章节质量分数是否大于等于80分 OR
// 2. 重试次数是否大于等于3次（防止无限循环）
// 满足任一条件（true）则进入章节保存，否则进入补充内容生成。
