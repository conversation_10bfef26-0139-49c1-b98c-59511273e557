请基于以下数据生成一个完整的10页专业HTML行业分析报告。

## 基础数据：
**行业**：{{ $json.chapterInfo?.industry || $json.industry || '未知行业' }}
**会话ID**：{{ $json.sessionId || $json.chapterInfo?.sessionId || 'unknown' }}
**报告标题**：{{ $json.chapterInfo?.industry || $json.industry || '未知行业' }}行业深度分析报告2025
**报告提供方**：深圳市米协尔企业管理咨询有限公司
**报告日期**：{{ new Date().toLocaleDateString('zh-CN', {year: 'numeric', month: 'long', day: 'numeric'}) }}

## AI生成的内容：
{{ JSON.stringify($json.reportData?.content || $json.content || $json.savedChapter?.content || $json.chapterData?.content || {}, null, 2) }}

## 图表配置：
{{ JSON.stringify($json.reportData?.visualizations || $json.visualizations || $json.savedChapter?.visualizations || $json.chapterData?.visualizations || [], null, 2) }}

## 数据来源检测：
- reportData内容：{{ $json.reportData?.content ? '✅' : '❌' }}
- reportData图表：{{ $json.reportData?.visualizations ? '✅' : '❌' }}
- 直接内容：{{ $json.content ? '✅' : '❌' }}
- 直接图表：{{ $json.visualizations ? '✅' : '❌' }}
- 保存章节：{{ $json.savedChapter ? '✅' : '❌' }}
- 章节数据：{{ $json.chapterData ? '✅' : '❌' }}
- 会话ID：{{ $json.sessionId || $json.savedChapter?.sessionId || $json.chapterInfo?.sessionId || 'unknown' }}
- 行业：{{ $json.chapterInfo?.industry || $json.industry || $json.savedChapter?.industry || 'unknown' }}

## 🎯 HTML生成要求：

### 1. 报告结构（10页专业版）
- **标题区域**：紧凑的标题卡片（不占用整页）+ 目录 + 执行摘要
- **第1页**：行业概述与定义 + 产业链图表
- **第2页**：市场规模与增长分析 + 趋势图表
- **第3页**：区域分布与地域特色 + 热力图表
- **第4页**：竞争格局与主要企业 + 竞争图表
- **第5页**：技术发展与创新趋势 + 技术图表
- **第6页**：政策环境与监管影响 + 政策图表
- **第7页**：投资机会与风险评估 + 投资图表
- **第8页**：未来发展趋势预测 + 预测图表
- **第9页**：结论与战略建议 + 建议图表

### 标题区域设计要求（紧凑卡片式）：
- **设计理念**：标题卡片，不是整页封面，紧凑美观
- **主标题**：{{ $json.chapterInfo?.industry || $json.industry || '未知行业' }}行业深度分析报告2025（中等字体）
- **副标题**：专业市场研究与战略分析（小字体）
- **公司信息**：深圳市米协尔企业管理咨询有限公司（极小字体）
- **报告日期**：2025年7月（极小字体）
- **布局要求**：紧凑的卡片式设计，高度控制在200-300px以内

### 2. 图表类型支持（12+种）
支持以下ECharts图表类型，请根据数据智能选择：
- **gauge**: 仪表盘图（健康度、指数评估）
- **line**: 折线图（趋势分析、时间序列）
- **bar**: 柱状图（对比分析、排名）
- **pie**: 饼图（占比分析、结构分布）
- **radar**: 雷达图（多维度分析、能力评估）
- **scatter**: 散点图（相关性、风险收益）
- **heatmap**: 热力图（区域分布、密度分析）
- **funnel**: 漏斗图（转化分析、层级分布）
- **timeline**: 时间轴（发展历程、路线图）
- **treemap**: 树状图（层级结构、细分市场）
- **sankey**: 桑基图（流向分析、价值链）
- **candlestick**: K线图（价格波动、金融分析）

### 3. 设计要求
- **响应式设计**：完美适配桌面端和移动端
- **专业配色**：根据{{ $json.chapterInfo?.industry || $json.industry || '未知行业' }}行业特点选择配色方案
- **现代布局**：使用TailwindCSS，布局美观专业
- **图表融合**：每个图表都要与对应章节内容深度融合

### 4. 技术要求
- **ECharts 5.4.3**：使用最新版本的ECharts
- **TailwindCSS**：使用CDN版本的TailwindCSS
- **移动优化**：确保在手机端显示完美
- **打印友好**：支持打印输出

## 📊 图表配置示例：

### 仪表盘图配置
```javascript
{
  title: { text: '{{ $json.chapterInfo?.industry || $json.industry || '未知行业' }}行业健康度指数', left: 'center' },
  series: [{
    type: 'gauge',
    data: [{ value: 85, name: '综合评分' }],
    axisLine: {
      lineStyle: {
        width: 20,
        color: [[0.3, '#fd666d'], [0.7, '#37a2da'], [1, '#67e0e3']]
      }
    }
  }]
}
```

### 折线图配置
```javascript
{
  title: { text: '市场规模增长趋势', left: 'center' },
  xAxis: { data: ['2021', '2022', '2023', '2024', '2025', '2026E', '2027E', '2028E', '2029E', '2030E'] },
  yAxis: { type: 'value', name: '规模(亿元)' },
  series: [{
    type: 'line',
    data: [100, 120, 145, 170, 200, 235, 275, 320, 370, 425],
    smooth: true,
    areaStyle: { opacity: 0.3 }
  }]
}
```

## 🎨 行业风格系统

### 行业风格映射表：
```javascript
const industryStyles = {
    // 科技类 - 蓝色科技风
    '科技': { theme: 'tech', primary: '#1890ff', secondary: '#52c41a', accent: '#faad14' },
    '人工智能': { theme: 'tech', primary: '#1890ff', secondary: '#52c41a', accent: '#faad14' },
    '软件': { theme: 'tech', primary: '#1890ff', secondary: '#52c41a', accent: '#faad14' },
    '互联网': { theme: 'tech', primary: '#1890ff', secondary: '#52c41a', accent: '#faad14' },

    // 金融类 - 金色商务风
    '金融': { theme: 'finance', primary: '#faad14', secondary: '#1890ff', accent: '#f5222d' },
    '银行': { theme: 'finance', primary: '#faad14', secondary: '#1890ff', accent: '#f5222d' },
    '保险': { theme: 'finance', primary: '#faad14', secondary: '#1890ff', accent: '#f5222d' },
    '投资': { theme: 'finance', primary: '#faad14', secondary: '#1890ff', accent: '#f5222d' },

    // 制造类 - 橙色工业风
    '制造业': { theme: 'manufacturing', primary: '#fa8c16', secondary: '#52c41a', accent: '#1890ff' },
    '汽车': { theme: 'manufacturing', primary: '#fa8c16', secondary: '#52c41a', accent: '#1890ff' },
    '新能源汽车': { theme: 'manufacturing', primary: '#fa8c16', secondary: '#52c41a', accent: '#1890ff' },
    '机械': { theme: 'manufacturing', primary: '#fa8c16', secondary: '#52c41a', accent: '#1890ff' },

    // 医疗类 - 绿色健康风
    '医疗': { theme: 'healthcare', primary: '#52c41a', secondary: '#1890ff', accent: '#faad14' },
    '医药': { theme: 'healthcare', primary: '#52c41a', secondary: '#1890ff', accent: '#faad14' },
    '生物': { theme: 'healthcare', primary: '#52c41a', secondary: '#1890ff', accent: '#faad14' },

    // 电商类 - 紫色国际风
    '跨境电商': { theme: 'ecommerce', primary: '#722ed1', secondary: '#1890ff', accent: '#52c41a' },
    '电商': { theme: 'ecommerce', primary: '#722ed1', secondary: '#1890ff', accent: '#52c41a' },
    '零售': { theme: 'ecommerce', primary: '#722ed1', secondary: '#1890ff', accent: '#52c41a' },

    // 新能源类 - 绿蓝环保风
    '新能源': { theme: 'energy', primary: '#52c41a', secondary: '#1890ff', accent: '#faad14' },
    '环保': { theme: 'energy', primary: '#52c41a', secondary: '#1890ff', accent: '#faad14' },

    // 默认风格
    'default': { theme: 'default', primary: '#1890ff', secondary: '#52c41a', accent: '#faad14' }
};
```

### 风格特色设计要求：

**科技风格 (tech)**：简洁现代、几何元素、渐变背景、卡片圆角16px、科技蓝主色调、字体Inter
**金融风格 (finance)**：稳重专业、商务感、数据突出、卡片圆角8px、金色主色调、字体Times
**制造业风格 (manufacturing)**：工业风格、流程导向、橙色活力、卡片圆角12px、字体Roboto
**医疗风格 (healthcare)**：清洁安全、健康绿色、圆润设计、卡片圆角20px、字体Helvetica
**电商风格 (ecommerce)**：国际化、多彩活泼、地图元素、卡片圆角24px、紫色主色调、字体Poppins

## 🎨 样式要求：

### 标题卡片样式优化
```css
.title-header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    padding: 2rem 1.5rem;
    text-align: center;
    margin-bottom: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(30, 60, 114, 0.3);
    max-width: 1000px;
    margin: 0 auto 3rem auto;
    position: relative;
}

.company-info {
    text-align: center;
    font-size: 0.8rem;
    opacity: 0.8;
    margin-bottom: 0.3rem;
    font-weight: 300;
}

.report-date {
    text-align: center;
    font-size: 0.75rem;
    opacity: 0.75;
    margin-bottom: 0;
    font-weight: 300;
}

.main-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.sub-title {
    font-size: 0.95rem;
    font-weight: 300;
    margin-bottom: 1rem;
    opacity: 0.9;
}

@media (max-width: 768px) {
    .title-header {
        padding: 1.5rem 1rem;
        margin-bottom: 2rem;
        border-radius: 8px;
    }
    .main-title { font-size: 1.4rem; }
    .sub-title { font-size: 0.85rem; }
    .company-info {
        font-size: 0.7rem;
        margin-bottom: 0.2rem;
    }
    .report-date {
        font-size: 0.65rem;
        margin-bottom: 0;
    }
}

/* 确保标题不会过大 */
.main-title {
    max-width: 80%;
    margin: 0 auto 1rem auto;
    text-align: center;
}

.sub-title {
    max-width: 70%;
    margin: 0 auto 2rem auto;
    text-align: center;
}
```

## ⚠️ 重要提醒：

### 1. JavaScript代码规范
- 所有JavaScript代码必须放在`<script>`标签内
- 确保ECharts变量名唯一，避免冲突
- 使用`document.getElementById()`获取DOM元素
- 图表配置对象必须是有效的JSON格式

### 2. 内容完整性要求
- 必须生成完整的10页报告内容
- 每页内容不少于200字
- 确保所有章节都有对应的图表
- 报告结尾必须有完整的结论

### 3. 封面布局要求
- 主标题居中显示，字体大而醒目
- 副标题在主标题下方，字体适中
- 公司信息放在右下角，字体较小，不抢夺主要视觉焦点
- 移动端自动调整为垂直居中布局

### 4. 部署兼容性
- 避免使用可能导致"illegal statement"的JavaScript语法
- 确保所有引号使用一致（建议使用单引号）
- 避免在字符串中使用未转义的特殊字符
- 所有图表ID必须唯一且符合HTML规范

## 📝 输出要求：

请生成一个完整的HTML报告，包含：

1. **完整的HTML结构**：从`<!DOCTYPE html>`到`</html>`
2. **优化的封面设计**：使用上述CSS样式，公司信息紧凑布局
3. **10个完整章节**：每个章节都有详细内容和对应图表
4. **无JavaScript错误**：确保所有图表代码正确无误
5. **移动端适配**：响应式设计，完美支持手机端
6. **专业排版**：使用TailwindCSS，布局美观专业

**特别注意**：
- 封面的公司信息要简洁，不要占用过多空间
- 确保报告内容完整，不要截断
- 所有JavaScript代码要符合部署要求
- 图表配置要基于实际的{{ $json.chapterInfo?.industry || $json.industry || '未知行业' }}行业数据

## 📋 标题卡片HTML结构示例：
```html
<div class="title-header">
    <h1 class="main-title">{{ $json.chapterInfo?.industry || $json.industry || '未知行业' }}行业深度分析报告2025</h1>
    <h2 class="sub-title">专业市场研究与战略分析</h2>
    <div class="company-info">深圳市米协尔企业管理咨询有限公司</div>
    <div class="report-date">2025年7月</div>
</div>

<!-- 紧接着是报告内容，不分页 -->
<div class="report-content">
    <!-- 目录和执行摘要等内容 -->
</div>
```

现在请基于以上要求生成完整的HTML报告。

**重要提醒**：
- **根据 {{ $json.chapterInfo?.industry || $json.industry || '未知行业' }} 行业自动选择对应的设计风格**
- 每个行业要有明显不同的视觉风格和排版特色
- 标题区域要设计为紧凑的风格化卡片式，不要占用整页
- 标题卡片高度控制在200-300px以内，体现行业特色
- 标题卡片下方紧接着是报告内容，不要分页
- 所有标题信息要紧凑排列，字体要相应缩小
- 图表配色要与行业主题完全一致
- **必须使用markdown代码块格式输出**：以```html开头，以```结尾
- 确保报告内容完整，图表配置正确
- 保持专业性的同时体现行业特色

## 🎨 动态风格生成要求：

### 1. 自动识别行业风格
根据 {{ $json.chapterInfo?.industry || $json.industry || '未知行业' }} 自动匹配对应的设计风格主题。

### 2. 风格化标题设计
- **主标题**：{{ $json.chapterInfo?.industry || $json.industry || '未知行业' }}行业深度分析报告2025（风格化字体）
- **副标题**：专业市场研究与战略分析（行业特色副标题）
- **公司信息**：深圳市米协尔企业管理咨询有限公司（小字体）
- **报告日期**：2025年7月（小字体）
- **布局要求**：紧凑的风格化卡片设计，高度200-300px

### 3. 内容区域风格化
- 章节标题使用行业主色调
- 关键数据卡片采用行业风格
- 图表配色与行业主题一致
- 整体布局体现行业特色

## 📋 输出格式要求：

请严格按照以下格式输出：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $json.chapterInfo?.industry || $json.industry || '未知行业' }}行业深度分析报告2025</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        /* 根据行业自动生成对应的风格样式 */
        /* 包含：主题色彩、字体、布局、动画等 */
    </style>
</head>
<body>
    <!-- 风格化的标题卡片 -->
    <div class="industry-header">
        <!-- 行业特色的标题设计 -->
    </div>

    <!-- 风格化的报告内容 -->
    <div class="industry-content">
        <!-- 10个章节，每个都体现行业风格 -->
    </div>

    <script>
        // 行业风格化的图表配置
        // 自动适配主题色彩和样式
    </script>
</body>
</html>
```