promptType: define
text: 请基于以下数据生成{{ $json.industry }}行业报告的深度分析内容。

## 章节要求：
**当前章节**：{{ $json.industry }}行业深度分析报告
- 目标字数：2500字以上（10页报告标准）
- 图表要求：6个高质量图表（多样化类型）
- 章节描述：{{ $json.industry }}行业全面深度分析，包含核心发现、关键洞察、市场趋势和发展前景

## 基础数据：
**会话信息**：
- 会话ID：{{ $json.sessionId }}
- 行业：{{ $json.industry }}
- 数据质量：{{ $json.agentData.dataQuality }}分
- 内容数量：{{ $json.agentData.contentCount }}条

**数据样本**：
{{ JSON.stringify($json.agentData.contentSample, null, 2) }}

**数据特征**：
- 数据丰富度：{{ $json.agentData.dataSummary.dataRichness }}
- 推荐方法：{{ $json.agentData.dataSummary.recommendedApproach }}
- 高质量数据：{{ $json.agentData.dataSummary.hasHighQualityData ? '是' : '否' }}

**报告框架参考**：
{{ $json.reportFramework.output }}

## 🎯 10页报告升级要求：
1. **内容深度**：2500字以上，确保10页报告质量
2. **图表配置**：6个高质量图表，12+种类型智能选择
3. **洞察深度**：8个关键洞察，每个150-200字深度分析
4. **数据支撑**：8个数据点，具体数值和来源
5. **专业术语**：10个行业专业术语，准确使用
6. **分析维度**：技术、市场、政策、资本、产业、用户6个维度全覆盖

## 📊 智能图表选择策略：
根据内容类型和行业特点，智能选择最合适的图表类型：

### 基础图表类型
- **gauge**: 仪表盘图 - 健康度、发展指数、成熟度评估
- **line**: 折线图 - 趋势分析、时间序列、增长曲线
- **bar**: 柱状图 - 企业对比、区域分析、排名对比
- **pie**: 饼图 - 市场份额、结构分析、占比分布

### 高级图表类型
- **radar**: 雷达图 - 多维度分析、能力评估、SWOT分析
- **scatter**: 散点图 - 风险收益、相关性分析、投资机会
- **heatmap**: 热力图 - 区域分布、技术热点、活跃度分析
- **funnel**: 漏斗图 - 转化分析、层级分布、风险等级
- **timeline**: 时间轴 - 发展历程、路线图、里程碑
- **treemap**: 树状图 - 层级结构、细分市场、产业结构
- **sankey**: 桑基图 - 价值流向、资金流、产业链分析
- **candlestick**: K线图 - 价格波动、市场表现（金融行业）

## 🧠 行业智能图表匹配：
### 科技行业优选
- radar（技术能力）、heatmap（创新热点）、timeline（技术路线）、treemap（产品结构）

### 金融行业优选  
- candlestick（市场表现）、scatter（风险收益）、funnel（业务转化）、sankey（资金流向）

### 制造业优选
- sankey（供应链）、timeline（生产流程）、heatmap（产能分布）、bar（产量对比）

### 零售行业优选
- funnel（销售转化）、heatmap（门店分布）、pie（品类结构）、line（销售趋势）

## 输出要求（10页报告标准）：
直接输出JSON对象，确保达到2500字、8洞察、8数据、6图表、10术语标准。

{
  "chapterInfo": {
    "id": "comprehensiveAnalysis",
    "title": "{{ $json.industry }}行业深度分析报告",
    "sessionId": "{{ $json.sessionId }}",
    "industry": "{{ $json.industry }}",
    "chapterIndex": 0,
    "generatedAt": "当前时间戳",
    "analysisDepth": "comprehensive",
    "reportType": "10_page_professional"
  },
  "content": {
    "executiveSummary": "执行摘要内容...(400字以上，概述整个行业的核心发现和关键结论，需要引用具体数据支撑)",
    "industryOverview": "行业概述内容...(500字以上，包含行业定义、发展历程、产业链结构等，结合产业链桑基图和发展时间轴进行深度分析)",
    "marketAnalysis": "市场分析内容...(500字以上，包含市场规模、增长趋势、区域分布等，必须结合市场趋势线图和细分市场树状图进行详细解读)",
    "regionalAnalysis": "区域分析内容...(400字以上，包含区域分布、地域特色、区域竞争等，结合区域热力图和城市对比柱状图分析)",
    "competitiveLandscape": "竞争格局内容...(400字以上，包含主要企业、市场份额、竞争态势等，必须结合市场份额饼图和企业能力雷达图进行深入分析)",
    "technologyInnovation": "技术创新内容...(400字以上，包含技术发展、创新趋势、研发投入等，结合技术热点热力图和研发投入对比图分析)",
    "policyEnvironment": "政策环境内容...(300字以上，包含政策支持、监管要求、合规标准等，结合政策影响雷达图和合规层级漏斗图分析)",
    "investmentAnalysis": "投资分析内容...(400字以上，包含投资机会、风险评估、回报预期等，结合风险收益散点图和投资趋势线图分析)",
    "futureTrends": "未来趋势内容...(300字以上，包含发展趋势、机遇挑战、未来展望等，结合发展路线图和趋势预测图分析)",
    "conclusions": "结论建议内容...(300字以上，包含核心结论、战略建议、实施方案等，结合战略建议雷达图分析)",
    "keyInsights": [
      "基于数据的关键洞察1（如：全球{{ $json.industry }}市场复合增长率及驱动因素）",
      "基于数据的关键洞察2（如：中国{{ $json.industry }}市场的结构性特征）",
      "基于数据的关键洞察3（如：{{ $json.industry }}企业发展水平和行业成熟度）",
      "基于数据的关键洞察4（如：{{ $json.industry }}技术创新和数字化转型趋势）",
      "基于数据的关键洞察5（如：{{ $json.industry }}政策环境和监管影响）",
      "基于数据的关键洞察6（如：{{ $json.industry }}投资机会和风险评估）",
      "基于数据的关键洞察7（如：{{ $json.industry }}区域发展差异和特色）",
      "基于数据的关键洞察8（如：{{ $json.industry }}未来发展趋势和战略方向）"
    ],
    "dataSupport": [
      "具体的市场规模数据和增长率统计",
      "主要企业的业绩表现和市场份额数据",
      "行业投资和融资情况的具体数字",
      "技术发展和创新指标的量化数据",
      "政策影响和市场变化的具体案例",
      "区域分布和地域特色的统计数据",
      "竞争格局和企业能力的评估数据",
      "未来趋势预测的模型数据"
    ],
    "professionalTerms": [
      "{{ $json.industry }}相关的专业术语1",
      "{{ $json.industry }}相关的专业术语2", 
      "{{ $json.industry }}相关的专业术语3",
      "{{ $json.industry }}相关的专业术语4",
      "{{ $json.industry }}相关的专业术语5",
      "{{ $json.industry }}相关的专业术语6",
      "{{ $json.industry }}相关的专业术语7",
      "{{ $json.industry }}相关的专业术语8",
      "{{ $json.industry }}相关的专业术语9",
      "{{ $json.industry }}相关的专业术语10"
    ]
  },
  "visualizations": [
    {
      "id": "industry_health_gauge",
      "type": "gauge",
      "title": "{{ $json.industry }}行业健康度指数2025",
      "description": "基于市场规模、增长率、企业数量等综合指标评估",
      "config": {
        "title": {
          "text": "{{ $json.industry }}行业健康度指数2025",
          "left": "center",
          "textStyle": { "fontSize": 16, "fontWeight": "bold" }
        },
        "tooltip": { "formatter": "{a} <br/>{b} : {c}%" },
        "series": [{
          "name": "健康度指数",
          "type": "gauge",
          "detail": { "formatter": "{value}%" },
          "data": [{ "value": 85, "name": "综合评分" }],
          "axisLine": {
            "lineStyle": {
              "width": 20,
              "color": [[0.3, "#fd666d"], [0.7, "#37a2da"], [1, "#67e0e3"]]
            }
          }
        }]
      }
    },
    {
      "id": "value_chain_sankey",
      "type": "sankey",
      "title": "{{ $json.industry }}产业链价值流向图",
      "description": "展示产业链各环节的价值流向和关联关系",
      "config": {
        "title": { "text": "{{ $json.industry }}产业链价值流向图", "left": "center" },
        "tooltip": { "trigger": "item", "triggerOn": "mousemove" },
        "series": [{
          "type": "sankey",
          "data": [
            {"name": "原材料供应"}, {"name": "生产制造"}, {"name": "渠道分销"},
            {"name": "终端销售"}, {"name": "售后服务"}
          ],
          "links": [
            {"source": "原材料供应", "target": "生产制造", "value": 30},
            {"source": "生产制造", "target": "渠道分销", "value": 25},
            {"source": "渠道分销", "target": "终端销售", "value": 20},
            {"source": "终端销售", "target": "售后服务", "value": 15}
          ]
        }]
      }
    },
    {
      "id": "development_timeline",
      "type": "timeline",
      "title": "{{ $json.industry }}行业发展历程",
      "description": "关键发展节点和里程碑事件",
      "config": {
        "title": { "text": "{{ $json.industry }}行业发展历程", "left": "center" },
        "tooltip": { "trigger": "item" },
        "timeline": {
          "data": ["2021", "2022", "2023", "2024", "2025"],
          "axisType": "category",
          "autoPlay": false,
          "playInterval": 3000
        },
        "options": [
          {
            "series": [{
              "type": "bar",
              "data": [100, 120, 145, 170, 200]
            }]
          }
        ]
      }
    },
    {
      "id": "market_growth_line",
      "type": "line",
      "title": "{{ $json.industry }}市场规模增长趋势",
      "description": "近5年市场规模变化和未来5年预测至2030",
      "config": {
        "title": { "text": "{{ $json.industry }}市场规模增长趋势", "left": "center" },
        "tooltip": { "trigger": "axis" },
        "xAxis": { "type": "category", "data": ["2021", "2022", "2023", "2024", "2025", "2026E", "2027E", "2028E", "2029E", "2030E"] },
        "yAxis": { "type": "value", "name": "市场规模(亿元)" },
        "series": [{
          "name": "市场规模",
          "type": "line",
          "data": [100, 120, 145, 170, 200, 235, 275, 320, 370, 425],
          "smooth": true,
          "lineStyle": { "width": 3 },
          "markLine": {
            "data": [{ "xAxis": "2025", "name": "预测起点" }]
          }
        }]
      }
    },
    {
      "id": "market_segmentation_treemap",
      "type": "treemap",
      "title": "{{ $json.industry }}细分市场结构图",
      "description": "各细分领域市场规模和占比分布",
      "config": {
        "title": { "text": "{{ $json.industry }}细分市场结构图", "left": "center" },
        "tooltip": { "trigger": "item" },
        "series": [{
          "type": "treemap",
          "data": [
            { "name": "细分市场A", "value": 40 },
            { "name": "细分市场B", "value": 30 },
            { "name": "细分市场C", "value": 20 },
            { "name": "细分市场D", "value": 10 }
          ]
        }]
      }
    },
    {
      "id": "regional_heatmap",
      "type": "heatmap",
      "title": "{{ $json.industry }}全国区域分布热力图",
      "description": "各省市行业发展活跃度和集中度分布",
      "config": {
        "title": { "text": "{{ $json.industry }}全国区域分布热力图", "left": "center" },
        "tooltip": { "position": "top" },
        "grid": { "height": "50%", "top": "10%" },
        "xAxis": { "type": "category", "data": ["北京", "上海", "广州", "深圳", "杭州", "成都", "武汉"] },
        "yAxis": { "type": "category", "data": ["发展指数", "企业数量", "投资规模", "创新能力"] },
        "visualMap": {
          "min": 0, "max": 100,
          "calculable": true,
          "orient": "horizontal",
          "left": "center", "bottom": "15%"
        },
        "series": [{
          "type": "heatmap",
          "data": [
            [0, 0, 90], [0, 1, 85], [0, 2, 80], [0, 3, 88],
            [1, 0, 88], [1, 1, 82], [1, 2, 85], [1, 3, 90],
            [2, 0, 75], [2, 1, 78], [2, 2, 82], [2, 3, 80],
            [3, 0, 85], [3, 1, 88], [3, 2, 90], [3, 3, 85],
            [4, 0, 70], [4, 1, 75], [4, 2, 78], [4, 3, 72],
            [5, 0, 68], [5, 1, 70], [5, 2, 72], [5, 3, 75],
            [6, 0, 65], [6, 1, 68], [6, 2, 70], [6, 3, 72]
          ]
        }]
      }
    }
  ],
  "qualityMetrics": {
    "totalWordCount": "实际总字数（数字，应超过2000字）",
    "executiveSummaryWordCount": "执行摘要字数",
    "industryOverviewWordCount": "行业概述字数",
    "marketAnalysisWordCount": "市场分析字数",
    "regionalAnalysisWordCount": "区域分析字数",
    "competitiveLandscapeWordCount": "竞争格局字数",
    "technologyInnovationWordCount": "技术创新字数",
    "policyEnvironmentWordCount": "政策环境字数",
    "investmentAnalysisWordCount": "投资分析字数",
    "futureTrendsWordCount": "未来趋势字数",
    "conclusionsWordCount": "结论建议字数",
    "insightCount": 8,
    "dataReferenceCount": "数据引用次数",
    "professionalTermCount": 10,
    "visualizationCount": 6,
    "chartTypeVariety": 6,
    "analysisDepth": "comprehensive",
    "reportPages": 10
  }
}

systemMessage

你是{{ $json.industry }}行业的资深专家和专业分析师，拥有15年的行业经验。你的专长包括：

1. **深度行业洞察**：对行业发展趋势、竞争格局、市场机会有深刻理解
2. **数据分析能力**：能从有限数据中提取关键信息和趋势洞察
3. **专业写作能力**：擅长撰写结构清晰、逻辑严密的专业报告
4. **可视化设计**：精通ECharts图表配置，能为数据选择最佳展示方式
5. **综合分析能力**：能够从多个维度全面分析行业现状和发展前景

**写作原则**：
- 基于提供的真实数据进行分析，不编造虚假信息
- 确保内容专业、深入、有数据支撑
- 字数必须达到要求，内容充实有价值
- 图表配置必须完整可用，与内容高度相关
- 输出严格JSON格式，不包含任何markdown标记
- 内容结构完整，涵盖行业分析的各个重要维度

**质量标准（10页专业报告）**：
- 字数达标：总字数不少于2500字，确保10页报告深度
- 洞察深度：至少8个基于数据的关键洞察，每个150-200字
- 数据支撑：至少8个具体的数据证据，包含数值和来源
- 可视化：6个高质量ECharts图表配置，类型多样化
- 专业术语：至少10个行业专业术语，准确使用
- 内容完整性：包含10个核心分析维度，全面覆盖行业各方面
- 逻辑连贯：各章节内容逻辑清晰，相互呼应，形成完整的分析体系