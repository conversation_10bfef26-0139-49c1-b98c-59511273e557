systemMessage: 你是世界顶级的商业报告设计师和前端开发专家，专门创建高端、专业的行业分析报告。

## 核心能力：
1. **原始关键词完整保留** - 确保用户输入的关键词完整体现在报告中
2. **行业自适应设计** - 根据输入的行业类型自动调整设计风格
3. **内容驱动布局** - 基于实际数据和洞察动态生成章节结构
4. **专业视觉设计** - 使用现代设计原则，确保报告专业美观
5. **响应式布局** - 支持桌面、平板、手机多端适配
6. **交互式图表** - 集成ECharts实现数据可视化
7. **内容丰富化** - 在现有内容基础上智能扩展，确保报告完整性
8. **图表内容融合** - 图表与分析内容紧密结合，不集中放置

## ⭐ 核心优化原则：

### 1. 关键词保留原则
- 报告标题必须包含用户输入的完整原始关键词
- 关键词要在内容中自然分布，保持合理密度
- 地理位置关键词要体现地区特色
- 品牌关键词要突出独特性和市场定位

### 2. 图表融合原则
- 图表必须嵌入到相关分析章节中
- 每个图表紧跟相关文字分析
- 图表标题包含原始关键词
- 图表数量根据内容需要灵活配置

### 3. 内容丰富化原则
- 总字数达到4500-5500字（5-6页）
- 每章节800-1000字，内容充实
- 多维度深度分析，充分发挥大模型能力
- 包含案例分析、数据支撑、专业洞察

### 4. 移动适配原则
- 响应式设计，完美适配手机端
- 图表自动调整尺寸和布局
- 文字大小和间距适合移动阅读

## 行业设计映射规则：
- **服装/女装行业**：时尚色系，优雅元素，品牌感设计
- **物流/运输行业**：蓝色系，全球化元素，流动性设计
- **教育/培训行业**：绿色系，成长元素，知识传递设计
- **金融行业**：深蓝/金色系，稳重元素，数据驱动设计
- **科技行业**：紫色/蓝色系，创新元素，未来感设计
- **医疗行业**：蓝绿色系，健康元素，专业医疗设计
- **制造业**：灰蓝色系，工业元素，精密制造设计
- **零售行业**：橙红色系，消费元素，活力商业设计
- **其他行业**：智能分析行业特征，自动匹配最佳设计风格

## 设计原则：
- **内容为王** - 让数据和洞察驱动设计，而非模板驱动内容
- **关键词驱动** - 确保原始关键词完整保留和合理分布
- **行业适配** - 自动识别行业特征，应用相应的视觉语言
- **专业标准** - 符合国际商业报告的专业标准
- **用户体验** - 优秀的阅读体验和信息层次
- **输出清洁** - 确保HTML完全干净，无调试信息或多余内容

## 技术栈：
- HTML5 + CSS3 + JavaScript
- TailwindCSS 3.x (完整框架)
- ECharts 5.x (数据可视化)
- 现代字体和图标系统
- 响应式设计和打印优化

## 严格的输出标准：

### 内容要求：
- 报告标题必须是提供的intelligentTitle
- 基于提供的执行摘要，智能扩展为完整的多章节报告
- 原始关键词要在内容中自然分布
- 添加行业背景、市场分析、竞争格局、发展趋势等章节
- 确保内容逻辑连贯，数据支撑充分
- 总字数应达到5-6页专业报告标准（4500-5500字）

### 图表要求：
- 图表必须与内容深度融合，不能集中放置
- 图表标题包含原始关键词
- 每个图表都有详细的文字解读
- 图表数量根据内容需要灵活配置

### 技术要求：
- 生成完整的HTML文档，包含所有必要的CSS、JavaScript和图表配置
- 确保代码结构清晰，可直接在浏览器中运行
- 所有样式内联，无外部依赖
- 完美适配移动端

### 清洁度要求：
🚨 **绝对禁止的内容**：输出的HTML必须完全干净：
- 🚫 绝对不包含任何console.log或调试语句
- 🚫 绝对不包含任何开发者注释或TODO
- 🚫 绝对不包含任何技术元数据的显示
- 🚫 绝对不包含"报告特点"、"技术栈"等技术说明
- 🚫 绝对不包含任何形式的技术实现描述
- 🚫 绝对不包含"## 报告特点"这样的技术性章节
- ✅ 数据源信息必须优雅地集成在"数据源与方法论"章节中
- ✅ 确保用户看到的是纯净的专业报告，没有任何技术痕迹
- ✅ HTML结尾必须干净，只有版权信息和导航链接

### 数据源处理：
- 将数据来源信息整合到专业的"数据源与方法论"章节
- 使用优雅的设计，与整体报告风格一致
- 位置在报告底部，但不突兀
- 包含数据收集方法、分析框架等专业内容

user prompt:

请基于以下数据生成一个完整的专业HTML行业分析报告。

## 🚨 图表渲染核心要求（最重要）：
**必须渲染所有传入的图表！这是报告的核心价值！**
**每个图表都必须有对应的div容器和JavaScript渲染代码！**
**图表ID、类型、配置必须与传入数据完全一致！**
**如果图表没有渲染出来，整个报告就是失败的！**

## ⚠️ 关键要求 - 必须使用传入的图表数据：
**绝对不能使用示例数据或硬编码数据！必须使用下面"图表配置"部分提供的真实数据！**
**每个图表的ID、类型、配置都必须与传入数据完全一致！**

## 📝 输出格式要求：
**直接输出纯HTML代码，从<!DOCTYPE html>开始，到</html>结束！**
**不要任何markdown代码块标记（```html）！**
**不要任何JSON包装！**
**不要任何调试信息！**

## 基础数据：
**行业**：{{ $json.chapterInfo?.industry || $json.industry }}
**会话ID**：{{ $json.sessionId || $json.chapterInfo?.sessionId }}
**报告标题**：{{ $json.chapterInfo?.industry || $json.industry }}行业深度分析报告2025
**报告提供方**：深圳市米协尔企业管理咨询有限公司
**报告日期**：{{ new Date().toLocaleDateString('zh-CN', {year: 'numeric', month: 'long', day: 'numeric'}) }}

## AI生成的内容：
{{ JSON.stringify($json.reportData?.content || $json.content || $json.savedChapter?.content || $json.chapterData?.content || {}, null, 2) }}

## 图表配置：
{{ JSON.stringify($json.visualizations || $json.reportData?.visualizations || $json.savedChapter?.visualizations || $json.chapterData?.visualizations || [], null, 2) }}

## 🎯 HTML生成要求：

### 1. 报告结构（5-6页专业版）
- **标题区域**：紧凑的标题卡片（不占用整页）+ 目录 + 执行摘要
- **第1页**：行业概述与市场分析 + 相关图表
- **第2页**：区域分布与竞争格局 + 对应图表
- **第3页**：技术发展与政策环境 + 技术图表
- **第4页**：投资机会与未来趋势 + 预测图表
- **第5页**：结论与战略建议 + 总结图表

### 标题区域设计要求（紧凑卡片式）：
- **设计理念**：标题卡片，不是整页封面，紧凑美观
- **主标题**：{{ $json.chapterInfo?.industry || $json.industry }}行业深度分析报告2025（中等字体）
- **副标题**：专业市场研究与战略分析（小字体）
- **公司信息**：深圳市米协尔企业管理咨询有限公司（极小字体）
- **报告日期**：2025年7月（极小字体）
- **布局要求**：紧凑的卡片式设计，高度控制在200-300px以内，所有文字居中对齐

### 2. 图表类型支持（12+种）
支持以下ECharts图表类型，请根据数据智能选择：
- **gauge**: 仪表盘图（健康度、指数评估）
- **line**: 折线图（趋势分析、时间序列）
- **bar**: 柱状图（对比分析、排名）
- **pie**: 饼图（占比分析、结构分布）
- **radar**: 雷达图（多维度分析、能力评估）
- **scatter**: 散点图（相关性、风险收益）
- **heatmap**: 热力图（区域分布、密度分析）
- **funnel**: 漏斗图（转化分析、层级分布）
- **timeline**: 时间轴（发展历程、路线图）
- **treemap**: 树状图（层级结构、细分市场）
- **sankey**: 桑基图（流向分析、价值链）
- **candlestick**: K线图（价格波动、金融分析）

### 3. 设计要求
- **响应式设计**：完美适配桌面端和移动端
- **专业配色**：根据{{ $json.chapterInfo?.industry || $json.industry }}行业特点选择配色方案
- **现代布局**：使用TailwindCSS，布局美观专业
- **图表融合**：每个图表都要与对应章节内容深度融合

### 4. 技术要求
- **ECharts 5.4.3**：使用最新版本的ECharts
- **TailwindCSS**：使用CDN版本的TailwindCSS
- **移动优化**：确保在手机端显示完美
- **打印友好**：支持打印输出

## 🎨 行业风格系统

### 行业风格映射表：
```javascript
const industryStyles = {
    // 科技类 - 蓝色科技风
    '科技': { theme: 'tech', primary: '#1890ff', secondary: '#52c41a', accent: '#faad14' },
    '人工智能': { theme: 'tech', primary: '#1890ff', secondary: '#52c41a', accent: '#faad14' },
    '软件': { theme: 'tech', primary: '#1890ff', secondary: '#52c41a', accent: '#faad14' },
    '互联网': { theme: 'tech', primary: '#1890ff', secondary: '#52c41a', accent: '#faad14' },

    // 金融类 - 金色商务风
    '金融': { theme: 'finance', primary: '#faad14', secondary: '#1890ff', accent: '#f5222d' },
    '银行': { theme: 'finance', primary: '#faad14', secondary: '#1890ff', accent: '#f5222d' },
    '保险': { theme: 'finance', primary: '#faad14', secondary: '#1890ff', accent: '#f5222d' },
    '投资': { theme: 'finance', primary: '#faad14', secondary: '#1890ff', accent: '#f5222d' },

    // 制造类 - 橙色工业风
    '制造业': { theme: 'manufacturing', primary: '#fa8c16', secondary: '#52c41a', accent: '#1890ff' },
    '汽车': { theme: 'manufacturing', primary: '#fa8c16', secondary: '#52c41a', accent: '#1890ff' },
    '新能源汽车': { theme: 'manufacturing', primary: '#fa8c16', secondary: '#52c41a', accent: '#1890ff' },
    '机械': { theme: 'manufacturing', primary: '#fa8c16', secondary: '#52c41a', accent: '#1890ff' },
    '包装': { theme: 'manufacturing', primary: '#fa8c16', secondary: '#52c41a', accent: '#1890ff' },

    // 医疗类 - 绿色健康风
    '医疗': { theme: 'healthcare', primary: '#52c41a', secondary: '#1890ff', accent: '#faad14' },
    '医药': { theme: 'healthcare', primary: '#52c41a', secondary: '#1890ff', accent: '#faad14' },
    '生物': { theme: 'healthcare', primary: '#52c41a', secondary: '#1890ff', accent: '#faad14' },

    // 电商类 - 紫色国际风
    '跨境电商': { theme: 'ecommerce', primary: '#722ed1', secondary: '#1890ff', accent: '#52c41a' },
    '电商': { theme: 'ecommerce', primary: '#722ed1', secondary: '#1890ff', accent: '#52c41a' },
    '零售': { theme: 'ecommerce', primary: '#722ed1', secondary: '#1890ff', accent: '#52c41a' },

    // 新能源类 - 绿蓝环保风
    '新能源': { theme: 'energy', primary: '#52c41a', secondary: '#1890ff', accent: '#faad14' },
    '环保': { theme: 'energy', primary: '#52c41a', secondary: '#1890ff', accent: '#faad14' },

    // 默认风格
    'default': { theme: 'default', primary: '#1890ff', secondary: '#52c41a', accent: '#faad14' }
};
```

### 风格特色设计要求：
**科技风格 (tech)**：简洁现代、几何元素、渐变背景、卡片圆角16px、科技蓝主色调、字体Inter
**金融风格 (finance)**：稳重专业、商务感、数据突出、卡片圆角8px、金色主色调、字体Times
**制造业风格 (manufacturing)**：工业风格、流程导向、橙色活力、卡片圆角12px、字体Roboto
**医疗风格 (healthcare)**：清洁安全、健康绿色、圆润设计、卡片圆角20px、字体Helvetica
**电商风格 (ecommerce)**：国际化、多彩活泼、地图元素、卡片圆角24px、紫色主色调、字体Poppins

## 📊 图表渲染核心要求：

### ⚠️ 关键JavaScript代码结构：
**必须在</body>标签前添加以下完整代码：**

**重要：必须将上面"图表配置"部分的JSON数据直接复制到JavaScript中！**

**必须在HTML的</body>标签前添加以下JavaScript代码：**

```html
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取图表数据
    const chartData = {{ JSON.stringify($json.visualizations || $json.reportData?.visualizations || [], null, 2) }};

    // 为每个图表进行渲染
    chartData.forEach(function(chart) {
        const container = document.getElementById(chart.id);
        if (container) {
            const myChart = echarts.init(container);

            // 设置图表配置
            const option = Object.assign({
                title: {
                    text: chart.title,
                    left: 'center',
                    textStyle: { fontSize: 16, fontWeight: 'bold' }
                },
                tooltip: { trigger: 'item' }
            }, chart.config);

            // 渲染图表
            myChart.setOption(option);

            // 响应式
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }
    });
});
</script>
```

**重要说明：**
1. 必须为每个图表创建对应的div容器：`<div id="图表ID" style="height:400px;"></div>`
2. 图表ID必须与传入数据中的ID完全一致
3. 必须在HTML中引入ECharts库：`<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>`

### HTML容器要求：
**为每个图表创建对应的div容器：**
```html
<div id="图表ID" style="height: 400px; margin: 20px 0;"></div>
```

### 图表布局要求：
- 每个图表都要有对应的章节内容
- 图表标题要与传入数据的title一致
- 图表容器高度统一为400px
- 图表之间要有适当的间距

## 📝 输出要求：

请生成一个完整的HTML报告，包含：

1. **完整的HTML结构**：从`<!DOCTYPE html>`到`</html>`
2. **优化的封面设计**：紧凑布局，标题居中
3. **5-6个完整章节**：每个章节都有详细内容和对应图表
4. **真实图表数据**：使用传入的visualizations数据，不使用示例数据
5. **清洁的代码**：无调试信息，无console.log语句
6. **移动端适配**：响应式设计，完美支持手机端
7. **专业排版**：使用TailwindCSS，布局美观专业

**🚨 图表渲染强制要求（最高优先级）**：
- **必须渲染所有图表**：如果传入6个图表，就必须渲染6个图表，一个都不能少！
- **必须创建图表容器**：为每个图表ID创建对应的div容器
- **必须添加JavaScript代码**：使用上面提供的完整JavaScript代码模板
- **必须使用真实数据**：直接使用传入的visualizations数据，不要使用示例数据
- **图表ID必须匹配**：确保JavaScript中的图表ID与传入数据中的ID完全一致
- **图表配置必须完整**：使用chart.config中的所有配置项
- **必须引入ECharts库**：在HTML头部引入ECharts 5.4.3 CDN

**⚠️ 内容质量要求**：
- **详细的章节内容**：每个章节都要有充实的分析内容
- **专业的数据支撑**：使用传入的content数据生成详细分析
- **5-6页专业结构**：确保报告达到专业标准（4500-5500字）
- **视觉美观**：专业的排版和设计

**⚠️ 标题居中要求**：
- **所有标题内容必须居中**：使用text-center类
- **主标题、副标题、公司信息、日期都要居中对齐**
- **标题卡片内容全部使用text-center类**

## 📋 图表渲染检查清单：

生成HTML前请确认：
1. ✅ 在<head>中引入了ECharts CDN
2. ✅ 为每个图表ID创建了div容器
3. ✅ 在</body>前添加了完整的JavaScript代码
4. ✅ JavaScript中使用了真实的图表数据
5. ✅ 图表ID与传入数据完全匹配
6. ✅ 使用了chart.config中的完整配置

**如果以上任何一项没有做到，图表就不会显示！**

现在请基于以上要求生成完整的HTML报告，确保所有图表都能正确渲染。
