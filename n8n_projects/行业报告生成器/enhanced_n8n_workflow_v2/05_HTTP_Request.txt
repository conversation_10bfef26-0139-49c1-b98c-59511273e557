{"url": "https://api.302.ai/searchapi/search", "sendQuery": true, "queryParameters": {"parameters": [{"name": "q", "value": "={{ $json.allSearchTerms.slice(3, 6).join(' ') }}"}, {"name": "=location", "value": "={{ $json.region === '中国' ? 'China' : 'Global' }}"}, {"name": "hl", "value": "={{ $json.language === 'zh-CN' ? 'zh-cn' : 'en' }}"}, {"name": "num", "value": "10"}, {"name": "api_key", "value": "sk-LMfpbQt8WPp71EXwFmW6fedhTJX9J4lag5znVNRReG5fWPSc"}, {"name": "engine", "value": "google"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}, {"name": "User-Agent", "value": "n8n-industry-report-generator"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}