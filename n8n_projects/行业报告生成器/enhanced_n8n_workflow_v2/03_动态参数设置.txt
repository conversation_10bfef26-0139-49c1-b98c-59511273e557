// 优化版参数设置节点 - 解决关键词提取和内容丰富化问题
// 解决问题：1. 原始关键词丢失 2. 报告内容不够丰富

const items = $input.all();
const rawInputData = items[0].json;

console.log('=== 优化版参数设置 ===');
console.log('原始输入数据:', JSON.stringify(rawInputData, null, 2));

// ===== 智能数据提取 =====
let inputData = {};

// 检查是否是webhook原始数据格式
if (rawInputData.body && rawInputData.webhookUrl && rawInputData.executionMode) {
  inputData = rawInputData.body;
  console.log('✅ 检测到webhook数据，从body中提取:', JSON.stringify(inputData, null, 2));
} else if (rawInputData.industry || rawInputData.chatInput) {
  inputData = rawInputData;
  console.log('✅ 使用直接数据格式');
} else {
  inputData = rawInputData;
  console.log('⚠️ 使用原始数据格式');
}

// ===== 原始关键词提取与保留系统 =====
function extractOriginalKeywords(rawInput) {
  if (!rawInput || typeof rawInput !== 'string') return null;
  
  const originalKeywords = {
    fullInput: rawInput.trim(),                    // 完整原始输入
    primaryKeywords: [],                           // 主要关键词
    locationKeywords: [],                          // 地理位置关键词
    industryKeywords: [],                          // 行业关键词
    brandKeywords: []                              // 品牌/特色关键词
  };
  
  // 地理位置提取（扩展版）
  const locationPattern = /(北京|上海|广州|深圳|杭州|成都|武汉|西安|南京|天津|重庆|苏州|青岛|长沙|大连|厦门|无锡|福州|济南|宁波|温州|石家庄|长春|哈尔滨|沈阳|太原|合肥|南昌|郑州|南宁|海口|贵阳|昆明|拉萨|西宁|银川|乌鲁木齐|东莞|佛山|中山|珠海|江门|湛江|茂名|肇庆|惠州|梅州|汕头|河源|阳江|清远|韶关|揭阳|潮州|云浮)/g;
  const locations = rawInput.match(locationPattern) || [];
  originalKeywords.locationKeywords = [...new Set(locations)];
  
  // 品牌/特色关键词提取（保留所有非通用词汇）
  const commonWords = ['行业', '分析', '报告', '市场', '发展', '趋势', '投资', '机会', '研究', '深度', '2024', '2025'];
  const words = rawInput.split(/[，。、\s\-_]+/).filter(word => 
    word.length >= 2 && 
    !commonWords.includes(word) &&
    !/^\d+$/.test(word) &&
    word.trim() !== ''
  );
  originalKeywords.brandKeywords = [...new Set(words)];
  
  // 提取主要关键词（最重要的1-3个词）
  originalKeywords.primaryKeywords = originalKeywords.brandKeywords.slice(0, 3);
  
  console.log('🔍 原始关键词提取结果:', originalKeywords);
  
  return originalKeywords;
}

// ===== 核心参数提取 =====
function extractIndustry(data) {
  const possibleFields = ['industry', 'chatInput', 'query', 'keyword', 'topic', 'subject'];
  for (const field of possibleFields) {
    if (data[field] && typeof data[field] === 'string' && data[field].trim()) {
      return data[field].trim();
    }
  }
  return null;
}

// ===== 增强版行业识别 =====
function intelligentIndustryRecognition(rawInput, originalKeywords = null) {
  if (!rawInput || typeof rawInput !== 'string') return null;

  const input = rawInput.toLowerCase();

  // 增强版行业关键词映射表 - 与02_参数设置.txt保持完全一致
  const industryMapping = {
    '广州女淑装': ['广州女淑装', '女淑装', '淑女装', '广州服装', '女装品牌', '淑女服饰', '广州女装'],
    '服装': ['服装', '女装', '男装', '童装', '时装', '服饰', '纺织', '时尚', '女淑装', '淑女装', '成衣', '服装设计', '时装设计', '服装品牌', '服装制造', '服装贸易'],
    '物流': ['物流', '快递', '运输', '配送', '仓储', '供应链', '跨境物流', '货运', '快运', '物流配送', '冷链物流'],
    '教育': ['教育', '培训', '学校', '在线教育', '职业教育', '教学', '培训机构', '教育培训', '学习', '教育科技'],
    '医疗': ['医疗', '医院', '药品', '医药', '健康', '医疗器械', '医疗服务', '医疗健康', '生物医药', '医疗科技'],
    '金融': ['金融', '银行', '保险', '证券', '投资', '理财', '支付', '金融服务', '互联网金融', '金融科技'],
    '科技': ['科技', '互联网', '软件', '人工智能', 'ai', '大数据', '云计算', '科技公司', '技术服务', '信息技术'],
    '制造': ['制造', '生产', '工厂', '制造业', '加工', '机械', '制造企业', '生产制造', '智能制造'],
    '零售': ['零售', '商超', '购物', '电商', '销售', '商业', '零售业', '商品销售', '新零售'],
    '包装': ['包装', '包装材料', '包装设计', '包装印刷', '包装机械', '包装容器', '包装盒', '包装袋', '包装工业'],
    '房地产': ['房地产', '地产', '房产', '建筑', '装修', '家居', '房地产开发', '建筑业', '房屋租赁'],
    '汽车': ['汽车', '车辆', '汽配', '新能源车', '电动车', '汽车制造', '汽车服务', '汽车销售'],
    '食品': ['食品', '餐饮', '食物', '饮料', '农业', '食材', '食品加工', '餐饮服务', '食品安全'],
    '旅游': ['旅游', '酒店', '旅行', '景区', '民宿', '度假', '旅游服务', '酒店服务', '文旅']
  };

  // 优先匹配完整关键词
  if (originalKeywords && originalKeywords.brandKeywords.length > 0) {
    const mainKeyword = originalKeywords.brandKeywords[0];
    for (const [industry, keywords] of Object.entries(industryMapping)) {
      if (keywords.some(keyword => mainKeyword.includes(keyword) || keyword.includes(mainKeyword))) {
        console.log('✅ 基于品牌关键词匹配行业:', industry, '关键词:', mainKeyword);
        return industry;
      }
    }
  }

  // 智能匹配
  for (const [industry, keywords] of Object.entries(industryMapping)) {
    if (keywords.some(keyword => input.includes(keyword))) {
      console.log('✅ 基于通用关键词匹配行业:', industry);
      return industry + '行业';
    }
  }

  return null;
}

// ===== 智能标题生成 =====
function generateIntelligentTitle(originalKeywords, industry) {
  let title = '';
  
  // 优先使用原始关键词
  if (originalKeywords && originalKeywords.brandKeywords.length > 0) {
    const mainKeyword = originalKeywords.brandKeywords[0];
    title = `${mainKeyword}`;
    
    // 添加地理位置
    if (originalKeywords.locationKeywords.length > 0) {
      const location = originalKeywords.locationKeywords[0];
      if (!title.includes(location)) {
        title = `${location}${title}`;
      }
    }
    
    // 添加行业后缀
    if (!title.includes('行业') && !title.includes('市场')) {
      title += `深度分析报告2024`;
    } else {
      title += `深度分析报告2024`;
    }
  } else {
    // 回退到传统方式
    title = `${industry}深度分析报告2024`;
  }
  
  return title;
}

// ===== 动态章节扩展 =====
function generateRichSections(industry, originalKeywords) {
  const baseSections = [
    '执行摘要',
    '行业概述与定义',
    '市场规模与增长分析',
    '细分市场深度研究',
    '区域市场分布分析',
    '竞争格局与主要企业',
    '产业链价值分析',
    '商业模式创新研究',
    '技术发展与创新趋势',
    '政策环境与监管影响',
    '投资机会与风险评估',
    '未来发展趋势预测',
    '战略建议与行动方案'
  ];
  
  // 根据原始关键词添加特色章节
  const customSections = [];
  
  if (originalKeywords && originalKeywords.locationKeywords.length > 0) {
    const location = originalKeywords.locationKeywords[0];
    customSections.push(`${location}地区市场特色分析`);
    customSections.push(`${location}政策环境与优势`);
  }
  
  if (originalKeywords && originalKeywords.brandKeywords.length > 0) {
    const brand = originalKeywords.brandKeywords[0];
    customSections.push(`${brand}细分领域深度研究`);
    customSections.push(`${brand}发展模式与案例分析`);
  }
  
  return [...baseSections, ...customSections];
}

// ===== 执行主逻辑 =====
const rawInput = extractIndustry(inputData);
const originalKeywords = extractOriginalKeywords(rawInput);

let industry = rawInput;

// 如果没有找到行业，进行智能识别
if (!industry) {
  industry = intelligentIndustryRecognition(rawInput, originalKeywords);
  
  if (industry) {
    console.log('✅ 智能识别行业:', industry, '原始输入:', rawInput);
  }
}

// 如果仍然没有识别出行业，返回错误
if (!industry) {
  return [{
    json: {
      error: true,
      message: '未能识别行业关键词，请检查输入参数',
      supportedFields: ['industry', 'chatInput', 'query', 'keyword', 'topic', 'subject'],
      inputData: inputData,
      suggestion: '请输入明确的行业关键词，如：服装、物流、教育、医疗等'
    }
  }];
}

// 生成智能标题
const intelligentTitle = generateIntelligentTitle(originalKeywords, industry);

// 生成丰富章节
const richSections = generateRichSections(industry, originalKeywords);

// ===== 智能参数配置 =====
const config = {
  // 基础参数
  industry: industry,
  region: inputData.region || '中国',
  timeRange: inputData.timeRange || '2024-2025',
  
  // 原始关键词保留 (新增)
  originalKeywords: originalKeywords,
  intelligentTitle: intelligentTitle,
  
  // 报告控制参数
  reportType: inputData.reportType || 'comprehensive',
  reportStyle: inputData.reportStyle || 'consulting',
  pageTarget: Math.min(Math.max(inputData.pageTarget || 6, 5), 8), // 限制5-8页
  
  // 内容控制参数
  includeCharts: inputData.includeCharts !== false,
  includeImages: inputData.includeImages !== false,
  language: inputData.language || 'zh-CN',
  
  // 丰富章节配置 (新增)
  customSections: richSections
};

// ===== A4页面控制计算 =====
const pageControl = {
  targetPages: config.pageTarget,
  wordsPerPage: 900, // 增加到900字每页，确保内容丰富
  totalWords: config.pageTarget * 900,
  sectionsCount: config.customSections.length,
  wordsPerSection: Math.floor((config.pageTarget * 900) / config.customSections.length),
  chartsPerPage: 1.5, // 增加图表密度
  totalCharts: Math.ceil(config.pageTarget * 1.5)
};

// ===== 增强搜索关键词生成 =====
const searchStrategies = {
  market: [
    `${industry} 市场规模 ${config.timeRange}`,
    `${industry} 行业分析 ${config.region}`,
    `${industry} 市场容量 统计数据`,
    `${industry} 产业发展现状 ${config.region}`
  ],
  competition: [
    `${industry} 竞争格局 主要企业`,
    `${industry} 市场份额 领军企业`,
    `${industry} 行业集中度分析`,
    `${industry} 企业排名 ${config.timeRange}`
  ],
  // 添加原始关键词相关搜索
  original: originalKeywords ? [
    `${originalKeywords.fullInput} 市场分析`,
    `${originalKeywords.fullInput} 发展趋势`,
    `${originalKeywords.fullInput} 竞争分析`,
    `${originalKeywords.fullInput} 投资机会`
  ] : []
};

// 合并所有搜索词
const allSearchTerms = Object.values(searchStrategies).flat().filter(term => term.trim() !== '');

// ===== 返回结果 =====
const result = {
  json: {
    // 基础配置
    ...config,
    
    // 页面控制
    pageControl: pageControl,
    
    // 搜索配置
    searchStrategies: searchStrategies,
    allSearchTerms: allSearchTerms,
    
    // 元数据
    metadata: {
      configVersion: '3.0-enhanced-keywords',
      timestamp: new Date().toISOString(),
      estimatedTokens: pageControl.totalWords * 1.5,
      processingStrategy: 'keyword-driven',
      qualityLevel: config.reportType,
      hasOriginalKeywords: !!originalKeywords,
      keywordCount: originalKeywords ? originalKeywords.brandKeywords.length : 0
    },
    
    // 状态
    status: {
      error: false,
      message: `参数配置成功 - ${intelligentTitle}`,
      keywordsExtracted: !!originalKeywords,
      nextSteps: ['基于关键词的数据采集', '内容丰富化生成', '图表智能配置']
    }
  }
};

console.log('✅ 优化版配置生成完成:', {
  industry: config.industry,
  title: intelligentTitle,
  originalKeywords: originalKeywords,
  sectionsCount: richSections.length,
  searchTermsCount: allSearchTerms.length,
  estimatedWords: pageControl.totalWords
});

return [result];

// ===== 使用说明 =====
/*
这个优化版参数设置节点解决了两个核心问题：

1. 关键词提取问题：
   - 完整保留用户输入的原始关键词（如"广州女淑装"）
   - 智能提取地理位置、品牌特色等关键词
   - 在报告标题和内容中合理分布这些关键词

2. 内容丰富化问题：
   - 动态扩展章节，基于关键词生成个性化章节
   - 增加每页字数到900字，确保内容充实
   - 提供更多搜索关键词，为大模型提供更丰富的数据基础

使用方法：
1. 将此代码替换原有的"参数设置"节点代码
2. 确保输入数据包含chatInput或industry字段
3. 系统会自动提取和保留原始关键词
4. 生成的配置会传递给后续节点使用

预期效果：
- 报告标题包含"广州女淑装"等原始关键词
- 报告内容达到5-6页，内容丰富专业
- 图表与内容深度融合，数量灵活配置
*/
