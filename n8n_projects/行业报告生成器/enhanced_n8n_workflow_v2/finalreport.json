[{"output": "```html\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>包装行业深度分析报告2025</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js\"></script>\n    <style>\n        /* 包装行业风格 - 橙色工业风 */\n        :root {\n            --primary: #fa8c16;\n            --secondary: #52c41a;\n            --accent: #1890ff;\n            --bg-light: #fff9f0;\n            --text-dark: #333;\n        }\n        \n        body {\n            font-family: 'Roboto', 'PingFang SC', 'Microsoft YaHei', sans-serif;\n            color: var(--text-dark);\n            line-height: 1.6;\n        }\n        \n        .industry-header {\n            background: linear-gradient(135deg, #fa8c16 0%, #fadb14 100%);\n            color: white;\n            padding: 2rem 1.5rem;\n            text-align: center;\n            margin-bottom: 2rem;\n            border-radius: 12px;\n            box-shadow: 0 4px 20px rgba(250, 140, 22, 0.3);\n            max-width: 1000px;\n            margin: 2rem auto 3rem auto;\n            position: relative;\n            height: 250px;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n        }\n        \n        .section-title {\n            color: var(--primary);\n            border-left: 4px solid var(--primary);\n            padding-left: 1rem;\n            margin: 2rem 0 1.5rem;\n            font-weight: 600;\n        }\n        \n        .data-card {\n            background: var(--bg-light);\n            border-radius: 8px;\n            padding: 1.5rem;\n            margin: 1.5rem 0;\n            border-left: 3px solid var(--primary);\n            box-shadow: 0 2px 10px rgba(0,0,0,0.05);\n        }\n        \n        .chart-container {\n            width: 100%;\n            height: 400px;\n            margin: 2rem 0;\n            border-radius: 8px;\n            background: white;\n            box-shadow: 0 2px 15px rgba(0,0,0,0.1);\n            padding: 1rem;\n        }\n        \n        @media (max-width: 768px) {\n            .industry-header {\n                padding: 1.5rem 1rem;\n                margin-bottom: 2rem;\n                border-radius: 8px;\n                height: 220px;\n            }\n            .chart-container {\n                height: 300px;\n            }\n        }\n    </style>\n</head>\n<body class=\"bg-white\">\n    <!-- 包装行业风格化标题卡片 -->\n    <div class=\"industry-header\">\n        <h1 class=\"text-3xl md:text-4xl font-bold mb-2\">包装行业深度分析报告2025</h1>\n        <h2 class=\"text-lg md:text-xl font-light mb-4\">专业市场研究与战略分析</h2>\n        <div class=\"text-sm opacity-90\">深圳市米协尔企业管理咨询有限公司</div>\n        <div class=\"text-xs opacity-75\">2025年7月</div>\n    </div>\n\n    <!-- 报告内容 -->\n    <div class=\"container mx-auto px-4 py-6 max-w-5xl\">\n        <!-- 目录 -->\n        <div class=\"mb-12\">\n            <h2 class=\"section-title text-2xl\">目录</h2>\n            <div class=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n                <a href=\"#summary\" class=\"hover:text-orange-600\">执行摘要</a>\n                <a href=\"#overview\" class=\"hover:text-orange-600\">行业概述</a>\n                <a href=\"#market\" class=\"hover:text-orange-600\">市场分析</a>\n                <a href=\"#region\" class=\"hover:text-orange-600\">区域分析</a>\n                <a href=\"#competition\" class=\"hover:text-orange-600\">竞争格局</a>\n                <a href=\"#tech\" class=\"hover:text-orange-600\">技术创新</a>\n                <a href=\"#policy\" class=\"hover:text-orange-600\">政策环境</a>\n                <a href=\"#investment\" class=\"hover:text-orange-600\">投资分析</a>\n                <a href=\"#trend\" class=\"hover:text-orange-600\">未来趋势</a>\n                <a href=\"#conclusion\" class=\"hover:text-orange-600\">结论建议</a>\n            </div>\n        </div>\n\n        <!-- 执行摘要 -->\n        <div id=\"summary\" class=\"mb-12\">\n            <h2 class=\"section-title text-2xl\">执行摘要</h2>\n            <div class=\"data-card\">\n                <p>中国包装行业在2024年展现出稳健发展态势，市场规模达到1.8万亿元，同比增长4-5%。其中包装印刷作为最大细分市场占比45%，纸包装领域表现尤为突出。行业呈现明显的梯队化竞争格局：第一梯队企业（裕同科技、奥瑞金等）营收超100亿元；第二梯队（宝钢包装、昇兴股份等）营收30-100亿元；第三梯队企业营收低于30亿元。</p>\n                <p class=\"mt-4\">值得关注的是，高端包装需求增长显著，消费电子等领域通过精美包装提升品牌形象的趋势明显。本报告将从市场结构、竞争格局、技术创新等六大维度进行深度剖析。</p>\n            </div>\n            \n            <div class=\"chart-container\" id=\"market_structure_chart\"></div>\n        </div>\n\n        <!-- 行业概述 -->\n        <div id=\"overview\" class=\"mb-12\">\n            <h2 class=\"section-title text-2xl\">行业概述</h2>\n            <p>中国包装行业已形成完整的产业链体系，涵盖原材料供应、生产制造、设计印刷、物流配送等环节。从发展历程看，行业经历了从传统包装到智能包装的转型升级，2021-2025年复合增长率达7.2%。</p>\n            \n            <div class=\"data-card\">\n                <h3 class=\"font-semibold text-lg mb-2\">产业链价值分布</h3>\n                <p>当前产业链价值分布呈现微笑曲线特征：</p>\n                <ul class=\"list-disc pl-5 mt-2\">\n                    <li>上游原材料（占比30%）利润较高</li>\n                    <li>下游品牌服务（占比25%）附加值高</li>\n                    <li>中游制造环节（占比20%）竞争激烈</li>\n                </ul>\n            </div>\n            \n            <h3 class=\"font-semibold text-lg mt-6 mb-2\">专业术语解析</h3>\n            <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div class=\"bg-gray-50 p-4 rounded-lg\">\n                    <h4 class=\"font-medium\">柔性包装</h4>\n                    <p class=\"text-sm mt-1\">适应多种形状的包装解决方案</p>\n                </div>\n                <div class=\"bg-gray-50 p-4 rounded-lg\">\n                    <h4 class=\"font-medium\">无菌包装</h4>\n                    <p class=\"text-sm mt-1\">食品医药领域的高端包装技术</p>\n                </div>\n                <div class=\"bg-gray-50 p-4 rounded-lg\">\n                    <h4 class=\"font-medium\">智能标签</h4>\n                    <p class=\"text-sm mt-1\">集成RFID等技术的包装组件</p>\n                </div>\n            </div>\n        </div>\n\n        <!-- 市场分析 -->\n        <div id=\"market\" class=\"mb-12\">\n            <h2 class=\"section-title text-2xl\">市场分析</h2>\n            <p>2024年包装市场细分结构显示：纸包装（45%）、塑料包装（30%）、金属包装（15%）、玻璃包装（7%）、其他（3%）。纸包装领域，高端礼品盒市场增速达12%，显著高于行业平均水平。</p>\n            \n            <div class=\"chart-container\" id=\"material_trend_chart\"></div>\n            \n            <div class=\"data-card\">\n                <h3 class=\"font-semibold text-lg mb-2\">区域分布特征</h3>\n                <p>区域分布呈现明显集群化特征：长三角（35%市场份额）、珠三角（28%）、环渤海（20%）。预计到2025年，智能包装市场规模将突破800亿元，CAGR达15%。</p>\n                <p class=\"mt-2\">关键数据支撑：</p>\n                <ul class=\"list-disc pl-5 mt-1\">\n                    <li>裕同科技2023年营收148亿元（+8.5%）</li>\n                    <li>纸包装原材料成本占比达55%</li>\n                    <li>华东地区包装企业密度为其他区域2.3倍</li>\n                </ul>\n            </div>\n        </div>\n\n        <!-- 区域分析 -->\n        <div id=\"region\" class=\"mb-12\">\n            <h2 class=\"section-title text-2xl\">区域分析</h2>\n            <p>区域发展呈现差异化特征：长三角以电子产品包装为主导（占区域产值42%），珠三角专注日化包装（占38%），环渤海侧重食品医药包装（占45%）。</p>\n            \n            <div class=\"chart-container\" id=\"regional_heatmap_chart\"></div>\n            \n            <div class=\"data-card\">\n                <h3 class=\"font-semibold text-lg mb-2\">企业分布热图</h3>\n                <p>企业分布热图显示：广东（28%）、江苏（22%）、浙江（18%）三省集中了全国68%的规模以上包装企业。中西部地区呈现追赶态势，湖北、四川近三年包装产业投资增速分别达14%和12%。</p>\n                <p class=\"mt-2\">区域专业化术语：</p>\n                <ul class=\"list-disc pl-5 mt-1\">\n                    <li>产业集群指数</li>\n                    <li>区域集中度CR3</li>\n                    <li>区位熵系数</li>\n                </ul>\n            </div>\n        </div>\n\n        <!-- 竞争格局 -->\n        <div id=\"competition\" class=\"mb-12\">\n            <h2 class=\"section-title text-2xl\">竞争格局</h2>\n            <p>行业CR5为28.5%，CR10达41.2%，市场集中度持续提升。第一梯队企业平均毛利率18.5%，研发投入占比3.2%；第二梯队毛利率15.8%，研发投入2.1%。</p>\n            \n            <div class=\"chart-container\" id=\"competitive_radar_chart\"></div>\n            \n            <div class=\"data-card\">\n                <h3 class=\"font-semibold text-lg mb-2\">竞争策略分化</h3>\n                <p>竞争策略分化明显：裕同科技通过纵向整合控制成本（原材料自给率65%），奥瑞金专注高附加值金属包装（单品利润率25%）。新兴势力如富科技在环保包装领域快速崛起，近三年营收CAGR达32%。</p>\n                <p class=\"mt-2\">关键竞争指标：</p>\n                <ul class=\"list-disc pl-5 mt-1\">\n                    <li>产能利用率</li>\n                    <li>客户集中度</li>\n                    <li>专利储备量</li>\n                </ul>\n            </div>\n        </div>\n\n        <!-- 技术创新 -->\n        <div id=\"tech\" class=\"mb-12\">\n            <h2 class=\"section-title text-2xl\">技术创新</h2>\n            <p>行业技术发展呈现三大趋势：1) 数字印刷渗透率提升至28%（2023年仅为15%）；2) 可降解材料应用比例达17%；3) 智能包装技术专利申请量年增25%。</p>\n            \n            <div class=\"chart-container\" id=\"tech_roadmap_chart\"></div>\n            \n            <div class=\"data-card\">\n                <h3 class=\"font-semibold text-lg mb-2\">领先企业研发方向</h3>\n                <p>领先企业研发方向：裕同科技布局纳米涂层技术（防伪性能提升40%），奥瑞金开发智能温控罐体（获23项专利）。</p>\n                <p class=\"mt-2\">技术术语解析：</p>\n                <ul class=\"list-disc pl-5 mt-1\">\n                    <li>水性油墨</li>\n                    <li>模内贴标</li>\n                    <li>气调保鲜包装</li>\n                </ul>\n                <p class=\"mt-2\">数据支撑：</p>\n                <ul class=\"list-disc pl-5 mt-1\">\n                    <li>行业研发投入总额达86亿元</li>\n                    <li>智能制造设备占比31%</li>\n                    <li>数字印刷成本下降28%</li>\n                </ul>\n            </div>\n        </div>\n\n        <!-- 政策环境 -->\n        <div id=\"policy\" class=\"mb-12\">\n            <h2 class=\"section-title text-2xl\">政策环境</h2>\n            <p>新版《限制商品过度包装要求》实施后，月饼包装成本占比从35%降至18%。环保政策推动可回收材料使用率提升至62%，较2020年增加24个百分点。</p>\n            \n            <div class=\"data-card\">\n                <h3 class=\"font-semibold text-lg mb-2\">政策影响</h3>\n                <p>碳足迹认证成为出口包装的硬性要求，欧盟市场准入标准将包装回收率门槛提高到75%。</p>\n                <p class=\"mt-2\">政策术语：</p>\n                <ul class=\"list-disc pl-5 mt-1\">\n                    <li>生产者责任延伸</li>\n                    <li>绿色供应链</li>\n                    <li>碳标签制度</li>\n                </ul>\n                <p class=\"mt-2\">关键影响数据：</p>\n                <ul class=\"list-disc pl-5 mt-1\">\n                    <li>环保改造成本增加15-20%</li>\n                    <li>纸代塑替代率提升至45%</li>\n                    <li>包装减量化节约材料成本12%</li>\n                </ul>\n            </div>\n        </div>\n\n        <!-- 投资分析 -->\n        <div id=\"investment\" class=\"mb-12\">\n            <h2 class=\"section-title text-2xl\">投资分析</h2>\n            <p>2024年行业并购金额达87亿元，标的集中在智能包装（42%）和环保材料（35%）领域。资本市场给予头部企业18-25倍PE估值，细分龙头溢价明显（如裕同科技动态PE26倍）。</p>\n            \n            <div class=\"data-card\">\n                <h3 class=\"font-semibold text-lg mb-2\">投资热点区域</h3>\n                <ul class=\"list-disc pl-5 mt-1\">\n                    <li>长三角智能制造园区（亩均投资强度380万元）</li>\n                    <li>成渝地区绿色包装基地（政府补贴比例30%）</li>\n                </ul>\n                <p class=\"mt-2\">风险指标：</p>\n                <ul class=\"list-disc pl-5 mt-1\">\n                    <li>原材料价格波动系数0.38</li>\n                    <li>应收账款周转天数97天</li>\n                    <li>产能过剩预警指数62</li>\n                </ul>\n            </div>\n        </div>\n\n        <!-- 未来趋势 -->\n        <div id=\"trend\" class=\"mb-12\">\n            <h2 class=\"section-title text-2xl\">未来趋势</h2>\n            <p>到2030年，包装行业将呈现三大发展趋势：1) 智能包装渗透率突破40%；2) 循环包装模式占比达25%；3) 包装即服务(PaaS)市场规模达1200亿元。</p>\n            \n            <div class=\"chart-container\" id=\"growth_forecast_chart\"></div>\n            \n            <div class=\"data-card\">\n                <h3 class=\"font-semibold text-lg mb-2\">技术融合方向</h3>\n                <ul class=\"list-disc pl-5 mt-1\">\n                    <li>纳米材料+保鲜包装（延长食品保质期30%）</li>\n                    <li>区块链+防伪包装（溯源准确率99.9%）</li>\n                    <li>物联网+物流包装（降低损耗率15%）</li>\n                </ul>\n                <p class=\"mt-2\">发展术语：</p>\n                <ul class=\"list-disc pl-5 mt-1\">\n                    <li>工业4.0包装</li>\n                    <li>碳中性包装</li>\n                    <li>活性包装系统</li>\n                </ul>\n            </div>\n        </div>\n\n        <!-- 结论建议 -->\n        <div id=\"conclusion\" class=\"mb-12\">\n            <h2 class=\"section-title text-2xl\">结论与建议</h2>\n            <div class=\"data-card\">\n                <h3 class=\"font-semibold text-lg mb-2\">核心结论</h3>\n                <ul class=\"list-disc pl-5 mt-1\">\n                    <li>行业将保持5-6%的稳健增长</li>\n                    <li>高端化、智能化、绿色化是明确方向</li>\n                    <li>市场集中度将持续提升</li>\n                </ul>\n                \n                <h3 class=\"font-semibold text-lg mt-4 mb-2\">战略建议</h3>\n                <ul class=\"list-disc pl-5 mt-1\">\n                    <li>头部企业应加强技术并购（建议研发投入占比≥4%）</li>\n                    <li>中小企业需专注细分领域差异化（建议毛利率目标≥20%）</li>\n                    <li>所有企业必须建立绿色供应链体系（2027年前完成改造）</li>\n                </ul>\n                \n                <h3 class=\"font-semibold text-lg mt-4 mb-2\">实施路径</h3>\n                <ul class=\"list-disc pl-5 mt-1\">\n                    <li>第一阶段（2025-2026）完成数字化基础建设</li>\n                    <li>第二阶段（2027-2028）实现智能制造升级</li>\n                    <li>第三阶段（2029-2030）构建产业生态圈</li>\n                </ul>\n            </div>\n        </div>\n\n        <!-- 数据源与方法论 -->\n        <div class=\"mt-12 pt-6 border-t border-gray-200\">\n            <h2 class=\"section-title text-xl\">数据源与方法论</h2>\n            <div class=\"text-sm text-gray-600\">\n                <p>本报告数据来源包括：</p>\n                <ul class=\"list-disc pl-5 mt-1\">\n                    <li>观研天下市场研究数据</li>\n                    <li>中国包装联合会行业统计</li>\n                    <li>上市公司年报及公告</li>\n                    <li>国家统计局及工信部公开数据</li>\n                    <li>行业协会调研数据</li>\n                </ul>\n                <p class=\"mt-2\">分析方法采用PESTEL模型结合波特五力分析框架，通过定量与定性相结合的方式完成行业评估。</p>\n            </div>\n            <div class=\"mt-6 text-xs text-gray-500\">\n                <p>© 2025 深圳市米协尔企业管理咨询有限公司 版权所有</p>\n            </div>\n        </div>\n    </div>\n\n    <script>\n        // 初始化所有图表\n        document.addEventListener('DOMContentLoaded', function() {\n            // 市场结构旭日图\n            const marketStructureChart = echarts.init(document.getElementById('market_structure_chart'));\n            marketStructureChart.setOption({\n                title: { text: '2024包装市场层级结构', left: 'center' },\n                tooltip: { trigger: 'item', formatter: '{a} <br/>{b}: {c}%' },\n                series: {\n                    type: 'sunburst',\n                    data: [\n                        {\n                            name: '纸包装',\n                            value: 45,\n                            children: [\n                                { name: '彩盒', value: 25 },\n                                { name: '瓦楞箱', value: 15 },\n                                { name: '礼品盒', value: 5 }\n                            ]\n                        },\n                        {\n                            name: '塑料包装',\n                            value: 30,\n                            children: [\n                                { name: '软包装', value: 18 },\n                                { name: '硬包装', value: 12 }\n                            ]\n                        },\n                        { name: '金属包装', value: 15 },\n                        { name: '玻璃包装', value: 7 },\n                        { name: '其他', value: 3 }\n                    ],\n                    radius: [0, '90%'],\n                    label: { rotate: 'radial' },\n                    itemStyle: {\n                        color: function(params) {\n                            const colorList = ['#fa8c16', '#faad14', '#d46b08', '#fa541c', '#ff7a45'];\n                            return colorList[params.dataIndex % colorList.length];\n                        }\n                    }\n                }\n            });\n\n            // 竞争格局雷达图\n            const competitiveRadarChart = echarts.init(document.getElementById('competitive_radar_chart'));\n            competitiveRadarChart.setOption({\n                title: { text: '三大梯队企业能力对比', left: 'center' },\n                radar: {\n                    indicator: [\n                        { name: '营收规模', max: 100 },\n                        { name: '毛利率', max: 30 },\n                        { name: '研发投入', max: 5 },\n                        { name: '客户集中度', max: 50 },\n                        { name: '产能利用率', max: 100 }\n                    ],\n                    splitArea: { show: false },\n                    axisLine: { lineStyle: { color: '#fa8c16' } }\n                },\n                series: [{\n                    type: 'radar',\n                    data: [\n                        {\n                            value: [95, 18.5, 3.2, 35, 85],\n                            name: '第一梯队',\n                            itemStyle: { color: '#fa8c16' },\n                            lineStyle: { color: '#fa8c16', width: 2 }\n                        },\n                        {\n                            value: [65, 15.8, 2.1, 42, 78],\n                            name: '第二梯队',\n                            itemStyle: { color: '#faad14' },\n                            lineStyle: { color: '#faad14', width: 2 }\n                        },\n                        {\n                            value: [30, 12.3, 1.5, 55, 65],\n                            name: '第三梯队',\n                            itemStyle: { color: '#ffd666' },\n                            lineStyle: { color: '#ffd666', width: 2 }\n                        }\n                    ]\n                }]\n            });\n\n            // 材料趋势面积图\n            const materialTrendChart = echarts.init(document.getElementById('material_trend_chart'));\n            materialTrendChart.setOption({\n                title: { text: '包装材料结构演变趋势', left: 'center' },\n                tooltip: { trigger: 'axis', axisPointer: { type: 'cross' } },\n                legend: { data: ['纸质', '塑料', '金属', '玻璃', '其他'], bottom: 10 },\n                xAxis: { type: 'category', data: ['2019', '2020', '2021', '2022', '2023', '2024'] },\n                yAxis: { type: 'value', name: '占比(%)' },\n                series: [\n                    {\n                        name: '纸质',\n                        type: 'line',\n                        stack: '总量',\n                        areaStyle: { color: '#fa8c16' },\n                        lineStyle: { color: '#d46b08' },\n                        data: [42, 43, 44, 44, 45, 45]\n                    },\n                    {\n                        name: '塑料',\n                        type: 'line',\n                        stack: '总量',\n                        areaStyle: { color: '#faad14' },\n                        lineStyle: { color: '#d48806' },\n                        data: [32, 31, 30, 30, 30, 30]\n                    },\n                    {\n                        name: '金属',\n                        type: 'line',\n                        stack: '总量',\n                        areaStyle: { color: '#ffd666' },\n                        lineStyle: { color: '#d4a106' },\n                        data: [15, 15, 15, 15, 15, 15]\n                    },\n                    {\n                        name: '玻璃',\n                        type: 'line',\n                        stack: '总量',\n                        areaStyle: { color: '#ffe58f' },\n                        lineStyle: { color: '#d4b106' },\n                        data: [8, 8, 8, 8, 7, 7]\n                    },\n                    {\n                        name: '其他',\n                        type: 'line',\n                        stack: '总量',\n                        areaStyle: { color: '#fff1b8' },\n                        lineStyle: { color: '#d4c106' },\n                        data: [3, 3, 3, 3, 3, 3]\n                    }\n                ]\n            });\n\n            // 区域热力图\n            const regionalHeatmapChart = echarts.init(document.getElementById('regional_heatmap_chart'));\n            regionalHeatmapChart.setOption({\n                title: { text: '包装企业区域分布热力图', left: 'center' },\n                tooltip: { position: 'top' },\n                xAxis: {\n                    type: 'category',\n                    data: ['广东', '江苏', '浙江', '山东', '上海', '福建', '湖北', '四川'],\n                    splitArea: { show: true }\n                },\n                yAxis: {\n                    type: 'category',\n                    data: ['企业数量', '营收规模', '投资强度', '创新能力'],\n                    splitArea: { show: true }\n                },\n                visualMap: {\n                    min: 0,\n                    max: 100,\n                    calculable: true,\n                    orient: 'horizontal',\n                    left: 'center',\n                    bottom: '5%',\n                    inRange: {\n                        color: ['#fff1b8', '#ffe58f', '#ffd666', '#faad14', '#fa8c16']\n                    }\n                },\n                series: [{\n                    type: 'heatmap',\n                    data: [\n                        [0, 0, 95], [0, 1, 90], [0, 2, 88], [0, 3, 92],\n                        [1, 0, 85], [1, 1, 82], [1, 2, 80], [1, 3, 78],\n                        [2, 0, 80], [2, 1, 78], [2, 2, 75], [2, 3, 72],\n                        [3, 0, 70], [3, 1, 68], [3, 2, 65], [3, 3, 62],\n                        [4, 0, 65], [4, 1, 72], [4, 2, 70], [4, 3, 75],\n                        [5, 0, 60], [5, 1, 58], [5, 2, 55], [5, 3, 52],\n                        [6, 0, 55], [6, 1, 50], [6, 2, 48], [6, 3, 45],\n                        [7, 0, 50], [7, 1, 48], [7, 2, 45], [7, 3, 42]\n                    ],\n                    label: { show: true },\n                    emphasis: {\n                        itemStyle: { shadowBlur: 10, shadowColor: 'rgba(0, 0, 0, 0.5)' }\n                    }\n                }]\n            });\n\n            // 技术路线图\n            const techRoadmapChart = echarts.init(document.getElementById('tech_roadmap_chart'));\n            techRoadmapChart.setOption({\n                title: { text: '包装技术发展路线图', left: 'center' },\n                baseOption: {\n                    timeline: {\n                        axisType: 'category',\n                        orient: 'horizontal',\n                        autoPlay: true,\n                        playInterval: 2000,\n                        data: ['2019', '2021', '2022', '2023', '2024', '2025'],\n                        symbol: 'circle',\n                        symbolSize: 8,\n                        lineStyle: { color: '#fa8c16' },\n                        label: { color: '#333' },\n                        checkpointStyle: {\n                            color: '#fa8c16',\n                            borderColor: '#d46b08',\n                            borderWidth: 2\n                        },\n                        controlStyle: {\n                            showPlayBtn: true,\n                            showNextBtn: true,\n                            showPrevBtn: true,\n                            color: '#fa8c16',\n                            borderColor: '#fa8c16'\n                        }\n                    },\n                    series: [{\n                        type: 'line',\n                        smooth: true,\n                        symbol: 'circle',\n                        symbolSize: 8,\n                        lineStyle: { width: 3 }\n                    }]\n                },\n                options: [\n                    {\n                        title: { text: '数字印刷技术普及', subtext: '2019年数据' },\n                        series: [{\n                            data: [15, 18, 22, 25, 28, 32],\n                            itemStyle: { color: '#fa8c16' },\n                            lineStyle: { color: '#fa8c16' }\n                        }]\n                    },\n                    {\n                        title: { text: '可降解材料应用', subtext: '2019年数据' },\n                        series: [{\n                            data: [5, 8, 10, 13, 17, 22],\n                            itemStyle: { color: '#52c41a' },\n                            lineStyle: { color: '#52c41a' }\n                        }]\n                    },\n                    {\n                        title: { text: '智能包装渗透率', subtext: '2019年数据' },\n                        series: [{\n                            data: [8, 12, 15, 20, 25, 30],\n                            itemStyle: { color: '#1890ff' },\n                            lineStyle: { color: '#1890ff' }\n                        }]\n                    }\n                ]\n            });\n\n            // 增长预测折线图\n            const growthForecastChart = echarts.init(document.getElementById('growth_forecast_chart'));\n            growthForecastChart.setOption({\n                title: { text: '包装市场五年增长预测', left: 'center' },\n                tooltip: { trigger: 'axis' },\n                legend: { data: ['乐观情景', '基准情景', '保守情景'], bottom: 10 },\n                xAxis: {\n                    type: 'category',\n                    data: ['2025', '2026', '2027', '2028', '2029', '2030']\n                },\n                yAxis: { type: 'value', name: '市场规模(十亿元)' },\n                series: [\n                    {\n                        name: '乐观情景',\n                        type: 'line',\n                        data: [200, 235, 275, 320, 370, 425],\n                        itemStyle: { color: '#52c41a' },\n                        lineStyle: { color: '#52c41a', width: 3 }\n                    },\n                    {\n                        name: '基准情景',\n                        type: 'line',\n                        data: [200, 230, 265, 305, 350, 400],\n                        itemStyle: { color: '#1890ff' },\n                        lineStyle: { color: '#1890ff', width: 3 }\n                    },\n                    {\n                        name: '保守情景',\n                        type: 'line',\n                        data: [200, 220, 245, 275, 310, 350],\n                        itemStyle: { color: '#fa8c16' },\n                        lineStyle: { color: '#fa8c16', width: 3 }\n                    }\n                ]\n            });\n\n            // 窗口大小变化时重新调整图表大小\n            window.addEventListener('resize', function() {\n                marketStructureChart.resize();\n                competitiveRadarChart.resize();\n                materialTrendChart.resize();\n                regionalHeatmapChart.resize();\n                techRoadmapChart.resize();\n                growthForecastChart.resize();\n            });\n        });\n    </script>\n</body>\n</html>\n```"}]