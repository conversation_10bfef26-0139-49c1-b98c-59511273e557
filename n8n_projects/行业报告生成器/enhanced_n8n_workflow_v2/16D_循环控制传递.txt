// 16D 循环控制传递节点
// 严格控制循环次数，防止无限循环

const inputData = $input.first().json;

console.log('=== 循环控制传递节点 ===');

// ===== 严格的循环控制 =====
const currentRetry = inputData.retryCount || 0;
const maxRetries = 3;

console.log('🔄 当前重试次数:', currentRetry);
console.log('📊 最大允许次数:', maxRetries);

// 🚨 强制循环控制：检查是否已经超过限制
if (currentRetry >= maxRetries) {
  console.error('🚨 已达到最大重试次数，强制停止循环');
  
  // 返回强制通过的数据，确保循环停止
  return [{
    json: {
      ...inputData,
      retryCount: maxRetries, // 确保不再增加
      loopInfo: {
        currentLoop: maxRetries,
        maxLoops: maxRetries,
        isLastChance: true,
        forceStopped: true
      },
      qualityAssessment: {
        ...inputData.qualityAssessment,
        score: 85,
        passed: true,
        forcePass: true,
        reason: '强制停止循环'
      },
      processingStatus: {
        ...inputData.processingStatus,
        loopControlled: true,
        forceStopped: true
      }
    }
  }];
}

// ===== 正常的循环计数增加 =====
const newRetryCount = currentRetry + 1;

console.log('🔄 更新循环计数:', currentRetry, '->', newRetryCount);

// 执行时间监控
const currentTime = Date.now();
const startTime = inputData.monitoring?.startTime || currentTime;
const totalTime = currentTime - startTime;
const totalSeconds = Math.round(totalTime / 1000);

console.log('⏱️ 总执行时间:', totalSeconds, '秒');

// 记录循环时间
const loopTimes = inputData.monitoring?.loopTimes || [];
const lastLoopTime = loopTimes.length > 0 ? loopTimes[loopTimes.length - 1].endTime : startTime;
const currentLoopTime = currentTime - lastLoopTime;

loopTimes.push({
  loopNumber: newRetryCount,
  startTime: lastLoopTime,
  endTime: currentTime,
  duration: currentLoopTime,
  durationSeconds: Math.round(currentLoopTime / 1000)
});

console.log('本次循环耗时:', Math.round(currentLoopTime / 1000), '秒');

// 性能警告
if (totalTime > 300000) { // 5分钟
  console.error('🚨 总执行时间过长:', totalSeconds, '秒，强制停止');
  
  return [{
    json: {
      ...inputData,
      retryCount: maxRetries, // 强制停止
      loopInfo: {
        currentLoop: maxRetries,
        maxLoops: maxRetries,
        isLastChance: true,
        timeoutStopped: true
      },
      qualityAssessment: {
        ...inputData.qualityAssessment,
        score: 85,
        passed: true,
        forcePass: true,
        reason: '执行时间超时，强制停止'
      },
      processingStatus: {
        ...inputData.processingStatus,
        loopControlled: true,
        timeoutStopped: true
      }
    }
  }];
} else if (totalTime > 180000) { // 3分钟
  console.warn('⚠️ 执行时间较长:', totalSeconds, '秒');
} else {
  console.log('✅ 执行时间正常:', totalSeconds, '秒');
}

// ===== 构建返回数据 =====
const isLastChance = newRetryCount >= maxRetries;

console.log('🎯 循环状态:');
console.log('- 新重试次数:', newRetryCount);
console.log('- 是否最后机会:', isLastChance);
console.log('- 下次将', isLastChance ? '强制通过' : '继续检测');

return [{
  json: {
    ...inputData,
    retryCount: newRetryCount,
    loopInfo: {
      currentLoop: newRetryCount,
      maxLoops: maxRetries,
      isLastChance: isLastChance,
      loopTimestamp: new Date().toISOString()
    },
    monitoring: {
      startTime: startTime,
      startTimeISO: new Date(startTime).toISOString(),
      currentTime: currentTime,
      currentTimeISO: new Date(currentTime).toISOString(),
      totalExecutionTime: totalTime,
      totalExecutionSeconds: totalSeconds,
      loopTimes: loopTimes,
      averageLoopTime: loopTimes.length > 0 ?
        Math.round(loopTimes.reduce((sum, loop) => sum + loop.duration, 0) / loopTimes.length / 1000) : 0,
      complexityLevel: inputData.monitoring?.complexityLevel || 'HIGH',
      estimatedLoops: maxRetries,
      estimatedTotalTime: inputData.monitoring?.estimatedTotalTime || 180000,
      initialized: true
    },
    processingStatus: {
      ...inputData.processingStatus,
      loopControlled: true,
      retryCountUpdated: true
    }
  }
}];
