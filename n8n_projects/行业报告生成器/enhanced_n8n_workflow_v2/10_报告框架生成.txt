{"promptType": "define", "text": "=基于以下数据为{{ $json.industry }}行业生成专业报告框架，特别注意为每个章节设计合适的数据可视化方案。\n\n## 数据分析：\n**基础信息**：\n- 行业：{{ $json.industry }}\n- 会话ID：{{ $json.sessionId }}\n- 数据质量：{{ $json.agentData.dataQuality }}分 (满分100)\n- 内容数量：{{ $json.agentData.contentCount }}条\n- 数据丰富度：{{ $json.agentData.dataSummary.dataRichness }}\n- 推荐分析方法：{{ $json.agentData.dataSummary.recommendedApproach }}\n\n**内容样本分析**：\n{{ JSON.stringify($json.agentData.contentSample, null, 2) }}\n\n## 任务要求：\n请设计一个6章节的专业行业分析报告框架，每章节都要包含具体的可视化设计方案。\n\n## 输出格式（严格JSON，不要任何其他文字）：\n{ ...详见 workflow JSON... }\n", "options": {"systemMessage": "=你是一位拥有15年经验的顶级行业分析师和数据可视化专家。你的专长包括：\n1. 行业分析能力...\n2. 数据解读能力...\n3. 可视化设计能力...\n4. 报告架构设计...\n...（详见 workflow JSON）"}}