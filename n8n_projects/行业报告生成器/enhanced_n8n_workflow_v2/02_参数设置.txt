// 2. 参数设置增强版 - 智能参数解析和配置生成
// 标注：ENHANCED - 支持多种输入格式，智能参数推断，A4页面控制
// 修改标注：★ 新增A4页面精确控制 ★ 智能行业识别 ★ 多语言支持

const items = $input.all();
const rawInputData = items[0].json;

console.log('=== 参数设置增强版 ===');
console.log('原始输入数据:', JSON.stringify(rawInputData, null, 2));

// ===== 智能数据提取 =====
let inputData = {};

// 检查是否是webhook原始数据格式
if (rawInputData.body && rawInputData.webhookUrl && rawInputData.executionMode) {
  // 从webhook的body中提取实际数据
  inputData = rawInputData.body;
  console.log('✅ 检测到webhook数据，从body中提取:', JSON.stringify(inputData, null, 2));
} else if (rawInputData.industry || rawInputData.chatInput) {
  // 直接数据格式
  inputData = rawInputData;
  console.log('✅ 使用直接数据格式');
} else {
  // 尝试从其他位置提取
  inputData = rawInputData;
  console.log('⚠️ 使用原始数据格式');
}

// ===== 核心参数提取和智能行业识别 =====
function extractIndustry(data) {
  const possibleFields = ['industry', 'chatInput', 'query', 'keyword', 'topic', 'subject'];
  for (const field of possibleFields) {
    if (data[field] && typeof data[field] === 'string' && data[field].trim()) {
      return data[field].trim();
    }
  }
  return null;
}

function intelligentIndustryRecognition(rawInput) {
  if (!rawInput || typeof rawInput !== 'string') return null;

  const input = rawInput.toLowerCase();

  // 行业关键词映射表 - 修复包装行业识别
  const industryMapping = {
    '服装': ['服装', '女装', '男装', '童装', '时装', '服饰', '纺织', '时尚', '女淑装', '淑女装'],
    '物流': ['物流', '快递', '运输', '配送', '仓储', '供应链', '跨境物流'],
    '教育': ['教育', '培训', '学校', '在线教育', '职业教育', '教学'],
    '医疗': ['医疗', '医院', '药品', '医药', '健康', '医疗器械', '医疗服务'],
    '金融': ['金融', '银行', '保险', '证券', '投资', '理财', '支付'],
    '科技': ['科技', '互联网', '软件', '人工智能', 'ai', '大数据', '云计算'],
    '制造': ['制造', '生产', '工厂', '制造业', '加工', '机械'],
    '零售': ['零售', '商超', '购物', '电商', '销售', '商业'],
    '包装': ['包装', '包装材料', '包装设计', '包装印刷', '包装机械', '包装容器', '包装盒', '包装袋', '包装工业'],
    '房地产': ['房地产', '地产', '房产', '建筑', '装修', '家居'],
    '汽车': ['汽车', '车辆', '汽配', '新能源车', '电动车'],
    '食品': ['食品', '餐饮', '食物', '饮料', '农业', '食材'],
    '旅游': ['旅游', '酒店', '旅行', '景区', '民宿', '度假']
  };

  // 智能匹配
  for (const [industry, keywords] of Object.entries(industryMapping)) {
    if (keywords.some(keyword => input.includes(keyword))) {
      return industry + '行业';
    }
  }

  return null;
}

let industry = extractIndustry(inputData);

// 如果没有找到行业，进行智能识别
if (!industry) {
  const rawInput = inputData.chatInput || inputData.query || inputData.keyword || '';
  industry = intelligentIndustryRecognition(rawInput);

  if (industry) {
    console.log('✅ 智能识别行业:', industry, '原始输入:', rawInput);
  }
}

// 如果仍然没有识别出行业，返回错误
if (!industry) {
  return [{
    json: {
      error: true,
      message: '未能识别行业关键词，请检查输入参数',
      supportedFields: ['industry', 'chatInput', 'query', 'keyword', 'topic', 'subject'],
      inputData: inputData,
      suggestion: '请输入明确的行业关键词，如：服装、物流、教育、医疗等'
    }
  }];
}

// ===== 智能参数配置 =====
const config = {
  // 基础参数
  industry: industry,
  region: inputData.region || '中国',
  timeRange: inputData.timeRange || '2024-2025',
  
  // 报告控制参数 (新增)
  reportType: inputData.reportType || 'comprehensive',
  reportStyle: inputData.reportStyle || 'consulting',
  pageTarget: Math.min(Math.max(inputData.pageTarget || 6, 5), 8), // 限制5-8页
  
  // 内容控制参数
  includeCharts: inputData.includeCharts !== false,
  includeImages: inputData.includeImages !== false,
  language: inputData.language || 'zh-CN',
  
  // 自定义章节 (新增)
  customSections: inputData.customSections || [
    '执行摘要', '市场概述', '细分市场', '区域分析', 
    '竞争格局', 'SWOT分析', '技术趋势', '政策环境',
    '投资分析', '风险评估', '未来预测', '战略建议'
  ]
};

// ===== A4页面控制计算 (新增) =====
const pageControl = {
  targetPages: config.pageTarget,
  wordsPerPage: 800, // A4页面约800字
  totalWords: config.pageTarget * 800,
  sectionsCount: config.customSections.length,
  wordsPerSection: Math.floor((config.pageTarget * 800) / config.customSections.length),
  chartsPerPage: 1.2, // 平均每页1.2个图表
  totalCharts: Math.ceil(config.pageTarget * 1.2)
};

// ===== 搜索关键词生成 (增强) =====
const searchStrategies = {
  market: [
    `${industry} 市场规模 ${config.timeRange}`,
    `${industry} 行业分析 ${config.region}`,
    `${industry} 市场容量 统计数据`,
    `${industry} 产业发展现状 ${config.region}`
  ],
  competition: [
    `${industry} 竞争格局 主要企业`,
    `${industry} 市场份额 领军企业`,
    `${industry} 行业集中度分析`,
    `${industry} 企业排名 ${config.timeRange}`
  ],
  technology: [
    `${industry} 技术发展趋势`,
    `${industry} 创新技术 新兴技术`,
    `${industry} 数字化转型`,
    `${industry} 技术标准 行业标准`
  ],
  policy: [
    `${industry} 政策支持 国家政策`,
    `${industry} 行业标准 监管政策`,
    `${industry} 产业政策 ${config.region}`,
    `${industry} 法规变化 ${config.timeRange}`
  ],
  investment: [
    `${industry} 投资趋势 融资情况`,
    `${industry} 投资机会 ${config.timeRange}`,
    `${industry} 资本市场 IPO`,
    `${industry} 并购重组 投资案例`
  ],
  supply: [
    `${industry} 供应链分析`,
    `${industry} 产业链上下游`,
    `${industry} 供应商分析`,
    `${industry} 产业链风险`
  ],
  regional: [
    `${industry} ${config.region} 发展情况`,
    `${industry} 区域分布 地域特色`,
    `${industry} 区域竞争力`,
    `${industry} 地区政策 ${config.region}`
  ],
  future: [
    `${industry} 未来展望 发展前景`,
    `${industry} 趋势预测 ${config.timeRange}`,
    `${industry} 发展机遇 挑战`,
    `${industry} 战略规划 发展目标`
  ]
};

// 合并所有搜索词
const allSearchTerms = Object.values(searchStrategies).flat();

// ===== 图片搜索关键词 (新增) =====
const imageKeywords = [
  `${industry} industry`,
  `${industry} technology`,
  `${industry} business`,
  `${industry} market`,
  `supply chain ${industry}`,
  `digital transformation ${industry}`,
  `innovation ${industry}`,
  `future ${industry}`
];

// ===== 数据源配置 (增强) =====
const dataSources = {
  search: {
    tavily: {
      enabled: true,
      maxResults: 15,
      searchDepth: 'advanced',
      includeRawContent: true
    },
    serp: {
      enabled: true,
      engine: 'google',
      maxResults: 10,
      region: config.region === '中国' ? 'cn' : 'global'
    }
  },
  images: {
    unsplash: {
      enabled: config.includeImages,
      maxImages: 8,
      orientation: 'landscape',
      quality: 'high'
    }
  },
  apis: {
    government: {
      enabled: config.region === '中国',
      sources: ['stats.gov.cn', 'mofcom.gov.cn']
    },
    enterprise: {
      enabled: true,
      sources: ['tianyancha', 'qichacha']
    }
  }
};

// ===== 内容生成配置 (新增) =====
const contentGeneration = {
  segments: [
    {
      name: '执行摘要',
      wordTarget: Math.floor(pageControl.wordsPerSection * 0.8),
      priority: 'high',
      includeCharts: false
    },
    {
      name: '市场概述', 
      wordTarget: pageControl.wordsPerSection,
      priority: 'high',
      includeCharts: true
    },
    {
      name: '竞争格局',
      wordTarget: pageControl.wordsPerSection,
      priority: 'high', 
      includeCharts: true
    },
    {
      name: '技术趋势',
      wordTarget: pageControl.wordsPerSection,
      priority: 'medium',
      includeCharts: true
    },
    {
      name: '未来预测',
      wordTarget: pageControl.wordsPerSection,
      priority: 'high',
      includeCharts: true
    }
  ]
};

// ===== 返回结果 =====
const result = {
  json: {
    // 基础配置
    ...config,
    
    // 页面控制 (新增)
    pageControl: pageControl,
    
    // 搜索配置
    searchStrategies: searchStrategies,
    allSearchTerms: allSearchTerms,
    imageKeywords: imageKeywords,
    
    // 数据源配置 (增强)
    dataSources: dataSources,
    
    // 内容生成配置 (新增)
    contentGeneration: contentGeneration,
    
    // 元数据
    metadata: {
      configVersion: '2.0-enhanced',
      timestamp: new Date().toISOString(),
      estimatedTokens: pageControl.totalWords * 1.5, // 估算token数
      processingStrategy: 'segmented', // 分段处理策略
      qualityLevel: config.reportType
    },
    
    // 状态
    status: {
      error: false,
      message: `参数配置成功 - ${industry} (${config.pageTarget}页报告)`,
      nextSteps: ['多源数据采集', '分段内容生成', '图表渲染', '模板应用']
    }
  }
};

console.log('配置生成完成:', {
  industry: config.industry,
  pageTarget: config.pageTarget,
  sectionsCount: config.customSections.length,
  searchTermsCount: allSearchTerms.length,
  estimatedWords: pageControl.totalWords
});

return [result];
