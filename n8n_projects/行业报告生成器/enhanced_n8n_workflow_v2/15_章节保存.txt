// 章节保存处理 - 修复版本
const inputData = $input.first().json;

console.log('=== 章节保存处理 ===');
console.log('🔍 输入数据键:', Object.keys(inputData));
console.log('📊 数据结构检查:');
console.log('- chapterInfo存在:', !!inputData.chapterInfo);
console.log('- reportData存在:', !!inputData.reportData);
console.log('- qualityAssessment存在:', !!inputData.qualityAssessment);

// 🔧 修复：适配新的数据结构
const chapterInfo = inputData.chapterInfo || {};
const reportData = inputData.reportData || {};
const qualityAssessment = inputData.qualityAssessment || {};

console.log('章节ID:', chapterInfo.id);
console.log('质量分数:', qualityAssessment.score);

return [{
  json: {
    // 保存的章节数据 - 使用正确的数据路径
    savedChapter: {
      id: chapterInfo.id,
      title: chapterInfo.title,
      content: reportData.content || {},  // 🔧 修复：使用reportData.content
      visualizations: reportData.visualizations || [],  // 🔧 修复：使用reportData.visualizations
      qualityScore: qualityAssessment.score,
      wordCount: qualityAssessment.totalWordCount,  // 🔧 修复：使用totalWordCount
      savedAt: new Date().toISOString()
    },
    
    // 会话信息
    sessionId: inputData.chapterInfo.sessionId,
    industry: inputData.chapterInfo.industry,
    
    // 处理状态
    currentChapterCompleted: true,
    qualityPassed: inputData.qualityAssessment.passed,
    qualityScore: inputData.qualityAssessment.score,
    
    // 循环控制信息
    processingComplete: true,
    nextAction: "checkLoop",
    
    // 完整的原始数据（供后续节点使用）
    originalData: inputData
  }
}];
