let html = $node['html'].json.output || $node['html'].json.html || $json.output || $json.html || '';

// 1. 去除 Markdown 代码块标记
html = html.replace(/^```html\s*|```$/gim, '').trim();

// 2. 去除 BOM、不可见字符（但保留换行和制表符）
html = html.replace(/^\uFEFF/, '').replace(/[\u200B-\u200D\uFEFF]/g, '');

// 3. 统一换行符
html = html.replace(/\r\n/g, '\n');

// 4. 保护 <script> 标签内容，避免破坏图表代码
const scriptBlocks = [];
html = html.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, (match, offset) => {
  const placeholder = `__SCRIPT_PLACEHOLDER_${scriptBlocks.length}__`;
  scriptBlocks.push(match);
  return placeholder;
});

// 5. 只在非script区域进行转义清理
html = html.replace(/\\([nrt])/g, '\n').replace(/\\"/g, '"').replace(/\\'/g, "'");

// 6. 去除多余的反斜杠（但不影响script内容）
html = html.replace(/\\\\/g, '\\');

// 7. 恢复 <script> 标签内容
scriptBlocks.forEach((script, index) => {
  const placeholder = `__SCRIPT_PLACEHOLDER_${index}__`;
  html = html.replace(placeholder, script);
});

// 8. 去除非法控制字符（但保留换行、制表、回车）
html = html.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');

// 9. 补全未闭合的标签（简单修复）
const fixTag = (tag) => {
  const open = new RegExp(`<${tag}[^>]*>`, 'gi');
  const close = new RegExp(`</${tag}>`, 'gi');
  const openCount = (html.match(open) || []).length;
  const closeCount = (html.match(close) || []).length;
  if (openCount > closeCount) html += `</${tag}>`;
};
fixTag('style');

// 10. 去除 HTML 注释（但保留条件注释）
html = html.replace(/<!--(?!\[if)[\s\S]*?(?<!\[endif\])-->/g, '');

// 11. 去除头尾空行
html = html.trim();

// 12. 验证HTML基本结构
if (!html.includes('<!DOCTYPE html>')) {
  console.warn('警告：HTML缺少DOCTYPE声明');
}
if (!html.includes('<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js">')) {
  console.warn('警告：HTML缺少ECharts库引用');
}

return [{ json: { cleanHtml: html } }];
