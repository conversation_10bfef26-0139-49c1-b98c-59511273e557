// 08_02 数据清洗节点 - 综合优化版本
// 综合08和08_01的优点，修复行业识别问题

const allItems = $input.all();
console.log('=== 数据预处理开始 ===');
console.log('输入数据源数量:', allItems.length);

// ===== 提取行业信息 =====
let industry = '未知行业';
let sessionId = '';

// 从输入数据中查找行业信息和会话ID
for (const item of allItems) {
  const data = item.json;
  if (data.industry && data.industry !== '未知行业') {
    industry = data.industry;
    console.log('✅ 找到行业信息:', industry);
  }
  if (data.sessionId) {
    sessionId = data.sessionId;
    console.log('✅ 找到会话ID:', sessionId);
  }
}

// 如果没有找到sessionId，生成一个
if (!sessionId) {
  sessionId = 'session_' + Date.now();
  console.log('🔧 生成新会话ID:', sessionId);
}

// 如果还是未知行业，尝试从搜索结果中智能识别
if (industry === '未知行业') {
  console.log('⚠️ 未找到明确行业信息，尝试智能识别...');

  // 从搜索结果中提取可能的行业关键词
  const searchTexts = [];
  for (const item of allItems) {
    const data = item.json;
    if (data.results) {
      data.results.forEach(result => {
        if (result.title) searchTexts.push(result.title);
        if (result.content) searchTexts.push(result.content.substring(0, 200));
      });
    }
    if (data.organic_results) {
      data.organic_results.forEach(result => {
        if (result.title) searchTexts.push(result.title);
        if (result.snippet) searchTexts.push(result.snippet);
      });
    }
  }

  // 行业关键词映射 - 精确识别细分行业（优先级从高到低）
  const industryKeywords = {
    // 细分行业优先识别（避免被泛化）
    '跨境物流': ['跨境物流', '国际物流', '跨境运输', '海外物流', '进出口物流', '跨境电商物流', '国际快递', '跨境供应链', '海关清关', '国际货运'],
    '跨境电商': ['跨境电商', '海外电商', '国际电商', '跨境贸易', '出海电商', '跨境零售'],
    '新能源汽车': ['新能源汽车', '电动汽车', '新能源车', '电动车', '混合动力', '充电桩', '动力电池'],
    '在线教育': ['在线教育', '网络教育', '远程教育', '教育科技', 'EdTech', '数字化教育'],
    '医疗器械': ['医疗器械', '医疗设备', '医疗仪器', '诊疗设备', '康复设备'],

    // 传统大行业（放在后面，避免过度泛化）
    '包装': ['包装', '包装材料', '包装设计', '包装印刷', '包装机械', '包装容器', '包装盒', '包装袋'],
    '服装': ['服装', '女装', '男装', '童装', '时装', '服饰', '纺织', '时尚'],
    '物流': ['物流', '快递', '运输', '配送', '仓储', '供应链'], // 放在跨境物流之后
    '教育': ['教育', '培训', '学校', '职业教育'], // 移除在线教育关键词
    '医疗': ['医疗', '医院', '药品', '医药', '健康'], // 移除医疗器械关键词
    '金融': ['金融', '银行', '保险', '证券', '投资', '理财'],
    '科技': ['科技', '互联网', '软件', 'AI', '大数据'], // 移除"人工智能"避免误匹配
    '制造': ['制造', '生产', '工厂', '制造业', '加工'],
    '零售': ['零售', '商超', '购物', '电商', '销售'],
    '食品': ['食品', '餐饮', '食物', '饮料', '农业', '食材'],
    '汽车': ['汽车', '车辆', '汽配'], // 移除新能源车关键词
    '房地产': ['房地产', '地产', '房产', '建筑', '装修', '家居'],
    '旅游': ['旅游', '酒店', '旅行', '景区', '民宿', '度假']
  };

  const allText = searchTexts.join(' ').toLowerCase();
  for (const [industryName, keywords] of Object.entries(industryKeywords)) {
    if (keywords.some(keyword => allText.includes(keyword.toLowerCase()))) {
      industry = industryName; // 不自动添加"行业"后缀
      console.log('🔍 智能识别行业:', industry);
      break;
    }
  }
} else {
  console.log('✅ 保持原有行业信息:', industry);
}

console.log('🎯 最终确定行业:', industry);

// ===== 数据源分类 =====
const dataSources = {
  tavily: [],
  serp: [],
  images: [],
  enterprise: [],
  unknown: []
};

// 分类输入数据
allItems.forEach((item, index) => {
  const data = item.json;
  console.log(`处理数据源 ${index}:`, data.source || 'unknown');
  
  if (data.source === 'tavily' || data.results) {
    dataSources.tavily.push(data);
  } else if (data.source === 'serp' || data.organic_results) {
    dataSources.serp.push(data);
  } else if (data.source === 'images' || (data.results && data.results[0]?.urls)) {
    dataSources.images.push(data);
  } else if (data.source === 'enterprise' || data.companies) {
    dataSources.enterprise.push(data);
  } else {
    dataSources.unknown.push(data);
  }
});

// ===== 内容清洗函数 =====
function cleanText(text) {
  if (!text || typeof text !== 'string') return '';
  
  return text
    .replace(/[\r\n\t]+/g, ' ')
    .replace(/\s+/g, ' ')
    .replace(/[^\u4e00-\u9fa5\w\s.,!?;:()[\]{}""''—-]/g, '')
    .trim()
    .substring(0, 2000);
}

function calculateQualityScore(content, title = '') {
  let score = 0;
  
  const length = content.length;
  if (length > 500) score += 30;
  else if (length > 200) score += 20;
  else if (length > 100) score += 10;
  
  const infoKeywords = ['市场', '规模', '增长', '企业', '技术', '政策', '投资', '竞争', '份额', '趋势'];
  const keywordCount = infoKeywords.filter(keyword => content.includes(keyword)).length;
  score += Math.min(keywordCount * 3, 25);
  
  const dataPatterns = [/\d+%/, /\d+亿/, /\d+万/, /\d{4}年/, /\d+\.?\d*倍/];
  const dataCount = dataPatterns.filter(pattern => pattern.test(content)).length;
  score += Math.min(dataCount * 5, 25);
  
  if (title) {
    if (title.length > 10 && title.length < 100) score += 10;
    if (infoKeywords.some(keyword => title.includes(keyword))) score += 10;
  }
  
  return Math.min(score, 100);
}

// ===== 处理各类数据 =====
const processedTavily = dataSources.tavily.flatMap(source => {
  if (!source.results) return [];
  
  return source.results.map(item => ({
    source: 'tavily',
    title: cleanText(item.title || ''),
    content: cleanText(item.content || ''),
    url: item.url || '',
    score: item.score || 0,
    qualityScore: calculateQualityScore(item.content || '', item.title || ''),
    type: 'text'
  })).filter(item => item.content.length > 50 && item.qualityScore > 30);
});

const processedSerp = dataSources.serp.flatMap(source => {
  if (!source.organic_results) return [];
  
  return source.organic_results.map(item => ({
    source: 'serp',
    title: cleanText(item.title || ''),
    content: cleanText(item.snippet || ''),
    url: item.link || '',
    position: item.position || 999,
    qualityScore: calculateQualityScore(item.snippet || '', item.title || ''),
    type: 'text'
  })).filter(item => item.content.length > 30 && item.qualityScore > 25);
});

// ===== 内容去重和分类 =====
function removeDuplicates(items) {
  const seen = new Set();
  return items.filter(item => {
    const key = item.title + item.content.substring(0, 100);
    if (seen.has(key)) return false;
    seen.add(key);
    return true;
  });
}

const allTextContent = [...processedTavily, ...processedSerp];
const uniqueTextContent = removeDuplicates(allTextContent);

console.log('✅ 数据清洗完成');
console.log('- Tavily数据:', processedTavily.length, '条');
console.log('- SERP数据:', processedSerp.length, '条');
console.log('- 去重后总计:', uniqueTextContent.length, '条');

// ===== 智能内容分析 =====
function analyzeContentThemes(textContent) {
  const themes = {
    market: { keywords: ['市场', '规模', '容量', '需求', '供给', '消费', '用户'], count: 0, quality: 0 },
    competition: { keywords: ['竞争', '份额', '排名', '领先', '主导', '企业', '公司'], count: 0, quality: 0 },
    technology: { keywords: ['技术', '创新', '研发', '专利', '数字化', '智能', '科技'], count: 0, quality: 0 },
    policy: { keywords: ['政策', '法规', '标准', '监管', '政府', '国家'], count: 0, quality: 0 },
    investment: { keywords: ['投资', '融资', '资本', '估值', 'IPO', '并购', '资金'], count: 0, quality: 0 },
    trend: { keywords: ['趋势', '发展', '未来', '预测', '前景', '方向'], count: 0, quality: 0 }
  };

  textContent.forEach(item => {
    const text = (item.title + ' ' + item.content).toLowerCase();

    Object.keys(themes).forEach(theme => {
      const matchCount = themes[theme].keywords.filter(keyword => text.includes(keyword)).length;
      if (matchCount > 0) {
        themes[theme].count += 1;
        themes[theme].quality += item.qualityScore;
      }
    });
  });

  // 计算平均质量
  Object.keys(themes).forEach(theme => {
    if (themes[theme].count > 0) {
      themes[theme].quality = Math.round(themes[theme].quality / themes[theme].count);
    }
  });

  return themes;
}

// ===== 生成数据摘要 =====
function generateDataSummary(textContent, themes) {
  return {
    totalContent: textContent.length,
    highQualityCount: textContent.filter(item => item.qualityScore > 60).length,
    dataRichness: {
      hasNumbers: textContent.filter(item => /\d+/.test(item.content)).length,
      hasPercentages: textContent.filter(item => /\d+%/.test(item.content)).length,
      hasCompanies: textContent.filter(item => /公司|企业|集团/.test(item.content)).length,
      hasFinancial: textContent.filter(item => /亿|万|元|美元/.test(item.content)).length
    },
    contentThemes: Object.fromEntries(
      Object.entries(themes)
        .filter(([_, data]) => data.count > 0)
        .sort(([_, a], [__, b]) => b.count - a.count)
    ),
    topSources: [...new Set(textContent.map(item => item.source))],
    qualityDistribution: {
      excellent: textContent.filter(item => item.qualityScore > 80).length,
      good: textContent.filter(item => item.qualityScore > 60 && item.qualityScore <= 80).length,
      fair: textContent.filter(item => item.qualityScore > 40 && item.qualityScore <= 60).length,
      poor: textContent.filter(item => item.qualityScore <= 40).length
    },
    recommendedApproach: uniqueTextContent.length > 15 ? 'comprehensive' : 'focused'
  };
}

const contentThemes = analyzeContentThemes(uniqueTextContent);
const dataSummary = generateDataSummary(uniqueTextContent, contentThemes);

console.log('🎯 内容主题分析:', Object.keys(contentThemes).map(theme =>
  `${theme}: ${contentThemes[theme].count}条`
).join(', '));

// ===== 输出数据 =====
const outputData = {
  // 核心数据
  industry: industry,
  sessionId: sessionId,
  textContent: uniqueTextContent,

  // 智能分析结果
  contentThemes: contentThemes,
  dataSummary: dataSummary,

  // 数据洞察
  dataInsights: {
    strongestThemes: Object.entries(contentThemes)
      .filter(([_, data]) => data.count > 0)
      .sort(([_, a], [__, b]) => b.count - a.count)
      .slice(0, 5)
      .map(([theme, data]) => ({ theme, count: data.count, quality: data.quality })),

    contentCharacteristics: {
      isDataRich: dataSummary.dataRichness.hasNumbers > uniqueTextContent.length * 0.3,
      hasFinancialData: dataSummary.dataRichness.hasFinancial > 5,
      hasCompetitorInfo: dataSummary.dataRichness.hasCompanies > 3,
      overallQuality: uniqueTextContent.length > 0
        ? Math.round(uniqueTextContent.reduce((sum, item) => sum + item.qualityScore, 0) / uniqueTextContent.length)
        : 0
    },

    recommendedApproach: dataSummary.recommendedApproach
  },

  // 统计信息
  totalItems: uniqueTextContent.length,
  averageQuality: uniqueTextContent.length > 0
    ? Math.round(uniqueTextContent.reduce((sum, item) => sum + item.qualityScore, 0) / uniqueTextContent.length)
    : 0,

  // 高质量内容
  highQualityContent: uniqueTextContent
    .filter(item => item.qualityScore > 60)
    .slice(0, 20),

  // 按来源分类
  tavilyCount: processedTavily.length,
  serpCount: processedSerp.length,

  // 状态信息
  success: true,
  message: `数据清洗完成 - 处理${uniqueTextContent.length}条内容，识别${Object.keys(contentThemes).filter(theme => contentThemes[theme].count > 0).length}个主题`,
  processedAt: new Date().toISOString()
};

console.log('✅ 数据预处理完成');
console.log('🎯 最终行业:', industry);
console.log('📊 输出数据键:', Object.keys(outputData));

// 返回单个对象
return [{
  json: outputData
}];
