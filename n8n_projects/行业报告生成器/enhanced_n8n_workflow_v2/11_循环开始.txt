// 章节循环初始化 - 基于LLM框架生成的结果
const inputData = $input.first().json;
const dataPrep = $node["数据准备"].json;

// 从LLM框架生成结果中提取章节信息
const reportFramework = inputData;
const chapters = reportFramework.chapters || [];

return [{
  json: {
    // 基础数据（来自数据准备）
    sessionId: dataPrep.sessionId,
    industry: dataPrep.industry,
    agentData: dataPrep.agentData,
    
    // 智能框架数据（来自LLM分析）
    reportFramework: reportFramework,
    
    // 动态章节信息（来自LLM分析，不是写死的）
    chapters: chapters,
    totalChapters: chapters.length,
    
    // 循环控制
    currentChapter: 0,
    
    // 存储区域
    generatedChapters: {},
    qualityScores: {},
    
    // 当前章节信息（动态获取）
    currentChapterInfo: {
      id: chapters[0]?.id,
      title: chapters[0]?.title,
      targetWords: chapters[0]?.targetWords,
      description: chapters[0]?.description,
      visualizations: chapters[0]?.visualizations
    },
    
    // 循环状态
    loopStatus: {
      started: true,
      progress: `0/${chapters.length}`,
      startTime: new Date().toISOString()
    }
  }
}];
