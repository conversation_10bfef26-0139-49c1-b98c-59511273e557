// 数据桥接节点
// 放在质量检测失败分支和补充内容生成之间
// 确保数据正确传递到补充内容生成节点

console.log('=== 数据桥接节点 ===');

// 获取质量检测的输出数据
let sourceData;
try {
    if ($input.all().length > 0) {
        sourceData = $input.first().json;
        console.log('✅ 从输入获取数据');
    } else if ($('质量检测').all().length > 0) {
        sourceData = $('质量检测').first().json;
        console.log('✅ 从质量检测节点获取数据');
    } else {
        throw new Error('无法获取源数据');
    }
} catch (error) {
    console.error('❌ 数据桥接失败:', error.message);
    return [{
        json: {
            error: '数据桥接失败',
            message: error.message,
            timestamp: new Date().toISOString()
        }
    }];
}

console.log('📊 桥接数据检查:');
console.log('- 行业信息:', sourceData.industry || '未知');
console.log('- 重试次数:', sourceData.retryCount || 0);
console.log('- 质量问题:', sourceData.qualityIssues?.length || 0, '个');
console.log('- 报告内容存在:', !!sourceData.reportContent);

// 确保数据结构完整
const bridgedData = {
    // 保持原有数据
    ...sourceData,
    
    // 确保关键字段存在
    industry: sourceData.industry || '未知行业',
    retryCount: sourceData.retryCount || 0,
    qualityIssues: sourceData.qualityIssues || ['数据结构不完整'],
    reportContent: sourceData.reportContent || {},
    
    // 添加桥接信息
    bridgeInfo: {
        bridgedAt: new Date().toISOString(),
        sourceNode: '质量检测',
        targetNode: '补充内容生成',
        dataIntegrity: 'verified'
    },
    
    // 添加调试信息
    debugInfo: {
        inputSources: $input.all().length,
        qualityDetectionAvailable: $('质量检测') ? $('质量检测').all().length : 0,
        dataKeys: Object.keys(sourceData)
    }
};

console.log('🔗 数据桥接完成');
console.log('📤 传递给补充内容生成的数据键:', Object.keys(bridgedData));

return [{
    json: bridgedData
}];
