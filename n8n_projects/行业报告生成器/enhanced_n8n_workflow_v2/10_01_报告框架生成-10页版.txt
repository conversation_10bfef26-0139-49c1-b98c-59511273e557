promptType: define
text: 基于以下数据为{{ $json.industry }}行业生成专业的10页深度分析报告框架，特别注意为每个章节设计合适的多样化数据可视化方案。

## 数据分析：
**基础信息**：
- 行业：{{ $json.industry }}
- 会话ID：{{ $json.sessionId }}
- 数据质量：{{ $json.agentData.dataQuality }}分 (满分100)
- 内容数量：{{ $json.agentData.contentCount }}条
- 数据丰富度：{{ $json.agentData.dataSummary.dataRichness }}
- 推荐分析方法：{{ $json.agentData.dataSummary.recommendedApproach }}

**内容样本分析**：
{{ JSON.stringify($json.agentData.contentSample, null, 2) }}

## 任务要求：
请设计一个10章节的专业行业深度分析报告框架，每章节都要包含具体的多样化可视化设计方案。

## 🎯 升级要求：
1. **报告规模**：10页专业报告，8000字以上
2. **图表丰富度**：15-20个图表，12+种不同类型
3. **内容深度**：每章节800-1000字，专业分析深度
4. **图表智能选择**：根据内容类型和行业特点智能选择最合适的图表类型

## 📊 可用图表类型库：
### 基础图表
- **gauge**: 仪表盘图 - 适用于指数、评分、健康度
- **line**: 折线图 - 适用于趋势、时间序列
- **bar**: 柱状图 - 适用于对比、排名
- **pie**: 饼图 - 适用于占比、结构分析

### 高级图表
- **radar**: 雷达图 - 适用于多维度分析、能力评估
- **scatter**: 散点图 - 适用于相关性、风险收益分析
- **heatmap**: 热力图 - 适用于分布、密度分析
- **funnel**: 漏斗图 - 适用于转化、层级分析
- **timeline**: 时间轴 - 适用于发展历程、路线图
- **treemap**: 树状图 - 适用于层级结构、细分市场
- **sankey**: 桑基图 - 适用于流向、价值链分析
- **candlestick**: K线图 - 适用于金融、价格波动

## 输出格式（严格JSON，不要任何其他文字）：
{
  "reportTitle": "{{ $json.industry }}行业深度分析报告2025",
  "sessionId": "{{ $json.sessionId }}",
  "industry": "{{ $json.industry }}",
  "reportMetadata": {
    "totalChapters": 10,
    "estimatedPages": 10,
    "totalTargetWords": 8000,
    "totalVisualizations": 18,
    "reportType": "comprehensive_industry_analysis",
    "analysisDepth": "deep",
    "dataQualityScore": {{ $json.agentData.dataQuality }},
    "contentCount": {{ $json.agentData.contentCount }},
    "analysisApproach": "{{ $json.agentData.dataSummary.recommendedApproach }}",
    "generatedAt": "{{ new Date().toISOString() }}",
    "industry": "{{ $json.industry }}",
    "sessionId": "{{ $json.sessionId }}"
  },
  "chapters": [
    {
      "id": "executiveSummary",
      "title": "执行摘要",
      "targetWords": 800,
      "priority": 1,
      "pageNumber": 1,
      "description": "基于数据的核心发现和关键洞察总结",
      "dataRequirements": "综合行业关键指标和核心结论",
      "keyQuestions": ["行业整体表现如何", "核心驱动因素是什么", "主要机遇和挑战"],
      "visualizations": [
        {
          "id": "industry_health_dashboard",
          "type": "gauge",
          "title": "{{ $json.industry }}行业健康度指数2025",
          "description": "综合评估行业发展健康度和成熟度",
          "dataSource": "行业综合指标",
          "chartPurpose": "直观展示行业整体状况",
          "position": "chapter_integrated"
        }
      ]
    },
    {
      "id": "industryOverview",
      "title": "行业概述与定义",
      "targetWords": 900,
      "priority": 2,
      "pageNumber": 2,
      "description": "行业定义、特征、发展历程和产业链结构",
      "dataRequirements": "行业基础信息、历史发展数据",
      "keyQuestions": ["行业如何定义", "发展历程如何", "产业链结构怎样"],
      "visualizations": [
        {
          "id": "industry_value_chain",
          "type": "sankey",
          "title": "{{ $json.industry }}产业链价值流向图",
          "description": "展示产业链各环节的价值流向和关联关系",
          "dataSource": "产业链结构数据",
          "chartPurpose": "分析产业链价值分布",
          "position": "chapter_integrated"
        },
        {
          "id": "development_timeline",
          "type": "timeline",
          "title": "{{ $json.industry }}行业发展历程",
          "description": "关键发展节点和里程碑事件",
          "dataSource": "历史发展数据",
          "chartPurpose": "梳理行业发展脉络",
          "position": "chapter_integrated"
        }
      ]
    },
    {
      "id": "marketScale",
      "title": "市场规模与增长分析",
      "targetWords": 1000,
      "priority": 3,
      "pageNumber": 3,
      "description": "市场规模、增长趋势、细分市场结构分析",
      "dataRequirements": "市场规模数据、增长数据、细分市场信息",
      "keyQuestions": ["市场规模有多大", "增长趋势如何", "细分市场结构怎样"],
      "visualizations": [
        {
          "id": "market_growth_trend",
          "type": "line",
          "title": "{{ $json.industry }}市场规模增长趋势",
          "description": "近5年市场规模变化和未来5年预测至2030",
          "dataSource": "历史和预测市场数据",
          "chartPurpose": "展示市场增长轨迹",
          "position": "chapter_integrated"
        },
        {
          "id": "market_segmentation",
          "type": "treemap",
          "title": "{{ $json.industry }}细分市场结构图",
          "description": "各细分领域市场规模和占比分布",
          "dataSource": "细分市场数据",
          "chartPurpose": "分析市场结构层次",
          "position": "chapter_integrated"
        }
      ]
    },
    {
      "id": "regionalAnalysis",
      "title": "区域分布与地域特色",
      "targetWords": 900,
      "priority": 4,
      "pageNumber": 4,
      "description": "区域市场分布、地域特色、区域竞争格局",
      "dataRequirements": "区域分布数据、地域特征信息",
      "keyQuestions": ["区域分布如何", "各地区特色是什么", "区域竞争态势"],
      "visualizations": [
        {
          "id": "regional_heatmap",
          "type": "heatmap",
          "title": "{{ $json.industry }}全国区域分布热力图",
          "description": "各省市行业发展活跃度和集中度分布",
          "dataSource": "区域分布统计数据",
          "chartPurpose": "可视化区域发展差异",
          "position": "chapter_integrated"
        },
        {
          "id": "top_cities_comparison",
          "type": "bar",
          "title": "主要城市{{ $json.industry }}发展水平对比",
          "description": "TOP10城市发展指标横向对比",
          "dataSource": "城市发展数据",
          "chartPurpose": "对比城市发展水平",
          "position": "chapter_integrated"
        }
      ]
    },
    {
      "id": "competitiveAnalysis",
      "title": "竞争格局与主要企业",
      "targetWords": 1000,
      "priority": 5,
      "pageNumber": 5,
      "description": "竞争对手分析、市场份额、竞争优势和战略",
      "dataRequirements": "竞争对手数据、市场份额信息、企业能力数据",
      "keyQuestions": ["主要竞争者有哪些", "市场份额如何分布", "竞争优势在哪里"],
      "visualizations": [
        {
          "id": "market_share_pie",
          "type": "pie",
          "title": "{{ $json.industry }}市场份额分布",
          "description": "主要企业市场占有率分布情况",
          "dataSource": "企业市场份额数据",
          "chartPurpose": "展示竞争格局",
          "position": "chapter_integrated"
        },
        {
          "id": "competitive_radar",
          "type": "radar",
          "title": "主要企业竞争力雷达图",
          "description": "多维度企业能力对比分析",
          "dataSource": "企业能力评估数据",
          "chartPurpose": "多维度竞争力分析",
          "position": "chapter_integrated"
        }
      ]
    },
    {
      "id": "technologyInnovation",
      "title": "技术发展与创新趋势",
      "targetWords": 900,
      "priority": 6,
      "pageNumber": 6,
      "description": "技术创新、研发投入、技术趋势和未来方向",
      "dataRequirements": "技术发展数据、创新投入信息、专利数据",
      "keyQuestions": ["技术发展现状如何", "创新热点在哪里", "未来技术方向"],
      "visualizations": [
        {
          "id": "innovation_heatmap",
          "type": "heatmap",
          "title": "{{ $json.industry }}技术创新热点分布",
          "description": "各技术领域创新活跃度和投入强度",
          "dataSource": "技术创新统计数据",
          "chartPurpose": "识别技术创新热点",
          "position": "chapter_integrated"
        },
        {
          "id": "rd_investment_bar",
          "type": "bar",
          "title": "主要企业研发投入对比",
          "description": "行业内主要企业研发投入规模和占比",
          "dataSource": "企业研发投入数据",
          "chartPurpose": "对比研发投入水平",
          "position": "chapter_integrated"
        }
      ]
    },
    {
      "id": "policyEnvironment",
      "title": "政策环境与监管影响",
      "targetWords": 800,
      "priority": 7,
      "pageNumber": 7,
      "description": "政策支持、监管要求、合规标准和政策影响",
      "dataRequirements": "政策文件、监管要求、合规标准",
      "keyQuestions": ["政策支持力度如何", "监管要求有哪些", "政策影响程度"],
      "visualizations": [
        {
          "id": "policy_impact_radar",
          "type": "radar",
          "title": "{{ $json.industry }}政策影响评估雷达图",
          "description": "多维度政策影响程度评估",
          "dataSource": "政策影响评估数据",
          "chartPurpose": "评估政策影响程度",
          "position": "chapter_integrated"
        },
        {
          "id": "compliance_funnel",
          "type": "funnel",
          "title": "行业合规要求层级图",
          "description": "从基础到高级的合规要求分布",
          "dataSource": "合规要求数据",
          "chartPurpose": "展示合规要求层次",
          "position": "chapter_integrated"
        }
      ]
    },
    {
      "id": "investmentAnalysis",
      "title": "投资机会与风险评估",
      "targetWords": 900,
      "priority": 8,
      "pageNumber": 8,
      "description": "投资热点、机会分析、风险识别和投资建议",
      "dataRequirements": "投资数据、风险评估信息、融资情况",
      "keyQuestions": ["投资机会在哪里", "主要风险因素", "投资回报预期"],
      "visualizations": [
        {
          "id": "risk_return_scatter",
          "type": "scatter",
          "title": "{{ $json.industry }}投资风险收益矩阵",
          "description": "各投资领域风险与收益分布关系",
          "dataSource": "投资风险收益数据",
          "chartPurpose": "评估投资机会优劣",
          "position": "chapter_integrated"
        },
        {
          "id": "investment_trend_line",
          "type": "line",
          "title": "行业投资热度趋势图",
          "description": "近年来投资规模和频次变化趋势",
          "dataSource": "投资历史数据",
          "chartPurpose": "展示投资趋势变化",
          "position": "chapter_integrated"
        }
      ]
    },
    {
      "id": "futureTrends",
      "title": "未来发展趋势预测",
      "targetWords": 800,
      "priority": 9,
      "pageNumber": 9,
      "description": "发展趋势预测、未来机遇、挑战分析和发展路径",
      "dataRequirements": "趋势预测数据、发展规划信息",
      "keyQuestions": ["未来发展趋势", "主要机遇挑战", "发展路径选择"],
      "visualizations": [
        {
          "id": "future_roadmap",
          "type": "timeline",
          "title": "{{ $json.industry }}未来发展路线图",
          "description": "未来3-5年关键发展节点和里程碑",
          "dataSource": "发展规划和预测数据",
          "chartPurpose": "规划未来发展路径",
          "position": "chapter_integrated"
        },
        {
          "id": "trend_forecast_line",
          "type": "line",
          "title": "关键指标发展趋势预测",
          "description": "核心业务指标未来发展趋势预测",
          "dataSource": "预测模型数据",
          "chartPurpose": "预测关键指标走势",
          "position": "chapter_integrated"
        }
      ]
    },
    {
      "id": "conclusions",
      "title": "结论与战略建议",
      "targetWords": 700,
      "priority": 10,
      "pageNumber": 10,
      "description": "总结性结论、战略建议和行动方案",
      "dataRequirements": "综合分析结果、战略建议框架",
      "keyQuestions": ["核心结论是什么", "战略建议有哪些", "如何实施"],
      "visualizations": [
        {
          "id": "strategy_radar",
          "type": "radar",
          "title": "{{ $json.industry }}战略建议雷达图",
          "description": "多维度战略建议重要性和可行性评估",
          "dataSource": "战略分析数据",
          "chartPurpose": "指导战略决策",
          "position": "chapter_integrated"
        }
      ]
    }
  ],
  "visualizationStandards": {
    "chartLibrary": "ECharts",
    "colorScheme": "industry_adaptive",
    "responsiveDesign": true,
    "dataLabels": true,
    "interactivity": "enhanced",
    "animationEffects": true,
    "mobileOptimized": true
  },
  "qualityStandards": {
    "minWordsPerChapter": 700,
    "maxWordsPerChapter": 1000,
    "requiredInsightsPerChapter": 4,
    "requiredDataSupport": 3,
    "requiredVisualizationsPerChapter": 2,
    "professionalTermsThreshold": 8,
    "totalWordCount": 8000,
    "totalPages": 10,
    "chartTypeVariety": 12
  }
}
