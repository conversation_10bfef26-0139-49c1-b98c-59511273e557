// 13_11 图表诊断质量检测节点
// 专门诊断图表数据缺失问题

console.log('=== 图表诊断质量检测开始 ===');

const inputData = $input.all()[0].json;

// ===== 第一步：详细的数据结构分析 =====
console.log('🔍 完整输入数据结构分析:');
console.log('- 顶层键:', Object.keys(inputData));

// 检查每个可能包含图表数据的位置
console.log('📊 图表数据位置检查:');

// 1. 检查顶层是否有visualizations
if (inputData.visualizations) {
  console.log('✅ 顶层发现visualizations:', inputData.visualizations.length, '个');
  inputData.visualizations.forEach((viz, index) => {
    console.log(`  ${index + 1}. ${viz.title} (${viz.type})`);
  });
} else {
  console.log('❌ 顶层未发现visualizations');
}

// 2. 检查output字段
if (inputData.output) {
  console.log('📄 output字段分析:');
  console.log('- output类型:', typeof inputData.output);
  console.log('- output长度:', inputData.output.length);
  console.log('- 包含visualizations关键词:', inputData.output.includes('visualizations'));
  console.log('- 包含图表关键词:', inputData.output.includes('图表'));
  console.log('- 包含ECharts关键词:', inputData.output.includes('echarts'));
  
  // 尝试解析output中的JSON
  if (typeof inputData.output === 'string') {
    try {
      let jsonStr = inputData.output;
      
      // 清理markdown标记
      if (jsonStr.includes('```json')) {
        jsonStr = jsonStr.replace(/^```json\s*\n/, '');
        jsonStr = jsonStr.replace(/\n\s*```\s*$/, '');
      }
      
      // 处理JavaScript函数
      if (jsonStr.includes('function')) {
        console.log('🔧 检测到JavaScript函数，进行处理');
        jsonStr = jsonStr.replace(/function\s*\([^)]*\)\s*\{[^}]*\}/g, '"[JavaScript Function]"');
        jsonStr = jsonStr.replace(/"color":\s*function\([^)]*\)\s*\{[\s\S]*?\}/g, '"color": "[JavaScript Function]"');
      }
      
      const parsedData = JSON.parse(jsonStr);
      console.log('✅ output JSON解析成功');
      console.log('📊 解析后数据键:', Object.keys(parsedData));
      
      if (parsedData.visualizations) {
        console.log('✅ 解析后发现visualizations:', parsedData.visualizations.length, '个');
        parsedData.visualizations.forEach((viz, index) => {
          console.log(`  ${index + 1}. ${viz.title || '无标题'} (${viz.type || '无类型'})`);
          if (viz.config) {
            console.log(`     配置键:`, Object.keys(viz.config));
          }
        });
      } else {
        console.log('❌ 解析后未发现visualizations');
      }
      
    } catch (error) {
      console.error('❌ output JSON解析失败:', error.message);
    }
  }
} else {
  console.log('❌ 未发现output字段');
}

// 3. 检查reportData字段
if (inputData.reportData) {
  console.log('📊 reportData字段分析:');
  console.log('- reportData类型:', typeof inputData.reportData);
  console.log('- reportData键:', Object.keys(inputData.reportData));
  
  if (inputData.reportData.visualizations) {
    console.log('✅ reportData中发现visualizations:', inputData.reportData.visualizations.length, '个');
    inputData.reportData.visualizations.forEach((viz, index) => {
      console.log(`  ${index + 1}. ${viz.title} (${viz.type})`);
    });
  } else {
    console.log('❌ reportData中未发现visualizations');
  }
} else {
  console.log('❌ 未发现reportData字段');
}

// ===== 第二步：尝试获取图表数据 =====
let reportData = null;
let visualizations = [];
let parseSuccess = false;

// 优先从已解析的reportData获取
if (inputData.reportData && inputData.reportData.visualizations) {
  reportData = inputData.reportData;
  visualizations = inputData.reportData.visualizations;
  parseSuccess = true;
  console.log('✅ 使用reportData中的图表数据');
}
// 其次尝试解析output
else if (inputData.output && typeof inputData.output === 'string') {
  try {
    let jsonStr = inputData.output;
    
    // 清理和修复JSON
    if (jsonStr.includes('```json')) {
      jsonStr = jsonStr.replace(/^```json\s*\n/, '');
      jsonStr = jsonStr.replace(/\n\s*```\s*$/, '');
    }
    
    if (jsonStr.includes('function')) {
      jsonStr = jsonStr.replace(/function\s*\([^)]*\)\s*\{[^}]*\}/g, '"[JavaScript Function]"');
      jsonStr = jsonStr.replace(/"color":\s*function\([^)]*\)\s*\{[\s\S]*?\}/g, '"color": "[JavaScript Function]"');
    }
    
    reportData = JSON.parse(jsonStr);
    visualizations = reportData.visualizations || [];
    parseSuccess = true;
    console.log('✅ 从output解析获取图表数据');
    
  } catch (error) {
    console.error('❌ output解析失败:', error.message);
  }
}
// 最后使用顶层数据
else if (inputData.visualizations) {
  visualizations = inputData.visualizations;
  reportData = inputData;
  parseSuccess = true;
  console.log('✅ 使用顶层图表数据');
}

// ===== 第三步：图表数据详细分析 =====
console.log('📊 图表数据详细分析:');
console.log('- 图表数量:', visualizations.length);
console.log('- 解析成功:', parseSuccess);

if (visualizations.length > 0) {
  console.log('📈 图表详细信息:');
  visualizations.forEach((viz, index) => {
    console.log(`\n图表 ${index + 1}:`);
    console.log('- ID:', viz.id || '无ID');
    console.log('- 标题:', viz.title || '无标题');
    console.log('- 类型:', viz.type || '无类型');
    console.log('- 描述:', viz.description || '无描述');
    console.log('- 配置存在:', !!viz.config);
    
    if (viz.config) {
      console.log('- 配置键:', Object.keys(viz.config));
      if (viz.config.series) {
        console.log('- 系列数据存在:', !!viz.config.series);
        if (Array.isArray(viz.config.series)) {
          console.log('- 系列数量:', viz.config.series.length);
        }
      }
    }
  });
} else {
  console.log('❌ 未发现任何图表数据');
  
  // 尝试在原始数据中搜索图表相关内容
  const dataStr = JSON.stringify(inputData);
  console.log('🔍 在原始数据中搜索图表关键词:');
  console.log('- 包含"visualizations":', dataStr.includes('visualizations'));
  console.log('- 包含"chart":', dataStr.includes('chart'));
  console.log('- 包含"图表":', dataStr.includes('图表'));
  console.log('- 包含"echarts":', dataStr.includes('echarts'));
  console.log('- 包含"gauge":', dataStr.includes('gauge'));
  console.log('- 包含"radar":', dataStr.includes('radar'));
}

// ===== 第四步：构建诊断报告 =====
const retryCount = inputData.retryCount || 0;
const industry = reportData?.chapterInfo?.industry || inputData.industry || '未知行业';

// 简化的质量评估
const assessment = {
  score: 0,
  issues: [],
  strengths: [],
  details: {},
  passed: false,
  diagnostics: {
    hasTopLevelVisualizations: !!inputData.visualizations,
    hasOutputField: !!inputData.output,
    hasReportDataField: !!inputData.reportData,
    outputParseable: false,
    visualizationsFound: visualizations.length,
    parseMethod: parseSuccess ? 'success' : 'failed',
    dataStructure: {
      inputKeys: Object.keys(inputData),
      reportDataKeys: reportData ? Object.keys(reportData) : [],
      visualizationsCount: visualizations.length
    }
  }
};

// 图表评估
let vizScore = 0;
if (visualizations.length >= 6) {
  vizScore = 20;
  assessment.strengths.push(`图表充足: ${visualizations.length}个`);
} else if (visualizations.length >= 3) {
  vizScore = 15;
  assessment.issues.push(`图表略少: ${visualizations.length}/6`);
} else if (visualizations.length > 0) {
  vizScore = 8;
  assessment.issues.push(`图表不足: ${visualizations.length}/6`);
} else {
  vizScore = 0;
  assessment.issues.push('完全缺少图表数据');
}

assessment.score = vizScore + 60; // 给基础分60分，专注图表诊断
assessment.details.vizScore = vizScore;
assessment.passed = assessment.score >= 70;

console.log('📊 图表诊断结果:');
console.log('- 图表数量:', visualizations.length);
console.log('- 图表得分:', vizScore, '/20');
console.log('- 总分:', assessment.score, '/100');
console.log('- 是否通过:', assessment.passed);

// ===== 返回诊断结果 =====
return [{
  json: {
    ...inputData,
    reportData: reportData || {
      content: {},
      visualizations: visualizations,
      qualityMetrics: {},
      chapterInfo: { industry: industry }
    },
    qualityAssessment: {
      ...assessment,
      parseMethod: 'chart_diagnostics',
      chartDiagnostics: true
    },
    chapterInfo: {
      industry: industry
    },
    retryCount: retryCount,
    processingStatus: {
      dataProcessed: true,
      dataParsed: parseSuccess,
      qualityChecked: true,
      chartDiagnostics: true
    }
  }
}];
