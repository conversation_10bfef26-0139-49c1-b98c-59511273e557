// 循环计数传递节点 - 简化版
// 专注于计数传递和关键信息保持

console.log('=== 循环计数传递 ===');

// 获取输入数据
let inputData;
try {
  inputData = $input.first().json;
  console.log('✅ 成功获取输入数据');
} catch (error) {
  console.error('❌ 获取输入数据失败:', error.message);
  return [{
    json: {
      error: '循环计数传递失败',
      message: error.message,
      timestamp: new Date().toISOString()
    }
  }];
}

// 获取当前重试次数
const currentRetryCount = inputData.retryCount || 0;
const newRetryCount = currentRetryCount + 1;

console.log('🔄 循环计数信息:');
console.log('- 当前重试次数:', currentRetryCount);
console.log('- 新重试次数:', newRetryCount);
console.log('- 最大重试次数: 2');

// 检查是否超过最大重试次数
if (newRetryCount > 2) {
  console.warn('⚠️ 超过最大重试次数，将在下次质量检测中强制通过');
}

// 保持关键信息完整性
const preservedData = {
  // 保持原有数据结构
  ...inputData,
  
  // 更新重试次数
  retryCount: newRetryCount,
  
  // 确保关键信息不丢失
  chapterInfo: {
    ...inputData.chapterInfo,
    // 确保industry信息存在
    industry: inputData.chapterInfo?.industry || 
              inputData.reportData?.chapterInfo?.industry || 
              '跨境电商',
    // 确保sessionId存在
    sessionId: inputData.chapterInfo?.sessionId || 
               inputData.reportData?.chapterInfo?.sessionId || 
               `RPT_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  },
  
  // 保持报告数据完整
  reportData: inputData.reportData || inputData,
  
  // 更新循环信息
  loopInfo: {
    currentLoop: newRetryCount,
    maxLoops: 2,
    isLastChance: newRetryCount >= 2,
    loopTimestamp: new Date().toISOString()
  },
  
  // 保持监控信息
  monitoring: {
    ...inputData.monitoring,
    loopTimes: [
      ...(inputData.monitoring?.loopTimes || []),
      {
        loopNumber: newRetryCount,
        timestamp: Date.now(),
        timestampISO: new Date().toISOString()
      }
    ]
  }
};

// 数据完整性检查
console.log('🔍 数据完整性检查:');
console.log('- industry:', preservedData.chapterInfo.industry);
console.log('- sessionId:', preservedData.chapterInfo.sessionId ? '存在' : '缺失');
console.log('- reportData:', preservedData.reportData ? '存在' : '缺失');
console.log('- qualityAssessment:', preservedData.qualityAssessment ? '存在' : '缺失');

// 如果是最后一次机会，添加特殊标记
if (newRetryCount >= 2) {
  preservedData.forcePassNext = true;
  preservedData.lastChanceReason = '达到最大重试次数，下次质量检测将强制通过';
  console.log('🚨 最后机会标记已设置');
}

console.log('✅ 循环计数传递完成');
console.log('📤 传递数据键:', Object.keys(preservedData));

return [{
  json: preservedData
}];
