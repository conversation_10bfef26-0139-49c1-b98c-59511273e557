// 13_09 处理JavaScript函数的质量检测节点
// 专门处理包含JavaScript函数的JSON数据

console.log('=== 处理JavaScript函数的质量检测开始 ===');

const inputData = $input.all()[0].json;

// ===== 第一步：检查输入数据 =====
console.log('🔍 输入数据分析:');
console.log('- 顶层键:', Object.keys(inputData));
console.log('- output字段存在:', !!inputData.output);
console.log('- output字段类型:', typeof inputData.output);

if (inputData.output) {
  console.log('- output字段长度:', inputData.output.length);
  console.log('- 包含```json:', inputData.output.includes('```json'));
  console.log('- 包含function:', inputData.output.includes('function'));
}

// ===== 第二步：处理包含JavaScript函数的JSON =====
let reportData = null;
let parseSuccess = false;
let parseError = null;

if (inputData.output && typeof inputData.output === 'string') {
  console.log('🧪 开始处理JavaScript函数的JSON');
  
  try {
    // 提取JSON字符串
    let jsonStr = inputData.output;
    
    // 移除markdown标记
    if (jsonStr.includes('```json')) {
      jsonStr = jsonStr.replace(/^```json\s*\n/, '');
      jsonStr = jsonStr.replace(/\n\s*```\s*$/, '');
    }
    
    console.log('📄 清理markdown后长度:', jsonStr.length);
    console.log('📄 是否包含function:', jsonStr.includes('function'));
    
    // 🔧 关键修复：处理JavaScript函数
    if (jsonStr.includes('function')) {
      console.log('🔧 检测到JavaScript函数，开始处理');
      
      // 将JavaScript函数替换为字符串
      jsonStr = jsonStr.replace(/function\s*\([^)]*\)\s*\{[^}]*\}/g, '"[JavaScript Function]"');
      
      // 处理更复杂的函数表达式
      jsonStr = jsonStr.replace(/"color":\s*function\([^)]*\)\s*\{[\s\S]*?\}/g, '"color": "[JavaScript Function]"');
      
      console.log('🔧 函数处理完成');
    }
    
    // 尝试解析处理后的JSON
    reportData = JSON.parse(jsonStr);
    parseSuccess = true;
    console.log('✅ JSON解析成功');
    
  } catch (error) {
    parseError = error.message;
    console.error('❌ JSON解析仍然失败:', error.message);
    
    // 🔧 备用方案：更激进的函数清理
    try {
      console.log('🔧 尝试更激进的函数清理');
      let jsonStr = inputData.output;
      
      // 移除markdown标记
      if (jsonStr.includes('```json')) {
        jsonStr = jsonStr.replace(/^```json\s*\n/, '');
        jsonStr = jsonStr.replace(/\n\s*```\s*$/, '');
      }
      
      // 更激进的函数清理
      // 1. 清理所有function定义
      jsonStr = jsonStr.replace(/function\s*\([^)]*\)\s*\{[\s\S]*?\}/g, '"[Function]"');
      
      // 2. 清理对象中的函数属性
      jsonStr = jsonStr.replace(/"[^"]*":\s*function\s*\([^)]*\)\s*\{[\s\S]*?\}/g, '"functionProperty": "[Function]"');
      
      // 3. 清理可能的箭头函数
      jsonStr = jsonStr.replace(/\([^)]*\)\s*=>\s*\{[\s\S]*?\}/g, '"[Arrow Function]"');
      
      // 4. 清理var声明
      jsonStr = jsonStr.replace(/var\s+[^;]+;/g, '');
      
      console.log('🔧 激进清理完成，尝试解析');
      
      reportData = JSON.parse(jsonStr);
      parseSuccess = true;
      console.log('✅ 激进清理后解析成功');
      
    } catch (secondError) {
      console.error('❌ 激进清理后仍然失败:', secondError.message);
      parseError = secondError.message;
    }
  }
}

// ===== 第三步：解析结果验证 =====
if (parseSuccess && reportData) {
  console.log('🔍 解析结果验证:');
  console.log('- reportData类型:', typeof reportData);
  console.log('- reportData键:', Object.keys(reportData));
  
  // 验证关键字段
  const hasContent = !!reportData.content;
  const hasQualityMetrics = !!reportData.qualityMetrics;
  const hasVisualizations = !!reportData.visualizations;
  const hasChapterInfo = !!reportData.chapterInfo;
  
  console.log('- content字段存在:', hasContent);
  console.log('- qualityMetrics字段存在:', hasQualityMetrics);
  console.log('- visualizations字段存在:', hasVisualizations);
  console.log('- chapterInfo字段存在:', hasChapterInfo);
  
  if (hasContent) {
    console.log('- content键数量:', Object.keys(reportData.content).length);
    console.log('- keyInsights数量:', reportData.content.keyInsights?.length || 0);
    console.log('- professionalTerms数量:', reportData.content.professionalTerms?.length || 0);
  }
  
  if (hasQualityMetrics) {
    console.log('- totalWordCount:', reportData.qualityMetrics.totalWordCount);
    console.log('- visualizationCount:', reportData.qualityMetrics.visualizationCount);
  }
  
  if (hasVisualizations) {
    console.log('- visualizations数量:', reportData.visualizations.length);
  }
  
} else {
  console.error('🚨 解析失败');
  return [{
    json: {
      ...inputData,
      qualityAssessment: {
        score: 0,
        passed: false,
        issues: ['JSON解析失败: ' + (parseError || '未知错误')],
        parseError: true,
        parseErrorDetails: parseError,
        debugInfo: {
          hasOutput: !!inputData.output,
          outputType: typeof inputData.output,
          outputLength: inputData.output?.length || 0,
          includesJsonMarker: inputData.output?.includes('```json') || false,
          includesFunction: inputData.output?.includes('function') || false
        }
      },
      retryCount: inputData.retryCount || 0
    }
  }];
}

// ===== 第四步：基础信息获取 =====
const retryCount = inputData.retryCount || 0;
const industry = reportData.chapterInfo?.industry || '跨境电商';
const isRetry = retryCount > 0;

console.log('📋 基础信息:');
console.log('- 重试次数:', retryCount);
console.log('- 行业信息:', industry);

// 强制通过条件
if (retryCount >= 2) {
  console.log('🚨 达到最大重试次数，强制通过');
  return [{
    json: {
      ...inputData,
      reportData: reportData,
      qualityAssessment: {
        score: 85,
        passed: true,
        issues: [],
        forcePass: true,
        reason: '达到最大重试次数',
        totalWordCount: reportData.qualityMetrics?.totalWordCount || 2500,
        targetWords: 2500,
        parseMethod: 'function_handled'
      },
      retryCount: retryCount
    }
  }];
}

// ===== 第五步：质量评估 =====
const assessment = {
  score: 0,
  issues: [],
  strengths: [],
  details: {},
  passed: false
};

console.log('📊 开始质量评估');

// 获取评估数据
const content = reportData.content || {};
const qualityMetrics = reportData.qualityMetrics || {};
const visualizations = reportData.visualizations || [];

// 1. 内容完整性评估 (35分)
let contentScore = 0;

console.log('📝 内容完整性评估:');

// 核心章节检查
const coreChapters = [
  'executiveSummary', 'industryOverview', 'marketAnalysis', 
  'competitiveLandscape', 'futureTrends', 'conclusions'
];

let validCoreChapters = 0;
coreChapters.forEach(chapter => {
  const chapterContent = content[chapter];
  const isValid = chapterContent && chapterContent.length > 50;
  
  if (isValid) {
    validCoreChapters++;
    contentScore += 5;
    console.log(`✅ 核心章节 ${chapter}: ${chapterContent.length}字`);
  } else {
    console.log(`❌ 核心章节 ${chapter}: 缺失或过短`);
  }
});

// 扩展章节检查
const extendedChapters = ['regionalAnalysis', 'technologyInnovation', 'policyEnvironment', 'investmentAnalysis'];
let validExtendedChapters = 0;
extendedChapters.forEach(chapter => {
  const chapterContent = content[chapter];
  const isValid = chapterContent && chapterContent.length > 50;
  
  if (isValid) {
    validExtendedChapters++;
    contentScore += 2;
    console.log(`✅ 扩展章节 ${chapter}: ${chapterContent.length}字`);
  }
});

// 关键洞察检查
const insights = content.keyInsights || [];
const insightTarget = isRetry ? 5 : 8;
console.log(`🔍 关键洞察: ${insights.length}/${insightTarget}`);

if (insights.length >= insightTarget) {
  contentScore += 8;
  assessment.strengths.push(`关键洞察充足: ${insights.length}个`);
} else if (insights.length >= Math.ceil(insightTarget * 0.6)) {
  contentScore += 5;
  assessment.issues.push(`关键洞察略少: ${insights.length}/${insightTarget}`);
} else {
  assessment.issues.push(`关键洞察不足: ${insights.length}/${insightTarget}`);
}

assessment.score += Math.min(contentScore, 35);
assessment.details.contentScore = contentScore;

console.log(`📝 内容评估: 核心${validCoreChapters}/6, 扩展${validExtendedChapters}/4, 洞察${insights.length}/${insightTarget}, 得分${contentScore}/35`);

// 2. 字数评估 (25分)
const wordTarget = isRetry ? 2000 : 2500;
const totalWordCount = qualityMetrics.totalWordCount || 0;

console.log(`📊 字数评估: ${totalWordCount}/${wordTarget}`);

let wordScore = 0;
if (totalWordCount >= wordTarget) {
  wordScore = 25;
  assessment.strengths.push(`字数达标: ${totalWordCount}字`);
} else if (totalWordCount >= wordTarget * 0.8) {
  wordScore = 20;
} else if (totalWordCount >= wordTarget * 0.6) {
  wordScore = 15;
  assessment.issues.push(`字数不足: ${totalWordCount}/${wordTarget}`);
} else if (totalWordCount > 500) {
  wordScore = 10;
  assessment.issues.push(`字数严重不足: ${totalWordCount}/${wordTarget}`);
} else {
  wordScore = 5;
  assessment.issues.push(`字数极少: ${totalWordCount}字`);
}

assessment.score += wordScore;
assessment.details.wordScore = wordScore;
assessment.totalWordCount = totalWordCount;
assessment.targetWords = wordTarget;

console.log(`📊 字数评估得分: ${wordScore}/25`);

// 3. 可视化评估 (20分)
const vizTarget = isRetry ? 3 : 6;
console.log(`📊 图表评估: ${visualizations.length}/${vizTarget}`);

let vizScore = 0;
if (visualizations.length >= vizTarget) {
  vizScore = 20;
  assessment.strengths.push(`图表充足: ${visualizations.length}个`);
} else if (visualizations.length >= Math.ceil(vizTarget * 0.6)) {
  vizScore = 15;
} else if (visualizations.length > 0) {
  vizScore = 8;
  assessment.issues.push(`图表不足: ${visualizations.length}/${vizTarget}`);
} else {
  assessment.issues.push('缺少图表');
}

assessment.score += vizScore;
assessment.details.vizScore = vizScore;

console.log(`📊 图表评估得分: ${vizScore}/20`);

// 4. 专业术语评估 (10分)
const terms = content.professionalTerms || [];
const termTarget = isRetry ? 7 : 10;
console.log(`📚 术语评估: ${terms.length}/${termTarget}`);

let termScore = 0;
if (terms.length >= termTarget) {
  termScore = 10;
  assessment.strengths.push(`专业术语丰富: ${terms.length}个`);
} else if (terms.length >= Math.ceil(termTarget * 0.7)) {
  termScore = 8;
} else if (terms.length >= Math.ceil(termTarget * 0.5)) {
  termScore = 5;
  assessment.issues.push(`专业术语不足: ${terms.length}/${termTarget}`);
} else if (terms.length > 0) {
  termScore = 3;
  assessment.issues.push(`专业术语严重不足: ${terms.length}/${termTarget}`);
} else {
  assessment.issues.push('缺少专业术语');
}

assessment.score += termScore;
assessment.details.termScore = termScore;

console.log(`📚 术语评估得分: ${termScore}/10`);

// 5. 行业相关性评估 (10分)
let industryScore = 0;
if (industry && industry !== '未知行业' && industry !== '未知') {
  industryScore = 10;
  assessment.strengths.push(`行业信息明确: ${industry}`);
} else {
  assessment.issues.push('行业信息不明确');
}

assessment.score += industryScore;
assessment.details.industryScore = industryScore;

// ===== 第六步：通过标准判定 =====
const passThreshold = isRetry ? 70 : 80;
assessment.passed = assessment.score >= passThreshold;

console.log('📊 最终评估结果:');
console.log(`- 内容完整性: ${assessment.details.contentScore}/35`);
console.log(`- 字数质量: ${assessment.details.wordScore}/25`);
console.log(`- 可视化: ${assessment.details.vizScore}/20`);
console.log(`- 专业术语: ${assessment.details.termScore}/10`);
console.log(`- 行业相关: ${assessment.details.industryScore}/10`);
console.log(`- 总分: ${assessment.score}/100`);
console.log(`- 通过标准: ${passThreshold}分`);
console.log(`- 评估结果: ${assessment.passed ? '✅ 通过' : '❌ 不通过'}`);

// ===== 返回结果 =====
return [{
  json: {
    ...inputData,
    reportData: reportData,
    qualityAssessment: {
      ...assessment,
      parseMethod: 'function_handled',
      functionProcessed: true
    },
    chapterInfo: {
      ...reportData.chapterInfo,
      industry: industry
    },
    retryCount: retryCount,
    processingStatus: {
      dataProcessed: true,
      dataParsed: parseSuccess,
      qualityChecked: true,
      parseMethod: 'function_handled',
      functionCleaned: true
    }
  }
}];
