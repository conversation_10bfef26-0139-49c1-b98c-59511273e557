// 循环监控初始化节点
// 这个节点放在"AI_Agent"之后，"质量检测"之前
// 用于初始化执行时间监控和循环控制

const inputData = $input.first().json;

console.log('=== 循环监控初始化 ===');

// 初始化监控信息
const startTime = Date.now();
const startTimeISO = new Date().toISOString();

console.log('开始时间:', startTimeISO);
console.log('开始监控循环质量检测过程...');

// 评估任务复杂度
const content = inputData.content || {};
const visualizations = inputData.visualizations || [];

let complexityLevel = 'LOW';
let estimatedLoops = 1;

// 基于内容评估复杂度
const totalContent = (content.executiveSummary?.length || 0) +
                    (content.industryOverview?.length || 0) +
                    (content.marketAnalysis?.length || 0) +
                    (content.competitiveLandscape?.length || 0) +
                    (content.trendsForecast?.length || 0);

const hasInsights = content.keyInsights && content.keyInsights.length >= 5;
const hasDataSupport = content.dataSupport && content.dataSupport.length >= 5;
const hasVisualizations = visualizations.length >= 3;

if (totalContent < 1000 || !hasInsights || !hasDataSupport || !hasVisualizations) {
    complexityLevel = 'HIGH';
    estimatedLoops = 3;
} else if (totalContent < 1300) {
    complexityLevel = 'MEDIUM';
    estimatedLoops = 2;
}

console.log('内容复杂度评估:', complexityLevel);
console.log('预估循环次数:', estimatedLoops);
console.log('预估总时间:', estimatedLoops * 60, '秒');

// 输出带监控信息的数据
return [{
    json: {
        ...inputData,
        retryCount: 0, // 初始化重试计数
        monitoring: {
            startTime: startTime,
            startTimeISO: startTimeISO,
            complexityLevel: complexityLevel,
            estimatedLoops: estimatedLoops,
            estimatedTotalTime: estimatedLoops * 60000, // 毫秒
            loopTimes: [],
            initialized: true
        },
        loopInfo: {
            currentLoop: 0,
            maxLoops: 3,
            isLastChance: false
        }
    }
}];
