// 数据准备节点 - 为LLM Agent准备数据
// 位置：数据清洗 → 数据准备 → LLM Agent

const cleanedData = $('数据清洗').first().json;
console.log('=== 数据准备节点启动 ===');
console.log('行业:', cleanedData.industry);
console.log('清洗后数据量:', cleanedData.totalItems);

// 生成唯一会话ID
const sessionId = `RPT_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// 准备存储到NocoDB的数据结构
const preparedData = {
  // 会话基础信息
  sessionInfo: {
    session_id: sessionId,
    industry: cleanedData.industry,
    total_items: cleanedData.totalItems,
    avg_quality: cleanedData.averageQuality,
    created_at: new Date().toISOString(),
    status: 'processing'
  },

  // 内容数据（供LLM Agent存储到NocoDB）
  contentItems: cleanedData.highQualityContent.map((item, index) => ({
    session_id: sessionId,
    source: item.source,
    title: item.title,
    content: item.content,
    quality_score: item.qualityScore,
    url: item.url || '',
    theme: item.theme || 'general',
    created_at: new Date().toISOString()
  })),

  // 数据洞察（供LLM分析使用）
  insights: cleanedData.dataInsights || {
    strongestThemes: [],
    contentCharacteristics: {
      isDataRich: false,
      hasFinancialData: false,
      hasCompetitorInfo: false,
      overallQuality: cleanedData.averageQuality
    },
    recommendedApproach: cleanedData.totalItems > 15 ? 'comprehensive' : 'focused'
  }
};

// 为LLM Agent准备的提示词数据
const llmAgentData = {
  sessionId: sessionId,
  industry: cleanedData.industry,
  dataQuality: cleanedData.averageQuality,
  contentCount: cleanedData.totalItems,
  
  // 数据摘要（供LLM理解数据特点）
  dataSummary: {
    totalItems: cleanedData.totalItems,
    averageQuality: cleanedData.averageQuality,
    hasHighQualityData: cleanedData.averageQuality > 60,
    dataRichness: cleanedData.totalItems > 15 ? 'rich' : 'moderate',
    recommendedApproach: preparedData.insights.recommendedApproach
  },

  // 高质量内容样本（供LLM参考）
  contentSample: cleanedData.highQualityContent.slice(0, 5).map(item => ({
    title: item.title,
    content: item.content.substring(0, 200) + '...',
    source: item.source,
    quality: item.qualityScore
  })),

  // 存储数据（供LLM Agent使用工具存储）
  storeData: preparedData
};

console.log('✅ 数据准备完成');
console.log('会话ID:', sessionId);
console.log('准备存储的内容条数:', preparedData.contentItems.length);
console.log('数据质量评估:', preparedData.insights.recommendedApproach);

// 返回准备好的数据
return [{
  json: {
    // LLM Agent需要的数据
    sessionId: sessionId,
    industry: cleanedData.industry,
    agentData: llmAgentData,
    
    // 状态信息
    status: {
      success: true,
      message: `数据准备完成 - ${cleanedData.industry}行业，${cleanedData.totalItems}条数据`,
      nextStep: 'llm_agent_execution'
    },
    
    // 元数据
    metadata: {
      preparedAt: new Date().toISOString(),
      dataQuality: cleanedData.averageQuality,
      contentCount: cleanedData.totalItems,
      sessionId: sessionId
    }
  }
}];
