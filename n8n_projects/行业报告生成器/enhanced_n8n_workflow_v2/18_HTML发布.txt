{
  "method": "POST",
  "url": "https://mcp-on-edge.edgeone.site/mcp-server",
  "sendHeaders": true,
  "headerParameters": {
    "parameters": [
      {
        "name": "Content_Type",
        "value": "application/json"
      }
    ]
  },
  "sendBody": true,
  "specifyBody": "json",
  "jsonBody": "{\n  \"jsonrpc\": \"2.0\",\n  \"id\": 1,\n  \"method\": \"tools/call\",\n  \"params\": {\n    \"name\": \"deploy-html\",\n    \"arguments\": {\n      \"value\": \"{{ JSON.stringify($node['html'].json.output).slice(1, -1) }}\"\n    }\n  }\n}",
  "options": {}
}
// 说明：
// 该节点为HTML发布节点，将生成的HTML报告通过API发布到指定服务。
