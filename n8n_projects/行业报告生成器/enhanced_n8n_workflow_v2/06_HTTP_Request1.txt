{"url": "https://api.unsplash.com/search/photos", "sendQuery": true, "queryParameters": {"parameters": [{"name": "query", "value": "={{ $json.imageKeywords[0] }}"}, {"name": "per_page", "value": "8 "}, {"name": "orientation", "value": "landscape"}, {"name": "order_by", "value": "relevant"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Client-ID UGpzJlz4NiUot1NboxIcaZGuoSuooHLnjLAr7Metg"}, {"name": "Accept", "value": "application/json"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}