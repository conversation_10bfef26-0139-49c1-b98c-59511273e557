// 4b_node(CleanMergeData)
// 作用：清洗和合并 Tavily、302ai 等多源搜索数据，提取主要内容，输出结构化 JSON，便于 LLM 分析
// 输入：Merge 节点输出的原始大 JSON
// 输出：{ allContents: [...], raw: {...} }

const input = $json;
let allContents = [];

// 递归提取所有 results/organic_results/snippet/content/title 字段
function extractText(obj) {
  if (Array.isArray(obj)) {
    obj.forEach(item => extractText(item));
  } else if (typeof obj === 'object' && obj !== null) {
    for (const key in obj) {
      if (["content", "snippet", "title", "description"].includes(key) && typeof obj[key] === 'string') {
        allContents.push(obj[key]);
      } else if (Array.isArray(obj[key]) || typeof obj[key] === 'object') {
        extractText(obj[key]);
      }
    }
  }
}

extractText(input);

// 去重、去空
allContents = Array.from(new Set(allContents)).filter(Boolean);

return [{
  json: {
    allContents,
    raw: input // 保留原始数据，便于 LLM 按需引用
  }
}]; 