// n8n代码节点：专业咨询报告HTML生成器（A4报告风格）
// 输入：来自OpenAI节点的结构化JSON报告数据
// 输出：完整的HTML报告，A4连续页面风格，确保所有图表正确渲染

const reportData = $json.output || $json;

// ===== 核心配置 =====
const config = {
  title: reportData["目录"] && reportData["目录"][0] ? 
    `${reportData["目录"][0]}行业研究报告` : '行业研究报告',
  generateTime: new Date().toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long', 
    day: 'numeric'
  }),
  colors: ['#2563eb', '#059669', '#dc2626', '#d97706', '#7c3aed', '#0891b2', '#65a30d', '#ea580c']
};

// ===== 全局变量 =====
let globalChartIndex = 0;
let allChartScripts = '';
let usedChartKeys = new Set();

// ===== 工具函数 =====

// 生成唯一ID
function generateId(text) {
  return text.replace(/[^\u4e00-\u9fa5\w]/g, '-').toLowerCase();
}

// 生成唯一图表ID
function generateChartId() {
  return `chart-${globalChartIndex++}`;
}

// 渲染目录导航
function renderTableOfContents(chapters) {
  const tocItems = chapters.map((chapter, index) => `
    <div class="toc-item">
      <span class="toc-number">${index + 1}.</span>
      <a href="#${generateId(chapter)}" class="toc-link">${chapter}</a>
      <span class="toc-dots"></span>
      <span class="toc-page">${index + 2}</span>
    </div>
  `).join('');

  return `
    <div class="page-section">
      <h2 class="section-title">目录</h2>
      <div class="toc-container">
        ${tocItems}
        <div class="toc-item">
          <span class="toc-number">附录.</span>
          <a href="#data-sources" class="toc-link">数据来源</a>
          <span class="toc-dots"></span>
          <span class="toc-page">${chapters.length + 2}</span>
        </div>
      </div>
    </div>
  `;
}



// 智能内容渲染
function renderContent(content, chapterName = '') {
  if (!content) return '<p class="no-data">暂无数据</p>';
  
  // 数组内容处理
  if (Array.isArray(content)) {
    // 企业列表特殊处理
    if (chapterName.includes('企业') || chapterName.includes('公司')) {
      return `
        <div class="company-grid">
          ${content.map((item, index) => `
            <div class="company-item company-${index % 4}">
              <div class="company-name">${item}</div>
            </div>
          `).join('')}
        </div>
      `;
    }
    
    // 一般列表内容
    return `
      <div class="content-list">
        ${content.map(item => `
          <div class="list-item">
            <span class="bullet">•</span>
            <span class="item-text">${item}</span>
          </div>
        `).join('')}
      </div>
    `;
  }
  
  // 对象内容处理
  if (typeof content === 'object' && content !== null) {
    return Object.entries(content).map(([key, value]) => `
      <div class="subsection">
        <h4 class="subsection-title">${key}</h4>
        <div class="subsection-content">
          ${renderContent(value, key)}
        </div>
      </div>
    `).join('');
  }
  
  // 文本内容处理
  return `<div class="text-content">${content}</div>`;
}

// 增强图表渲染函数
function renderChart(chartKey, chartData) {
  if (!chartData) {
    console.log(`图表数据为空: ${chartKey}`);
    return { html: '', script: '' };
  }
  
  const chartId = generateChartId();
  console.log(`渲染图表: ${chartKey}, ID: ${chartId}`, chartData);
  
  // SWOT分析特殊处理
  if (chartKey === 'SWOT' || (chartData.优势 && chartData.劣势)) {
    return renderSWOTChart(chartId, chartData, chartKey);
  }
  
  // 检查常规图表数据
  if (!chartData.values || !Array.isArray(chartData.values) || chartData.values.length === 0) {
    console.log(`图表数据values无效: ${chartKey}`, chartData.values);
    return { html: '', script: '' };
  }
  
  const chartType = chartData.type || 'line';
  const xAxisData = chartData.years || chartData.labels || chartData.categories || [];
  
  // 生成图表HTML
  const chartHtml = `
    <div class="chart-container">
      <div class="chart-title">${chartKey}</div>
      <div id="${chartId}" class="chart-canvas"></div>
      ${chartData.interpretation ? `
        <div class="chart-interpretation">
          <div class="interpretation-title">图表解读</div>
          <div class="interpretation-text">${chartData.interpretation}</div>
        </div>
      ` : ''}
    </div>
  `;
  
  // 生成图表配置
  const chartScript = generateChartScript(chartId, chartKey, chartData, chartType, xAxisData);
  
  return { html: chartHtml, script: chartScript };
}

// 生成图表脚本
function generateChartScript(chartId, chartKey, chartData, chartType, xAxisData) {
  const series = generateChartSeries(chartKey, chartData, chartType, xAxisData);
  
  return `
    // 渲染图表: ${chartKey}
    (function() {
      try {
        const chartElement = document.getElementById('${chartId}');
        if (!chartElement) {
          console.error('图表容器未找到: ${chartId}');
          return;
        }
        
        const chart = echarts.init(chartElement);
        const option = {
          tooltip: {
            trigger: '${chartType === 'pie' ? 'item' : 'axis'}',
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            borderColor: '#e5e7eb',
            borderWidth: 1,
            textStyle: { color: '#374151', fontSize: 12 }
          },
          legend: {
            show: ${chartType === 'pie' || (chartData.subSeries && Object.keys(chartData.subSeries).length > 0)},
            bottom: '8%',
            textStyle: { color: '#6b7280', fontSize: 11 }
          },
          ${chartType === 'pie' ? '' : `
          grid: {
            left: '8%',
            right: '8%',
            bottom: '15%',
            top: '10%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: ${JSON.stringify(xAxisData)},
            axisLine: { lineStyle: { color: '#e5e7eb' } },
            axisLabel: { 
              color: '#6b7280',
              fontSize: 11,
              rotate: ${xAxisData.length > 6 ? 30 : 0}
            }
          },
          yAxis: {
            type: 'value',
            name: '${chartData.unit || ''}',
            nameTextStyle: { color: '#6b7280', fontSize: 11 },
            axisLine: { lineStyle: { color: '#e5e7eb' } },
            axisLabel: { color: '#6b7280', fontSize: 11 },
            splitLine: { lineStyle: { color: '#f3f4f6' } }
          },`}
          series: ${series}
        };
        
        chart.setOption(option);
        
        // 响应式处理
        window.addEventListener('resize', () => chart.resize());
        
        console.log('图表渲染成功: ${chartKey}');
      } catch (error) {
        console.error('图表渲染失败: ${chartKey}', error);
      }
    })();
  `;
}

// 生成图表系列数据
function generateChartSeries(chartKey, chartData, chartType, xAxisData) {
  // 饼图处理
  if (chartType === 'pie') {
    const pieData = xAxisData.map((label, index) => ({
      name: label,
      value: chartData.values[index],
      itemStyle: { color: config.colors[index % config.colors.length] }
    }));
    
    return JSON.stringify([{
      name: chartKey,
      type: 'pie',
      radius: ['45%', '75%'],
      center: ['50%', '50%'],
      data: pieData,
      label: {
        show: true,
        formatter: '{b}: {c}' + (chartData.unit || ''),
        color: '#374151',
        fontSize: 11
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]);
  }
  
  // 多系列数据处理
  if (chartData.subSeries) {
    const series = Object.entries(chartData.subSeries).map(([name, values], index) => ({
      name,
      type: chartType,
      data: values,
      itemStyle: { color: config.colors[index % config.colors.length] },
      smooth: chartType === 'line',
      showSymbol: chartType === 'line'
    }));
    return JSON.stringify(series);
  }
  
  // 单系列数据
  return JSON.stringify([{
    name: chartKey,
    type: chartType,
    data: chartData.values,
    itemStyle: { color: config.colors[0] },
    smooth: chartType === 'line',
    showSymbol: chartType === 'line'
  }]);
}

// SWOT分析专用图表
function renderSWOTChart(chartId, swotData, chartKey) {
  const swotItems = [
    { name: '优势', key: '优势', color: '#059669', items: swotData.优势 || [] },
    { name: '劣势', key: '劣势', color: '#dc2626', items: swotData.劣势 || [] },
    { name: '机会', key: '机会', color: '#2563eb', items: swotData.机会 || [] },
    { name: '威胁', key: '威胁', color: '#d97706', items: swotData.威胁 || [] }
  ];
  
  const chartHtml = `
    <div class="chart-container">
      <div class="chart-title">SWOT分析</div>
      <div class="swot-layout">
        <div id="${chartId}" class="swot-chart"></div>
        <div class="swot-details">
          ${swotItems.map(item => `
            <div class="swot-category" style="border-left-color: ${item.color}">
              <div class="swot-category-title" style="color: ${item.color}">
                ${item.name} (${item.items.length}项)
              </div>
              <div class="swot-items">
                ${item.items.map(i => `<div class="swot-item">• ${i}</div>`).join('')}
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    </div>
  `;
  
  const chartScript = `
    // SWOT分析图表
    (function() {
      try {
        const chartElement = document.getElementById('${chartId}');
        if (!chartElement) {
          console.error('SWOT图表容器未找到: ${chartId}');
          return;
        }
        
        const chart = echarts.init(chartElement);
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: function(params) {
              const items = ${JSON.stringify(swotItems)}[params.dataIndex].items;
              return '<strong>' + params.name + '</strong><br/>' + 
                     items.slice(0, 3).map(i => '• ' + i).join('<br/>') +
                     (items.length > 3 ? '<br/>...' : '');
            }
          },
          legend: {
            bottom: '5%',
            textStyle: { color: '#6b7280', fontSize: 11 }
          },
          series: [{
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '45%'],
            data: ${JSON.stringify(swotItems.map(item => ({
              name: item.name,
              value: item.items.length,
              itemStyle: { color: item.color }
            })))},
            label: {
              show: true,
              formatter: '{b}: {c}项',
              color: '#374151',
              fontSize: 11
            }
          }]
        };
        
        chart.setOption(option);
        window.addEventListener('resize', () => chart.resize());
        
        console.log('SWOT图表渲染成功: ${chartId}');
      } catch (error) {
        console.error('SWOT图表渲染失败: ${chartId}', error);
      }
    })();
  `;
  
  return { html: chartHtml, script: chartScript };
}

// 智能图表匹配
function findMatchingChart(chapterName, chartData) {
  // 精确匹配
  if (chartData[chapterName]) {
    return { key: chapterName, data: chartData[chapterName] };
  }
  
  // 模糊匹配规则
  const matchRules = {
    '市场概述': ['市场规模', '总体规模', '整体规模'],
    '竞争格局': ['市场份额', '份额分析', '竞争份额'],
    '发展趋势': ['增长率', '增长趋势', '发展速度'],
    '技术趋势': ['技术发展', '技术增长', '创新指数'],
    '区域分析': ['地区分布', '区域占比', '地域分析']
  };
  
  // 应用匹配规则
  if (matchRules[chapterName]) {
    for (const rule of matchRules[chapterName]) {
      for (const [key, data] of Object.entries(chartData)) {
        if (key.includes(rule) || rule.includes(key)) {
          return { key, data };
        }
      }
    }
  }
  
  // 通用模糊匹配
  for (const [key, data] of Object.entries(chartData)) {
    if (chapterName.includes(key) || key.includes(chapterName)) {
      return { key, data };
    }
  }
  
  return null;
}

// 渲染章节
function renderChapter(chapterName, content, chartData) {
  const chapterId = generateId(chapterName);
  
  // 查找匹配的图表
  let matchedChart = null;
  
  // SWOT特殊处理
  if (chapterName === 'SWOT' && typeof content === 'object' && content.优势) {
    matchedChart = { key: 'SWOT', data: content };
  } else {
    // 常规图表匹配
    matchedChart = findMatchingChart(chapterName, chartData);
  }
  
  const hasChart = matchedChart && (
    (matchedChart.data.values && matchedChart.data.values.length > 0) || 
    matchedChart.data.优势
  );
  
  console.log(`章节: ${chapterName}, 匹配图表: ${matchedChart?.key}`, hasChart ? '有数据' : '无数据');
  
  // 渲染图表
  let chartBlock = { html: '', script: '' };
  if (hasChart) {
    chartBlock = renderChart(matchedChart.key, matchedChart.data);
    allChartScripts += chartBlock.script;
    usedChartKeys.add(matchedChart.key);
  }
  
  // 根据内容和图表决定布局
  let sectionContent = '';
  
  // SWOT特殊处理：只显示图表，不显示重复的文字内容
  if (chapterName === 'SWOT' && hasChart) {
    sectionContent = `
      <div class="single-column-layout">
        <div class="chart-section">
          ${chartBlock.html}
        </div>
      </div>
    `;
  } else if (hasChart && typeof content === 'string' && content.length > 300) {
    // 图文并排布局
    sectionContent = `
      <div class="two-column-layout">
        <div class="text-column">
          ${renderContent(content, chapterName)}
        </div>
        <div class="chart-column">
          ${chartBlock.html}
        </div>
      </div>
    `;
  } else if (hasChart) {
    // 图表在下方
    sectionContent = `
      <div class="single-column-layout">
        ${renderContent(content, chapterName)}
        <div class="chart-section">
          ${chartBlock.html}
        </div>
      </div>
    `;
  } else {
    // 纯文本布局
    sectionContent = `
      <div class="single-column-layout">
        ${renderContent(content, chapterName)}
      </div>
    `;
  }
  
  return `
    <div class="page-section">
      <h2 id="${chapterId}" class="section-title">${chapterName}</h2>
      ${sectionContent}
    </div>
  `;
}

// ===== 主要渲染逻辑 =====

const chapters = reportData["目录"] || Object.keys(reportData).filter(key => 
  !['目录', '图表数据', '数据来源'].includes(key)
);

const chartData = reportData["图表数据"] || {};
let allChapters = '';

// 渲染所有章节
chapters.forEach(chapterName => {
  const content = reportData[chapterName];
  if (content) {
    allChapters += renderChapter(chapterName, content, chartData);
  }
});

// 处理未被章节引用的独立图表
Object.keys(chartData).forEach(chartKey => {
  if (!usedChartKeys.has(chartKey)) {
    const chartResult = renderChart(chartKey, chartData[chartKey]);
    if (chartResult.html) {
      allChapters += `
        <div class="page-section">
          <h2 id="${generateId(chartKey)}" class="section-title">${chartKey}</h2>
          <div class="single-column-layout">
            ${chartResult.html}
          </div>
        </div>
      `;
      allChartScripts += chartResult.script;
    }
  }
});

// 数据来源章节
const dataSourcesSection = `
  <div class="page-section">
    <h2 id="data-sources" class="section-title">数据来源</h2>
    <div class="data-sources-content">
      <div class="sources-grid">
        <div class="sources-column">
          <h4 class="sources-subtitle">主要数据源</h4>
          <ul class="sources-list">
            <li>公开互联网数据搜索</li>
            <li>行业研究报告</li>
            <li>政府统计数据</li>
            <li>企业公开信息</li>
          </ul>
        </div>
        <div class="sources-column">
          <h4 class="sources-subtitle">研究方法</h4>
          <ul class="sources-list">
            <li>AI智能数据采集</li>
            <li>多源数据交叉验证</li>
            <li>专业分析模型</li>
            <li>可视化数据呈现</li>
          </ul>
        </div>
      </div>
      <div class="disclaimer">
        <strong>免责声明：</strong>本报告基于公开数据和AI分析生成，仅供参考。投资决策请结合专业咨询意见。
      </div>
    </div>
  </div>
`;

// ===== 生成完整HTML =====
const finalHTML = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${config.title}</title>
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
  <style>
    /* A4报告样式 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #2d3748;
      background: #ffffff;
      font-size: 14px;
    }
    
    .report-container {
      max-width: 210mm;
      margin: 0 auto;
      padding: 20mm;
      background: white;
      box-shadow: 0 0 20px rgba(0,0,0,0.1);
    }
    
    /* 报告头部 */
    .report-header {
      text-align: center;
      margin-bottom: 40px;
      padding-bottom: 30px;
      border-bottom: 3px solid #2563eb;
    }
    
    .report-title {
      font-size: 28px;
      font-weight: bold;
      color: #1a202c;
      margin-bottom: 15px;
      line-height: 1.3;
    }
    
    .report-subtitle {
      font-size: 16px;
      color: #4a5568;
      margin-bottom: 10px;
    }
    
    .report-date {
      font-size: 14px;
      color: #718096;
    }
    
    /* 页面章节 */
    .page-section {
      margin-bottom: 40px;
      page-break-inside: avoid;
    }
    
    .section-title {
      font-size: 20px;
      font-weight: bold;
      color: #2d3748;
      margin-bottom: 20px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e2e8f0;
      position: relative;
    }
    
    .section-title::before {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 60px;
      height: 2px;
      background: #2563eb;
    }
    
    /* 目录样式 */
    .toc-container {
      background: #f8fafc;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e2e8f0;
    }
    
    .toc-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px dotted #cbd5e1;
    }
    
    .toc-item:last-child {
      border-bottom: none;
    }
    
    .toc-number {
      font-weight: bold;
      color: #2563eb;
      min-width: 30px;
    }
    
    .toc-link {
      color: #2d3748;
      text-decoration: none;
      flex: 1;
    }
    
    .toc-link:hover {
      color: #2563eb;
    }
    
    .toc-dots {
      flex: 1;
      border-bottom: 1px dotted #cbd5e1;
      margin: 0 10px;
      height: 1px;
    }
    
    .toc-page {
      color: #718096;
      font-weight: bold;
      min-width: 20px;
      text-align: right;
    }
    
    /* 布局样式 */
    .single-column-layout {
      width: 100%;
    }
    
    .two-column-layout {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      align-items: start;
    }
    
    .text-column {
      padding-right: 15px;
    }
    
    .chart-column {
      padding-left: 15px;
    }
    
    .chart-section {
      margin-top: 25px;
    }
    

    
    /* 内容样式 */
    .text-content {
      text-align: justify;
      line-height: 1.8;
      color: #2d3748;
      word-wrap: break-word;
      word-break: normal;
    }
    
    .content-list {
      margin: 15px 0;
    }
    
    .list-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 8px;
    }
    
    .bullet {
      color: #2563eb;
      font-weight: bold;
      margin-right: 8px;
      margin-top: 2px;
      flex-shrink: 0;
    }
    
    .item-text {
      flex: 1;
      line-height: 1.6;
    }
    
    .subsection {
      margin: 20px 0;
    }
    
    .subsection-title {
      font-size: 16px;
      font-weight: bold;
      color: #2d3748;
      margin-bottom: 12px;
      position: relative;
      padding-left: 15px;
    }
    
    .subsection-title::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background: #2563eb;
      border-radius: 2px;
    }
    
    .subsection-content {
      padding-left: 15px;
    }
    
    /* 企业网格 */
    .company-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 12px;
      margin: 15px 0;
    }
    
    .company-item {
      padding: 12px;
      border-radius: 6px;
      text-align: center;
      border: 2px solid;
      transition: all 0.3s ease;
    }
    
    .company-0 { border-color: #2563eb; background: #eff6ff; }
    .company-1 { border-color: #059669; background: #ecfdf5; }
    .company-2 { border-color: #dc2626; background: #fef2f2; }
    .company-3 { border-color: #d97706; background: #fffbeb; }
    
    .company-name {
      font-weight: bold;
      color: #1a202c;
    }
    
    /* 图表样式 */
    .chart-container {
      background: #ffffff;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 20px;
      margin: 20px 0;
    }
    
    .chart-title {
      font-size: 16px;
      font-weight: bold;
      color: #2d3748;
      text-align: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e2e8f0;
    }
    
    .chart-canvas {
      width: 100%;
      height: 300px;
      min-height: 300px;
    }
    
    .chart-interpretation {
      margin-top: 15px;
      background: #f0f9ff;
      border: 1px solid #bae6fd;
      border-radius: 6px;
      padding: 15px;
    }
    
    .interpretation-title {
      font-weight: bold;
      color: #0369a1;
      margin-bottom: 8px;
    }
    
    .interpretation-text {
      color: #0c4a6e;
      line-height: 1.6;
    }
    
    /* SWOT专用样式 */
    .swot-layout {
      display: block;
      width: 100%;
    }
    
    .swot-chart {
      width: 100%;
      height: 300px;
      margin-bottom: 25px;
    }
    
    .swot-details {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      max-width: 800px;
      margin: 0 auto;
    }
    
    .swot-category {
      border-left: 4px solid;
      padding: 15px;
      margin-bottom: 0;
      background: #f8fafc;
      border-radius: 0 8px 8px 0;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      min-height: 120px;
      display: flex;
      flex-direction: column;
    }
    
    .swot-category-title {
      font-weight: bold;
      margin-bottom: 12px;
      font-size: 15px;
      color: inherit;
      flex-shrink: 0;
    }
    
    .swot-items {
      font-size: 13px;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    
    .swot-item {
      margin-bottom: 6px;
      color: #4a5568;
      line-height: 1.5;
      padding-left: 12px;
      position: relative;
    }
    
    .swot-item::before {
      content: '•';
      color: inherit;
      position: absolute;
      left: 0;
      top: 0;
      font-weight: bold;
    }
    
    /* 数据来源样式 */
    .data-sources-content {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 20px;
    }
    
    .sources-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      margin-bottom: 20px;
    }
    
    .sources-subtitle {
      font-size: 16px;
      font-weight: bold;
      color: #2d3748;
      margin-bottom: 12px;
    }
    
    .sources-list {
      list-style: none;
    }
    
    .sources-list li {
      padding: 4px 0;
      padding-left: 15px;
      position: relative;
    }
    
    .sources-list li::before {
      content: '•';
      color: #2563eb;
      font-weight: bold;
      position: absolute;
      left: 0;
    }
    
    .disclaimer {
      background: #fef3c7;
      border: 1px solid #f59e0b;
      border-radius: 6px;
      padding: 15px;
      color: #92400e;
      font-size: 13px;
      line-height: 1.5;
    }
    
    .no-data {
      color: #718096;
      font-style: italic;
      text-align: center;
      padding: 20px;
    }
    
    /* 移动端响应式设计 */
    @media (max-width: 768px) {
      .report-container {
        max-width: 100%;
        padding: 15px;
        margin: 0;
        box-shadow: none;
      }
      
      .report-title {
        font-size: 22px;
        line-height: 1.2;
      }
      
      .report-subtitle {
        font-size: 14px;
      }
      
      .section-title {
        font-size: 18px;
        margin-bottom: 15px;
      }
      
      .two-column-layout {
        display: block;
      }
      
      .text-column,
      .chart-column {
        padding: 0;
        margin-bottom: 20px;
      }
      
      .sources-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }
      
      .swot-layout {
        display: block;
      }
      
      .swot-chart {
        margin-bottom: 20px;
      }
      
      .swot-details {
        grid-template-columns: 1fr;
        gap: 15px;
        max-width: none;
      }
      
      .company-grid {
        grid-template-columns: 1fr 1fr;
        gap: 10px;
      }
      
      .chart-canvas {
        height: 250px;
        min-height: 250px;
      }
      
      .swot-chart {
        height: 250px;
      }
      
      .toc-container {
        padding: 15px;
      }
      
      .toc-item {
        flex-wrap: wrap;
      }
      
      .toc-dots {
        display: none;
      }
      
      .toc-page {
        margin-left: auto;
      }
    }
    
    /* 小屏幕设备 */
    @media (max-width: 480px) {
      .report-container {
        padding: 10px;
      }
      
      .report-title {
        font-size: 20px;
      }
      
      .section-title {
        font-size: 16px;
      }
      
      .company-grid {
        grid-template-columns: 1fr;
      }
      
      .chart-canvas {
        height: 200px;
        min-height: 200px;
      }
      
      .swot-chart {
        height: 200px;
      }
      
      .swot-category {
        min-height: auto;
        padding: 12px;
      }
      
      .swot-category-title {
        font-size: 14px;
      }
      
      .swot-items {
        font-size: 12px;
      }
    }
    
    /* 打印样式 */
    @media print {
      body { background: white; }
      .report-container { 
        box-shadow: none; 
        max-width: none;
        padding: 15mm;
      }
      .page-section { page-break-inside: avoid; }
      .chart-container { page-break-inside: avoid; }
    }
    
    /* 平滑滚动 */
    html { scroll-behavior: smooth; }
  </style>
</head>
<body>
  <div class="report-container">
    
    <!-- 报告头部 -->
    <header class="report-header">
      <h1 class="report-title">${config.title}</h1>
      <p class="report-subtitle">AI智能生成 · 专业分析报告</p>
      <p class="report-date">生成时间：${config.generateTime}</p>
    </header>
    
    <!-- 目录 -->
    ${renderTableOfContents(chapters)}
    
    <!-- 报告主体 -->
    <main>
      ${allChapters}
      ${dataSourcesSection}
    </main>
    
    <!-- 页脚 -->
    <footer style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #e2e8f0; color: #718096; font-size: 12px;">
      <p>本报告由AI智能生成，数据来源于公开信息 | 图表数量: ${globalChartIndex} | © ${new Date().getFullYear()} 行业研究报告生成器</p>
    </footer>
    
  </div>
  
  <script>
    // 等待DOM加载完成
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🚀 开始渲染专业咨询报告...');
      console.log('📊 预期图表数量:', ${globalChartIndex});
      
      // 检查ECharts是否加载
      if (typeof echarts === 'undefined') {
        console.error('❌ ECharts未加载，图表将无法显示');
        return;
      }
      
      console.log('✅ ECharts已加载，开始渲染图表...');
      
      // 渲染所有图表
      ${allChartScripts}
      
      console.log('✅ 专业咨询报告已生成完成');
      console.log('📊 实际图表数量:', ${globalChartIndex});
      console.log('📝 章节数量:', ${chapters.length});
    });
  </script>
</body>
</html>`;

// 返回结果给n8n
return {
  json: {
    html: finalHTML,
    metadata: {
      title: config.title,
      generateTime: config.generateTime,
      chaptersCount: chapters.length,
      chartsCount: globalChartIndex,
      usedChartKeys: Array.from(usedChartKeys)
    }
  }
};