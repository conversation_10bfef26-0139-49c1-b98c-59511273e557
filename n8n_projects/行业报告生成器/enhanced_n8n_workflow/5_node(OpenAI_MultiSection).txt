# 5_node(OpenAI_MultiSection)

## 作用

调用 OpenAI（或兼容 LLM）对合并后的多源数据进行多章节、深度结构化分析，输出多维度行业报告。

## 配置建议

- Model: deepseek/大型模型
- 输入：MergeData 节点输出的所有数据
- 输出格式：严格 JSON，分章节、分维度

**system prompt:**

你是一名资深行业分析师，擅长将多条行业摘要整合为一份结构化、可视化友好的行业研究报告。你的输出必须是单一的、完整的 JSON 对象，不允许有任何解释、代码块标记、数组包裹或多余内容。

## User prompt:

请基于以下多条行业摘要，撰写一份结构化、详细、适合可视化的行业研究报告，只输出一个完整的 JSON 对象，内容如下：

{{ $json.allSummaries }}

要求：
1. 只输出单一有效 JSON，不要输出任何解释、代码块标记、数组或多余内容。
2. 报告分为多个章节：市场概述、细分市场、区域分析、竞争格局、SWOT、政策环境、技术趋势、未来预测等。
3. 每章节内容不少于200字，包含真实数据和引用。
4. 输出格式为严格有效的JSON，结构如下
{
"目录": ["市场概述", "细分市场", ...],
"市场概述": "...",
"细分市场": ["...", "..."],
"区域分析": ["...", "..."],
"竞争格局": {"主要企业": [...], "市场份额": [...]},
"SWOT": {"优势": [...], "劣势": [...], "机会": [...], "威胁": [...]},
"政策环境": "...",
"技术趋势": "...",
"未来预测": "...",
"图表数据": {
"市场规模": {
"type": "line",  // 图表类型：line, bar, pie, scatter
"years": [2020, 2021, 2022, 2023],
"values": [1000, 1500, 2000, 2800],
"unit": "亿元",
"interpretation": "市场规模持续增长，年复合增长率达到40%",
"subSeries": {  // 可选：多系列数据
"中国": [500, 800, 1200, 1600],
"美国": [300, 400, 500, 700]
}
},
"市场份额": {...}, "增长率": {...}, ...
}
}
5. 图表数据要求：
   - 每个图表必须包含interpretation字段，提供专业解读
   - 数据来源要明确，数值要真实可信
   - 支持多维度对比（如地区、时间、产品类别）
   - 图表类型要与数据特性匹配

6. 内容长度控制：
   - 短内容（<120字）将自动渲染为卡片样式
   - 数组内容将渲染为标签卡片
   - 长文本保持段落格式
7. 内容必须为中文。
8. 如果无法生成完整内容，也必须输出结构完整的 JSON，字段内容可为空字符串或空数组。