# 3_node(MultiDataSource)

## 作用
并行抓取多个数据源的行业信息，为后续AI分析提供更丰富的数据。

## 推荐节点
- Tavily Search
- HTTP Request（如调用第三方API、政府统计、行业数据库等）

## 配置建议
### Tavily Search 节点
Credential to connect with
Tavily account
Resource
Search
Operation
Query
Query
{{ $json.searchVariants }}行业分析 市场数据 {{ $('Set').item.json.timeRange }}
 
Options
Search Depth
Advanced
Max Results
10
Include Raw Content

Include Images



### HTTP Request 节点

Method
GET
URL
https://api.302.ai/searchapi/search
Authentication
None
Send Query Parameters

Specify Query Parameters
Using Fields Below
Query Parameters
Name
q
Value
{{ $json.searchVariants }}
 
Name
region
 
region
Value
{{ $('Set').item.json.region }}
 
Name
timeRange
Value
{{ $('Set').item.json.timestamp }}
 
Name
num
Value
10
Name
api_key
Value
sk-LMfpbQt8WPp71EXwFmW6fedhTJX9J4lag5znVNRReG5fWPSc
Name
engine
Value
google
Send Headers

Specify Headers
Using Fields Below
Header Parameters
Name
accept
Value
application/json
Send Body

Options
Response
Include Response Headers and Status

Never Error

Response Format
JSON
