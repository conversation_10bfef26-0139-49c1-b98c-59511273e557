// 2.1_node(SetParams_Enhanced) - 简化修复版
// 专门解决"跨境物流"输入问题

// 直接从第一个输入项获取数据
const items = $input.all();
const firstItem = items[0];
const inputData = firstItem.json;

// 详细调试输出
console.log('=== 调试信息 ===');
console.log('输入项数量:', items.length);
console.log('第一项数据:', JSON.stringify(inputData, null, 2));
console.log('industry字段:', inputData.industry);
console.log('industry类型:', typeof inputData.industry);

// 获取行业信息 - 多种尝试方式
let industry = '';

// 方式1: 直接获取industry字段
if (inputData.industry) {
  industry = String(inputData.industry).trim();
  console.log('方式1成功 - industry:', industry);
}

// 方式2: 如果没有industry，尝试其他字段
if (!industry && inputData.chatInput) {
  industry = String(inputData.chatInput).trim();
  console.log('方式2成功 - chatInput:', industry);
}

// 方式3: 尝试query字段
if (!industry && inputData.query) {
  industry = String(inputData.query).trim();
  console.log('方式3成功 - query:', industry);
}

// 方式4: 尝试keyword字段
if (!industry && inputData.keyword) {
  industry = String(inputData.keyword).trim();
  console.log('方式4成功 - keyword:', industry);
}

console.log('最终industry值:', industry);
console.log('industry长度:', industry.length);

// 如果还是没有获取到行业信息
if (!industry) {
  console.log('错误：未能获取到行业信息');
  return [{
    json: {
      error: true,
      message: '未能获取到行业关键词',
      debug: {
        inputData: inputData,
        allKeys: Object.keys(inputData),
        industryField: inputData.industry,
        chatInputField: inputData.chatInput,
        queryField: inputData.query,
        keywordField: inputData.keyword
      }
    }
  }];
}

// 获取其他参数
const region = String(inputData.region || '中国').trim();
const timeRange = String(inputData.timeRange || '2024-2025').trim();
const subSector = String(inputData.subSector || '').trim();
const reportVersion = inputData.reportVersion || 'enhanced';
const reportDepth = inputData.reportDepth || 'comprehensive';
const analysisType = inputData.analysisType || 'full';
const outputLanguage = inputData.outputLanguage || 'zh-CN';
const includeCharts = inputData.includeCharts !== false;

console.log('所有参数获取完成');
console.log('industry:', industry);
console.log('region:', region);
console.log('timeRange:', timeRange);

// 生成搜索关键词
const searchVariants = [
  `${industry} 市场规模 ${timeRange}`,
  `${industry} 行业分析报告 ${region}`,
  `${industry} 产业发展现状`,
  `${industry} 市场容量 统计数据`,
  `${industry} 竞争格局 主要企业`,
  `${industry} 市场份额 领军企业`,
  `${industry} 行业集中度分析`,
  `${industry} 企业排名 ${timeRange}`,
  `${industry} 技术发展趋势`,
  `${industry} 创新技术 新兴技术`,
  `${industry} 数字化转型`,
  `${industry} 政策支持 国家政策`,
  `${industry} 行业标准 监管政策`,
  `${industry} 产业政策 ${region}`,
  `${industry} 投资趋势 融资情况`,
  `${industry} 供应链分析`,
  `${industry} 产业链上下游`,
  `${industry} ${region} 发展情况`,
  `${industry} 区域分布 地域特色`,
  `${industry} 未来展望 发展前景`
];

// 生成章节配置
const sections = [
  '市场概述', '细分市场', '区域分析', '竞争格局', 
  'SWOT分析', '技术趋势', '政策环境', '投资分析', 
  '供应链分析', '风险评估', '未来预测', '战略建议'
];

// 生成搜索配置
const searchConfig = {
  maxResults: 30,
  includeImages: includeCharts,
  searchDepth: reportDepth,
  timeRange: timeRange,
  region: region
};

console.log('生成搜索词数量:', searchVariants.length);
console.log('生成章节数量:', sections.length);

// 返回成功结果
const result = {
  json: {
    // 基础参数
    industry: industry,
    region: region,
    timeRange: timeRange,
    subSector: subSector,
    
    // 增强版参数
    reportVersion: reportVersion,
    reportDepth: reportDepth,
    analysisType: analysisType,
    outputLanguage: outputLanguage,
    includeCharts: includeCharts,
    sections: sections,
    
    // 搜索配置
    searchVariants: searchVariants,
    searchConfig: searchConfig,
    
    // 元数据
    timestamp: new Date().toISOString(),
    configVersion: '2.1-simple-fixed',
    estimatedTokens: 25000,
    
    // 状态
    error: false,
    message: `参数设置成功 - 行业: ${industry}`,
    
    // 调试信息
    debug: {
      originalInput: inputData,
      industrySource: 'industry字段',
      searchVariantsCount: searchVariants.length,
      sectionsCount: sections.length,
      processingTime: new Date().toISOString()
    }
  }
};

console.log('返回结果:', JSON.stringify(result, null, 2));

return [result];
