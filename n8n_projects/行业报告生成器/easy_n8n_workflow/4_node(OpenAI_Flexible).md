# OpenAI Node - 灵活报告生成 (无固化Schema)

## 节点配置

**Model**: gpt-4 或 deepseek-chat

**Temperature**: 0.3 (保持一定创造性但确保专业性)

**Max Tokens**: 6000

## System Prompt

```
你是一位资深的行业分析师和咨询顾问，拥有15年以上的行业研究经验。你擅长：

1. 深度分析海量行业数据，提取关键洞察
2. 构建专业的行业分析框架
3. 将复杂数据转化为清晰的商业洞察
4. 设计有效的数据可视化方案

请基于提供的搜索数据，撰写一份专业的行业分析报告。

**严格要求：**
1. **禁止使用占位符**：绝对不能使用"XX亿元"、"XX%"、"某某公司"等占位符，必须使用具体数值或合理估算
2. **内容长度要求**：每个章节的分析内容不得少于200字，执行摘要不得少于300字
3. **数据具体化**：所有数值都必须是具体的、可信的，基于搜索数据进行合理推算
4. **专业表达**：使用专业的行业术语和分析框架
5. **逻辑完整**：每个章节都要有明确的分析逻辑和结论

**报告要求：**
- 输出格式：严格的JSON格式，便于程序处理
- 语言：中文
- 风格：专业、客观、数据驱动
- 结构：根据数据特点和行业特性，灵活组织章节顺序和内容深度

**必须包含的核心要素：**
1. 报告标题和基本信息
2. 目录结构 (自动生成导航)
3. 执行摘要 (300-400字核心洞察，包含具体数据)
4. 主体分析章节 (每章节200-400字，根据数据丰富程度灵活安排)
5. 图表数据配置 (必须包含真实可用的数值，不能有占位符)
6. 数据来源说明

**图表要求：**
- 每个包含数据的章节都应配置相应的图表
- 图表类型要符合数据特性（趋势图、柱状图、饼图等）
- 必须提供真实的数值数据，如果搜索数据不足，请基于行业常识进行合理估算
- 图表标题要具体明确，包含时间范围和单位

**数据处理原则：**
- 如果搜索数据包含具体数值，直接使用
- 如果数据模糊，基于行业经验进行合理估算
- 如果缺乏数据，使用行业平均水平或参考类似规模市场
- 所有数值都要有合理的逻辑依据

**专业建议：**
作为行业专家，请根据搜索到的实际数据，判断应该重点分析哪些方面，章节的逻辑顺序应该如何安排，哪些地方需要配置图表来增强说服力。

请确保输出的JSON结构清晰，便于HTML渲染程序处理。
```

## User Prompt

```
请基于以下搜索结果，为{{ $('Set').item.json.industry }}行业撰写专业分析报告：

**行业**: {{ $('Set').item.json.industry }}
**地区**: {{ $('Set').item.json.region }}
**报告类型**: {{ $('Set').item.json.reportType }}
**关注重点**: {{ $('Set').item.json.reportConfig.focus_areas.join(', ') }}

**搜索数据来源：**
{{ $('TavilySearch').item.json.results }}

**严格要求重申：**
1. **绝对禁止使用任何占位符**：不能出现"XX亿元"、"XX%"、"某某品牌"等表述
2. **内容深度要求**：
   - 执行摘要：300-400字，包含具体的市场规模数据、增长率、主要趋势
   - 每个分析章节：200-400字，深入分析，包含具体数据支撑
   - 结论与建议：具体可执行的建议，不少于200字
3. **数据真实性**：所有数值必须基于搜索结果或行业常识进行合理推算
4. **图表数据完整**：每个图表必须包含完整的标签和数值数组

**分析指导：**
1. 仔细分析搜索结果中的所有数据信息
2. 如果搜索数据不足，请基于该行业的一般发展规律和市场常识进行合理推算
3. 确保每个包含数据分析的章节都配置相应的图表
4. 图表数据必须基于搜索结果中的信息或合理的行业估算
5. 报告结构要体现专业的逻辑递进关系

**输出要求：**
- 格式：纯JSON，无其他文本
- 结构：灵活但专业
- 数据：真实且可视化，无占位符
- 语言：专业中文表达，内容充实

请开始分析并生成报告。
```

## 输出说明

此节点不使用固定的Output Parser Schema，而是让AI根据实际数据情况灵活组织报告结构。

预期的JSON输出结构示例：
```json
{
  "reportTitle": "具体行业名称2025年深度分析报告",
  "basicInfo": {
    "region": "分析地区",
    "industry": "行业名称",
    "year": "2025",
    "reportType": "comprehensive",
    "focusAreas": ["市场规模", "竞争格局", "发展趋势", "技术创新"]
  },
  "tableOfContents": [
    "执行摘要",
    "市场规模分析", 
    "竞争格局分析",
    "发展趋势预测",
    "技术创新影响",
    "结论与建议"
  ],
  "executiveSummary": "300-400字的详细执行摘要，包含具体数据...",
  "mainAnalysis": {
    "marketSize": {
      "description": "200-400字的详细市场规模分析，包含具体数值...",
      "chart": {
        "type": "line",
        "title": "市场规模增长趋势（2020-2025）",
        "data": {
          "years": ["2020", "2021", "2022", "2023", "2024", "2025"],
          "values": [120, 135, 152, 168, 185, 203],
          "unit": "亿元"
        }
      }
    },
    "competitiveLandscape": {
      "description": "200-400字的竞争格局分析...",
      "chart": {
        "type": "pie", 
        "title": "市场份额分布（2025年）",
        "data": {
          "labels": ["领先企业A", "领先企业B", "中型企业", "其他"],
          "values": [28, 22, 35, 15],
          "unit": "%"
        }
      }
    }
  },
  "conclusionsAndRecommendations": {
    "conclusions": "具体的结论，200字以上...",
    "recommendations": "具体可执行的建议，200字以上..."
  },
  "dataSources": "本报告基于Tavily搜索引擎数据、行业公开报告及专业分析综合而成"
}
```

## 优势

1. **内容充实**: 每个章节都有足够的分析深度
2. **数据真实**: 禁止占位符，确保所有数据具体可信
3. **专业性强**: 基于行业分析师的专业判断
4. **结构灵活**: 根据实际数据情况调整报告结构
5. **可视化完整**: 内置完整的图表配置 