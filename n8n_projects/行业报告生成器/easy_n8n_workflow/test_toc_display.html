
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>目录测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto p-8">
        <h1 class="text-2xl font-bold mb-6">目录显示测试</h1>
        
        <!-- 目录 -->
        <nav class="w-1/3 bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-4 text-gray-800">报告目录</h2>
            <ul class="space-y-2">
                
            <li class="toc-item p-2 rounded cursor-pointer" onclick="scrollToSection('executive_summary')">
                <span class="text-sm font-medium text-gray-700">执行摘要</span>
                <span class="text-xs text-gray-500 float-right">P1</span>
            </li>
        
            <li class="toc-item p-2 rounded cursor-pointer" onclick="scrollToSection('marketSize')">
                <span class="text-sm font-medium text-gray-700">市场规模分析</span>
                <span class="text-xs text-gray-500 float-right">P2</span>
            </li>
        
            <li class="toc-item p-2 rounded cursor-pointer" onclick="scrollToSection('competitiveLandscape')">
                <span class="text-sm font-medium text-gray-700">竞争格局分析</span>
                <span class="text-xs text-gray-500 float-right">P3</span>
            </li>
        
            <li class="toc-item p-2 rounded cursor-pointer" onclick="scrollToSection('trends')">
                <span class="text-sm font-medium text-gray-700">发展趋势预测</span>
                <span class="text-xs text-gray-500 float-right">P4</span>
            </li>
        
            <li class="toc-item p-2 rounded cursor-pointer" onclick="scrollToSection('technologicalInnovation')">
                <span class="text-sm font-medium text-gray-700">技术创新影响</span>
                <span class="text-xs text-gray-500 float-right">P5</span>
            </li>
        
            <li class="toc-item p-2 rounded cursor-pointer" onclick="scrollToSection('conclusions')">
                <span class="text-sm font-medium text-gray-700">结论与建议</span>
                <span class="text-xs text-gray-500 float-right">P6</span>
            </li>
        
            </ul>
        </nav>
        
        <div class="mt-8 p-4 bg-green-100 rounded">
            <h3 class="font-bold text-green-800">测试结果</h3>
            <p class="text-green-700">目录项数量: 6</p>
            <p class="text-green-700">包含undefined: 否</p>
        </div>
    </div>
    
    <script>
        function scrollToSection(sectionId) {
            console.log('点击章节:', sectionId);
            alert('点击了章节: ' + sectionId);
        }
    </script>
</body>
</html>