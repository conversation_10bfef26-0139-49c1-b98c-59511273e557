* [X] ***user prompt:***


请基于以下搜索结果，分析{{$('Set').item.json.industry}}行业在{{ $('Set').item.json.timestamp }}时间的发展情况：

搜索结果：
{{$('TavilySearch').item.json.results}}

请严格按照JSON格式输出分析报告。


* [X] ***system prompt:***


你是一个专业的行业分析师，擅长撰写详细的行业研究报告。你的回答应该准确、专业、结构化，并严格按照提供的JSON格式输出。

要求：

1. 输出格式必须是有效的JSON，不包含任何其他文本
2. 所有内容必须使用中文
3. 包含以下字段：

   - title: 报告标题（包含行业名称）
   - summary: 执行摘要（150-200字）
   - market_size: 当前市场规模数据（包含具体数值和单位）
   - trends: 发展趋势（3-5个要点，每个要点简洁明了）
   - key_players: 主要参与者（3-5家公司，包含简短描述）
   - challenges: 面临挑战（3个要点）
   - opportunities: 发展机遇（3个要点）
   - forecast: 未来3-5年预测
   - chart_data: 图表数据（必须包含真实可用的数值）
4. chart_data格式要求：

   ```
   {
   "market_trend": {
   "years": ["2022", "2023", "2024", "2025"],
   "values": [实际数值数组],
   "unit": "亿元"
   },
   "growth_rate": {
   "years": ["2022", "2023", "2024", "2025"],
   "values": [实际增长率数组],
   "unit": "%"
   }
   }
   ```

请确保所有数据基于真实市场信息，分析客观深入，避免过度乐观或悲观的预测。


* [X] ***output parser schema json:***

```
{
  "type": "object",
  "properties": {
    "title": {
      "type": "string",
      "description": "报告标题，包含行业名称"
    },
    "summary": {
      "type": "string",
      "description": "行业分析的执行摘要"
    },
    "market_size": {
      "type": "string",
      "description": "当前市场规模，包含单位"
    },
    "trends": {
      "type": "array",
      "items": {
        "type": "string"
      },
      "description": "行业关键趋势"
    },
    "key_players": {
      "type": "array",
      "items": {
        "type": "string"
      },
      "description": "行业主要参与者和公司"
    },
    "challenges": {
      "type": "array",
      "items": {
        "type": "string"
      },
      "description": "行业面临的挑战和障碍"
    },
    "opportunities": {
      "type": "array",
      "items": {
        "type": "string"
      },
      "description": "市场机遇和潜力"
    },
    "forecast": {
      "type": "string",
      "description": "未来展望和预测"
    },
    "chart_data": {
      "type": "object",
      "properties": {
        "market_trend": {
          "type": "object",
          "properties": {
            "years": {
              "type": "array",
              "items": {
                "type": "string"
              },
              "description": "年份数组"
            },
            "values": {
              "type": "array",
              "items": {
                "type": "number"
              },
              "description": "市场规模数值"
            },
            "unit": {
              "type": "string",
              "description": "数值单位，如亿元、万亿等"
            }
          },
          "required": ["years", "values", "unit"]
        },
        "growth_rate": {
          "type": "object",
          "properties": {
            "years": {
              "type": "array",
              "items": {
                "type": "string"
              },
              "description": "年份数组"
            },
            "values": {
              "type": "array",
              "items": {
                "type": "number"
              },
              "description": "增长率数值"
            },
            "unit": {
              "type": "string",
              "description": "增长率单位，通常为%"
            }
          },
          "required": ["years", "values", "unit"]
        }
      },
      "required": ["market_trend", "growth_rate"]
    }
  },
  "required": ["title", "summary", "market_size", "trends", "key_players", "challenges", "opportunities", "forecast", "chart_data"]
}
```
