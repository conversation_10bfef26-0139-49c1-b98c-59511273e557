// Universal HTML Report Generator - 通用行业报告生成器
// 能够适应任何灵活的JSON输入结构，自动生成专业的可视化报告

// 获取上游数据
const setData = $('Set').item.json;
const searchData = $('TavilySearch').item.json;
const aiData = $('OpenAI').item.json;

// 通用数据解析函数
function parseAIResponse(response) {
    console.log('开始解析AI响应...');
    
    let content = null;
    
    // 多种格式兼容解析
    // 首先检查是否有output字段且为字符串（如temp.json的情况）
    if (response?.output && typeof response.output === 'string') {
        console.log('检测到output字符串字段，尝试解析JSON');
        try {
            content = JSON.parse(response.output);
            console.log('output字符串JSON解析成功');
            return normalizeReportData(content);
        } catch (parseError) {
            console.log('output字符串JSON解析失败，尝试其他方法');
            content = response.output;
        }
    }
    // 检查是否有output字段且为对象
    else if (response?.output && typeof response.output === 'object') {
        content = response.output;
    } else if (response?.choices?.[0]?.message?.content) {
        content = response.choices[0].message.content;
    } else if (response?.content) {
        content = response.content;
    } else if (typeof response === 'string') {
        content = response;
    } else if (typeof response === 'object') {
        content = response;
    }
    
    // 如果是字符串，尝试解析JSON
    if (typeof content === 'string') {
        try {
            // 清理markdown标记
            content = content.replace(/```(?:json|javascript|js)?\s*/g, '').replace(/```\s*$/g, '').trim();
            
            // 提取JSON对象
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                content = JSON.parse(jsonMatch[0]);
            }
        } catch (e) {
            console.log('JSON解析失败，使用文本提取');
            return extractFromText(content);
        }
    }
    
    return normalizeReportData(content);
}

// 从文本中提取结构化数据
function extractFromText(text) {
    const sections = {};
    const charts = [];
    
    // 简单的文本分段逻辑
    const paragraphs = text.split(/\n\s*\n/);
    let currentSection = 'content';
    
    paragraphs.forEach((para, index) => {
        if (para.trim()) {
            if (!sections[currentSection]) {
                sections[currentSection] = {
                    title: `分析章节 ${index + 1}`,
                    content: para.trim()
                };
            } else {
                sections[currentSection].content += '\n\n' + para.trim();
            }
        }
    });
    
    return {
        report_meta: {
            title: `${setData.industry}行业分析报告`,
            subtitle: '基于AI智能分析',
            industry: setData.industry,
            region: setData.region,
            date: new Date().toISOString().split('T')[0]
        },
        sections: sections,
        charts: charts
    };
}

// 标准化报告数据结构
function normalizeReportData(data) {
    if (!data || typeof data !== 'object') {
        return getDefaultReportStructure();
    }
    
    // 确保基本结构存在
    const normalized = {
        report_meta: data.report_meta || {
            title: data.reportTitle || `${setData.industry}行业分析报告`,
            subtitle: data.title || '专业行业分析报告',
            industry: setData.industry,
            region: setData.region || data.basicInfo?.region || '中国',
            date: new Date().toISOString().split('T')[0],
            author: 'AI行业分析师',
            report_type: data.basicInfo?.reportType || 'comprehensive',
            focus_areas: data.basicInfo?.focusAreas || []
        },
        table_of_contents: data.table_of_contents || data.tableOfContents || [],
        sections: data.sections || {},
        charts: data.charts || [],
        data_sources: data.data_sources || data.dataSources ? [data.dataSources] : ['Tavily搜索引擎', 'AI智能分析']
    };
    
    // 如果没有sections，尝试从其他字段提取
    if (Object.keys(normalized.sections).length === 0) {
        normalized.sections = extractSectionsFromData(data);
    }
    
    // 自动生成目录
    if (normalized.table_of_contents.length === 0) {
        normalized.table_of_contents = generateTableOfContents(normalized.sections);
    }
    
    // 确保图表配置
    if (normalized.charts.length === 0) {
        normalized.charts = extractChartsFromSections(normalized.sections);
    }
    
    return normalized;
}

// 灵活提取章节数据 - 不固化任何特定格式
function extractSectionsFromData(data) {
    const sections = {};
    
    // 检测并处理各种可能的数据结构
    
    // 1. 处理temp.json格式（reportTitle + mainAnalysis）
    if (data.reportTitle && data.mainAnalysis) {
        console.log('检测到temp.json格式');
        
        // 执行摘要
        if (data.executiveSummary) {
            sections.executive_summary = {
                title: '执行摘要',
                content: data.executiveSummary
            };
        }
        
        // 动态处理mainAnalysis中的所有字段
        if (data.mainAnalysis && typeof data.mainAnalysis === 'object') {
            Object.keys(data.mainAnalysis).forEach(key => {
                const analysisItem = data.mainAnalysis[key];
                if (analysisItem && typeof analysisItem === 'object') {
                    // 生成友好的章节标题
                    const sectionTitle = generateSectionTitle(key);
                    sections[key] = {
                        title: sectionTitle,
                        content: analysisItem.description || analysisItem.content || '分析内容',
                        chart_config: analysisItem.chart
                    };
                }
            });
        }
        
        // 结论与建议
        if (data.conclusionsAndRecommendations) {
            const conclusions = data.conclusionsAndRecommendations;
            sections.conclusions = {
                title: '结论与建议',
                content: formatConclusionsContent(conclusions),
                subsections: {
                    conclusions: {
                        title: '结论',
                        content: conclusions.conclusions || '结论分析'
                    },
                    recommendations: {
                        title: '建议',
                        content: conclusions.recommendations || '建议分析'
                    }
                }
            };
        }
    }
    
    // 2. 处理标准sections格式
    else if (data.sections && typeof data.sections === 'object') {
        Object.keys(data.sections).forEach(key => {
            const section = data.sections[key];
            if (section && typeof section === 'object') {
                sections[key] = {
                    title: section.title || generateSectionTitle(key),
                    content: section.content || '内容分析',
                    ...section // 保留其他属性
                };
            }
        });
    }
    
    // 3. 处理扁平化字段格式
    else {
        const fieldMapping = {
            executive_summary: ['executiveSummary', 'summary', 'exec_summary'],
            market_size: ['marketSize', 'market_size', 'marketAnalysis'],
            competitive_analysis: ['competitiveLandscape', 'competition', 'competitive'],
            trends: ['trends', 'trend_analysis', 'market_trends'],
            technology: ['technologicalInnovation', 'technology', 'tech_analysis'],
            challenges: ['challenges', 'risks', 'obstacles'],
            opportunities: ['opportunities', 'potential', 'prospects'],
            forecast: ['forecast', 'future_outlook', 'predictions']
        };
        
        Object.keys(fieldMapping).forEach(sectionKey => {
            const possibleFields = fieldMapping[sectionKey];
            for (const field of possibleFields) {
                if (data[field]) {
                    sections[sectionKey] = {
                        title: generateSectionTitle(sectionKey),
                        content: extractContentFromField(data[field])
                    };
                    break;
                }
            }
        });
    }
    
    // 如果仍然没有sections，创建默认结构
    if (Object.keys(sections).length === 0) {
        sections.default_analysis = {
            title: '行业分析',
            content: '基于AI智能分析生成的行业报告内容。'
        };
    }
    
    return sections;
}

// 生成友好的章节标题
function generateSectionTitle(key) {
    const titleMap = {
        // 英文到中文映射
        executive_summary: '执行摘要',
        market_size: '市场规模分析', 
        marketSize: '市场规模分析',
        competitive_analysis: '竞争格局分析',
        competitiveLandscape: '竞争格局分析',
        trends: '发展趋势预测',
        technology: '技术分析',
        technologicalInnovation: '技术创新影响',
        challenges: '面临挑战',
        opportunities: '发展机遇',
        forecast: '未来预测',
        conclusions: '结论与建议',
        policy: '政策环境',
        investment: '投资分析',
        swot: 'SWOT分析'
    };
    
    return titleMap[key] || key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()) || '分析章节';
}

// 格式化结论内容
function formatConclusionsContent(conclusions) {
    if (!conclusions) return '结论与建议分析';
    
    let content = '';
    if (conclusions.conclusions) {
        content += `**结论：**\n${conclusions.conclusions}\n\n`;
    }
    if (conclusions.recommendations) {
        content += `**建议：**\n${conclusions.recommendations}`;
    }
    return content || '结论与建议分析';
}

// 从字段中提取内容
function extractContentFromField(field) {
    if (typeof field === 'string') {
        return field;
    } else if (Array.isArray(field)) {
        return field.map((item, index) => `${index + 1}. ${item}`).join('\n\n');
    } else if (typeof field === 'object' && field.description) {
        return field.description;
    } else if (typeof field === 'object') {
        return JSON.stringify(field, null, 2);
    }
    return '分析内容';
}

// 从章节生成目录 - 增强版本，确保目录正确显示
function generateTableOfContents(sections) {
    console.log('开始生成目录，sections:', Object.keys(sections));
    
    const toc = [];
    let pageNum = 1;
    
    Object.keys(sections).forEach(key => {
        const section = sections[key];
        const title = section.title || generateSectionTitle(key) || '未命名章节';
        
        const tocItem = {
            section: key,
            title: title,
            page: pageNum++
        };
        
        toc.push(tocItem);
        console.log('添加目录项:', tocItem);
    });
    
    console.log('生成目录完成，共', toc.length, '项');
    return toc;
}

// 从章节提取图表配置 - 完全灵活的图表处理
function extractChartsFromSections(sections) {
    const charts = [];
    let chartId = 1;
    
    Object.keys(sections).forEach(sectionKey => {
        const section = sections[sectionKey];
        
        // 检查是否有图表配置
        if (section.chart_config) {
            const chartConfig = section.chart_config;
            const chart = {
                id: `chart_${chartId++}`,
                section: sectionKey,
                type: chartConfig.type || 'line',
                title: chartConfig.title || section.title + '图表',
                // 灵活处理数据格式
                data: normalizeChartData(chartConfig)
            };
            charts.push(chart);
        }
        
        // 检查子章节
        if (section.subsections) {
            Object.keys(section.subsections).forEach(subKey => {
                const subsection = section.subsections[subKey];
                if (subsection.chart_config) {
                    const chartConfig = subsection.chart_config;
                    const chart = {
                        id: `chart_${chartId++}`,
                        section: sectionKey,
                        subsection: subKey,
                        type: chartConfig.type || 'line',
                        title: chartConfig.title || subsection.title + '图表',
                        data: normalizeChartData(chartConfig)
                    };
                    charts.push(chart);
                }
            });
        }
    });
    
    // 如果没有图表，生成默认图表
    if (charts.length === 0) {
        charts.push(...getDefaultCharts());
    }
    
    return charts;
}

// 标准化图表数据 - 处理各种可能的数据格式
function normalizeChartData(chartConfig) {
    const normalized = {
        labels: [],
        values: [],
        unit: ''
    };
    
    // 处理temp.json格式: chart.data.years, chart.data.values
    if (chartConfig.data) {
        const data = chartConfig.data;
        if (data.years && data.values) {
            normalized.labels = data.years;
            normalized.values = data.values;
            normalized.unit = data.unit || '';
        } else if (data.labels && data.values) {
            normalized.labels = data.labels;
            normalized.values = data.values;
            normalized.unit = data.unit || '';
        } else if (Array.isArray(data.labels) && Array.isArray(data.values)) {
            normalized.labels = data.labels;
            normalized.values = data.values;
        }
        
        // 处理特殊格式：data["2020"], data["2025"]
        if (data["2020"] && data["2025"]) {
            normalized.labels = data.labels || [];
            normalized.values = data["2020"]; // 或者合并多年数据
        }
    }
    
    // 处理config格式: config.xAxis, config.yAxis
    else if (chartConfig.config) {
        const config = chartConfig.config;
        normalized.labels = config.xAxis || config.labels || [];
        normalized.values = config.yAxis || config.values || [];
        normalized.unit = config.unit || '';
    }
    
    // 直接在chartConfig层级
    else {
        normalized.labels = chartConfig.labels || chartConfig.xAxis || [];
        normalized.values = chartConfig.values || chartConfig.yAxis || [];
        normalized.unit = chartConfig.unit || '';
    }
    
    return normalized;
}

// 获取默认图表
function getDefaultCharts() {
    return [
        {
            id: 'default_chart_1',
            type: 'line',
            title: '市场发展趋势',
            section: 'market_overview',
            data: {
                labels: ['2022', '2023', '2024', '2025E'],
                values: [1200, 1450, 1680, 1950],
                unit: '亿元'
            }
        },
        {
            id: 'default_chart_2',
            type: 'bar',
            title: '年增长率',
            section: 'market_overview',
            data: {
                labels: ['2022', '2023', '2024', '2025E'],
                values: [18, 21, 16, 16],
                unit: '%'
            }
        }
    ];
}

// 获取默认报告结构
function getDefaultReportStructure() {
    return {
        report_meta: {
            title: `${setData.industry}行业分析报告`,
            subtitle: '基于AI智能分析',
            industry: setData.industry,
            region: setData.region,
            date: new Date().toISOString().split('T')[0],
            author: 'AI行业分析师'
        },
        table_of_contents: [
            {section: 'executive_summary', title: '执行摘要', page: 1},
            {section: 'market_overview', title: '市场概述', page: 2},
            {section: 'competitive_analysis', title: '竞争分析', page: 3},
            {section: 'future_forecast', title: '未来预测', page: 4}
        ],
        sections: {
            executive_summary: {
                title: '执行摘要',
                content: `本报告对${setData.industry}行业进行了全面分析，基于最新的市场数据和AI智能分析，为您提供专业的行业洞察和发展趋势预测。`
            },
            market_overview: {
                title: '市场概述',
                content: `${setData.industry}行业在${setData.region}地区呈现出稳定增长的态势，市场潜力巨大，发展前景广阔。`
            },
            competitive_analysis: {
                title: '竞争分析',
                content: `行业竞争格局日趋激烈，头部企业优势明显，新兴企业通过技术创新寻求突破。`
            },
            future_forecast: {
                title: '未来预测',
                content: `预计未来3-5年，${setData.industry}行业将继续保持增长态势，数字化转型将成为重要驱动力。`
            }
        },
        charts: getDefaultCharts(),
        data_sources: ['Tavily搜索引擎', 'AI智能分析']
    };
}

// 生成专业HTML报告
function generateProfessionalReport(reportData) {
    const meta = reportData.report_meta;
    const sections = reportData.sections;
    const charts = reportData.charts;
    const toc = reportData.table_of_contents;
    const sources = reportData.data_sources;
    
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${meta.title}</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .chart-container { width: 100%; height: 400px; margin: 20px 0; }
        .section { margin-bottom: 3rem; }
        .subsection { margin: 2rem 0; padding-left: 1rem; border-left: 3px solid #e5e7eb; }
        body { font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; line-height: 1.6; }
        .fade-in { animation: fadeIn 0.8s ease-in; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
        .toc-item { transition: all 0.3s ease; }
        .toc-item:hover { background-color: #f3f4f6; transform: translateX(5px); }
        .section-header { border-bottom: 2px solid #ddd; padding-bottom: 0.5rem; margin-bottom: 1.5rem; }
        .data-highlight { background: linear-gradient(45deg, #f0f9ff, #e0f2fe); padding: 1rem; border-radius: 8px; border-left: 4px solid #0ea5e9; }
        .chart-wrapper { background: white; border-radius: 12px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); padding: 1.5rem; margin: 2rem 0; }
    </style>
</head>
<body class="bg-gray-50">
    <div class="max-w-7xl mx-auto bg-white shadow-2xl">
        <!-- 报告封面 -->
        <header class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-8 text-center">
            <h1 class="text-4xl font-bold mb-2">${meta.title || '行业分析报告'}</h1>
            <p class="text-xl opacity-90">${meta.subtitle || '专业行业分析报告'}</p>
            <div class="mt-4 text-sm opacity-80">
                <span>行业：${meta.industry || '未指定'}</span> | 
                <span>地区：${meta.region || '全球'}</span> | 
                <span>日期：${meta.date || new Date().toISOString().split('T')[0]}</span> | 
                <span>分析师：${meta.author || 'AI分析师'}</span>
            </div>
        </header>

        <div class="flex">
            <!-- 侧边目录 -->
            <nav class="w-1/4 bg-gray-100 p-6 sticky top-0 h-screen overflow-y-auto">
                <h2 class="text-lg font-semibold mb-4 text-gray-800">报告目录</h2>
                <ul class="space-y-2">
                    ${toc.map(item => `
                        <li class="toc-item p-2 rounded cursor-pointer" onclick="scrollToSection('${item.section}')">
                            <span class="text-sm font-medium text-gray-700">${item.title || '未命名章节'}</span>
                            <span class="text-xs text-gray-500 float-right">P${item.page || 1}</span>
                        </li>
                    `).join('')}
                </ul>
            </nav>

            <!-- 主要内容 -->
            <main class="w-3/4 p-8">
                ${Object.keys(sections).map(sectionKey => {
                    const section = sections[sectionKey];
                    const sectionCharts = charts.filter(chart => chart.section === sectionKey);
                    
                    return `
                        <section id="${sectionKey}" class="section fade-in">
                            <div class="section-header">
                                <h2 class="text-3xl font-bold text-gray-800">${section.title || '分析章节'}</h2>
                            </div>
                            
                            <div class="content-area">
                                ${formatContent(section.content)}
                            </div>
                            
                            ${section.subsections ? Object.keys(section.subsections).map(subKey => {
                                const subsection = section.subsections[subKey];
                                return `
                                    <div class="subsection">
                                        <h3 class="text-xl font-semibold text-gray-700 mb-3">${subsection.title || '子章节'}</h3>
                                        <div class="content-area">
                                            ${formatContent(subsection.content)}
                                        </div>
                                        ${subsection.data_points ? `
                                            <div class="data-highlight mt-4">
                                                <h4 class="font-semibold mb-2">关键数据点：</h4>
                                                <ul class="list-disc list-inside space-y-1">
                                                    ${subsection.data_points.map(point => `<li>${point}</li>`).join('')}
                                                </ul>
                                            </div>
                                        ` : ''}
                                    </div>
                                `;
                            }).join('') : ''}
                            
                            ${section.key_points ? `
                                <div class="data-highlight mt-4">
                                    <h4 class="font-semibold mb-2">核心要点：</h4>
                                    <ul class="list-disc list-inside space-y-1">
                                        ${section.key_points.map(point => `<li>${point}</li>`).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                            
                            ${sectionCharts.map(chart => `
                                <div class="chart-wrapper">
                                    <h4 class="text-lg font-semibold mb-4 text-center">${chart.title || '数据图表'}</h4>
                                    <div id="${chart.id}" class="chart-container"></div>
                                </div>
                            `).join('')}
                        </section>
                    `;
                }).join('')}

                <!-- 数据来源 -->
                <section class="section fade-in bg-gray-50 p-6 rounded-lg">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">数据来源与说明</h2>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div>
                            <h3 class="font-semibold mb-2">主要数据源：</h3>
                            <ul class="list-disc list-inside space-y-1 text-sm text-gray-600">
                                ${sources.map(source => `<li>${source}</li>`).join('')}
                            </ul>
                        </div>
                        <div>
                            <h3 class="font-semibold mb-2">报告说明：</h3>
                            <p class="text-sm text-gray-600">
                                本报告基于AI智能分析生成，数据来源于公开渠道，
                                仅供参考，不构成投资建议。报告内容会根据最新数据持续更新。
                            </p>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <script>
        // 图表渲染 - 完全灵活的图表处理
        document.addEventListener('DOMContentLoaded', function() {
            const chartsConfig = ${JSON.stringify(charts)};
            
            chartsConfig.forEach(chartConfig => {
                try {
                    const chartDom = document.getElementById(chartConfig.id);
                    if (!chartDom) {
                        console.warn('图表容器未找到:', chartConfig.id);
                        return;
                    }
                    
                    const myChart = echarts.init(chartDom);
                    const chartData = chartConfig.data || {};
                    
                    let option = {};
                    
                    if (chartConfig.type === 'line') {
                        option = {
                            title: {
                                text: chartConfig.title,
                                left: 'center',
                                textStyle: { fontSize: 16 }
                            },
                            tooltip: {
                                trigger: 'axis',
                                formatter: function(params) {
                                    return params[0].name + ': ' + params[0].value + ' ' + (chartData.unit || '');
                                }
                            },
                            xAxis: {
                                type: 'category',
                                data: chartData.labels || []
                            },
                            yAxis: {
                                type: 'value',
                                name: chartData.unit || ''
                            },
                            series: [{
                                data: chartData.values || [],
                                type: 'line',
                                smooth: true,
                                lineStyle: { 
                                    color: '#1f77b4', 
                                    width: 3 
                                },
                                itemStyle: { 
                                    color: '#1f77b4' 
                                },
                                areaStyle: {
                                    color: {
                                        type: 'linear',
                                        x: 0, y: 0, x2: 0, y2: 1,
                                        colorStops: [
                                            { offset: 0, color: 'rgba(31, 119, 180, 0.3)' },
                                            { offset: 1, color: 'rgba(31, 119, 180, 0.1)' }
                                        ]
                                    }
                                }
                            }]
                        };
                    } else if (chartConfig.type === 'bar') {
                        option = {
                            title: {
                                text: chartConfig.title,
                                left: 'center',
                                textStyle: { fontSize: 16 }
                            },
                            tooltip: {
                                trigger: 'axis',
                                formatter: function(params) {
                                    return params[0].name + ': ' + params[0].value + (chartData.unit || '');
                                }
                            },
                            xAxis: {
                                type: 'category',
                                data: chartData.labels || []
                            },
                            yAxis: {
                                type: 'value',
                                name: chartData.unit || ''
                            },
                            series: [{
                                data: chartData.values || [],
                                type: 'bar',
                                itemStyle: {
                                    color: {
                                        type: 'linear',
                                        x: 0, y: 0, x2: 0, y2: 1,
                                        colorStops: [
                                            { offset: 0, color: '#ff7f0e' },
                                            { offset: 1, color: 'rgba(255, 127, 14, 0.8)' }
                                        ]
                                    }
                                }
                            }]
                        };
                    } else if (chartConfig.type === 'pie') {
                        const pieData = (chartData.values || []).map((value, index) => ({
                            value: value,
                            name: (chartData.labels || [])[index] || \`类别\${index + 1}\`
                        }));
                        
                        option = {
                            title: {
                                text: chartConfig.title,
                                left: 'center',
                                textStyle: { fontSize: 16 }
                            },
                            tooltip: {
                                trigger: 'item',
                                formatter: '{a} <br/>{b}: {c} ({d}%)'
                            },
                            series: [{
                                name: chartConfig.title,
                                type: 'pie',
                                radius: '50%',
                                data: pieData,
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }]
                        };
                    }
                    
                    if (option && Object.keys(option).length > 0) {
                        myChart.setOption(option);
                        console.log('图表渲染成功:', chartConfig.id);
                    } else {
                        console.warn('图表配置为空:', chartConfig.id);
                    }
                    
                    // 响应式
                    window.addEventListener('resize', function() {
                        myChart.resize();
                    });
                    
                } catch (error) {
                    console.error('图表渲染失败:', chartConfig.id, error);
                }
            });
        });
        
        // 目录导航
        function scrollToSection(sectionId) {
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }
    </script>
</body>
</html>`;
}

// 格式化内容
function formatContent(content) {
    if (!content) return '<p class="text-gray-600">暂无相关内容</p>';
    
    // 处理换行和段落
    const paragraphs = content.split(/\n\s*\n/);
    return paragraphs.map(para => {
        // 检查是否是列表
        if (para.includes('\n') && /^\d+\.|^-|^\*/.test(para.trim())) {
            const items = para.split('\n').filter(item => item.trim());
            return `<ul class="list-disc list-inside space-y-2 my-4">${items.map(item => 
                `<li class="text-gray-700">${item.replace(/^\d+\.\s*|^[-*]\s*/, '')}</li>`
            ).join('')}</ul>`;
        } else {
            return `<p class="text-gray-700 mb-4">${para.trim()}</p>`;
        }
    }).join('');
}

// 主执行逻辑
try {
    console.log('开始处理报告数据...');
    
    // 解析AI响应
    const reportData = parseAIResponse(aiData);
    console.log('报告数据解析完成:', Object.keys(reportData));
    console.log('章节数量:', Object.keys(reportData.sections).length);
    console.log('图表数量:', reportData.charts.length);
    
    // 确保目录数据存在
    if (!reportData.table_of_contents || reportData.table_of_contents.length === 0) {
        console.log('目录数据缺失，重新生成...');
        reportData.table_of_contents = generateTableOfContents(reportData.sections);
    }
    console.log('目录数据:', reportData.table_of_contents);
    
    // 生成HTML报告
    const htmlReport = generateProfessionalReport(reportData);
    console.log('HTML报告生成完成，长度:', htmlReport.length);
    
    // 返回结果
    return {
        html: htmlReport,
        filename: `${setData.industry}_专业分析报告_${new Date().toISOString().split('T')[0]}.html`,
        report_data: reportData,
        metadata: {
            industry: setData.industry,
            region: setData.region,
            generated_at: new Date().toISOString(),
            workflow_version: "universal_v4.0_flexible",
            sections_count: Object.keys(reportData.sections).length,
            charts_count: reportData.charts.length,
            toc_count: reportData.table_of_contents.length
        },
        success: true,
        message: "专业报告生成成功"
    };
    
} catch (error) {
    console.error('报告生成过程中发生错误:', error);
    
    // 错误情况下返回默认报告
    const defaultData = getDefaultReportStructure();
    const errorReport = generateProfessionalReport(defaultData);
    
    return {
        html: errorReport,
        filename: `${setData.industry}_报告_${new Date().toISOString().split('T')[0]}_fallback.html`,
        report_data: defaultData,
        metadata: {
            industry: setData.industry,
            region: setData.region,
            generated_at: new Date().toISOString(),
            workflow_version: "universal_v4.0_flexible",
            error_fallback: true
        },
        success: false,
        error: error.message,
        message: "使用默认模板生成报告"
    };
} 