**Method**

Fixed

Expression

[ get]

**URL**

Fixed

Expression

[ https://api.302.ai/searchapi/search]

**Authentication**

[ ]

**Send Query Parameters**

[ true]

**Specify Query Parameters**

Fixed

Expression

[ ]

**Query Parameters**

**Name**

Fixed

Expression

[ q]

**Value**

Fixed

Expression

**{{ $json.searchVariants }}**

人工智能 市场规模

**Name**

Fixed

Expression

**region**

region

**Value**

Fixed

Expression

**{{ $('Set').item.json.region }}**

中国

**Name**

Fixed

Expression

[ timeRange]

**Value**

Fixed

Expression

**{{ $('Set').item.json.timestamp }}**

2025-06-18T03:46:56.869Z

**Name**

Fixed

Expression

[ num]

**Value**

Fixed

Expression

[ 10]

**Name**

Fixed

Expression

[ engine]

**Value**

Fixed

Expression

[google ]

**Name**

Fixed

Expression

[ ]

**Value**

Fixed

Expression

[ ]

Add Parameter

**Send Headers**

[true ]

**Specify Headers**

Fixed

Expression

[ accept]

**Header Parameters**

**Name**

Fixed

Expression

[ application/json]

**Value**

Fixed

Expression

[ ]

Add Parameter

**Send Body**

[ ]

**Options**

**Response**

**Include Response Headers and Status**

Fixed

Expression

[ ]

**Never Error**

Fixed

Expression

[ true]

**Response Format**

[ json]
