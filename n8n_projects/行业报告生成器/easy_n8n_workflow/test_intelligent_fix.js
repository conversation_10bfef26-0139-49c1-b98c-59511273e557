// 测试智能修复版报告生成器
const fs = require('fs');

// 模拟不同行业的测试
const testIndustries = ['人工智能', '新能源', '电商', '金融科技', '医疗健康'];

function testIntelligentReportGeneration() {
    console.log('=== 测试智能修复版报告生成器 ===\n');
    
    testIndustries.forEach((industry, index) => {
        console.log(`🔍 测试行业 ${index + 1}: ${industry}`);
        
        // 模拟AI数据（包含重复标题问题）
        const mockAiData = {
            output: `${industry}行业分析报告2025行业在当前经济环境下展现出强劲的发展势头。中国${industry}行业分析报告2025行业正处于快速发展期，市场规模持续扩大。`
        };
        
        // 测试内容清理功能
        const cleanedContent = cleanContent(mockAiData.output, industry);
        
        console.log('原始内容:', mockAiData.output);
        console.log('清理后:', cleanedContent);
        
        // 测试智能图表生成
        const charts = generateIntelligentCharts(industry, {});
        console.log('生成图表:');
        charts.forEach(chart => {
            console.log(`  - ${chart.title} (${chart.type}图) -> ${chart.section}章节`);
        });
        
        console.log('---\n');
    });
    
    console.log('✅ 修复验证完成！');
    console.log('\n🔧 主要修复内容:');
    console.log('1. ✅ 去除重复的行业标题');
    console.log('2. ✅ 智能生成行业特定图表');
    console.log('3. ✅ 图表与章节内容关联');
    console.log('4. ✅ 内容清理和格式优化');
}

// 内容清理函数（从主文件复制）
function cleanContent(content, industry) {
    if (!content) return '';
    
    const patterns = [
        new RegExp(`${industry}行业分析报告\\d{4}行业`, 'g'),
        new RegExp(`${industry}行业分析报告\\d{4}`, 'g'),
        new RegExp(`中国${industry}行业分析报告\\d{4}行业`, 'g'),
        new RegExp(`中国${industry}行业分析报告\\d{4}`, 'g'),
        /行业分析报告\d{4}行业/g,
        /分析报告\d{4}行业/g
    ];
    
    let cleanedContent = content;
    patterns.forEach(pattern => {
        cleanedContent = cleanedContent.replace(pattern, `${industry}行业`);
    });
    
    cleanedContent = cleanedContent.replace(/行业行业/g, '行业');
    cleanedContent = cleanedContent.replace(/市场市场/g, '市场');
    cleanedContent = cleanedContent.replace(/分析分析/g, '分析');
    
    return cleanedContent.trim();
}

// 智能图表生成函数（从主文件复制）
function generateIntelligentCharts(industry, sections) {
    const industryChartMap = {
        '人工智能': [
            { type: 'line', title: 'AI技术发展指数', section: 'technology_innovation' },
            { type: 'bar', title: '投资规模分布', section: 'market_analysis' }
        ],
        '新能源': [
            { type: 'line', title: '新能源装机容量趋势', section: 'market_analysis' },
            { type: 'pie', title: '能源结构占比', section: 'industry_trends' }
        ],
        '电商': [
            { type: 'line', title: '电商交易规模增长', section: 'market_analysis' },
            { type: 'bar', title: '平台用户分布', section: 'competitive_landscape' }
        ],
        '金融科技': [
            { type: 'line', title: '金融科技投资趋势', section: 'market_analysis' },
            { type: 'pie', title: '应用场景分布', section: 'technology_innovation' }
        ],
        '医疗健康': [
            { type: 'line', title: '医疗健康市场规模', section: 'market_analysis' },
            { type: 'bar', title: '细分领域投资', section: 'competitive_landscape' }
        ]
    };
    
    const industryCharts = industryChartMap[industry] || [
        { type: 'line', title: `${industry}发展趋势`, section: 'market_analysis' },
        { type: 'pie', title: '市场结构分析', section: 'competitive_landscape' }
    ];
    
    return industryCharts.map((chartConfig, index) => ({
        id: `chart_${index + 1}`,
        type: chartConfig.type,
        title: chartConfig.title,
        section: chartConfig.section,
        data: {
            labels: chartConfig.type === 'pie' ? 
                ['头部企业', '中等企业', '小型企业', '新兴企业'] :
                ['2020', '2021', '2022', '2023', '2024', '2025E'],
            values: chartConfig.type === 'pie' ? 
                [35, 28, 22, 15] :
                [100, 118, 135, 158, 185, 220]
        }
    }));
}

// 运行测试
testIntelligentReportGeneration(); 