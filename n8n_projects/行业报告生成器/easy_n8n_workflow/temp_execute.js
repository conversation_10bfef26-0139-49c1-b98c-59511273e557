
// 模拟n8n环境
const setData = {"industry":"服装","region":"广州","keywords":["女装","时尚","淑女装"]};
const $ = (name) => ({ item: { json: setData } });

// 主文件的函数
报告生成器
// 基于Sample Reports深度分析，完全内容驱动，不固化任何模板内容
// 智能识别行业特色，自动适配配色、图表、布局

const setData = $('Set').item.json;
const searchData = $('TavilySearch').item.json;
const aiData = $('OpenAI').item.json;

console.log('=== 新一代灵活自适应专业报告生成器启动 ===');

// 行业特色配置系统 - 根据行业自动适配
const INDUSTRY_THEMES = {
  '人工智能': {
    colors: { primary: '#1e40af', secondary: '#3b82f6', accent: '#06b6d4' },
    charts: ['line', 'radar', 'scatter', 'sankey'],
    keywords: ['技术', '算法', '模型', '数据', '智能', '自动化', 'AI', '机器学习']
  },
  '新能源': {
    colors: { primary: '#059669', secondary: '#10b981', accent: '#34d399' },
    charts: ['line', 'bar', 'pie', 'area'],
    keywords: ['能源', '电池', '充电', '绿色', '环保', '可持续', '光伏', '风能']
  },
  '电商': {
    colors: { primary: '#dc2626', secondary: '#ef4444', accent: '#f97316' },
    charts: ['pie', 'bar', 'line', 'funnel'],
    keywords: ['平台', '用户', '交易', '流量', '转化', '营销', '电商', '零售']
  },
  '金融': {
    colors: { primary: '#1e3a8a', secondary: '#3b82f6', accent: '#6366f1' },
    charts: ['line', 'bar', 'pie', 'candlestick'],
    keywords: ['资金', '投资', '风险', '收益', '资产', '市场', '银行', '保险']
  },
  '医疗': {
    colors: { primary: '#be185d', secondary: '#ec4899', accent: '#f472b6' },
    charts: ['bar', 'line', 'pie', 'radar'],
    keywords: ['健康', '治疗', '药物', '医院', '患者', '诊断', '医疗', '生物']
  },
  '制造': {
    colors: { primary: '#7c2d12', secondary: '#ea580c', accent: '#fb923c' },
    charts: ['bar', 'line', 'gantt', 'sankey'],
    keywords: ['生产', '制造', '工艺', '质量', '效率', '自动化', '工业', '智造']
  },
  '物流': {
    colors: { primary: '#374151', secondary: '#6b7280', accent: '#9ca3af' },
    charts: ['line', 'bar', 'map', 'network'],
    keywords: ['运输', '仓储', '配送', '供应链', '物流', '效率', '快递', '货运']
  },
  '咨询': {
    colors: { primary: '#4338ca', secondary: '#6366f1', accent: '#8b5cf6' },
    charts: ['radar', 'bar', 'line', 'matrix'],
    keywords: ['咨询', '服务', '管理', '战略', '分析', '建议', '专业', '顾问']
  },
  'default': {
    colors: { primary: '#1e40af', secondary: '#3b82f6', accent: '#06b6d4' },
    charts: ['line', 'bar', 'pie', 'radar'],
    keywords: ['市场', '发展', '趋势', '分析', '增长', '竞争']
  }
};

// 智能行业识别器


// 执行测试
const aiData = {"output":"广州女淑装2025年行业趋势分析报告\n\n广州女淑装市场预计到2025年将达到150亿元人民币规模，年复合增长率维持在8%左右。\n\n到2025年，广州女淑装行业将呈现三大发展趋势：一是可持续时尚将成为主流，预计使用环保面料的产品占比将从现在的20%提升至40%；二是智能定制服务兴起，3D量体、AI推荐等技术应用将使个性化定制成本降低30%；三是线上线下深度融合，AR虚拟试衣、门店数字化改造将提升30%的转化率。\n\n本报告基于以下数据源进行综合分析： **官方统计数据**：国家统计局、工信部、商务部等政府机构发布的行业统计数据和政策文件。 **行业协会资料**：相关行业协会发布的市场研究报告、行业白皮书和发展规划。 **企业公开信息**：上市公司年报、财务报告、投资者关系资料和企业官方发布信息。 **第三方研究**：知名咨询机构、研究院所发布的专业研究报告和市场分析。 **实地调研**：通过专家访谈、企业调研、用户调查等方式获取的一手资料。 数据统计截止时间：2025年6月"};
console.log('🚀 开始处理数据...');

try {
    const result = generateAdaptiveHTML(aiData, [], '服装');
    
    // 保存结果
    const fs = require('fs');
    const filename = 'final_verification_report.html';
    fs.writeFileSync(filename, result);
    console.log('💾 最终验证报告已保存:', filename);
    
    // 验证数据源部分
    console.log('🔍 数据源验证:');
    console.log('✅ 包含数据源样式:', result.includes('data-sources-professional'));
    console.log('✅ 包含新CSS:', result.includes('source-description-text'));
    console.log('✅ 包含数据源内容:', result.includes('官方统计数据'));
    console.log('✅ 没有旧的空白结构:', !result.includes('<div class="source-list">'));
    console.log('✅ 连续文本格式:', result.includes('本报告基于以下数据源进行综合分析：<strong>官方统计数据</strong>'));
    
    // 检查是否有空的div
    const emptyDivs = (result.match(/<div[^>]*>\s*<\/div>/g) || []).length;
    console.log('🔍 空div检查:', emptyDivs === 0 ? '✅ 无空div' : '⚠️ 发现空div: ' + emptyDivs);
    
    console.log('🎉 最终验证完成！');
    
} catch (error) {
    console.error('❌ 验证失败:', error.message);
}
