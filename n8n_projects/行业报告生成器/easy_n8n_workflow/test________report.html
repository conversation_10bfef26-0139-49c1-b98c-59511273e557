
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>执行摘要</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        
        :root {
            --primary-color: #059669;
            --secondary-color: #10b981;
            --accent-color: #34d399;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --background-primary: #ffffff;
            --background-secondary: #f9fafb;
            --border-color: #e5e7eb;
        }
    
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: var(--background-secondary);
        }
        
        .report-container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--background-primary);
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
            min-height: 100vh;
        }
        
        /* 报告头部 */
        .report-header {
            background: linear-gradient(135deg, #059669 0%, #10b981 50%, #34d399 100%);
            color: white;
            padding: 80px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
            border-bottom: 5px solid #059669;
        }
        
        .report-title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 25px;
            text-shadow: 0 3px 6px rgba(0,0,0,0.2);
            letter-spacing: 1px;
            line-height: 1.2;
        }
        
        .report-subtitle {
            font-size: 22px;
            opacity: 0.95;
            margin-bottom: 35px;
            font-weight: 500;
            border-bottom: 1px solid rgba(255,255,255,0.3);
            padding-bottom: 15px;
            display: inline-block;
        }
        
        .report-meta {
            font-size: 16px;
            opacity: 0.8;
            border-top: 1px solid rgba(255,255,255,0.2);
            padding-top: 20px;
        }
        
        /* 目录样式 */
        .table-of-contents {
            padding: 60px 80px;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border-bottom: 3px solid #e2e8f0;
        }
        
        .toc-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 40px;
            text-align: center;
        }
        
        .toc-list {
            list-style: none;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .toc-item {
            border-bottom: 1px dotted #ddd;
            transition: all 0.3s ease;
        }
        
        .toc-item:hover {
            background: rgba(30, 64, 175, 0.05);
        }
        
        .toc-link {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 18px 20px;
            text-decoration: none;
            color: var(--text-primary);
        }
        
        .toc-left {
            display: flex;
            align-items: center;
        }
        
        .toc-number {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-color);
            margin-right: 15px;
            min-width: 30px;
        }
        
        .toc-title-text {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .toc-dots {
            flex: 1;
            border-bottom: 1px dotted #ccc;
            margin: 0 15px;
            height: 1px;
        }
        
        .toc-page {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        /* 主要内容区域 */
        .main-content {
            padding: 50px 40px;
        }
        
        .report-section {
            margin-bottom: 60px;
            scroll-margin-top: 100px;
        }
        
        .section-header {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            border-bottom: 3px solid var(--primary-color);
            padding-bottom: 15px;
        }
        
        .section-number {
            background: var(--primary-color);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            font-weight: 600;
            margin-right: 20px;
        }
        
        .content-grid {
            display: grid;
            gap: 40px;
            align-items: start;
        }
        
        .content-grid.single-column {
            grid-template-columns: 1fr;
        }
        
        .content-grid.layout-left-right {
            grid-template-columns: 1fr 1fr;
        }
        
        .content-grid.layout-top-bottom {
            grid-template-columns: 1fr;
        }
        
        .section-content {
            font-size: 16px;
            line-height: 1.8;
            color: var(--text-primary);
            text-align: justify;
            white-space: pre-line;
        }
        
        .chart-container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e5e7eb;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 2px solid var(--accent-color);
            padding-bottom: 10px;
        }
        
        .chart-canvas {
            width: 100%;
            height: 400px;
        }
        
        .chart-note {
            font-size: 12px;
            color: var(--text-secondary);
            text-align: center;
            margin-top: 15px;
        }
        
        .data-source {
            font-size: 12px;
            color: var(--text-secondary);
            text-align: center;
            margin-top: 5px;
            font-style: italic;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .report-header {
                padding: 40px 20px;
            }
            
            .report-title {
                font-size: 28px;
            }
            
            .table-of-contents {
                padding: 40px 20px;
            }
            
            .main-content {
                padding: 30px 20px;
            }
            
            .content-grid.layout-left-right {
                grid-template-columns: 1fr;
            }
            
            .section-title {
                font-size: 24px;
            }
            
            .chart-canvas {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <header class="report-header">
            <h1 class="report-title">执行摘要</h1>
            <p class="report-subtitle">专业深度分析报告</p>
            <div class="report-meta">
                生成时间：2025/6/19 | 行业类型：新能源
            </div>
        </header>
        
        <nav class="table-of-contents">
            <h2 class="toc-title">目录</h2>
            <ul class="toc-list">
                
        <li class="toc-item">
            <a href="#新能源汽车行业深度分析报告2025" class="toc-link">
                <div class="toc-left">
                    <span class="toc-number">1</span>
                    <span class="toc-title-text">新能源汽车行业深度分析报告2025</span>
                </div>
                <div class="toc-dots"></div>
                <span class="toc-page">1</span>
            </a>
        </li>
            </ul>
        </nav>
        
        <main class="main-content">
            
      <section id="新能源汽车行业深度分析报告2025" class="report-section">
        <div class="section-header">
          <h2 class="section-title">
            <span class="section-number">1</span>
            新能源汽车行业深度分析报告2025
          </h2>
        </div>
        
        <div class="section-body">
          <div class="content-grid layout-top-bottom">
            <div class="content-text">
              <p class="section-content">执行摘要
        新能源汽车市场持续快速增长，预计2025年销量将突破1000万辆。

市场分析
        中国新能源汽车市场从2020年的136万辆增长至2024年的950万辆。

竞争格局
        比亚迪、特斯拉、蔚来等品牌形成多元化竞争格局。

发展趋势
        智能化、网联化、共享化成为发展主流趋势。

挑战与机遇
        充电基础设施建设、电池技术突破、政策支持力度等是关键因素。

未来展望
        预计未来5年新能源汽车渗透率将超过50%。

数据来源
        基于工信部、中汽协、企业财报等权威数据整理分析。</p>
            </div>
            
            
            <div class="content-chart">
              <div class="chart-container">
                <h3 class="chart-title">新能源汽车行业深度分析报告2025分析图表</h3>
                <div id="chart_1" class="chart-canvas"></div>
                <p class="chart-note">注：基于公开数据整理分析</p>
                <p class="data-source">数据来源：国家统计局、行业协会、公开资料整理</p>
              </div>
            </div>
            
          </div>
        </div>
      </section>
        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const charts = [{"id":"chart_1","title":"新能源汽车行业深度分析报告2025分析图表","type":"line","option":{"color":["#059669","#10b981","#34d399","#94a3b8","#cbd5e1"],"tooltip":{"trigger":"axis"},"xAxis":{"type":"category","data":["2021年","2022年","2023年","2024年","2025年"]},"yAxis":{"type":"value"},"series":[{"data":[150,188,237,296,369],"type":"line","smooth":true,"lineStyle":{"width":3}}]}}];
            
            charts.forEach(chart => {
                const chartDom = document.getElementById(chart.id);
                if (!chartDom) return;
                
                const myChart = echarts.init(chartDom);
                const option = chart.option;
                
                myChart.setOption(option);
                
                // 响应式调整
                window.addEventListener('resize', () => {
                    myChart.resize();
                });
            });
        });
    </script>
</body>
</html>