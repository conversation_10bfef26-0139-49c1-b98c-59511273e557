const fs = require('fs');

// 模拟n8n数据结构
const testData = {
    data: `广州女淑装2025年行业趋势分析报告

广州女淑装市场预计到2025年将达到150亿元人民币规模，年复合增长率维持在8%左右。

到2025年，广州女淑装行业将呈现三大发展趋势：一是可持续时尚将成为主流，预计使用环保面料的产品占比将从现在的20%提升至40%；二是智能定制服务兴起，3D量体、AI推荐等技术应用将使个性化定制成本降低30%；三是线上线下深度融合，AR虚拟试衣、门店数字化改造将提升30%的转化率。

本报告基于以下数据源进行综合分析： **官方统计数据**：国家统计局、工信部、商务部等政府机构发布的行业统计数据和政策文件。 **行业协会资料**：相关行业协会发布的市场研究报告、行业白皮书和发展规划。 **企业公开信息**：上市公司年报、财务报告、投资者关系资料和企业官方发布信息。 **第三方研究**：知名咨询机构、研究院所发布的专业研究报告和市场分析。 **实地调研**：通过专家访谈、企业调研、用户调查等方式获取的一手资料。 数据统计截止时间：2025年6月`
};

console.log('🧪 测试广州女淑装行业报告生成');
console.log('='.repeat(50));

// 导入生成器
try {
    // 由于n8n环境限制，我们需要手动模拟处理逻辑
    console.log('📄 原始数据:');
    console.log(testData.data);
    console.log('\n🔍 检查数据源格式问题...');
    
    // 检查是否存在markdown格式问题
    const hasMarkdownIssues = testData.data.includes('**') || testData.data.includes('：');
    console.log(`❌ 发现markdown格式问题: ${hasMarkdownIssues}`);
    
    // 检查章节结构
    const sections = testData.data.split('\n\n').filter(s => s.trim());
    console.log(`📊 章节数量: ${sections.length}`);
    
    sections.forEach((section, index) => {
        console.log(`第${index + 1}章节: ${section.substring(0, 50)}...`);
        if (section.includes('**')) {
            console.log(`⚠️  第${index + 1}章节包含markdown格式`);
        }
    });
    
} catch (error) {
    console.error('❌ 测试失败:', error.message);
}

console.log('\n🎯 需要修复的问题:');
console.log('1. 数据源章节的markdown格式需要转换为HTML格式');
console.log('2. 章节结构需要优化，确保美观的排版');
console.log('3. 需要仿照车海洋报告的格式风格'); 