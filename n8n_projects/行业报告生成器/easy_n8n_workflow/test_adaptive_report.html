
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国人工智能行业深度分析报告2025</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        :root {
            --primary-color: #1e40af;
            --secondary-color: #3b82f6;
            --accent-color: #06b6d4;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --background-primary: #ffffff;
            --background-secondary: #f9fafb;
            --border-color: #e5e7eb;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background: var(--background-secondary);
        }
        
        .report-container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--background-primary);
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
            min-height: 100vh;
        }
        
        /* 报告头部 - 专业蓝色渐变设计 */
        .report-header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #06b6d4 100%);
            color: white;
            padding: 80px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
            border-bottom: 5px solid #1e40af;
        }
        
        .report-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 40%, rgba(255,255,255,0.1) 0%, transparent 50%);
            pointer-events: none;
        }
        
        .report-title {
            font-size: 42px;
            font-weight: 800;
            margin-bottom: 25px;
            position: relative;
            z-index: 1;
            text-shadow: 0 3px 6px rgba(0,0,0,0.2);
            letter-spacing: 1px;
            line-height: 1.2;
        }
        
        .report-subtitle {
            font-size: 22px;
            opacity: 0.95;
            margin-bottom: 35px;
            position: relative;
            z-index: 1;
            font-weight: 500;
            border-bottom: 1px solid rgba(255,255,255,0.3);
            padding-bottom: 15px;
            display: inline-block;
        }
        
        .report-meta {
            font-size: 16px;
            opacity: 0.8;
            border-top: 1px solid rgba(255,255,255,0.2);
            padding-top: 20px;
            position: relative;
            z-index: 1;
        }
        
        /* 传统专业目录 - 增强设计 */
        .table-of-contents {
            padding: 60px 80px;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border-bottom: 3px solid #e2e8f0;
            position: relative;
        }
        
        .table-of-contents::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1e40af, #3b82f6, #06b6d4);
        }
        
        .toc-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 40px;
            text-align: center;
            position: relative;
        }
        
        .toc-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 3px;
            background: var(--primary-color);
        }
        
        .toc-list {
            list-style: none;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .toc-item {
            border-bottom: 1px dotted #ddd;
            transition: all 0.3s ease;
        }
        
        .toc-item:hover {
            background: rgba(30, 64, 175, 0.05);
        }
        
        .toc-link {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 18px 20px;
            text-decoration: none;
            color: var(--text-primary);
        }
        
        .toc-left {
            display: flex;
            align-items: center;
        }
        
        .toc-number {
            font-size: 16px;
            font-weight: 600;
            color: var(--primary-color);
            margin-right: 15px;
            min-width: 30px;
        }
        
        .toc-title-text {
            font-size: 16px;
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .toc-dots {
            flex: 1;
            border-bottom: 1px dotted #ccc;
            margin: 0 15px;
            height: 1px;
        }
        
        .toc-page {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        /* 主要内容区域 */
        .main-content {
            padding: 50px 40px;
        }
        
        .report-section {
            margin-bottom: 60px;
            scroll-margin-top: 100px;
        }
        
        .section-header {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            padding-bottom: 15px;
            border-bottom: 3px solid var(--primary-color);
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 80px;
            height: 3px;
            background: var(--accent-color);
        }
        
        .section-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 12px;
            font-weight: 600;
            margin-right: 20px;
            flex-shrink: 0;
        }
        
        .content-grid {
            margin-bottom: 30px;
        }
        
        .content-grid.layout-left-right {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 40px;
            align-items: start;
        }
        
        .content-grid.layout-top-bottom {
            display: block;
        }
        
        .content-grid.layout-top-bottom .content-chart {
            margin-top: 30px;
        }
        
        .content-grid.single-column {
            display: block;
        }
        
        .content-text {
            font-size: 16px;
            line-height: 1.8;
            color: var(--text-primary);
        }
        
        .section-content {
            text-align: justify;
            text-indent: 2em;
            margin-bottom: 25px;
            line-height: 1.9;
            font-size: 16px;
            color: var(--text-primary);
            min-height: 120px;
            padding: 20px;
            background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
            border-radius: 8px;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        /* 图表样式 - 专业报告标准 */
        .chart-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            padding: 35px 25px;
            margin: 30px 0;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
            position: relative;
        }
        
        .chart-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #1e40af, #3b82f6, #06b6d4);
            border-radius: 15px 15px 0 0;
        }
        
        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 30px;
            text-align: center;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 15px;
            position: relative;
        }
        
        .chart-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: #3b82f6;
        }
        
        .chart-icon {
            margin-right: 10px;
            font-size: 20px;
        }
        
        .chart-canvas {
            width: 100%;
            height: 350px;
            border-radius: 8px;
        }
        
        .chart-note {
            font-size: 12px;
            color: var(--text-secondary);
            text-align: right;
            margin-top: 15px;
            font-style: italic;
        }
        
        .data-source {
            font-size: 12px;
            color: #64748b;
            text-align: center;
            margin-top: 15px;
            background: #f1f5f9;
            padding: 8px 15px;
            border-radius: 6px;
            border-left: 3px solid #3b82f6;
            font-style: italic;
        }
        
        /* 数据高亮区域 */
        .data-highlights {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 40px 0;
            padding: 30px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 15px;
            border: 2px solid #0ea5e9;
        }
        
        .data-highlight-item {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .data-highlight-item:hover {
            transform: translateY(-5px);
        }
        
        .highlight-number {
            font-size: 32px;
            font-weight: 800;
            color: #1e40af;
            display: block;
            margin-bottom: 8px;
        }
        
        .highlight-label {
            font-size: 14px;
            color: #64748b;
            font-weight: 500;
        }
        
        .highlight-trend {
            font-size: 12px;
            margin-top: 5px;
            padding: 3px 8px;
            border-radius: 12px;
            display: inline-block;
        }
        
        .trend-up {
            background: #dcfce7;
            color: #16a34a;
        }
        
        .trend-down {
            background: #fef2f2;
            color: #dc2626;
        }
        
        /* 要点摘要 */
        .highlights-section {
            background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-primary) 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 40px 0;
            border-left: 5px solid var(--primary-color);
        }
        
        .highlights-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 20px;
        }
        
        .highlights-list {
            list-style: none;
        }
        
        .highlight-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            font-size: 15px;
            color: var(--text-primary);
        }
        
        .highlight-bullet {
            color: var(--primary-color);
            margin-right: 10px;
            font-weight: 600;
            flex-shrink: 0;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .report-container {
                margin: 0;
                box-shadow: none;
            }
            
            .report-header {
                padding: 40px 20px;
            }
            
            .report-title {
                font-size: 28px;
            }
            
            .table-of-contents {
                padding: 30px 20px;
            }
            
            .toc-list {
                grid-template-columns: 1fr;
            }
            
            .main-content {
                padding: 30px 20px;
            }
            
            .content-grid {
                grid-template-columns: 1fr !important;
                gap: 30px;
            }
            
            .section-title {
                font-size: 24px;
                flex-direction: column;
                align-items: flex-start;
            }
            
            .section-number {
                margin-bottom: 15px;
                margin-right: 0;
            }
        }
        
        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }
        
        /* 打印样式 */
        @media print {
            .report-container {
                box-shadow: none;
                max-width: none;
            }
            
            .report-section {
                page-break-inside: avoid;
            }
            
            .chart-canvas {
                height: 250px !important;
            }
        }
    </style>
</head>
<body>
    <div class="report-container">
        <!-- 报告头部 -->
        <header class="report-header">
            <h1 class="report-title">中国人工智能行业深度分析报告2025</h1>
            <div class="report-subtitle">专业行业研究报告 · 人工智能行业深度分析</div>
            <div class="report-meta">
                行业：人工智能 | 地区：中国 | 
                生成日期：2025/6/19 | 分析师：AI智能分析团队
            </div>
        </header>

                        <!-- 目录导航 -->
        <nav class="table-of-contents">
            <h2 class="toc-title">目录</h2>
            <ul class="toc-list">
                
      <li class="toc-item">
        <a href="#zhixing-zhaiyao" class="toc-link">
          <div class="toc-left">
            <span class="toc-number">1.</span>
            <span class="toc-title-text">执行摘要</span>
          </div>
          <div class="toc-dots"></div>
          <span class="toc-page">2</span>
        </a>
      </li>
    
      <li class="toc-item">
        <a href="#shichang-fenxi" class="toc-link">
          <div class="toc-left">
            <span class="toc-number">2.</span>
            <span class="toc-title-text">市场分析</span>
          </div>
          <div class="toc-dots"></div>
          <span class="toc-page">3</span>
        </a>
      </li>
    
      <li class="toc-item">
        <a href="#jingzheng-geju" class="toc-link">
          <div class="toc-left">
            <span class="toc-number">3.</span>
            <span class="toc-title-text">竞争格局</span>
          </div>
          <div class="toc-dots"></div>
          <span class="toc-page">4</span>
        </a>
      </li>
    
      <li class="toc-item">
        <a href="#fazhan-qushi" class="toc-link">
          <div class="toc-left">
            <span class="toc-number">4.</span>
            <span class="toc-title-text">发展趋势</span>
          </div>
          <div class="toc-dots"></div>
          <span class="toc-page">5</span>
        </a>
      </li>
    
      <li class="toc-item">
        <a href="#tiaozhan-jiyu" class="toc-link">
          <div class="toc-left">
            <span class="toc-number">5.</span>
            <span class="toc-title-text">挑战与机遇</span>
          </div>
          <div class="toc-dots"></div>
          <span class="toc-page">6</span>
        </a>
      </li>
    
      <li class="toc-item">
        <a href="#weilai-zhanwang" class="toc-link">
          <div class="toc-left">
            <span class="toc-number">6.</span>
            <span class="toc-title-text">未来展望</span>
          </div>
          <div class="toc-dots"></div>
          <span class="toc-page">7</span>
        </a>
      </li>
    
      <li class="toc-item">
        <a href="#shuju-laiyuan" class="toc-link">
          <div class="toc-left">
            <span class="toc-number">7.</span>
            <span class="toc-title-text">数据来源</span>
          </div>
          <div class="toc-dots"></div>
          <span class="toc-page">8</span>
        </a>
      </li>
    
            </ul>
        </nav>

        <!-- 主要内容 -->
        <main class="main-content">
            <!-- 核心数据展示 -->
            <div class="data-highlights">
                <div class="data-highlight-item">
                    <span class="highlight-number">372.3亿</span>
                    <div class="highlight-label">2024年市场规模</div>
                    <div class="highlight-trend trend-up">+10.08%</div>
                </div>
                <div class="data-highlight-item">
                    <span class="highlight-number">3.7%</span>
                    <div class="highlight-label">全球市场份额</div>
                    <div class="highlight-trend trend-up">+0.5%</div>
                </div>
                <div class="data-highlight-item">
                    <span class="highlight-number">6.3%</span>
                    <div class="highlight-label">自动化率增长</div>
                    <div class="highlight-trend trend-up">+2.1%</div>
                </div>
                <div class="data-highlight-item">
                    <span class="highlight-number">25%</span>
                    <div class="highlight-label">年复合增长率</div>
                    <div class="highlight-trend trend-up">预期2025-2030</div>
                </div>
            </div>
            
            
    <div class="highlights-section">
      <h3 class="highlights-title">📌 核心要点</h3>
      <ul class="highlights-list">
        
          <li class="highlight-item">
            <span class="highlight-bullet">▶</span>
            市场规模预计2025年达1500亿元
          </li>
        
          <li class="highlight-item">
            <span class="highlight-bullet">▶</span>
            年复合增长率超过25%
          </li>
        
          <li class="highlight-item">
            <span class="highlight-bullet">▶</span>
            头部企业市场份额占比45%
          </li>
        
          <li class="highlight-item">
            <span class="highlight-bullet">▶</span>
            专利申请数量全球第一
          </li>
        
          <li class="highlight-item">
            <span class="highlight-bullet">▶</span>
            投资金额同比增长35%
          </li>
        
      </ul>
    </div>
  
            
      <section id="zhixing-zhaiyao" class="report-section">
        <div class="section-header">
          <h2 class="section-title">
            <span class="section-number">1</span>
            执行摘要
          </h2>
        </div>
        
        <div class="section-body">
          <div class="content-grid single-column">
            <div class="content-text">
              <p class="section-content">人工智能行业正处于快速发展期，市场规模预计将达到1500亿元，年复合增长率超过25%。技术创新不断涌现，应用场景日益丰富，产业生态逐步完善。</p>
            </div>
            
            
          </div>
        </div>
      </section>
    
      <section id="shichang-fenxi" class="report-section">
        <div class="section-header">
          <h2 class="section-title">
            <span class="section-number">2</span>
            市场分析
          </h2>
        </div>
        
        <div class="section-body">
          <div class="content-grid layout-top-bottom">
            <div class="content-text">
              <p class="section-content">中国人工智能市场规模从2020年的500亿元增长至2024年的1200亿元，预计2025年将突破1500亿元。计算机视觉、自然语言处理、机器学习等细分领域发展迅速。</p>
            </div>
            
            
            <div class="content-chart">
              <div class="chart-container">
                <h3 class="chart-title">市场规模发展趋势</h3>
                <div id="adaptive_chart_1" class="chart-canvas"></div>
                <p class="chart-note">注：基于公开数据整理分析</p>
                <p class="data-source">数据来源：国家统计局、行业协会、公开资料整理</p>
              </div>
            </div>
            
          </div>
        </div>
      </section>
    
      <section id="jingzheng-geju" class="report-section">
        <div class="section-header">
          <h2 class="section-title">
            <span class="section-number">3</span>
            竞争格局
          </h2>
        </div>
        
        <div class="section-body">
          <div class="content-grid layout-left-right">
            <div class="content-text">
              <p class="section-content">百度、阿里巴巴、腾讯等科技巨头占据主导地位，商汤科技、旷视科技等专业AI公司快速崛起，形成多层次竞争态势</p>
            </div>
            
            
            <div class="content-chart">
              <div class="chart-container">
                <h3 class="chart-title">市场竞争格局分布</h3>
                <div id="adaptive_chart_2" class="chart-canvas"></div>
                <p class="chart-note">注：基于公开数据整理分析</p>
                <p class="data-source">数据来源：国家统计局、行业协会、公开资料整理</p>
              </div>
            </div>
            
          </div>
        </div>
      </section>
    
      <section id="fazhan-qushi" class="report-section">
        <div class="section-header">
          <h2 class="section-title">
            <span class="section-number">4</span>
            发展趋势
          </h2>
        </div>
        
        <div class="section-body">
          <div class="content-grid layout-top-bottom">
            <div class="content-text">
              <p class="section-content">技术发展趋势包括大模型技术突破、多模态AI融合、边缘计算普及、AI芯片性能提升等。应用趋势体现在智能制造、自动驾驶、医疗健康、金融科技等领域深度渗透。</p>
            </div>
            
            
            <div class="content-chart">
              <div class="chart-container">
                <h3 class="chart-title">行业发展趋势分析</h3>
                <div id="adaptive_chart_3" class="chart-canvas"></div>
                <p class="chart-note">注：基于公开数据整理分析</p>
                <p class="data-source">数据来源：国家统计局、行业协会、公开资料整理</p>
              </div>
            </div>
            
          </div>
        </div>
      </section>
    
      <section id="tiaozhan-jiyu" class="report-section">
        <div class="section-header">
          <h2 class="section-title">
            <span class="section-number">5</span>
            挑战与机遇
          </h2>
        </div>
        
        <div class="section-body">
          <div class="content-grid single-column">
            <div class="content-text">
              <p class="section-content">主要挑战包括数据安全与隐私保护、算法公平性、人才短缺、技术标准不统一等。发展机遇体现在政策大力支持、市场需求旺盛、技术不断突破、资本持续投入等方面。</p>
            </div>
            
            
          </div>
        </div>
      </section>
    
      <section id="weilai-zhanwang" class="report-section">
        <div class="section-header">
          <h2 class="section-title">
            <span class="section-number">6</span>
            未来展望
          </h2>
        </div>
        
        <div class="section-body">
          <div class="content-grid layout-top-bottom">
            <div class="content-text">
              <p class="section-content">未来3-5年，人工智能将进入规模化应用阶段，预计市场规模年均增长率保持在20%以上。建议重点关注垂直领域应用、跨界融合创新、国际合作交流等发展方向。</p>
            </div>
            
            
            <div class="content-chart">
              <div class="chart-container">
                <h3 class="chart-title">未来发展预测对比</h3>
                <div id="adaptive_chart_4" class="chart-canvas"></div>
                <p class="chart-note">注：基于公开数据整理分析</p>
                <p class="data-source">数据来源：国家统计局、行业协会、公开资料整理</p>
              </div>
            </div>
            
          </div>
        </div>
      </section>
    
      <section id="shuju-laiyuan" class="report-section">
        <div class="section-header">
          <h2 class="section-title">
            <span class="section-number">7</span>
            数据来源
          </h2>
        </div>
        
        <div class="section-body">
          <div class="content-grid single-column">
            <div class="content-text">
              <p class="section-content">本报告基于以下数据源进行综合分析：
    
**官方统计数据**：国家统计局、工信部、商务部等政府机构发布的行业统计数据和政策文件。

**行业协会资料**：相关行业协会发布的市场研究报告、行业白皮书和发展规划。

**企业公开信息**：上市公司年报、财务报告、投资者关系资料和企业官方发布信息。

**第三方研究**：知名咨询机构、研究院所发布的专业研究报告和市场分析。

**实地调研**：通过专家访谈、企业调研、用户调查等方式获取的一手资料。

数据统计截止时间：2025年6月</p>
            </div>
            
            
          </div>
        </div>
      </section>
    
        </main>
    </div>

    <script>
        // 智能图表渲染系统
        document.addEventListener('DOMContentLoaded', function() {
            const charts = [{"id":"adaptive_chart_1","type":"line","title":"市场规模发展趋势","icon":"📈","section":"market_analysis","sectionId":"shichang-fenxi","data":{"labels":["2021年","2022年","2023年","2024年","2025年"],"values":[150,188,237,296,369],"unit":"亿元"},"colors":["#1e40af","#3b82f6","#06b6d4","#94a3b8","#cbd5e1"]},{"id":"adaptive_chart_2","type":"pie","title":"市场竞争格局分布","icon":"🥧","section":"competitive_landscape","sectionId":"jingzheng-geju","data":{"labels":["头部企业","中型企业","小型企业","其他"],"values":[45,28,18,9],"unit":"%"},"colors":["#1e40af","#3b82f6","#06b6d4","#94a3b8","#cbd5e1"]},{"id":"adaptive_chart_3","type":"radar","title":"行业发展趋势分析","icon":"🎯","section":"industry_trends","sectionId":"fazhan-qushi","data":{"labels":["技术创新","市场规模","政策支持","资本投入","人才储备"],"values":[66.66666666666667,66.66666666666667,66.66666666666667,66.66666666666667,66.66666666666667],"unit":"分"},"colors":["#1e40af","#3b82f6","#06b6d4","#94a3b8","#cbd5e1"]},{"id":"adaptive_chart_4","type":"bar","title":"未来发展预测对比","icon":"📊","section":"future_outlook","sectionId":"weilai-zhanwang","data":{"labels":["短期预测","中期预测","长期预测"],"values":[180,278,420],"unit":"%"},"colors":["#1e40af","#3b82f6","#06b6d4","#94a3b8","#cbd5e1"]}];
            
            charts.forEach(chart => {
                const chartDom = document.getElementById(chart.id);
                if (!chartDom) return;
                
                const myChart = echarts.init(chartDom);
                const chartData = chart.data;
                
                // 通用图表主题
                const theme = {
                    color: chart.colors,
                    backgroundColor: 'transparent',
                    textStyle: {
                        fontFamily: 'PingFang SC, Microsoft YaHei',
                        color: '#374151'
                    },
                    animation: true,
                    animationDuration: 1000
                };
                
                let option = {
                    ...theme,
                    tooltip: {
                        trigger: chart.type === 'pie' ? 'item' : 'axis',
                        backgroundColor: 'rgba(255, 255, 255, 0.95)',
                        borderColor: '#e5e7eb',
                        borderWidth: 1,
                        textStyle: { color: '#374151' }
                    },
                    legend: {
                        bottom: '5%',
                        textStyle: { color: '#6b7280' }
                    }
                };
                
                // 根据图表类型生成配置
                if (chart.type === 'pie') {
                    option.series = [{
                        type: 'pie',
                        radius: ['40%', '70%'],
                        center: ['50%', '45%'],
                        data: chartData.labels.map((label, index) => ({
                            value: chartData.values[index],
                            name: label
                        })),
                        label: {
                            show: true,
                            formatter: '{b}: {c}' + chartData.unit
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }];
                } else if (chart.type === 'radar') {
                    option.radar = {
                        indicator: chartData.labels.map(label => ({ name: label, max: 100 })),
                        center: ['50%', '50%'],
                        radius: '60%'
                    };
                    option.series = [{
                        type: 'radar',
                        data: [{
                            value: chartData.values,
                            name: chart.title
                        }],
                        areaStyle: { opacity: 0.3 }
                    }];
                } else if (chart.type === 'bar') {
                    option.grid = {
                        top: '15%',
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        containLabel: true
                    };
                    option.xAxis = {
                        type: 'category',
                        data: chartData.labels,
                        axisLabel: { color: '#6b7280' }
                    };
                    option.yAxis = {
                        type: 'value',
                        name: chartData.unit,
                        axisLabel: { color: '#6b7280' }
                    };
                    option.series = [{
                        type: 'bar',
                        data: chartData.values,
                        itemStyle: { color: chart.colors[0] },
                        barWidth: '60%'
                    }];
                } else {
                    // 默认折线图
                    option.grid = {
                        top: '15%',
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        containLabel: true
                    };
                    option.xAxis = {
                        type: 'category',
                        data: chartData.labels,
                        axisLabel: { color: '#6b7280' }
                    };
                    option.yAxis = {
                        type: 'value',
                        name: chartData.unit,
                        axisLabel: { color: '#6b7280' }
                    };
                    option.series = [{
                        type: 'line',
                        data: chartData.values,
                        smooth: true,
                        itemStyle: { color: chart.colors[0] },
                        lineStyle: { width: 3 },
                        symbolSize: 8
                    }];
                }
                
                myChart.setOption(option);
                
                // 响应式调整
                window.addEventListener('resize', function() {
                    myChart.resize();
                });
            });
        });
    </script>
</body>
</html>
  