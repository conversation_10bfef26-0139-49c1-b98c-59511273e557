**Operation**

[ write file to desk]

**File Path and Name**

Fixed

Expression

[ report.html]

**Input Binary Field**

Fixed

Expression

**data**


optional:


- Set Node Configuration :
- Field Name: data
- Value: {{ $node["OpenAI"].json["output"].replace(/^```html\s*|```\s*$/g, "") }}
- This removes the Markdown code block markers while preserving the HTML content
- Write Binary File Node Configuration :
- Binary Data: Leave empty
- File Name: report.html
- Property Name: data
- Operation: Write
