// 改进的代码节点HTML生成器
// 节点顺序：Set → Tavily Search → OpenAI → Code (此节点)
// 这个代码节点会生成完整的HTML报告并返回给下游节点

// 获取上游节点数据
const setData = $('Set').item.json;
const searchResults = $('TavilySearch').item.json;
const aiResponse = $('OpenAI').item.json;

// 增强的AI响应解析函数
function parseAIResponse(aiResponse) {
    console.log('开始解析AI响应...');
    
    let aiContent = null;
    
    // 步骤1: 提取AI内容 - 支持多种格式
    try {
        // 检查是否有output字段且为字符串（如temp.json的情况）
        if (aiResponse?.output && typeof aiResponse.output === 'string') {
            console.log('从output字符串字段解析JSON');
            try {
                const parsedOutput = JSON.parse(aiResponse.output);
                return validateAndNormalizeData(parsedOutput);
            } catch (parseError) {
                console.log('output字段JSON解析失败，尝试其他方法');
                aiContent = aiResponse.output;
            }
        }
        // 检查是否有output字段且为对象
        else if (aiResponse?.output && typeof aiResponse.output === 'object') {
            console.log('使用output对象字段');
            return validateAndNormalizeData(aiResponse.output);
        }
        // OpenAI格式: 从choices中提取
        else if (aiResponse?.choices?.[0]?.message?.content) {
            aiContent = aiResponse.choices[0].message.content;
        } else if (aiResponse?.content) {
            aiContent = aiResponse.content;
        } else if (aiResponse?.message?.content) {
            aiContent = aiResponse.message.content;
        } else if (typeof aiResponse === 'string') {
            aiContent = aiResponse;
        } else {
            throw new Error('无法从AI响应中提取内容');
        }
        
        console.log('AI内容提取成功，长度:', aiContent.length);
    } catch (error) {
        console.error('AI内容提取失败:', error.message);
        return getDefaultAnalysisData();
    }
    
    // 步骤2: 清理和预处理内容
    try {
        // 移除markdown代码块标记
        aiContent = aiContent
            .replace(/```(?:json|javascript|js)?\s*/g, '')
            .replace(/```\s*$/g, '')
            .trim();
        
        // 尝试提取JSON对象
        const jsonMatch = aiContent.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
            aiContent = jsonMatch[0];
        }
        
        // 处理可能的转义字符
        if (aiContent.startsWith('"') && aiContent.endsWith('"')) {
            try {
                const unescaped = JSON.parse(aiContent);
                if (typeof unescaped === 'string' && unescaped.startsWith('{')) {
                    aiContent = unescaped;
                }
            } catch (e) {
                // 忽略转义处理错误
            }
        }
        
        console.log('内容清理完成');
    } catch (error) {
        console.error('内容清理失败:', error.message);
        return getDefaultAnalysisData();
    }
    
    // 步骤3: 尝试JSON解析
    try {
        const parsedData = JSON.parse(aiContent);
        console.log('JSON解析成功');
        return validateAndNormalizeData(parsedData);
    } catch (jsonError) {
        console.log('JSON解析失败，尝试文本提取:', jsonError.message);
        return extractFromText(aiContent);
    }
}

// 从文本中提取结构化数据
function extractFromText(content) {
    console.log('开始文本提取...');
    
    const extractField = (patterns) => {
        for (const pattern of patterns) {
            const match = content.match(pattern);
            if (match && match[1]) {
                return match[1].trim().replace(/^["']|["']$/g, '');
            }
        }
        return null;
    };
    
    const extractList = (patterns) => {
        for (const pattern of patterns) {
            const match = content.match(pattern);
            if (match && match[1]) {
                return match[1]
                    .split(/[\n\r]+/)
                    .map(item => item.replace(/^[-*•]\s*/, '').trim())
                    .filter(item => item.length > 0 && !item.match(/^[\d.]+$/));
            }
        }
        return [];
    };
    
    const result = {
        executive_summary: extractField([
            /执行摘要[：:]([\s\S]*?)(?=市场规模|发展趋势|主要参与者|面临挑战|$)/i,
            /executive_summary[：:]\s*([^\n]+)/i,
            /摘要[：:]([\s\S]*?)(?=规模|趋势|参与者|挑战|$)/i
        ]) || "基于AI分析生成的行业报告摘要",
        
        market_size: extractField([
            /市场规模[：:]([\s\S]*?)(?=发展趋势|主要参与者|面临挑战|增长率|$)/i,
            /market_size[：:]\s*([^\n]+)/i,
            /规模[：:]([\s\S]*?)(?=趋势|参与者|挑战|$)/i
        ]) || "市场规模数据分析中",
        
        trends: extractList([
            /发展趋势[：:]([\s\S]*?)(?=主要参与者|面临挑战|发展机遇|增长率|$)/i,
            /trends[：:]([\s\S]*?)(?=key_players|challenges|opportunities|$)/i,
            /趋势[：:]([\s\S]*?)(?=参与者|挑战|机遇|$)/i
        ]),
        
        key_players: extractList([
            /主要参与者[：:]([\s\S]*?)(?=面临挑战|发展机遇|增长率|未来预测|$)/i,
            /key_players[：:]([\s\S]*?)(?=challenges|opportunities|growth|$)/i,
            /参与者[：:]([\s\S]*?)(?=挑战|机遇|增长|$)/i
        ]),
        
        challenges: extractList([
            /面临挑战[：:]([\s\S]*?)(?=发展机遇|增长率|未来预测|$)/i,
            /challenges[：:]([\s\S]*?)(?=opportunities|growth|forecast|$)/i,
            /挑战[：:]([\s\S]*?)(?=机遇|增长|预测|$)/i
        ]),
        
        opportunities: extractList([
            /发展机遇[：:]([\s\S]*?)(?=增长率|未来预测|数据来源|$)/i,
            /opportunities[：:]([\s\S]*?)(?=growth|forecast|source|$)/i,
            /机遇[：:]([\s\S]*?)(?=增长|预测|来源|$)/i
        ]),
        
        growth_rate: extractField([
            /增长率[：:]([\s\S]*?)(?=未来预测|数据来源|$)/i,
            /growth_rate[：:]\s*([^\n]+)/i,
            /增长[：:]([\s\S]*?)(?=预测|来源|$)/i
        ]) || "增长率数据分析中",
        
        future_forecast: extractField([
            /未来预测[：:]([\s\S]*?)(?=数据来源|$)/i,
            /future_forecast[：:]\s*([\s\S]*?)(?=source|$)/i,
            /预测[：:]([\s\S]*?)(?=来源|$)/i
        ]) || "未来预测分析中"
    };
    
    console.log('文本提取完成');
    return validateAndNormalizeData(result);
}

// 验证和标准化数据 - 新增对temp.json格式的支持
function validateAndNormalizeData(data) {
    console.log('开始数据标准化，原始数据结构:', Object.keys(data));
    
    // 处理temp.json中的数据结构
    if (data.reportTitle && data.mainAnalysis) {
        console.log('检测到temp.json格式，进行转换');
        const normalized = {
            title: data.reportTitle,
            executive_summary: data.executiveSummary || "行业分析摘要",
            market_size: data.mainAnalysis?.marketSize?.description || "市场规模信息",
            trends: data.mainAnalysis?.trends?.description ? [data.mainAnalysis.trends.description] : ["发展趋势分析中"],
            key_players: data.mainAnalysis?.competitiveLandscape?.description ? [data.mainAnalysis.competitiveLandscape.description] : ["主要参与者分析中"],
            challenges: ["行业面临的主要挑战分析中"],
            opportunities: ["发展机遇分析中"],
            growth_rate: "根据预测，行业将保持稳定增长",
            future_forecast: data.conclusionsAndRecommendations?.conclusions || "未来预测分析中",
            chart_data: extractChartDataFromMainAnalysis(data.mainAnalysis)
        };
        
        console.log('temp.json格式转换完成');
        return normalized;
    }
    
    // 原有的标准化逻辑
    const normalized = {
        title: data.title || data.reportTitle || `${setData.industry}行业分析报告`,
        executive_summary: data.executive_summary || data.summary || data.executiveSummary || "行业分析摘要",
        market_size: data.market_size || data.marketSize || "市场规模信息",
        trends: Array.isArray(data.trends) ? data.trends : 
                (typeof data.trends === 'string' ? [data.trends] : ["发展趋势分析中"]),
        key_players: Array.isArray(data.key_players) ? data.key_players :
                     Array.isArray(data.keyPlayers) ? data.keyPlayers :
                     (typeof data.key_players === 'string' ? [data.key_players] : ["主要参与者分析中"]),
        challenges: Array.isArray(data.challenges) ? data.challenges :
                    (typeof data.challenges === 'string' ? [data.challenges] : ["挑战分析中"]),
        opportunities: Array.isArray(data.opportunities) ? data.opportunities :
                       (typeof data.opportunities === 'string' ? [data.opportunities] : ["机遇分析中"]),
        growth_rate: data.growth_rate || data.growthRate || "增长率分析中",
        future_forecast: data.future_forecast || data.futureForecast || data.forecast || "未来预测分析中",
        chart_data: data.chart_data || data.chartData || getDefaultChartData()
    };
    
    console.log('数据标准化完成:', Object.keys(normalized));
    return normalized;
}

// 新增：从mainAnalysis中提取图表数据
function extractChartDataFromMainAnalysis(mainAnalysis) {
    if (!mainAnalysis) return getDefaultChartData();
    
    const chartData = {
        market_trend: {
            years: [],
            values: [],
            unit: "亿元"
        },
        growth_rate: {
            years: [],
            values: [],
            unit: "%"
        }
    };
    
    // 从marketSize中提取趋势数据
    if (mainAnalysis.marketSize?.chart?.data) {
        const marketChart = mainAnalysis.marketSize.chart.data;
        if (marketChart.years && marketChart.values) {
            chartData.market_trend.years = marketChart.years;
            chartData.market_trend.values = marketChart.values;
        }
    }
    
    // 如果没有增长率数据，计算增长率
    if (chartData.market_trend.values.length > 1) {
        const values = chartData.market_trend.values;
        const growthRates = [];
        for (let i = 1; i < values.length; i++) {
            const rate = ((values[i] - values[i-1]) / values[i-1] * 100).toFixed(1);
            growthRates.push(parseFloat(rate));
        }
        chartData.growth_rate.years = chartData.market_trend.years.slice(1);
        chartData.growth_rate.values = growthRates;
    }
    
    return chartData;
}

// 获取默认分析数据
function getDefaultAnalysisData() {
    return {
        executive_summary: "本报告基于最新的市场数据和AI分析，为您提供全面的行业洞察。报告涵盖了市场规模、发展趋势、主要参与者、面临挑战和发展机遇等关键信息。",
        market_size: "根据最新数据显示，该行业市场规模持续增长，预计在未来几年将保持稳定的增长态势。",
        trends: [
            "数字化转型加速推进，技术创新成为重要驱动力",
            "可持续发展理念深入人心，绿色环保成为新标准",
            "消费者需求日益多样化，个性化服务需求增长",
            "国际合作与竞争并存，全球化布局成为发展趋势"
        ],
        key_players: ["行业领军企业A", "创新科技公司B", "传统巨头企业C", "新兴独角兽D"],
        challenges: [
            "技术更新换代速度加快，需要持续投入研发",
            "市场竞争日趋激烈，利润空间受到压缩",
            "监管政策不断完善，合规成本上升"
        ],
        opportunities: [
            "新兴市场需求旺盛，提供广阔发展空间",
            "技术创新带来新的商业模式和盈利点",
            "政策支持力度加大，为发展提供有力保障"
        ],
        growth_rate: "预计年均增长率将保持在15-25%之间",
        future_forecast: "未来3-5年，该行业将继续保持快速发展态势，建议相关企业加强技术研发，提升服务质量。",
        chart_data: getDefaultChartData()
    };
}

// 获取默认图表数据
function getDefaultChartData() {
    return {
        market_size_trend: [
            {year: 2022, value: 1200},
            {year: 2023, value: 1450},
            {year: 2024, value: 1680},
            {year: 2025, value: 1950}
        ],
        growth_rate_trend: [
            {year: 2022, value: 18},
            {year: 2023, value: 21},
            {year: 2024, value: 16},
             {year: 2025, value: 16}
        ],
        // DeepSeek格式兼容
        market_trend: {
            years: ["2022", "2023", "2024", "2025"],
            values: [1.2, 1.5, 1.8, 2.2],
            unit: "万亿元"
        },
        growth_rate: {
            years: ["2022", "2023", "2024", "2025"],
            values: [8, 10, 12, 10],
            unit: "%"
        }
    };
}

// 生成HTML报告
function generateHTMLReport(analysisData, setData) {
    const reportTitle = `${setData.industry}行业分析报告`;
    const currentDate = new Date();
    const dateString = currentDate.toLocaleDateString('zh-CN');
    const timeString = currentDate.toLocaleString('zh-CN');
    
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${reportTitle}</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .chart-container { width: 100%; height: 400px; }
        .section { margin-bottom: 2rem; }
        body { font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; }
        .fade-in { animation: fadeIn 0.5s ease-in; }
        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
    </style>
</head>
<body class="bg-gray-50 p-6">
    <div class="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-8 fade-in">
        <!-- 报告标题 -->
        <header class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">${reportTitle}</h1>
            <p class="text-gray-600">生成时间：${dateString}</p>
            <p class="text-sm text-gray-500">地区：${setData.region || '全球'} | 时间范围：${setData.timeRange || '2024-2025'}</p>
        </header>

        <!-- 执行摘要 -->
        <section class="section">
            <h2 class="text-2xl font-semibold text-blue-600 mb-4 border-b-2 border-blue-200 pb-2">执行摘要</h2>
            <div class="bg-blue-50 p-4 rounded-lg">
                <p class="text-gray-700 leading-relaxed">${analysisData.executive_summary}</p>
            </div>
        </section>

        <!-- 市场规模 -->
        <section class="section">
            <h2 class="text-2xl font-semibold text-green-600 mb-4 border-b-2 border-green-200 pb-2">市场规模</h2>
            <div class="bg-green-50 p-4 rounded-lg mb-4">
                <p class="text-gray-700">${analysisData.market_size}</p>
            </div>
            <div id="marketChart" class="chart-container bg-white rounded border shadow"></div>
        </section>

        <!-- 发展趋势 -->
        <section class="section">
            <h2 class="text-2xl font-semibold text-purple-600 mb-4 border-b-2 border-purple-200 pb-2">发展趋势</h2>
            <div class="grid md:grid-cols-2 gap-4">
                ${analysisData.trends.map((trend, index) => `
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-purple-800 mb-2">趋势 ${index + 1}</h3>
                        <p class="text-gray-700">${trend}</p>
                    </div>
                `).join('')}
            </div>
        </section>

        <!-- 主要参与者 -->
        <section class="section">
            <h2 class="text-2xl font-semibold text-orange-600 mb-4 border-b-2 border-orange-200 pb-2">主要参与者</h2>
            <div class="grid md:grid-cols-3 gap-4">
                ${analysisData.key_players.map(player => `
                    <div class="bg-orange-50 p-4 rounded-lg text-center">
                        <h3 class="font-semibold text-orange-800">${player}</h3>
                    </div>
                `).join('')}
            </div>
        </section>

        <!-- 挑战与机遇 -->
        <section class="section">
            <div class="grid md:grid-cols-2 gap-6">
                <!-- 挑战 -->
                <div>
                    <h2 class="text-2xl font-semibold text-red-600 mb-4 border-b-2 border-red-200 pb-2">面临挑战</h2>
                    ${analysisData.challenges.map(challenge => `
                        <div class="bg-red-50 p-3 rounded-lg mb-3">
                            <p class="text-gray-700">${challenge}</p>
                        </div>
                    `).join('')}
                </div>
                
                <!-- 机遇 -->
                <div>
                    <h2 class="text-2xl font-semibold text-teal-600 mb-4 border-b-2 border-teal-200 pb-2">发展机遇</h2>
                    ${analysisData.opportunities.map(opportunity => `
                        <div class="bg-teal-50 p-3 rounded-lg mb-3">
                            <p class="text-gray-700">${opportunity}</p>
                        </div>
                    `).join('')}
                </div>
            </div>
        </section>

        <!-- 增长率图表 -->
        <section class="section">
            <h2 class="text-2xl font-semibold text-indigo-600 mb-4 border-b-2 border-indigo-200 pb-2">增长率趋势</h2>
            <div id="growthChart" class="chart-container bg-white rounded border shadow"></div>
        </section>

        <!-- 未来预测 -->
        <section class="section">
            <h2 class="text-2xl font-semibold text-gray-600 mb-4 border-b-2 border-gray-200 pb-2">未来预测</h2>
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-gray-700 leading-relaxed">${analysisData.future_forecast}</p>
            </div>
        </section>

        <!-- 数据来源 -->
        <section class="section">
            <h2 class="text-2xl font-semibold text-gray-600 mb-4 border-b-2 border-gray-200 pb-2">数据来源</h2>
            <div class="bg-gray-50 p-4 rounded-lg">
                <p class="text-sm text-gray-600 mb-2">本报告基于以下数据源生成：</p>
                <ul class="text-sm text-gray-600 list-disc ml-4">
                    <li>Tavily搜索引擎实时数据</li>
                    <li>AI智能分析处理</li>
                    <li>生成时间：${timeString}</li>
                </ul>
            </div>
        </section>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chartData = ${JSON.stringify(analysisData.chart_data)};
            
            // 市场规模趋势图
            try {
                const marketChart = echarts.init(document.getElementById('marketChart'));
                
                // 兼容DeepSeek和原有格式
                let marketData, marketYears, marketValues, marketUnit;
                if (chartData.market_trend && chartData.market_trend.years) {
                    // DeepSeek格式
                    marketYears = chartData.market_trend.years;
                    marketValues = chartData.market_trend.values;
                    marketUnit = chartData.market_trend.unit || '亿元';
                } else if (chartData.market_size_trend) {
                    // 原有格式
                    marketYears = chartData.market_size_trend.map(item => item.year);
                    marketValues = chartData.market_size_trend.map(item => item.value);
                    marketUnit = '亿元';
                } else {
                    // 默认数据
                    marketYears = ['2022', '2023', '2024', '2025'];
                    marketValues = [1200, 1450, 1680, 1950];
                    marketUnit = '亿元';
                }
                
                const marketOption = {
                    title: {
                        text: '市场规模趋势',
                        left: 'center',
                        textStyle: { fontSize: 16, fontWeight: 'bold' }
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            return params[0].name + ': ' + params[0].value + ' ' + marketUnit;
                        }
                    },
                    xAxis: {
                        type: 'category',
                        data: marketYears
                    },
                    yAxis: {
                        type: 'value',
                        name: marketUnit
                    },
                    series: [{
                        data: marketValues,
                        type: 'line',
                        smooth: true,
                        lineStyle: { color: '#10B981', width: 3 },
                        itemStyle: { color: '#10B981' },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0, y: 0, x2: 0, y2: 1,
                                colorStops: [
                                    { offset: 0, color: 'rgba(16, 185, 129, 0.3)' },
                                    { offset: 1, color: 'rgba(16, 185, 129, 0.1)' }
                                ]
                            }
                        }
                    }]
                };
                marketChart.setOption(marketOption);
            } catch (error) {
                console.error('市场规模图表渲染失败:', error);
                document.getElementById('marketChart').innerHTML = '<p class="text-center text-gray-500 py-8">图表数据加载失败</p>';
            }

            // 增长率图表
            try {
                const growthChart = echarts.init(document.getElementById('growthChart'));
                
                // 兼容DeepSeek和原有格式
                let growthYears, growthValues, growthUnit;
                if (chartData.growth_rate && chartData.growth_rate.years) {
                    // DeepSeek格式
                    growthYears = chartData.growth_rate.years;
                    growthValues = chartData.growth_rate.values;
                    growthUnit = chartData.growth_rate.unit || '%';
                } else if (chartData.growth_rate_trend) {
                    // 原有格式
                    growthYears = chartData.growth_rate_trend.map(item => item.year);
                    growthValues = chartData.growth_rate_trend.map(item => item.value);
                    growthUnit = '%';
                } else {
                    // 默认数据
                    growthYears = ['2022', '2023', '2024', '2025'];
                    growthValues = [18, 21, 16, 16];
                    growthUnit = '%';
                }
                
                const growthOption = {
                    title: {
                        text: '年增长率',
                        left: 'center',
                        textStyle: { fontSize: 16, fontWeight: 'bold' }
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params) {
                            return params[0].name + ': ' + params[0].value + growthUnit;
                        }
                    },
                    xAxis: {
                        type: 'category',
                        data: growthYears
                    },
                    yAxis: {
                        type: 'value',
                        name: '增长率 (' + growthUnit + ')'
                    },
                    series: [{
                        data: growthValues,
                        type: 'bar',
                        itemStyle: {
                            color: {
                                type: 'linear',
                                x: 0, y: 0, x2: 0, y2: 1,
                                colorStops: [
                                    { offset: 0, color: '#6366F1' },
                                    { offset: 1, color: '#8B5CF6' }
                                ]
                            }
                        }
                    }]
                };
                growthChart.setOption(growthOption);
            } catch (error) {
                console.error('增长率图表渲染失败:', error);
                document.getElementById('growthChart').innerHTML = '<p class="text-center text-gray-500 py-8">图表数据加载失败</p>';
            }

            // 响应式图表
            window.addEventListener('resize', function() {
                try {
                    echarts.getInstanceByDom(document.getElementById('marketChart'))?.resize();
                    echarts.getInstanceByDom(document.getElementById('growthChart'))?.resize();
                } catch (error) {
                    console.error('图表resize失败:', error);
                }
            });
        });
    </script>
</body>
</html>`;
}

// 主执行逻辑
try {
    console.log('开始处理数据...');
    
    // 解析AI响应
    const analysisData = parseAIResponse(aiResponse);
    console.log('数据解析完成:', Object.keys(analysisData));
    
    // 生成HTML报告
    const htmlReport = generateHTMLReport(analysisData, setData);
    console.log('HTML报告生成完成，长度:', htmlReport.length);
    
    // 返回结果
    return {
        html: htmlReport,
        filename: `${setData.industry}_行业分析报告_${new Date().toISOString().split('T')[0]}.html`,
        analysis_data: analysisData,
        metadata: {
            industry: setData.industry,
            region: setData.region,
            timeRange: setData.timeRange,
            generated_at: new Date().toISOString(),
            workflow_version: "improved_v2.0",
            parsing_method: "enhanced_robust_parsing"
        },
        success: true,
        message: "HTML报告生成成功"
    };
    
} catch (error) {
    console.error('处理过程中发生错误:', error);
    
    // 错误情况下返回默认报告
    const defaultData = getDefaultAnalysisData();
    const errorReport = generateHTMLReport(defaultData, setData);
    
    return {
        html: errorReport,
        filename: `${setData.industry}_行业分析报告_${new Date().toISOString().split('T')[0]}_error.html`,
        analysis_data: defaultData,
        metadata: {
            industry: setData.industry,
            region: setData.region,
            timeRange: setData.timeRange,
            generated_at: new Date().toISOString(),
            workflow_version: "improved_v2.0",
            parsing_method: "error_fallback"
        },
        success: false,
        error: error.message,
        message: "使用默认数据生成报告"
    };
  }
