// Enhanced Set Node - 增强版参数设置节点
// 支持更丰富的行业分析参数配置

// 获取输入数据
const chatInput = $input.item.json.chatInput || $input.item.json.industry || "";
const region = $input.item.json.region || "中国";
const reportType = $input.item.json.reportType || "comprehensive"; // comprehensive, quick, deep
const focusAreas = $input.item.json.focusAreas || [];

// 清理和标准化行业关键词
const rawIndustry = chatInput.trim();

if (!rawIndustry) {
  throw new Error("请提供有效的行业关键词");
}

// 清理输入：保留中英文、数字和空格
const industry = rawIndustry
  .replace(/[^\w\s\u4e00-\u9fff]/g, '')
  .replace(/\s+/g, ' ')
  .trim();

if (industry.length < 2) {
  throw new Error("行业关键词至少需要2个字符");
}

// 生成多维度搜索关键词组合
const searchDimensions = {
  // 市场规模维度
  market_size: [
    `${industry} 市场规模 2024`,
    `${industry} 产业规模 统计`,
    `${industry} 市场容量 分析`,
    `${industry} 行业收入 数据`
  ],
  
  // 竞争格局维度
  competition: [
    `${industry} 竞争格局 分析`,
    `${industry} 主要企业 排名`,
    `${industry} 市场份额 分布`,
    `${industry} 龙头企业 对比`
  ],
  
  // 发展趋势维度
  trends: [
    `${industry} 发展趋势 2024`,
    `${industry} 未来前景 预测`,
    `${industry} 技术发展 方向`,
    `${industry} 创新趋势 分析`
  ],
  
  // 政策环境维度
  policy: [
    `${industry} 政策环境 分析`,
    `${industry} 监管政策 影响`,
    `${industry} 国家政策 支持`,
    `${industry} 行业标准 规范`
  ],
  
  // 投资融资维度
  investment: [
    `${industry} 投资现状 分析`,
    `${industry} 融资情况 统计`,
    `${industry} 资本市场 表现`,
    `${industry} IPO 上市 企业`
  ],
  
  // 技术创新维度
  technology: [
    `${industry} 技术创新 突破`,
    `${industry} 研发投入 统计`,
    `${industry} 专利申请 数量`,
    `${industry} 技术壁垒 分析`
  ]
};

// 根据地区添加地域化搜索词
if (region && region !== "全球") {
  Object.keys(searchDimensions).forEach(key => {
    searchDimensions[key] = searchDimensions[key].map(term => 
      `${term} ${region}`
    );
  });
}

// 英文搜索词补充（如果包含英文）
if (/[a-zA-Z]/.test(industry)) {
  const englishTerms = {
    market_size: [`${industry} market size 2024`, `${industry} industry revenue`],
    competition: [`${industry} competitive landscape`, `${industry} market leaders`],
    trends: [`${industry} trends 2024`, `${industry} future outlook`],
    policy: [`${industry} regulations`, `${industry} policy impact`],
    investment: [`${industry} investment analysis`, `${industry} funding rounds`],
    technology: [`${industry} technology innovation`, `${industry} R&D investment`]
  };
  
  Object.keys(englishTerms).forEach(key => {
    searchDimensions[key].push(...englishTerms[key]);
  });
}

// 合并所有搜索词
const allSearchTerms = Object.values(searchDimensions).flat();

// 生成报告配置
const reportConfig = {
  type: reportType,
  structure: {
    executive_summary: true,
    market_overview: true,
    competitive_analysis: true,
    swot_analysis: true,
    policy_environment: true,
    technology_trends: true,
    investment_analysis: true,
    regional_analysis: region !== "全球",
    future_forecast: true,
    charts_required: true
  },
  focus_areas: focusAreas.length > 0 ? focusAreas : [
    "市场规模", "竞争格局", "发展趋势", "技术创新"
  ]
};

// 返回增强的数据结构
return [{
  json: {
    // 基础信息
    industry: industry,
    region: region,
    reportType: reportType,
    
    // 搜索配置
    searchDimensions: searchDimensions,
    allSearchTerms: allSearchTerms,
    searchTermsCount: allSearchTerms.length,
    
    // 报告配置
    reportConfig: reportConfig,
    
    // 元数据
    metadata: {
      originalInput: rawIndustry,
      sanitized: industry !== rawIndustry,
      hasEnglish: /[a-zA-Z]/.test(industry),
      timestamp: new Date().toISOString(),
      workflow_version: "enhanced_v3.0"
    },
    
    // 用于下游节点的便捷访问
    primarySearchTerms: searchDimensions.market_size.concat(
      searchDimensions.competition,
      searchDimensions.trends
    ).slice(0, 15) // 限制主要搜索词数量
  }
}]; 