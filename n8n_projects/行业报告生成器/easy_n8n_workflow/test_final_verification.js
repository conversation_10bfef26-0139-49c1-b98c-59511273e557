// 最终验证测试 - 确保主文件生成的报告没有空白问题
const fs = require('fs');

// 模拟n8n的setData
const setData = {
    industry: '服装',
    region: '广州',
    keywords: ['女装', '时尚', '淑女装']
};

// 测试数据
const testData = {
    output: `广州女淑装2025年行业趋势分析报告

广州女淑装市场预计到2025年将达到150亿元人民币规模，年复合增长率维持在8%左右。

到2025年，广州女淑装行业将呈现三大发展趋势：一是可持续时尚将成为主流，预计使用环保面料的产品占比将从现在的20%提升至40%；二是智能定制服务兴起，3D量体、AI推荐等技术应用将使个性化定制成本降低30%；三是线上线下深度融合，AR虚拟试衣、门店数字化改造将提升30%的转化率。

本报告基于以下数据源进行综合分析： **官方统计数据**：国家统计局、工信部、商务部等政府机构发布的行业统计数据和政策文件。 **行业协会资料**：相关行业协会发布的市场研究报告、行业白皮书和发展规划。 **企业公开信息**：上市公司年报、财务报告、投资者关系资料和企业官方发布信息。 **第三方研究**：知名咨询机构、研究院所发布的专业研究报告和市场分析。 **实地调研**：通过专家访谈、企业调研、用户调查等方式获取的一手资料。 数据统计截止时间：2025年6月`
};

console.log('🎯 最终验证测试 - 主文件数据源空白问题');
console.log('============================================================');

// 直接读取并执行主文件的代码
const mainFileContent = fs.readFileSync('./5_node(HTML_Adaptive_Professional).js', 'utf8');

// 提取主要函数
const funcStart = mainFileContent.indexOf('function detectIndustry(');
const funcEnd = mainFileContent.lastIndexOf('return result;') + 'return result;'.length;
const functionsCode = mainFileContent.substring(funcStart, funcEnd);

// 创建执行环境
const executeCode = `
// 模拟n8n环境
const setData = ${JSON.stringify(setData)};
const $ = (name) => ({ item: { json: setData } });

// 主文件的函数
${functionsCode}

// 执行测试
const aiData = ${JSON.stringify(testData)};
console.log('🚀 开始处理数据...');

try {
    const result = generateAdaptiveHTML(aiData, [], '服装');
    
    // 保存结果
    const fs = require('fs');
    const filename = 'final_verification_report.html';
    fs.writeFileSync(filename, result);
    console.log('💾 最终验证报告已保存:', filename);
    
    // 验证数据源部分
    console.log('🔍 数据源验证:');
    console.log('✅ 包含数据源样式:', result.includes('data-sources-professional'));
    console.log('✅ 包含新CSS:', result.includes('source-description-text'));
    console.log('✅ 包含数据源内容:', result.includes('官方统计数据'));
    console.log('✅ 没有旧的空白结构:', !result.includes('<div class="source-list">'));
    console.log('✅ 连续文本格式:', result.includes('本报告基于以下数据源进行综合分析：<strong>官方统计数据</strong>'));
    
    // 检查是否有空的div
    const emptyDivs = (result.match(/<div[^>]*>\\s*<\\/div>/g) || []).length;
    console.log('🔍 空div检查:', emptyDivs === 0 ? '✅ 无空div' : '⚠️ 发现空div: ' + emptyDivs);
    
    console.log('🎉 最终验证完成！');
    
} catch (error) {
    console.error('❌ 验证失败:', error.message);
}
`;

// 写入临时执行文件
fs.writeFileSync('temp_execute.js', executeCode);

console.log('📝 临时执行文件已创建，正在运行...'); 