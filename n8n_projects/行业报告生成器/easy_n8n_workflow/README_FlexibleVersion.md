# 行业报告生成器 - 灵活版本工作流

## 概述

这是一个完全重新设计的行业报告自动化生成工作流，专门解决了原版本中JSON schema固化的问题。新版本让AI具备了根据专业判断自由组织报告结构的能力，同时HTML生成器能够适应任何输入格式。

## 核心改进

### 1. 去除固化限制
- **取消Output Parser Schema**: 不再强制AI按照固定的JSON格式输出
- **灵活结构设计**: AI可以根据数据质量和行业特性自由组织报告章节
- **专业判断导向**: 基于15年行业分析师经验的System Prompt指导

### 2. 通用HTML生成器
- **万能适配**: 能够处理任何JSON输入结构
- **智能解析**: 支持多种AI响应格式（OpenAI、DeepSeek等）
- **优雅降级**: 解析失败时自动使用文本提取和默认模板

### 3. 专业报告设计
- **咨询级别排版**: 采用专业咨询公司报告样式
- **响应式布局**: 侧边目录导航 + 主内容区域
- **图文并茂**: 自动识别数据并生成相应图表
- **多图表支持**: 线图、柱图、饼图等多种可视化

## 工作流架构

```
1_node(Trigger) → 2_node(Set_Enhanced) → 3_node(TavilySearch_Enhanced) → 4_node(OpenAI_Flexible) → 5_node(HTML_Universal)
```

### 节点详细说明

#### 2_node(Set_Enhanced).js
**增强版参数设置节点**
- 支持多维度搜索关键词生成
- 自动地域化搜索词配置
- 英文关键词补充
- 报告类型和关注领域配置

**主要功能:**
- 生成6个维度的搜索关键词（市场规模、竞争格局、发展趋势、政策环境、投资融资、技术创新）
- 根据地区自动调整搜索策略
- 输出结构化的搜索配置和报告配置

#### 3_node(TavilySearch_Enhanced).md
**智能搜索配置**
- 使用增强的搜索关键词组合
- 支持权威数据源域名过滤
- 提高搜索结果的相关性和权威性

#### 4_node(OpenAI_Flexible).md
**灵活AI分析节点**
- **System Prompt**: 设定资深行业分析师角色，拥有15年经验
- **核心要求**: 根据数据特点灵活组织报告结构
- **专业指导**: 判断重点分析方向和章节逻辑顺序
- **图表配置**: 自动为数据丰富的章节配置相应图表

**关键特性:**
- 无固化Schema限制
- 支持动态章节结构
- 智能图表配置
- 基于真实数据的分析

#### 5_node(HTML_Universal).js
**通用HTML生成器**
- **多格式兼容**: 支持OpenAI、DeepSeek等各种AI响应格式
- **智能解析**: JSON解析失败时自动文本提取
- **结构标准化**: 将任意输入转换为标准报告结构
- **图表自适应**: 根据数据类型自动选择图表类型

## 预期输出格式

AI可以根据实际情况灵活输出，但建议包含以下结构：

```json
{
  "report_meta": {
    "title": "具体行业分析报告",
    "subtitle": "基于2024年最新数据",
    "industry": "行业名称",
    "region": "分析地区"
  },
  "table_of_contents": [
    {"section": "executive_summary", "title": "执行摘要", "page": 1},
    // ... 其他章节
  ],
  "sections": {
    "executive_summary": {
      "title": "执行摘要",
      "content": "专业分析内容...",
      "key_points": ["要点1", "要点2"]
    },
    "market_overview": {
      "title": "市场概述",
      "content": "详细分析...",
      "subsections": {
        "market_size": {
          "title": "市场规模",
          "content": "具体内容...",
          "data_points": ["数据点1", "数据点2"]
        }
      },
      "chart_config": {
        "type": "line",
        "title": "市场趋势图",
        "data": {
          "labels": ["2022", "2023", "2024"],
          "values": [1200, 1450, 1680],
          "unit": "亿元"
        }
      }
    }
    // ... 其他章节根据数据情况灵活添加
  },
  "charts": [
    {
      "id": "market_trend",
      "type": "line",
      "title": "市场发展趋势",
      "section": "market_overview",
      "config": {
        "xAxis": ["2022", "2023", "2024", "2025E"],
        "yAxis": [1200, 1450, 1680, 1950],
        "unit": "亿元"
      }
    }
  ],
  "data_sources": ["数据来源列表"]
}
```

## 使用方法

1. **输入参数**:
   ```json
   {
     "industry": "人工智能",
     "region": "中国",
     "reportType": "comprehensive",
     "focusAreas": ["市场规模", "技术趋势"]
   }
   ```

2. **工作流执行**:
   - 自动生成多维度搜索关键词
   - 执行智能搜索获取最新数据
   - AI根据数据质量灵活组织报告结构
   - 生成专业的HTML可视化报告

3. **输出结果**:
   - 专业级别的HTML报告文件
   - 完整的报告数据结构
   - 执行元数据和统计信息

## 优势对比

| 特性 | 原版本 | 灵活版本 |
|------|--------|----------|
| 报告结构 | 固化Schema | 完全灵活 |
| AI创造性 | 受限 | 专业判断 |
| 适应性 | 单一模板 | 万能适配 |
| 图表配置 | 固定类型 | 智能选择 |
| 错误处理 | 容易失败 | 优雅降级 |
| 专业性 | 一般 | 咨询级别 |

## 技术特点

- **容错性强**: 多层级解析机制，确保始终能生成报告
- **扩展性好**: 支持新的AI模型和数据格式
- **维护性高**: 模块化设计，易于调试和优化
- **专业性强**: 基于真实咨询经验的设计理念

## 适用场景

- 各行各业的市场分析报告
- 投资决策支持报告
- 政策影响分析报告
- 技术趋势研究报告
- 竞争格局分析报告

这个灵活版本真正实现了"让AI像专业分析师一样思考"的目标，而不是简单地填充预设模板。 