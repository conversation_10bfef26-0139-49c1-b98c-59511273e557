// 测试目录显示修复效果
const fs = require('fs');

// 读取实际的temp.json数据
const tempDataArray = JSON.parse(fs.readFileSync('temp.json', 'utf8'));
const tempData = tempDataArray[0];

// 模拟n8n节点数据
global.$ = (nodeName) => {
    const mockData = {
        'Set': {
            item: {
                json: {
                    industry: "广州女淑装行业",
                    region: "中国"
                }
            }
        },
        'TavilySearch': {
            item: {
                json: {
                    results: []
                }
            }
        },
        'OpenAI': {
            item: {
                json: tempData
            }
        }
    };
    return mockData[nodeName];
};

// 模拟核心函数
function generateSectionTitle(key) {
    const titleMap = {
        executive_summary: '执行摘要',
        market_size: '市场规模分析', 
        marketSize: '市场规模分析',
        competitive_analysis: '竞争格局分析',
        competitiveLandscape: '竞争格局分析',
        trends: '发展趋势预测',
        technology: '技术分析',
        technologicalInnovation: '技术创新影响',
        challenges: '面临挑战',
        opportunities: '发展机遇',
        forecast: '未来预测',
        conclusions: '结论与建议',
        policy: '政策环境',
        investment: '投资分析',
        swot: 'SWOT分析'
    };
    
    return titleMap[key] || key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()) || '分析章节';
}

function generateTableOfContents(sections) {
    console.log('开始生成目录，sections:', Object.keys(sections));
    
    const toc = [];
    let pageNum = 1;
    
    Object.keys(sections).forEach(key => {
        const section = sections[key];
        const title = section.title || generateSectionTitle(key) || '未命名章节';
        
        const tocItem = {
            section: key,
            title: title,
            page: pageNum++
        };
        
        toc.push(tocItem);
        console.log('添加目录项:', tocItem);
    });
    
    console.log('生成目录完成，共', toc.length, '项');
    return toc;
}

// 测试目录生成
function testTOCGeneration() {
    console.log('=== 测试目录生成修复 ===\n');
    
    try {
        // 解析temp.json数据
        const aiData = tempData;
        let content = null;
        
        if (aiData?.output && typeof aiData.output === 'string') {
            console.log('✓ 检测到output字符串字段');
            content = JSON.parse(aiData.output);
        }
        
        // 提取章节
        const sections = {};
        
        if (content.executiveSummary) {
            sections.executive_summary = {
                title: '执行摘要',
                content: content.executiveSummary
            };
        }
        
        if (content.mainAnalysis) {
            Object.keys(content.mainAnalysis).forEach(key => {
                const analysis = content.mainAnalysis[key];
                sections[key] = {
                    title: generateSectionTitle(key),
                    content: analysis.description,
                    chart_config: analysis.chart
                };
            });
        }
        
        if (content.conclusionsAndRecommendations) {
            sections.conclusions = {
                title: '结论与建议',
                content: `结论：${content.conclusionsAndRecommendations.conclusions}\n\n建议：${content.conclusionsAndRecommendations.recommendations}`
            };
        }
        
        console.log('提取的章节:');
        Object.keys(sections).forEach(key => {
            console.log(`  - ${key}: ${sections[key].title}`);
        });
        
        // 生成目录
        const toc = generateTableOfContents(sections);
        
        console.log('\n生成的目录:');
        toc.forEach(item => {
            console.log(`  ${item.page}. ${item.title} (${item.section})`);
        });
        
        // 验证目录HTML生成
        console.log('\n目录HTML片段:');
        const tocHTML = toc.map(item => `
            <li class="toc-item p-2 rounded cursor-pointer" onclick="scrollToSection('${item.section}')">
                <span class="text-sm font-medium text-gray-700">${item.title || '未命名章节'}</span>
                <span class="text-xs text-gray-500 float-right">P${item.page || 1}</span>
            </li>
        `).join('');
        
        console.log(tocHTML);
        
        // 检查是否有undefined
        const hasUndefined = toc.some(item => 
            item.title.includes('undefined') || 
            item.title === 'undefined' ||
            !item.title
        );
        
        console.log('\n=== 测试结果 ===');
        console.log('✅ 目录项数量:', toc.length);
        console.log('✅ 是否包含undefined:', hasUndefined ? '❌ 是' : '✅ 否');
        console.log('✅ 所有标题都有值:', toc.every(item => item.title && item.title.trim()) ? '✅ 是' : '❌ 否');
        
        if (!hasUndefined && toc.length > 0) {
            console.log('\n🎉 目录生成测试通过！');
            
            // 保存测试HTML文件
            const testHTML = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>目录测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50">
    <div class="container mx-auto p-8">
        <h1 class="text-2xl font-bold mb-6">目录显示测试</h1>
        
        <!-- 目录 -->
        <nav class="w-1/3 bg-white p-6 rounded-lg shadow">
            <h2 class="text-lg font-semibold mb-4 text-gray-800">报告目录</h2>
            <ul class="space-y-2">
                ${tocHTML}
            </ul>
        </nav>
        
        <div class="mt-8 p-4 bg-green-100 rounded">
            <h3 class="font-bold text-green-800">测试结果</h3>
            <p class="text-green-700">目录项数量: ${toc.length}</p>
            <p class="text-green-700">包含undefined: ${hasUndefined ? '是' : '否'}</p>
        </div>
    </div>
    
    <script>
        function scrollToSection(sectionId) {
            console.log('点击章节:', sectionId);
            alert('点击了章节: ' + sectionId);
        }
    </script>
</body>
</html>`;
            
            fs.writeFileSync('test_toc_display.html', testHTML);
            console.log('✅ 测试HTML文件已保存: test_toc_display.html');
        } else {
            console.log('\n❌ 目录生成测试失败！');
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
}

// 执行测试
testTOCGeneration(); 