// 测试A4格式报告生成器
const fs = require('fs');

// 模拟n8n节点数据
const mockSetData = {
    industry: '人工智能',
    region: '中国'
};

const mockAiData = {
    output: `人工智能行业正迎来快速发展期，市场规模持续扩大。技术创新不断涌现，应用场景日益丰富。竞争格局呈现多元化特征，头部企业优势明显。未来发展前景广阔，但也面临技术挑战和监管压力。建议企业加强技术研发，优化产业布局。`,
    sections: {
        executive_summary: '人工智能行业执行摘要内容...',
        market_analysis: '人工智能市场分析内容...',
        competitive_landscape: '竞争格局分析内容...'
    }
};

// 模拟生成器函数
function generateA4Report() {
    console.log('=== 测试A4格式报告生成器 ===');
    
    // 生成充实的章节内容
    const sections = {
        'executive_summary': {
            title: '执行摘要',
            content: `人工智能行业在当前经济环境下展现出强劲的发展势头和广阔的市场前景。通过对行业数据的深入分析和市场调研，本报告发现该行业正处于快速发展期，市场规模持续扩大，技术创新不断涌现。从市场表现来看，人工智能行业在过去几年中保持了稳定的增长态势，年复合增长率超过行业平均水平。主要驱动因素包括消费升级、政策支持、技术进步等多重利好因素的叠加效应。

竞争格局方面，行业内企业分化明显，头部企业通过技术创新和规模优势不断巩固市场地位，中小企业则通过差异化定位和细分市场策略寻求发展空间。整体而言，行业竞争呈现出良性发展态势。展望未来，人工智能行业面临着数字化转型、绿色发展、国际化拓展等重大机遇，同时也需要应对技术变革、监管政策、市场竞争等挑战。

建议相关企业加强技术研发投入，优化产业链布局，提升核心竞争力。从长期发展角度来看，人工智能行业具备良好的发展基础和广阔的市场空间，行业内企业应当把握发展机遇，应对各种挑战。`,
            minLength: 300
        },
        'market_analysis': {
            title: '市场分析',
            content: `人工智能市场规模分析显示，该行业正经历快速发展阶段，市场容量不断扩大，参与主体日益多元化。根据最新市场数据，行业整体呈现出良好的发展态势和巨大的增长潜力。从市场结构来看，人工智能行业呈现出明显的分层特征，高端市场由技术领先、品牌影响力强的企业主导，中端市场竞争激烈，低端市场则以价格竞争为主。

消费者需求分析表明，市场对人工智能产品和服务的需求呈现出多样化、个性化、高品质化的趋势。消费者越来越注重产品的功能性、环保性和用户体验，这为行业创新发展提供了明确方向。区域市场分布方面，一线城市和发达地区仍是主要消费市场，但二三线城市的市场潜力正在快速释放。

随着城镇化进程的推进和消费水平的提升，下沉市场将成为行业增长的重要引擎。价格走势分析显示，人工智能行业整体价格水平保持相对稳定，但不同细分领域存在差异。高端产品价格稳中有升，中低端产品价格竞争激烈，行业整体向价值链高端发展的趋势明显。`,
            minLength: 400
        },
        'competitive_landscape': {
            title: '竞争格局分析',
            content: `人工智能行业竞争格局呈现出多元化、层次化的特征，市场参与者包括国际巨头、本土龙头企业、专业化公司和新兴创业企业等不同类型的竞争主体。头部企业凭借技术优势、品牌影响力和渠道资源，在市场中占据主导地位。这些企业通过持续的研发投入、并购整合和国际化扩张，不断强化竞争优势，市场份额进一步集中。

中等规模企业通过专业化定位和差异化战略，在细分市场中建立竞争优势。这些企业往往专注于特定领域或客户群体，通过深度服务和技术创新获得市场认可。新兴企业和初创公司则依托新技术、新模式，在传统企业较少涉足的领域寻求突破。

这些企业虽然规模较小，但创新能力强，发展速度快，为行业注入新的活力。国际竞争方面，跨国企业凭借技术、资本和管理优势在高端市场占据重要地位，本土企业则通过成本优势和本土化服务在中低端市场形成竞争力，国际化与本土化并存的格局日趋明显。`,
            minLength: 300
        }
    };
    
    const reportMeta = {
        title: '人工智能行业深度分析报告2025',
        subtitle: '专业行业研究报告',
        industry: '人工智能',
        region: '中国',
        date: '2025-01-27',
        author: 'AI行业分析团队'
    };
    
    const toc = Object.keys(sections).map((key, index) => ({
        section: key,
        title: sections[key].title,
        page: index + 1
    }));
    
    // 生成A4格式HTML
    const htmlContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${reportMeta.title}</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.8;
            color: #2c3e50;
            background: #ffffff;
            font-size: 16px;
        }
        
        /* A4页面容器 */
        .page-container {
            max-width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            position: relative;
        }
        
        /* 手机端适配 */
        @media screen and (max-width: 768px) {
            .page-container {
                max-width: 100%;
                margin: 0;
                box-shadow: none;
                min-height: auto;
            }
            body { font-size: 15px; line-height: 1.7; }
        }
        
        /* 报告头部 */
        .report-header {
            background: linear-gradient(135deg, #3498db 0%, #2c3e50 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .report-title {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 15px;
            line-height: 1.3;
        }
        
        .report-subtitle {
            font-size: 18px;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        .report-meta {
            font-size: 14px;
            opacity: 0.8;
            border-top: 1px solid rgba(255,255,255,0.2);
            padding-top: 15px;
        }
        
        /* 目录样式 */
        .table-of-contents {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 3px solid #3498db;
        }
        
        .toc-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .toc-list {
            list-style: none;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .toc-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 20px;
            margin-bottom: 8px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            text-decoration: none;
            color: #2c3e50;
            transition: all 0.3s ease;
        }
        
        .toc-item:hover {
            background: #ecf0f1;
            transform: translateX(5px);
        }
        
        .toc-page { color: #7f8c8d; font-weight: 500; }
        
        /* 内容区域 */
        .content-area { padding: 40px 30px; }
        
        .section {
            margin-bottom: 50px;
            page-break-inside: avoid;
        }
        
        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 3px solid #3498db;
            position: relative;
        }
        
        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            bottom: -3px;
            width: 60px;
            height: 3px;
            background: #e74c3c;
        }
        
        .section-content {
            font-size: 16px;
            line-height: 1.8;
            color: #34495e;
            text-align: justify;
        }
        
        .section-content p {
            margin-bottom: 18px;
            text-indent: 2em;
        }
        
        .section-content p:first-child {
            text-indent: 0;
            font-weight: 500;
        }
        
        /* 手机端内容适配 */
        @media screen and (max-width: 768px) {
            .report-header { padding: 25px 20px; }
            .report-title { font-size: 22px; }
            .report-subtitle { font-size: 16px; }
            .table-of-contents { padding: 20px 15px; }
            .toc-item { padding: 10px 15px; font-size: 14px; }
            .content-area { padding: 25px 20px; }
            .section { margin-bottom: 35px; }
            .section-title { font-size: 20px; margin-bottom: 20px; }
            .section-content { font-size: 15px; line-height: 1.7; }
            .section-content p { margin-bottom: 15px; text-indent: 1.5em; }
        }
        
        /* 数据来源 */
        .data-source {
            background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
            padding: 30px;
            margin-top: 40px;
            border-radius: 8px;
            border-left: 5px solid #3498db;
        }
        
        .data-source h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 18px;
        }
        
        /* 打印样式 */
        @media print {
            .page-container { max-width: none; box-shadow: none; margin: 0; }
            .section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <header class="report-header">
            <h1 class="report-title">${reportMeta.title}</h1>
            <div class="report-subtitle">${reportMeta.subtitle}</div>
            <div class="report-meta">
                行业：${reportMeta.industry} | 地区：${reportMeta.region} | 
                日期：${reportMeta.date} | 分析师：${reportMeta.author}
            </div>
        </header>

        <nav class="table-of-contents">
            <h2 class="toc-title">📋 报告目录</h2>
            <div class="toc-list">
                ${toc.map(item => `
                    <a href="#${item.section}" class="toc-item">
                        <span>${item.title}</span>
                        <span class="toc-page">第${item.page}章</span>
                    </a>
                `).join('')}
            </div>
        </nav>

        <main class="content-area">
            ${Object.keys(sections).map(sectionKey => {
                const section = sections[sectionKey];
                return `
                    <section id="${sectionKey}" class="section">
                        <h2 class="section-title">${section.title}</h2>
                        <div class="section-content">
                            ${section.content.split('\n\n').map(para => 
                                para.trim() ? `<p>${para.trim()}</p>` : ''
                            ).join('')}
                        </div>
                    </section>
                `;
            }).join('')}

            <div class="data-source">
                <h3>📊 数据来源与说明</h3>
                <p>本报告采用AI智能分析技术，结合多源数据，严格按照专业报告标准制作，每个章节内容充实，适合A4格式打印和手机阅读。报告内容仅供参考，具体投资决策请结合实际情况谨慎考虑。</p>
            </div>
        </main>
    </div>

    <script>
        // 平滑滚动导航
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ 
                        behavior: 'smooth', 
                        block: 'start' 
                    });
                }
            });
        });
    </script>
</body>
</html>`;
    
    // 保存测试文件
    fs.writeFileSync('test_a4_report.html', htmlContent, 'utf8');
    
    console.log('=== A4格式测试报告生成完成 ===');
    console.log('文件名: test_a4_report.html');
    console.log('章节数:', Object.keys(sections).length);
    
    // 验证内容长度
    Object.keys(sections).forEach(key => {
        const section = sections[key];
        const wordCount = section.content.length;
        const isLongEnough = wordCount >= section.minLength;
        console.log(`${section.title}: ${wordCount}字符 (要求: ${section.minLength}) ${isLongEnough ? '✅' : '❌'}`);
    });
    
    console.log('\n=== 格式特点 ===');
    console.log('✅ A4页面尺寸 (210mm × 297mm)');
    console.log('✅ 手机端响应式适配');
    console.log('✅ 专业报告样式');
    console.log('✅ 目录导航功能');
    console.log('✅ 打印友好设计');
    console.log('✅ 内容充实 (每章节200+字)');
    
    return {
        success: true,
        filename: 'test_a4_report.html',
        sections_count: Object.keys(sections).length,
        format: 'A4_mobile_optimized'
    };
}

// 运行测试
generateA4Report(); 