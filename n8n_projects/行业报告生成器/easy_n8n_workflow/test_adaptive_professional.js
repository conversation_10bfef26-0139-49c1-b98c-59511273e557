// 新一代灵活自适应专业报告生成器测试脚本
const fs = require('fs');

console.log('🧪 新一代灵活自适应专业报告生成器测试开始');

// 模拟n8n环境数据
global.$ = function(nodeName) {
    const mockData = {
        'Set': {
            item: {
                json: {
                    industry: '人工智能',
                    region: '中国',
                    reportType: 'comprehensive'
                }
            }
        },
        'TavilySearch': {
            item: {
                json: {
                    results: [
                        { title: 'AI市场规模分析', content: '人工智能市场规模持续增长，预计到2025年将达到1500亿元...' },
                        { title: '技术发展趋势', content: '深度学习、机器学习等技术不断突破，推动行业发展...' },
                        { title: '竞争格局分析', content: '头部企业占据主要市场份额，中小企业积极布局...' }
                    ]
                }
            }
        },
        'OpenAI': {
            item: {
                json: {
                    output: JSON.stringify({
                        reportTitle: '中国人工智能行业深度分析报告2025',
                        mainAnalysis: {
                            executive_summary: '人工智能行业正处于快速发展期，市场规模预计将达到1500亿元，年复合增长率超过25%。技术创新不断涌现，应用场景日益丰富，产业生态逐步完善。',
                            market_analysis: '中国人工智能市场规模从2020年的500亿元增长至2024年的1200亿元，预计2025年将突破1500亿元。计算机视觉、自然语言处理、机器学习等细分领域发展迅速。',
                            competitive_landscape: '市场呈现头部集中、百花齐放的格局。百度、阿里巴巴、腾讯等科技巨头占据主导地位，商汤科技、旷视科技等专业AI公司快速崛起，形成多层次竞争态势。',
                            industry_trends: '技术发展趋势包括大模型技术突破、多模态AI融合、边缘计算普及、AI芯片性能提升等。应用趋势体现在智能制造、自动驾驶、医疗健康、金融科技等领域深度渗透。',
                            challenges_opportunities: '主要挑战包括数据安全与隐私保护、算法公平性、人才短缺、技术标准不统一等。发展机遇体现在政策大力支持、市场需求旺盛、技术不断突破、资本持续投入等方面。',
                            future_outlook: '未来3-5年，人工智能将进入规模化应用阶段，预计市场规模年均增长率保持在20%以上。建议重点关注垂直领域应用、跨界融合创新、国际合作交流等发展方向。'
                        },
                        highlights: [
                            '市场规模预计2025年达1500亿元',
                            '年复合增长率超过25%',
                            '头部企业市场份额占比45%',
                            '专利申请数量全球第一',
                            '投资金额同比增长35%'
                        ]
                    })
                }
            }
        }
    };
    
    return mockData[nodeName] || { item: { json: {} } };
};

try {
    // 读取并执行新一代自适应报告生成器
    const generatorCode = fs.readFileSync('5_node(HTML_Adaptive_Professional).js', 'utf8');
    
    // 执行代码
    const result = eval(`(function() {
        ${generatorCode}
    })()`);
    
    console.log('\n✅ 测试结果:');
    console.log('📊 生成成功:', result.success);
    
    if (result.success) {
        console.log('🎯 识别行业:', result.metadata.industry);
        console.log('📝 报告标题:', result.metadata.title);
        console.log('📄 章节数量:', result.metadata.sectionsCount);
        console.log('📈 图表数量:', result.metadata.chartsCount);
        console.log('⏰ 生成时间:', result.metadata.generatedAt);
        
        // 保存生成的HTML文件
        fs.writeFileSync('test_adaptive_report.html', result.html);
        console.log('💾 报告已保存为: test_adaptive_report.html');
        
        // 验证HTML结构
        const htmlContent = result.html;
        
        console.log('\n🔍 HTML结构验证:');
        console.log('✅ 包含DOCTYPE:', htmlContent.includes('<!DOCTYPE html>'));
        console.log('✅ 包含meta标签:', htmlContent.includes('<meta charset="UTF-8">'));
        console.log('✅ 包含ECharts库:', htmlContent.includes('echarts@5.4.3'));
        console.log('✅ 包含CSS变量:', htmlContent.includes('--primary-color'));
        console.log('✅ 包含响应式设计:', htmlContent.includes('@media (max-width: 768px)'));
        console.log('✅ 包含图表脚本:', htmlContent.includes('echarts.init'));
        
        // 验证内容特征
        console.log('\n📊 内容特征验证:');
        console.log('✅ 行业特色配色:', htmlContent.includes('#1e40af')); // AI行业蓝色
        console.log('✅ 智能目录生成:', htmlContent.includes('toc-list'));
        console.log('✅ 章节内容完整:', htmlContent.includes('section-content'));
        console.log('✅ 图表容器存在:', htmlContent.includes('chart-canvas'));
        console.log('✅ 核心要点展示:', htmlContent.includes('highlights-section'));
        
        // 验证自适应特性
        console.log('\n🎨 自适应特性验证:');
        console.log('✅ 动态配色系统:', htmlContent.includes('var(--primary-color)'));
        console.log('✅ 智能布局调整:', htmlContent.includes('content-grid'));
        console.log('✅ 响应式图表:', htmlContent.includes('window.addEventListener'));
        console.log('✅ 移动端适配:', htmlContent.includes('grid-template-columns: 1fr !important'));
        
        console.log('\n🎉 新一代灵活自适应专业报告生成器测试通过！');
        
    } else {
        console.log('❌ 生成失败:', result.error);
    }
    
} catch (error) {
    console.error('❌ 测试执行失败:', error.message);
    console.error('详细错误:', error.stack);
}

console.log('\n📋 测试总结:');
console.log('- 基于Sample Reports深度分析设计');
console.log('- 完全内容驱动，不固化模板');
console.log('- 智能行业识别和配色适配');
console.log('- 自适应图表类型选择');
console.log('- 响应式布局设计');
console.log('- 专业级视觉效果');
console.log('- 强大的容错机制');

console.log('\n🚀 新一代报告生成器特色:');
console.log('1. 🎯 智能行业识别 - 8大行业主题自动适配');
console.log('2. 📊 自适应图表系统 - 根据内容智能选择图表类型');
console.log('3. 🎨 动态配色方案 - 行业特色色彩自动应用');
console.log('4. 📱 完全响应式设计 - 完美适配各种设备');
console.log('5. 🔍 智能内容解析 - 多格式数据兼容');
console.log('6. ⚡ 高性能渲染 - 优化的HTML结构和CSS');
console.log('7. 🛡️ 强大容错机制 - 确保稳定性');
console.log('8. 🎪 专业视觉效果 - 咨询级别报告标准'); 