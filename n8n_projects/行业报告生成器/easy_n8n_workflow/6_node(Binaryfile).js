// 获取HTML内容，支持多种数据结构
let html;
if (items[0].json.output) {
  html = items[0].json.output;
} else if (items[0].json.html) {
  html = items[0].json.html;
} else if (items[0].json.data) {
  html = items[0].json.data;
} else {
  // 如果都没有，尝试获取整个json作为字符串
  html = JSON.stringify(items[0].json);
}

// 确保html是字符串类型
if (typeof html !== 'string') {
  html = String(html);
}

// 移除Markdown代码块标记并清理内容
const cleanHtml = html.replace(/^```html\s*|```\s*$/g, "").trim();
// 创建二进制数据
return {
  json: {
    data: cleanHtml
  },
  binary: {
    data: {
      data: Buffer.from(cleanHtml).toString('base64'),
      mimeType: 'text/html',
      fileName: 'report.html'
    }
  }
};