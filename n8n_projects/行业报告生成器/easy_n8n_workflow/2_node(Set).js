// Get industry keyword from webhook input
const webhookData = $input.item.json;
const chatInput = webhookData.industry || webhookData.chatInput || "";

// Clean and normalize the keyword
const rawIndustry = chatInput.trim();

// Basic input validation
if (!rawIndustry) {
  throw new Error("Please provide a valid industry keyword in 'industry' field");
}

// Sanitize input: remove special characters and normalize spaces
const industry = rawIndustry
  .replace(/[^\w\s\u4e00-\u9fff]/g, '') // Keep only word chars, spaces, and Chinese characters
  .replace(/\s+/g, ' ') // Normalize spaces
  .trim();

// Validate sanitized input
if (industry.length < 2) {
  throw new Error("Industry keyword must be at least 2 characters long");
}

// Generate comprehensive search variants
const searchVariants = [
  // Market analysis variants
  `${industry} 市场规模`,
  `${industry} 行业分析报告`,
  `${industry} 产业分析`,
  `${industry} 市场分析`,
  
  // Competition analysis variants
  `${industry} 竞争格局`,
  `${industry} 竞争分析`,
  `${industry} 主要企业`,
  `${industry} 市场份额`,
  
  // Development trend variants
  `${industry} 发展趋势`,
  `${industry} 未来展望`,
  `${industry} 前景分析`,
  `${industry} 最新动态`,
  
  // Data and statistics variants
  `${industry} 最新数据`,
  `${industry} 统计数据`,
  `${industry} 行业数据`,
  `${industry} 市场容量`
];

// Add English variants if the input contains English characters
if (/[a-zA-Z]/.test(industry)) {
  const englishVariants = [
    `${industry} market size`,
    `${industry} industry analysis`,
    `${industry} market analysis`,
    `${industry} competitive landscape`,
    `${industry} market share`,
    `${industry} industry trends`,
    `${industry} future outlook`,
    `${industry} statistics`,
    `${industry} market data`
  ];
  searchVariants.push(...englishVariants);
}

// Return the processed data
return [{
  json: {
    industry: industry,
    searchVariants: searchVariants,
    timestamp: new Date().toISOString(),
    metadata: {
      originalInput: rawIndustry,
      sanitized: industry !== rawIndustry,
      hasEnglish: /[a-zA-Z]/.test(industry),
      variantCount: searchVariants.length
    }
  }
}];