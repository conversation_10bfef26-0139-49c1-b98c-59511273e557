# --- Google Cloud Run 部署修复指南 ---
#
# 关键概念：您本地电脑的端口占用（如 8080 被占用）与 Google Cloud Run 的部署完全无关。
# Cloud Run 是一个独立的云环境，您可以在上面自由使用任何端口，如 8080。
#
# 失败原因：您遇到的失败是因为 Cloud Run 的健康检查配置错误，而不是端口冲突。
#
# 如何修复：
# 1. 编辑您在 Google Cloud Run 上的服务。
# 2. 找到“容器” > “容器端口”设置，将其设置为 8080。
#    (因为您提到别人使用 8080 成功了，这表明容器内部监听的是 8080 端口)。
# 3. 检查“变量与密钥”部分，删除任何您手动添加的名为 `PORT` 的环境变量。
# 4. 保存并重新部署。服务应该就能成功启动。
#
# --- 本地 Docker 环境配置 (参考) ---

version: '3.7'

services:
  nca:
    image: nocodearchitect/nca-ce:latest
    container_name: nca
    restart: always
    ports:
      # 本地部署时，我们将主机的 8081 端口映射到容器的 8080 端口。
      # 格式为 "主机端口:容器端口"
      - "8081:8080"
    volumes:
      - ./data:/app/data
    environment:
      - TZ=Asia/Shanghai
