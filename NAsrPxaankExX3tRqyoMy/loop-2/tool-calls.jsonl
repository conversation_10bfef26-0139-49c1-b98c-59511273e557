[{"toolCallId": "call_0_406fd401-e536-43fe-b96e-bb183c5b4860", "name": "browser_get_markdown", "args": {}, "result": [{"type": "text", "text": "                Trending repositories on GitHub today · GitHub                                                                 \n\n[Skip to content](#start-of-content)   \n\n## Navigation Menu\n\nToggle navigation\n\n[](/)\n\n[Sign in](/login?return_to=https%3A%2F%2Fgithub.com%2Ftrending)\n\nAppearance settings   \n\n*   Product\n    \n    *   [\n        \n        GitHub Copilot\n        \n        Write better code with AI\n        \n        ](https://github.com/features/copilot)\n    *   [\n        \n        GitHub Models New\n        \n        Manage and compare prompts\n        \n        ](https://github.com/features/models)\n    *   [\n        \n        GitHub Advanced Security\n        \n        Find and fix vulnerabilities\n        \n        ](https://github.com/security/advanced-security)\n    *   [\n        \n        Actions\n        \n        Automate any workflow\n        \n        ](https://github.com/features/actions)\n    *   [\n        \n        Codespaces\n        \n        Instant dev environments\n        \n        ](https://github.com/features/codespaces)\n    \n    *   [\n        \n        Issues\n        \n        Plan and track work\n        \n        ](https://github.com/features/issues)\n    *   [\n        \n        Code Review\n        \n        Manage code changes\n        \n        ](https://github.com/features/code-review)\n    *   [\n        \n        Discussions\n        \n        Collaborate outside of code\n        \n        ](https://github.com/features/discussions)\n    *   [\n        \n        Code Search\n        \n        Find more, search less\n        \n        ](https://github.com/features/code-search)\n    \n    Explore\n    \n    *   [Why GitHub](https://github.com/why-github)\n    *   [All features](https://github.com/features)\n    *   [Documentation](https://docs.github.com)\n    *   [GitHub Skills](https://skills.github.com)\n    *   [Blog](https://github.blog)\n    \n*   Solutions\n    \n    By company size\n    \n    *   [Enterprises](https://github.com/enterprise)\n    *   [Small and medium teams](https://github.com/team)\n    *   [Startups](https://github.com/enterprise/startups)\n    *   [Nonprofits](/solutions/industry/nonprofits)\n    \n    By use case\n    \n    *   [DevSecOps](/solutions/use-case/devsecops)\n    *   [DevOps](/solutions/use-case/devops)\n    *   [CI/CD](/solutions/use-case/ci-cd)\n    *   [View all use cases](/solutions/use-case)\n    \n    By industry\n    \n    *   [Healthcare](/solutions/industry/healthcare)\n    *   [Financial services](/solutions/industry/financial-services)\n    *   [Manufacturing](/solutions/industry/manufacturing)\n    *   [Government](/solutions/industry/government)\n    *   [View all industries](/solutions/industry)\n    \n    [View all solutions](/solutions)\n    \n*   Resources\n    \n    Topics\n    \n    *   [AI](/resources/articles/ai)\n    *   [DevOps](/resources/articles/devops)\n    *   [Security](/resources/articles/security)\n    *   [Software Development](/resources/articles/software-development)\n    *   [View all](/resources/articles)\n    \n    Explore\n    \n    *   [Learning Pathways](https://resources.github.com/learn/pathways)\n    *   [Events & Webinars](https://resources.github.com)\n    *   [Ebooks & Whitepapers](https://github.com/resources/whitepapers)\n    *   [Customer Stories](https://github.com/customer-stories)\n    *   [Partners](https://partner.github.com)\n    *   [Executive Insights](https://github.com/solutions/executive-insights)\n    \n*   Open Source\n    \n    *   [\n        \n        GitHub Sponsors\n        \n        Fund open source developers\n        \n        ](/sponsors)\n    \n    *   [\n        \n        The ReadME Project\n        \n        GitHub community articles\n        \n        ](https://github.com/readme)\n    \n    Repositories\n    \n    *   [Topics](https://github.com/topics)\n    *   [Trending](https://github.com/trending)\n    *   [Collections](https://github.com/collections)\n    \n*   Enterprise\n    \n    *   [\n        \n        Enterprise platform\n        \n        AI-powered developer platform\n        \n        ](/enterprise)\n    \n    Available add-ons\n    \n    *   [\n        \n        GitHub Advanced Security\n        \n        Enterprise-grade security features\n        \n        ](https://github.com/security/advanced-security)\n    *   [\n        \n        Copilot for business\n        \n        Enterprise-grade AI features\n        \n        ](/features/copilot/copilot-business)\n    *   [\n        \n        Premium Support\n        \n        Enterprise-grade 24/7 support\n        \n        ](/premium-support)\n    \n*   [Pricing](https://github.com/pricing)\n\nSearch or jump to...\n\n# Search code, repositories, users, issues, pull requests...\n\nSearch\n\nClear\n\n[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)\n\n# Provide feedback\n\nWe read every piece of feedback, and take your input very seriously.\n\n Include my email address so I can be contacted\n\nCancel Submit feedback\n\n# Saved searches\n\n## Use saved searches to filter your results more quickly\n\nName  \n\nQuery \n\nTo see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax).\n\nCancel Create saved search\n\n[Sign in](/login?return_to=https%3A%2F%2Fgithub.com%2Ftrending)\n\n[Sign up](/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2Ftrending&source=header)\n\nAppearance settings   \n\nResetting focus\n\nYou signed in with another tab or window. Reload to refresh your session. You signed out in another tab or window. Reload to refresh your session. You switched accounts on another tab or window. Reload to refresh your session. Dismiss alert\n\n[Explore](/explore) [Topics](/topics) [Trending](/trending) [Collections](/collections) [Events](/events) [GitHub Sponsors](/sponsors/explore)\n\n# Trending\n\nSee what the GitHub community is most excited about today.\n\n[Repositories](/trending) [Developers](/trending/developers)\n\nSpoken Language: Any\n\nSelect a spoken language\n\n[Abkhazian](/trending?spoken_language_code=ab) [Afar](/trending?spoken_language_code=aa) [Afrikaans](/trending?spoken_language_code=af) [Akan](/trending?spoken_language_code=ak) [Albanian](/trending?spoken_language_code=sq) [Amharic](/trending?spoken_language_code=am) [Arabic](/trending?spoken_language_code=ar) [Aragonese](/trending?spoken_language_code=an) [Armenian](/trending?spoken_language_code=hy) [Assamese](/trending?spoken_language_code=as) [Avaric](/trending?spoken_language_code=av) [Avestan](/trending?spoken_language_code=ae) [Aymara](/trending?spoken_language_code=ay) [Azerbaijani](/trending?spoken_language_code=az) [Bambara](/trending?spoken_language_code=bm) [Bashkir](/trending?spoken_language_code=ba) [Basque](/trending?spoken_language_code=eu) [Belarusian](/trending?spoken_language_code=be) [Bengali](/trending?spoken_language_code=bn) [Bihari languages](/trending?spoken_language_code=bh) [Bislama](/trending?spoken_language_code=bi) [Bosnian](/trending?spoken_language_code=bs) [Breton](/trending?spoken_language_code=br) [Bulgarian](/trending?spoken_language_code=bg) [Burmese](/trending?spoken_language_code=my) [Catalan, Valencian](/trending?spoken_language_code=ca) [Chamorro](/trending?spoken_language_code=ch) [Chechen](/trending?spoken_language_code=ce) [Chichewa, Chewa, Nyanja](/trending?spoken_language_code=ny) [Chinese](/trending?spoken_language_code=zh) [Chuvash](/trending?spoken_language_code=cv) [Cornish](/trending?spoken_language_code=kw) [Corsican](/trending?spoken_language_code=co) [Cree](/trending?spoken_language_code=cr) [Croatian](/trending?spoken_language_code=hr) [Czech](/trending?spoken_language_code=cs) [Danish](/trending?spoken_language_code=da) [Divehi, Dhivehi, Maldivian](/trending?spoken_language_code=dv) [Dutch, Flemish](/trending?spoken_language_code=nl) [Dzongkha](/trending?spoken_language_code=dz) [English](/trending?spoken_language_code=en) [Esperanto](/trending?spoken_language_code=eo) [Estonian](/trending?spoken_language_code=et) [Ewe](/trending?spoken_language_code=ee) [Faroese](/trending?spoken_language_code=fo) [Fijian](/trending?spoken_language_code=fj) [Finnish](/trending?spoken_language_code=fi) [French](/trending?spoken_language_code=fr) [Fulah](/trending?spoken_language_code=ff) [Galician](/trending?spoken_language_code=gl) [Georgian](/trending?spoken_language_code=ka) [German](/trending?spoken_language_code=de) [Greek, Modern](/trending?spoken_language_code=el) [Guarani](/trending?spoken_language_code=gn) [Gujarati](/trending?spoken_language_code=gu) [Haitian, Haitian Creole](/trending?spoken_language_code=ht) [Hausa](/trending?spoken_language_code=ha) [Hebrew](/trending?spoken_language_code=he) [Herero](/trending?spoken_language_code=hz) [Hindi](/trending?spoken_language_code=hi) [Hiri Motu](/trending?spoken_language_code=ho) [Hungarian](/trending?spoken_language_code=hu) [Interlingua (International Auxil...](/trending?spoken_language_code=ia) [Indonesian](/trending?spoken_language_code=id) [Interlingue, Occidental](/trending?spoken_language_code=ie) [Irish](/trending?spoken_language_code=ga) [Igbo](/trending?spoken_language_code=ig) [Inupiaq](/trending?spoken_language_code=ik) [Ido](/trending?spoken_language_code=io) [Icelandic](/trending?spoken_language_code=is) [Italian](/trending?spoken_language_code=it) [Inuktitut](/trending?spoken_language_code=iu) [Japanese](/trending?spoken_language_code=ja) [Javanese](/trending?spoken_language_code=jv) [Kalaallisut, Greenlandic](/trending?spoken_language_code=kl) [Kannada](/trending?spoken_language_code=kn) [Kanuri](/trending?spoken_language_code=kr) [Kashmiri](/trending?spoken_language_code=ks) [Kazakh](/trending?spoken_language_code=kk) [Central Khmer](/trending?spoken_language_code=km) [Kikuyu, Gikuyu](/trending?spoken_language_code=ki) [Kinyarwanda](/trending?spoken_language_code=rw) [Kirghiz, Kyrgyz](/trending?spoken_language_code=ky) [Komi](/trending?spoken_language_code=kv) [Kongo](/trending?spoken_language_code=kg) [Korean](/trending?spoken_language_code=ko) [Kurdish](/trending?spoken_language_code=ku) [Kuanyama, Kwanyama](/trending?spoken_language_code=kj) [Latin](/trending?spoken_language_code=la) [Luxembourgish, Letzeburgesch](/trending?spoken_language_code=lb) [Ganda](/trending?spoken_language_code=lg) [Limburgan, Limburger, Limburgish](/trending?spoken_language_code=li) [Lingala](/trending?spoken_language_code=ln) [Lao](/trending?spoken_language_code=lo) [Lithuanian](/trending?spoken_language_code=lt) [Luba-Katanga](/trending?spoken_language_code=lu) [Latvian](/trending?spoken_language_code=lv) [Manx](/trending?spoken_language_code=gv) [Macedonian](/trending?spoken_language_code=mk) [Malagasy](/trending?spoken_language_code=mg) [Malay](/trending?spoken_language_code=ms) [Malayalam](/trending?spoken_language_code=ml) [Maltese](/trending?spoken_language_code=mt) [Maori](/trending?spoken_language_code=mi) [Marathi](/trending?spoken_language_code=mr) [Marshallese](/trending?spoken_language_code=mh) [Mongolian](/trending?spoken_language_code=mn) [Nauru](/trending?spoken_language_code=na) [Navajo, Navaho](/trending?spoken_language_code=nv) [North Ndebele](/trending?spoken_language_code=nd) [Nepali](/trending?spoken_language_code=ne) [Ndonga](/trending?spoken_language_code=ng) [Norwegian Bokmål](/trending?spoken_language_code=nb) [Norwegian Nynorsk](/trending?spoken_language_code=nn) [Norwegian](/trending?spoken_language_code=no) [Sichuan Yi, Nuosu](/trending?spoken_language_code=ii) [South Ndebele](/trending?spoken_language_code=nr) [Occitan](/trending?spoken_language_code=oc) [Ojibwa](/trending?spoken_language_code=oj) [Church Slavic, Old Slavonic, Chu...](/trending?spoken_language_code=cu) [Oromo](/trending?spoken_language_code=om) [Oriya](/trending?spoken_language_code=or) [Ossetian, Ossetic](/trending?spoken_language_code=os) [Punjabi, Panjabi](/trending?spoken_language_code=pa) [Pali](/trending?spoken_language_code=pi) [Persian](/trending?spoken_language_code=fa) [Polish](/trending?spoken_language_code=pl) [Pashto, Pushto](/trending?spoken_language_code=ps) [Portuguese](/trending?spoken_language_code=pt) [Quechua](/trending?spoken_language_code=qu) [Romansh](/trending?spoken_language_code=rm) [Rundi](/trending?spoken_language_code=rn) [Romanian, Moldavian, Moldovan](/trending?spoken_language_code=ro) [Russian](/trending?spoken_language_code=ru) [Sanskrit](/trending?spoken_language_code=sa) [Sardinian](/trending?spoken_language_code=sc) [Sindhi](/trending?spoken_language_code=sd) [Northern Sami](/trending?spoken_language_code=se) [Samoan](/trending?spoken_language_code=sm) [Sango](/trending?spoken_language_code=sg) [Serbian](/trending?spoken_language_code=sr) [Gaelic, Scottish Gaelic](/trending?spoken_language_code=gd) [Shona](/trending?spoken_language_code=sn) [Sinhala, Sinhalese](/trending?spoken_language_code=si) [Slovak](/trending?spoken_language_code=sk) [Slovenian](/trending?spoken_language_code=sl) [Somali](/trending?spoken_language_code=so) [Southern Sotho](/trending?spoken_language_code=st) [Spanish, Castilian](/trending?spoken_language_code=es) [Sundanese](/trending?spoken_language_code=su) [Swahili](/trending?spoken_language_code=sw) [Swati](/trending?spoken_language_code=ss) [Swedish](/trending?spoken_language_code=sv) [Tamil](/trending?spoken_language_code=ta) [Telugu](/trending?spoken_language_code=te) [Tajik](/trending?spoken_language_code=tg) [Thai](/trending?spoken_language_code=th) [Tigrinya](/trending?spoken_language_code=ti) [Tibetan](/trending?spoken_language_code=bo) [Turkmen](/trending?spoken_language_code=tk) [Tagalog](/trending?spoken_language_code=tl) [Tswana](/trending?spoken_language_code=tn) [Tonga (Tonga Islands)](/trending?spoken_language_code=to) [Turkish](/trending?spoken_language_code=tr) [Tsonga](/trending?spoken_language_code=ts) [Tatar](/trending?spoken_language_code=tt) [Twi](/trending?spoken_language_code=tw) [Tahitian](/trending?spoken_language_code=ty) [Uighur, Uyghur](/trending?spoken_language_code=ug) [Ukrainian](/trending?spoken_language_code=uk) [Urdu](/trending?spoken_language_code=ur) [Uzbek](/trending?spoken_language_code=uz) [Venda](/trending?spoken_language_code=ve) [Vietnamese](/trending?spoken_language_code=vi) [Volapük](/trending?spoken_language_code=vo) [Walloon](/trending?spoken_language_code=wa) [Welsh](/trending?spoken_language_code=cy) [Wolof](/trending?spoken_language_code=wo) [Western Frisian](/trending?spoken_language_code=fy) [Xhosa](/trending?spoken_language_code=xh) [Yiddish](/trending?spoken_language_code=yi) [Yoruba](/trending?spoken_language_code=yo) [Zhuang, Chuang](/trending?spoken_language_code=za) [Zulu](/trending?spoken_language_code=zu)\n\nLoading\n\nLanguage: Any\n\nSelect a language\n\n[Unknown languages](/trending/unknown?since=daily) [1C Enterprise](/trending/1c-enterprise?since=daily) [2-Dimensional Array](/trending/2-dimensional-array?since=daily) [4D](/trending/4d?since=daily) [ABAP](/trending/abap?since=daily) [ABAP CDS](/trending/abap-cds?since=daily) [ABNF](/trending/abnf?since=daily) [ActionScript](/trending/actionscript?since=daily) [Ada](/trending/ada?since=daily) [Adblock Filter List](/trending/adblock-filter-list?since=daily) [Adobe Font Metrics](/trending/adobe-font-metrics?since=daily) [Agda](/trending/agda?since=daily) [AGS Script](/trending/ags-script?since=daily) [AIDL](/trending/aidl?since=daily) [Aiken](/trending/aiken?since=daily) [AL](/trending/al?since=daily) [Alloy](/trending/alloy?since=daily) [Alpine Abuild](/trending/alpine-abuild?since=daily) [Altium Designer](/trending/altium-designer?since=daily) [AMPL](/trending/ampl?since=daily) [AngelScript](/trending/angelscript?since=daily) [Answer Set Programming](/trending/answer-set-programming?since=daily) [Ant Build System](/trending/ant-build-system?since=daily) [Antlers](/trending/antlers?since=daily) [ANTLR](/trending/antlr?since=daily) [ApacheConf](/trending/apacheconf?since=daily) [Apex](/trending/apex?since=daily) [API Blueprint](/trending/api-blueprint?since=daily) [APL](/trending/apl?since=daily) [Apollo Guidance Computer](/trending/apollo-guidance-computer?since=daily) [AppleScript](/trending/applescript?since=daily) [Arc](/trending/arc?since=daily) [AsciiDoc](/trending/asciidoc?since=daily) [ASL](/trending/asl?since=daily) [ASN.1](/trending/asn.1?since=daily) [Classic ASP](/trending/classic-asp?since=daily) [ASP.NET](/trending/asp.net?since=daily) [AspectJ](/trending/aspectj?since=daily) [Assembly](/trending/assembly?since=daily) [Astro](/trending/astro?since=daily) [Asymptote](/trending/asymptote?since=daily) [ATS](/trending/ats?since=daily) [Augeas](/trending/augeas?since=daily) [AutoHotkey](/trending/autohotkey?since=daily) [AutoIt](/trending/autoit?since=daily) [Avro IDL](/trending/avro-idl?since=daily) [Awk](/trending/awk?since=daily) [B4X](/trending/b4x?since=daily) [Ballerina](/trending/ballerina?since=daily) [BASIC](/trending/basic?since=daily) [Batchfile](/trending/batchfile?since=daily) [Beef](/trending/beef?since=daily) [Befunge](/trending/befunge?since=daily) [Berry](/trending/berry?since=daily) [BibTeX](/trending/bibtex?since=daily) [BibTeX Style](/trending/bibtex-style?since=daily) [Bicep](/trending/bicep?since=daily) [Bikeshed](/trending/bikeshed?since=daily) [Bison](/trending/bison?since=daily) [BitBake](/trending/bitbake?since=daily) [Blade](/trending/blade?since=daily) [BlitzBasic](/trending/blitzbasic?since=daily) [BlitzMax](/trending/blitzmax?since=daily) [Bluespec](/trending/bluespec?since=daily) [Bluespec BH](/trending/bluespec-bh?since=daily) [Boo](/trending/boo?since=daily) [Boogie](/trending/boogie?since=daily) [BQN](/trending/bqn?since=daily) [Brainfuck](/trending/brainfuck?since=daily) [BrighterScript](/trending/brighterscript?since=daily) [Brightscript](/trending/brightscript?since=daily) [Zeek](/trending/zeek?since=daily) [Browserslist](/trending/browserslist?since=daily) [BuildStream](/trending/buildstream?since=daily) [C](/trending/c?since=daily) [C#](/trending/c%23?since=daily) [C++](/trending/c++?since=daily) [C-ObjDump](/trending/c-objdump?since=daily) [C2hs Haskell](/trending/c2hs-haskell?since=daily) [Cabal Config](/trending/cabal-config?since=daily) [Caddyfile](/trending/caddyfile?since=daily) [Cadence](/trending/cadence?since=daily) [Cairo](/trending/cairo?since=daily) [Cairo Zero](/trending/cairo-zero?since=daily) [CameLIGO](/trending/cameligo?since=daily) [CAP CDS](/trending/cap-cds?since=daily) [Cap'n Proto](/trending/cap'n-proto?since=daily) [Carbon](/trending/carbon?since=daily) [CartoCSS](/trending/cartocss?since=daily) [Ceylon](/trending/ceylon?since=daily) [Chapel](/trending/chapel?since=daily) [Charity](/trending/charity?since=daily) [Checksums](/trending/checksums?since=daily) [ChucK](/trending/chuck?since=daily) [CIL](/trending/cil?since=daily) [Circom](/trending/circom?since=daily) [Cirru](/trending/cirru?since=daily) [Clarion](/trending/clarion?since=daily) [Clarity](/trending/clarity?since=daily) [Classic ASP](/trending/classic-asp?since=daily) [Clean](/trending/clean?since=daily) [Click](/trending/click?since=daily) [CLIPS](/trending/clips?since=daily) [Clojure](/trending/clojure?since=daily) [Closure Templates](/trending/closure-templates?since=daily) [Cloud Firestore Security Rules](/trending/cloud-firestore-security-rules?since=daily) [Clue](/trending/clue?since=daily) [CMake](/trending/cmake?since=daily) [COBOL](/trending/cobol?since=daily) [CODEOWNERS](/trending/codeowners?since=daily) [CodeQL](/trending/codeql?since=daily) [CoffeeScript](/trending/coffeescript?since=daily) [ColdFusion](/trending/coldfusion?since=daily) [ColdFusion CFC](/trending/coldfusion-cfc?since=daily) [COLLADA](/trending/collada?since=daily) [Common Lisp](/trending/common-lisp?since=daily) [Common Workflow Language](/trending/common-workflow-language?since=daily) [Component Pascal](/trending/component-pascal?since=daily) [CoNLL-U](/trending/conll-u?since=daily) [Cool](/trending/cool?since=daily) [Rocq Prover](/trending/rocq-prover?since=daily) [Cpp-ObjDump](/trending/cpp-objdump?since=daily) [Creole](/trending/creole?since=daily) [crontab](/trending/crontab?since=daily) [Crystal](/trending/crystal?since=daily) [CSON](/trending/cson?since=daily) [Csound](/trending/csound?since=daily) [Csound Document](/trending/csound-document?since=daily) [Csound Score](/trending/csound-score?since=daily) [CSS](/trending/css?since=daily) [CSV](/trending/csv?since=daily) [Cuda](/trending/cuda?since=daily) [CUE](/trending/cue?since=daily) [Cue Sheet](/trending/cue-sheet?since=daily) [cURL Config](/trending/curl-config?since=daily) [Curry](/trending/curry?since=daily) [CWeb](/trending/cweb?since=daily) [Cycript](/trending/cycript?since=daily) [Cylc](/trending/cylc?since=daily) [Cypher](/trending/cypher?since=daily) [Cython](/trending/cython?since=daily) [D](/trending/d?since=daily) [D-ObjDump](/trending/d-objdump?since=daily) [D2](/trending/d2?since=daily) [Dafny](/trending/dafny?since=daily) [Darcs Patch](/trending/darcs-patch?since=daily) [Dart](/trending/dart?since=daily) [Daslang](/trending/daslang?since=daily) [DataWeave](/trending/dataweave?since=daily) [Debian Package Control File](/trending/debian-package-control-file?since=daily) [DenizenScript](/trending/denizenscript?since=daily) [desktop](/trending/desktop?since=daily) [Dhall](/trending/dhall?since=daily) [Diff](/trending/diff?since=daily) [DIGITAL Command Language](/trending/digital-command-language?since=daily) [dircolors](/trending/dircolors?since=daily) [DirectX 3D File](/trending/directx-3d-file?since=daily) [DM](/trending/dm?since=daily) [DNS Zone](/trending/dns-zone?since=daily) [Dockerfile](/trending/dockerfile?since=daily) [Dogescript](/trending/dogescript?since=daily) [Dotenv](/trending/dotenv?since=daily) [DTrace](/trending/dtrace?since=daily) [Dune](/trending/dune?since=daily) [Dylan](/trending/dylan?since=daily) [E](/trending/e?since=daily) [E-mail](/trending/e-mail?since=daily) [Eagle](/trending/eagle?since=daily) [Earthly](/trending/earthly?since=daily) [Easybuild](/trending/easybuild?since=daily) [EBNF](/trending/ebnf?since=daily) [eC](/trending/ec?since=daily) [Ecere Projects](/trending/ecere-projects?since=daily) [ECL](/trending/ecl?since=daily) [ECLiPSe](/trending/eclipse?since=daily) [Ecmarkup](/trending/ecmarkup?since=daily) [Edge](/trending/edge?since=daily) [EdgeQL](/trending/edgeql?since=daily) [EditorConfig](/trending/editorconfig?since=daily) [Edje Data Collection](/trending/edje-data-collection?since=daily) [edn](/trending/edn?since=daily) [Eiffel](/trending/eiffel?since=daily) [EJS](/trending/ejs?since=daily) [Elixir](/trending/elixir?since=daily) [Elm](/trending/elm?since=daily) [Elvish](/trending/elvish?since=daily) [Elvish Transcript](/trending/elvish-transcript?since=daily) [Emacs Lisp](/trending/emacs-lisp?since=daily) [EmberScript](/trending/emberscript?since=daily) [E-mail](/trending/e-mail?since=daily) [EQ](/trending/eq?since=daily) [Erlang](/trending/erlang?since=daily) [Euphoria](/trending/euphoria?since=daily) [F#](/trending/f%23?since=daily) [F\\*](/trending/f*?since=daily) [Factor](/trending/factor?since=daily) [Fancy](/trending/fancy?since=daily) [Fantom](/trending/fantom?since=daily) [Faust](/trending/faust?since=daily) [Fennel](/trending/fennel?since=daily) [FIGlet Font](/trending/figlet-font?since=daily) [Filebench WML](/trending/filebench-wml?since=daily) [Filterscript](/trending/filterscript?since=daily) [FIRRTL](/trending/firrtl?since=daily) [fish](/trending/fish?since=daily) [Fluent](/trending/fluent?since=daily) [FLUX](/trending/flux?since=daily) [Formatted](/trending/formatted?since=daily) [Forth](/trending/forth?since=daily) [Fortran](/trending/fortran?since=daily) [Fortran Free Form](/trending/fortran-free-form?since=daily) [FreeBASIC](/trending/freebasic?since=daily) [FreeMarker](/trending/freemarker?since=daily) [Frege](/trending/frege?since=daily) [Futhark](/trending/futhark?since=daily) [G-code](/trending/g-code?since=daily) [Game Maker Language](/trending/game-maker-language?since=daily) [GAML](/trending/gaml?since=daily) [GAMS](/trending/gams?since=daily) [GAP](/trending/gap?since=daily) [GCC Machine Description](/trending/gcc-machine-description?since=daily) [GDB](/trending/gdb?since=daily) [GDScript](/trending/gdscript?since=daily) [GDShader](/trending/gdshader?since=daily) [GEDCOM](/trending/gedcom?since=daily) [Gemfile.lock](/trending/gemfile.lock?since=daily) [Gemini](/trending/gemini?since=daily) [Genero 4gl](/trending/genero-4gl?since=daily) [Genero per](/trending/genero-per?since=daily) [Genie](/trending/genie?since=daily) [Genshi](/trending/genshi?since=daily) [Gentoo Ebuild](/trending/gentoo-ebuild?since=daily) [Gentoo Eclass](/trending/gentoo-eclass?since=daily) [Gerber Image](/trending/gerber-image?since=daily) [Gettext Catalog](/trending/gettext-catalog?since=daily) [Gherkin](/trending/gherkin?since=daily) [Git Attributes](/trending/git-attributes?since=daily) [Git Config](/trending/git-config?since=daily) [Git Revision List](/trending/git-revision-list?since=daily) [Gleam](/trending/gleam?since=daily) [Glimmer JS](/trending/glimmer-js?since=daily) [Glimmer TS](/trending/glimmer-ts?since=daily) [GLSL](/trending/glsl?since=daily) [Glyph](/trending/glyph?since=daily) [Glyph Bitmap Distribution Format](/trending/glyph-bitmap-distribution-format?since=daily) [GN](/trending/gn?since=daily) [Gnuplot](/trending/gnuplot?since=daily) [Go](/trending/go?since=daily) [Go Checksums](/trending/go-checksums?since=daily) [Go Module](/trending/go-module?since=daily) [Go Workspace](/trending/go-workspace?since=daily) [Godot Resource](/trending/godot-resource?since=daily) [Golo](/trending/golo?since=daily) [Gosu](/trending/gosu?since=daily) [Grace](/trending/grace?since=daily) [Gradle](/trending/gradle?since=daily) [Gradle Kotlin DSL](/trending/gradle-kotlin-dsl?since=daily) [Grammatical Framework](/trending/grammatical-framework?since=daily) [Graph Modeling Language](/trending/graph-modeling-language?since=daily) [GraphQL](/trending/graphql?since=daily) [Graphviz (DOT)](/trending/graphviz-\\(dot\\)?since=daily) [Groovy](/trending/groovy?since=daily) [Groovy Server Pages](/trending/groovy-server-pages?since=daily) [GSC](/trending/gsc?since=daily) [Hack](/trending/hack?since=daily) [Haml](/trending/haml?since=daily) [Handlebars](/trending/handlebars?since=daily) [HAProxy](/trending/haproxy?since=daily) [Harbour](/trending/harbour?since=daily) [Hare](/trending/hare?since=daily) [Haskell](/trending/haskell?since=daily) [Haxe](/trending/haxe?since=daily) [HCL](/trending/hcl?since=daily) [HIP](/trending/hip?since=daily) [HiveQL](/trending/hiveql?since=daily) [HLSL](/trending/hlsl?since=daily) [HOCON](/trending/hocon?since=daily) [HolyC](/trending/holyc?since=daily) [hoon](/trending/hoon?since=daily) [Hosts File](/trending/hosts-file?since=daily) [HTML](/trending/html?since=daily) [Jinja](/trending/jinja?since=daily) [HTML+ECR](/trending/html+ecr?since=daily) [HTML+EEX](/trending/html+eex?since=daily) [HTML+ERB](/trending/html+erb?since=daily) [HTML+PHP](/trending/html+php?since=daily) [HTML+Razor](/trending/html+razor?since=daily) [HTTP](/trending/http?since=daily) [HXML](/trending/hxml?since=daily) [Hy](/trending/hy?since=daily) [HyPhy](/trending/hyphy?since=daily) [iCalendar](/trending/icalendar?since=daily) [IDL](/trending/idl?since=daily) [Idris](/trending/idris?since=daily) [Ignore List](/trending/ignore-list?since=daily) [IGOR Pro](/trending/igor-pro?since=daily) [ImageJ Macro](/trending/imagej-macro?since=daily) [Imba](/trending/imba?since=daily) [Inform 7](/trending/inform-7?since=daily) [INI](/trending/ini?since=daily) [Ink](/trending/ink?since=daily) [Inno Setup](/trending/inno-setup?since=daily) [Io](/trending/io?since=daily) [Ioke](/trending/ioke?since=daily) [IRC log](/trending/irc-log?since=daily) [Isabelle](/trending/isabelle?since=daily) [Isabelle ROOT](/trending/isabelle-root?since=daily) [ISPC](/trending/ispc?since=daily) [J](/trending/j?since=daily) [Jai](/trending/jai?since=daily) [Janet](/trending/janet?since=daily) [JAR Manifest](/trending/jar-manifest?since=daily) [Jasmin](/trending/jasmin?since=daily) [Java](/trending/java?since=daily) [Java Properties](/trending/java-properties?since=daily) [Java Server Pages](/trending/java-server-pages?since=daily) [Java Template Engine](/trending/java-template-engine?since=daily) [JavaScript](/trending/javascript?since=daily) [JavaScript+ERB](/trending/javascript+erb?since=daily) [JCL](/trending/jcl?since=daily) [Jest Snapshot](/trending/jest-snapshot?since=daily) [JetBrains MPS](/trending/jetbrains-mps?since=daily) [JFlex](/trending/jflex?since=daily) [Jinja](/trending/jinja?since=daily) [Jison](/trending/jison?since=daily) [Jison Lex](/trending/jison-lex?since=daily) [Jolie](/trending/jolie?since=daily) [jq](/trending/jq?since=daily) [JSON](/trending/json?since=daily) [JSON with Comments](/trending/json-with-comments?since=daily) [JSON5](/trending/json5?since=daily) [JSONiq](/trending/jsoniq?since=daily) [JSONLD](/trending/jsonld?since=daily) [Jsonnet](/trending/jsonnet?since=daily) [Julia](/trending/julia?since=daily) [Julia REPL](/trending/julia-repl?since=daily) [Jupyter Notebook](/trending/jupyter-notebook?since=daily) [Just](/trending/just?since=daily) [Kaitai Struct](/trending/kaitai-struct?since=daily) [KakouneScript](/trending/kakounescript?since=daily) [KDL](/trending/kdl?since=daily) [KerboScript](/trending/kerboscript?since=daily) [KiCad Layout](/trending/kicad-layout?since=daily) [KiCad Legacy Layout](/trending/kicad-legacy-layout?since=daily) [KiCad Schematic](/trending/kicad-schematic?since=daily) [Kickstart](/trending/kickstart?since=daily) [Kit](/trending/kit?since=daily) [Koka](/trending/koka?since=daily) [Kotlin](/trending/kotlin?since=daily) [KRL](/trending/krl?since=daily) [Kusto](/trending/kusto?since=daily) [kvlang](/trending/kvlang?since=daily) [LabVIEW](/trending/labview?since=daily) [Lark](/trending/lark?since=daily) [Lasso](/trending/lasso?since=daily) [Latte](/trending/latte?since=daily) [Lean](/trending/lean?since=daily) [Lean 4](/trending/lean-4?since=daily) [Leo](/trending/leo?since=daily) [Less](/trending/less?since=daily) [Lex](/trending/lex?since=daily) [LFE](/trending/lfe?since=daily) [LigoLANG](/trending/ligolang?since=daily) [LilyPond](/trending/lilypond?since=daily) [Limbo](/trending/limbo?since=daily) [Linear Programming](/trending/linear-programming?since=daily) [Linker Script](/trending/linker-script?since=daily) [Linux Kernel Module](/trending/linux-kernel-module?since=daily) [Liquid](/trending/liquid?since=daily) [Literate Agda](/trending/literate-agda?since=daily) [Literate CoffeeScript](/trending/literate-coffeescript?since=daily) [Literate Haskell](/trending/literate-haskell?since=daily) [LiveCode Script](/trending/livecode-script?since=daily) [LiveScript](/trending/livescript?since=daily) [LLVM](/trending/llvm?since=daily) [Logos](/trending/logos?since=daily) [Logtalk](/trending/logtalk?since=daily) [LOLCODE](/trending/lolcode?since=daily) [LookML](/trending/lookml?since=daily) [LoomScript](/trending/loomscript?since=daily) [LSL](/trending/lsl?since=daily) [LTspice Symbol](/trending/ltspice-symbol?since=daily) [Lua](/trending/lua?since=daily) [Luau](/trending/luau?since=daily) [M](/trending/m?since=daily) [M3U](/trending/m3u?since=daily) [M4](/trending/m4?since=daily) [M4Sugar](/trending/m4sugar?since=daily) [Macaulay2](/trending/macaulay2?since=daily) [Makefile](/trending/makefile?since=daily) [Mako](/trending/mako?since=daily) [Markdown](/trending/markdown?since=daily) [Marko](/trending/marko?since=daily) [Mask](/trending/mask?since=daily) [Mathematica](/trending/mathematica?since=daily) [MATLAB](/trending/matlab?since=daily) [Maven POM](/trending/maven-pom?since=daily) [Max](/trending/max?since=daily) [MAXScript](/trending/maxscript?since=daily) [mcfunction](/trending/mcfunction?since=daily) [mdsvex](/trending/mdsvex?since=daily) [MDX](/trending/mdx?since=daily) [Wikitext](/trending/wikitext?since=daily) [Mercury](/trending/mercury?since=daily) [Mermaid](/trending/mermaid?since=daily) [Meson](/trending/meson?since=daily) [Metal](/trending/metal?since=daily) [Microsoft Developer Studio Project](/trending/microsoft-developer-studio-project?since=daily) [Microsoft Visual Studio Solution](/trending/microsoft-visual-studio-solution?since=daily) [MiniD](/trending/minid?since=daily) [MiniYAML](/trending/miniyaml?since=daily) [MiniZinc](/trending/minizinc?since=daily) [MiniZinc Data](/trending/minizinc-data?since=daily) [Mint](/trending/mint?since=daily) [Mirah](/trending/mirah?since=daily) [mIRC Script](/trending/mirc-script?since=daily) [MLIR](/trending/mlir?since=daily) [Modelica](/trending/modelica?since=daily) [Modula-2](/trending/modula-2?since=daily) [Modula-3](/trending/modula-3?since=daily) [Module Management System](/trending/module-management-system?since=daily) [Mojo](/trending/mojo?since=daily) [Monkey](/trending/monkey?since=daily) [Monkey C](/trending/monkey-c?since=daily) [Moocode](/trending/moocode?since=daily) [MoonBit](/trending/moonbit?since=daily) [MoonScript](/trending/moonscript?since=daily) [Motoko](/trending/motoko?since=daily) [Motorola 68K Assembly](/trending/motorola-68k-assembly?since=daily) [Move](/trending/move?since=daily) [MQL4](/trending/mql4?since=daily) [MQL5](/trending/mql5?since=daily) [MTML](/trending/mtml?since=daily) [MUF](/trending/muf?since=daily) [mupad](/trending/mupad?since=daily) [Muse](/trending/muse?since=daily) [Mustache](/trending/mustache?since=daily) [Myghty](/trending/myghty?since=daily) [nanorc](/trending/nanorc?since=daily) [Nasal](/trending/nasal?since=daily) [NASL](/trending/nasl?since=daily) [NCL](/trending/ncl?since=daily) [Nearley](/trending/nearley?since=daily) [Nemerle](/trending/nemerle?since=daily) [NEON](/trending/neon?since=daily) [nesC](/trending/nesc?since=daily) [NetLinx](/trending/netlinx?since=daily) [NetLinx+ERB](/trending/netlinx+erb?since=daily) [NetLogo](/trending/netlogo?since=daily) [NewLisp](/trending/newlisp?since=daily) [Nextflow](/trending/nextflow?since=daily) [Nginx](/trending/nginx?since=daily) [Nim](/trending/nim?since=daily) [Ninja](/trending/ninja?since=daily) [Nit](/trending/nit?since=daily) [Nix](/trending/nix?since=daily) [NL](/trending/nl?since=daily) [NMODL](/trending/nmodl?since=daily) [Noir](/trending/noir?since=daily) [NPM Config](/trending/npm-config?since=daily) [NSIS](/trending/nsis?since=daily) [Nu](/trending/nu?since=daily) [NumPy](/trending/numpy?since=daily) [Nunjucks](/trending/nunjucks?since=daily) [Nushell](/trending/nushell?since=daily) [NWScript](/trending/nwscript?since=daily) [OASv2-json](/trending/oasv2-json?since=daily) [OASv2-yaml](/trending/oasv2-yaml?since=daily) [OASv3-json](/trending/oasv3-json?since=daily) [OASv3-yaml](/trending/oasv3-yaml?since=daily) [Oberon](/trending/oberon?since=daily) [ObjDump](/trending/objdump?since=daily) [Object Data Instance Notation](/trending/object-data-instance-notation?since=daily) [Objective-C](/trending/objective-c?since=daily) [Objective-C++](/trending/objective-c++?since=daily) [Objective-J](/trending/objective-j?since=daily) [ObjectScript](/trending/objectscript?since=daily) [OCaml](/trending/ocaml?since=daily) [Odin](/trending/odin?since=daily) [Omgrofl](/trending/omgrofl?since=daily) [OMNeT++ MSG](/trending/omnet++-msg?since=daily) [OMNeT++ NED](/trending/omnet++-ned?since=daily) [OMNeT++ MSG](/trending/omnet++-msg?since=daily) [OMNeT++ NED](/trending/omnet++-ned?since=daily) [ooc](/trending/ooc?since=daily) [Opa](/trending/opa?since=daily) [Opal](/trending/opal?since=daily) [Open Policy Agent](/trending/open-policy-agent?since=daily) [OpenAPI Specification v2](/trending/openapi-specification-v2?since=daily) [OpenAPI Specification v3](/trending/openapi-specification-v3?since=daily) [OpenCL](/trending/opencl?since=daily) [OpenEdge ABL](/trending/openedge-abl?since=daily) [OpenQASM](/trending/openqasm?since=daily) [OpenRC runscript](/trending/openrc-runscript?since=daily) [OpenSCAD](/trending/openscad?since=daily) [OpenStep Property List](/trending/openstep-property-list?since=daily) [OpenType Feature File](/trending/opentype-feature-file?since=daily) [Option List](/trending/option-list?since=daily) [Org](/trending/org?since=daily) [OverpassQL](/trending/overpassql?since=daily) [Ox](/trending/ox?since=daily) [Oxygene](/trending/oxygene?since=daily) [Oz](/trending/oz?since=daily) [P4](/trending/p4?since=daily) [Pact](/trending/pact?since=daily) [Pan](/trending/pan?since=daily) [Papyrus](/trending/papyrus?since=daily) [Parrot](/trending/parrot?since=daily) [Parrot Assembly](/trending/parrot-assembly?since=daily) [Parrot Internal Representation](/trending/parrot-internal-representation?since=daily) [Pascal](/trending/pascal?since=daily) [Pawn](/trending/pawn?since=daily) [PDDL](/trending/pddl?since=daily) [PEG.js](/trending/peg.js?since=daily) [Pep8](/trending/pep8?since=daily) [Perl](/trending/perl?since=daily) [PHP](/trending/php?since=daily) [Pic](/trending/pic?since=daily) [Pickle](/trending/pickle?since=daily) [PicoLisp](/trending/picolisp?since=daily) [PigLatin](/trending/piglatin?since=daily) [Pike](/trending/pike?since=daily) [Pip Requirements](/trending/pip-requirements?since=daily) [Pkl](/trending/pkl?since=daily) [PlantUML](/trending/plantuml?since=daily) [PLpgSQL](/trending/plpgsql?since=daily) [PLSQL](/trending/plsql?since=daily) [Pod](/trending/pod?since=daily) [Pod 6](/trending/pod-6?since=daily) [PogoScript](/trending/pogoscript?since=daily) [Polar](/trending/polar?since=daily) [Pony](/trending/pony?since=daily) [Portugol](/trending/portugol?since=daily) [PostCSS](/trending/postcss?since=daily) [PostScript](/trending/postscript?since=daily) [POV-Ray SDL](/trending/pov-ray-sdl?since=daily) [PowerBuilder](/trending/powerbuilder?since=daily) [PowerShell](/trending/powershell?since=daily) [Praat](/trending/praat?since=daily) [Prisma](/trending/prisma?since=daily) [Processing](/trending/processing?since=daily) [Procfile](/trending/procfile?since=daily) [Proguard](/trending/proguard?since=daily) [Prolog](/trending/prolog?since=daily) [Promela](/trending/promela?since=daily) [Propeller Spin](/trending/propeller-spin?since=daily) [Protocol Buffer](/trending/protocol-buffer?since=daily) [Protocol Buffer Text Format](/trending/protocol-buffer-text-format?since=daily) [Public Key](/trending/public-key?since=daily) [Pug](/trending/pug?since=daily) [Puppet](/trending/puppet?since=daily) [Pure Data](/trending/pure-data?since=daily) [PureBasic](/trending/purebasic?since=daily) [PureScript](/trending/purescript?since=daily) [Pyret](/trending/pyret?since=daily) [Python](/trending/python?since=daily) [Python console](/trending/python-console?since=daily) [Python traceback](/trending/python-traceback?since=daily) [q](/trending/q?since=daily) [Q#](/trending/q%23?since=daily) [QMake](/trending/qmake?since=daily) [QML](/trending/qml?since=daily) [Qt Script](/trending/qt-script?since=daily) [Quake](/trending/quake?since=daily) [QuickBASIC](/trending/quickbasic?since=daily) [R](/trending/r?since=daily) [Racket](/trending/racket?since=daily) [Ragel](/trending/ragel?since=daily) [Raku](/trending/raku?since=daily) [RAML](/trending/raml?since=daily) [Rascal](/trending/rascal?since=daily) [Raw token data](/trending/raw-token-data?since=daily) [RBS](/trending/rbs?since=daily) [RDoc](/trending/rdoc?since=daily) [Readline Config](/trending/readline-config?since=daily) [REALbasic](/trending/realbasic?since=daily) [Reason](/trending/reason?since=daily) [ReasonLIGO](/trending/reasonligo?since=daily) [Rebol](/trending/rebol?since=daily) [Record Jar](/trending/record-jar?since=daily) [Red](/trending/red?since=daily) [Redcode](/trending/redcode?since=daily) [Redirect Rules](/trending/redirect-rules?since=daily) [Regular Expression](/trending/regular-expression?since=daily) [Ren'Py](/trending/ren'py?since=daily) [RenderScript](/trending/renderscript?since=daily) [ReScript](/trending/rescript?since=daily) [reStructuredText](/trending/restructuredtext?since=daily) [REXX](/trending/rexx?since=daily) [Rez](/trending/rez?since=daily) [Rich Text Format](/trending/rich-text-format?since=daily) [Ring](/trending/ring?since=daily) [Riot](/trending/riot?since=daily) [RMarkdown](/trending/rmarkdown?since=daily) [RobotFramework](/trending/robotframework?since=daily) [robots.txt](/trending/robots.txt?since=daily) [Roc](/trending/roc?since=daily) [Rocq Prover](/trending/rocq-prover?since=daily) [Roff](/trending/roff?since=daily) [Roff Manpage](/trending/roff-manpage?since=daily) [RON](/trending/ron?since=daily) [Rouge](/trending/rouge?since=daily) [RouterOS Script](/trending/routeros-script?since=daily) [RPC](/trending/rpc?since=daily) [RPGLE](/trending/rpgle?since=daily) [RPM Spec](/trending/rpm-spec?since=daily) [Ruby](/trending/ruby?since=daily) [RUNOFF](/trending/runoff?since=daily) [Rust](/trending/rust?since=daily) [Sage](/trending/sage?since=daily) [Sail](/trending/sail?since=daily) [SaltStack](/trending/saltstack?since=daily) [SAS](/trending/sas?since=daily) [Sass](/trending/sass?since=daily) [Scala](/trending/scala?since=daily) [Scaml](/trending/scaml?since=daily) [Scenic](/trending/scenic?since=daily) [Scheme](/trending/scheme?since=daily) [Scilab](/trending/scilab?since=daily) [SCSS](/trending/scss?since=daily) [sed](/trending/sed?since=daily) [Self](/trending/self?since=daily) [SELinux Policy](/trending/selinux-policy?since=daily) [ShaderLab](/trending/shaderlab?since=daily) [Shell](/trending/shell?since=daily) [ShellCheck Config](/trending/shellcheck-config?since=daily) [ShellSession](/trending/shellsession?since=daily) [Shen](/trending/shen?since=daily) [Sieve](/trending/sieve?since=daily) [Simple File Verification](/trending/simple-file-verification?since=daily) [Singularity](/trending/singularity?since=daily) [Slang](/trending/slang?since=daily) [Slash](/trending/slash?since=daily) [Slice](/trending/slice?since=daily) [Slim](/trending/slim?since=daily) [Slint](/trending/slint?since=daily) [Smali](/trending/smali?since=daily) [Smalltalk](/trending/smalltalk?since=daily) [Smarty](/trending/smarty?since=daily) [Smithy](/trending/smithy?since=daily) [SmPL](/trending/smpl?since=daily) [SMT](/trending/smt?since=daily) [Snakemake](/trending/snakemake?since=daily) [Solidity](/trending/solidity?since=daily) [Soong](/trending/soong?since=daily) [SourcePawn](/trending/sourcepawn?since=daily) [SPARQL](/trending/sparql?since=daily) [Spline Font Database](/trending/spline-font-database?since=daily) [SQF](/trending/sqf?since=daily) [SQL](/trending/sql?since=daily) [SQLPL](/trending/sqlpl?since=daily) [Squirrel](/trending/squirrel?since=daily) [SRecode Template](/trending/srecode-template?since=daily) [SSH Config](/trending/ssh-config?since=daily) [Stan](/trending/stan?since=daily) [Standard ML](/trending/standard-ml?since=daily) [STAR](/trending/star?since=daily) [Starlark](/trending/starlark?since=daily) [Stata](/trending/stata?since=daily) [STL](/trending/stl?since=daily) [STON](/trending/ston?since=daily) [StringTemplate](/trending/stringtemplate?since=daily) [Stylus](/trending/stylus?since=daily) [SubRip Text](/trending/subrip-text?since=daily) [SugarSS](/trending/sugarss?since=daily) [SuperCollider](/trending/supercollider?since=daily) [Survex data](/trending/survex-data?since=daily) [Svelte](/trending/svelte?since=daily) [SVG](/trending/svg?since=daily) [Sway](/trending/sway?since=daily) [Sweave](/trending/sweave?since=daily) [Swift](/trending/swift?since=daily) [SWIG](/trending/swig?since=daily) [SystemVerilog](/trending/systemverilog?since=daily) [Tact](/trending/tact?since=daily) [Talon](/trending/talon?since=daily) [Tcl](/trending/tcl?since=daily) [Tcsh](/trending/tcsh?since=daily) [Tea](/trending/tea?since=daily) [templ](/trending/templ?since=daily) [Terra](/trending/terra?since=daily) [Terraform Template](/trending/terraform-template?since=daily) [TeX](/trending/tex?since=daily) [Texinfo](/trending/texinfo?since=daily) [Text](/trending/text?since=daily) [TextGrid](/trending/textgrid?since=daily) [Textile](/trending/textile?since=daily) [TextMate Properties](/trending/textmate-properties?since=daily) [Thrift](/trending/thrift?since=daily) [TI Program](/trending/ti-program?since=daily) [TL-Verilog](/trending/tl-verilog?since=daily) [TLA](/trending/tla?since=daily) [Toit](/trending/toit?since=daily) [TOML](/trending/toml?since=daily) [Tor Config](/trending/tor-config?since=daily) [Tree-sitter Query](/trending/tree-sitter-query?since=daily) [TSPLIB data](/trending/tsplib-data?since=daily) [TSQL](/trending/tsql?since=daily) [TSV](/trending/tsv?since=daily) [TSX](/trending/tsx?since=daily) [Turing](/trending/turing?since=daily) [Turtle](/trending/turtle?since=daily) [Twig](/trending/twig?since=daily) [TXL](/trending/txl?since=daily) [Type Language](/trending/type-language?since=daily) [TypeScript](/trending/typescript?since=daily) [TypeSpec](/trending/typespec?since=daily) [Typst](/trending/typst?since=daily) [Unified Parallel C](/trending/unified-parallel-c?since=daily) [Unity3D Asset](/trending/unity3d-asset?since=daily) [Unix Assembly](/trending/unix-assembly?since=daily) [Uno](/trending/uno?since=daily) [UnrealScript](/trending/unrealscript?since=daily) [Untyped Plutus Core](/trending/untyped-plutus-core?since=daily) [UrWeb](/trending/urweb?since=daily) [V](/trending/v?since=daily) [Vala](/trending/vala?since=daily) [Valve Data Format](/trending/valve-data-format?since=daily) [VBA](/trending/vba?since=daily) [VBScript](/trending/vbscript?since=daily) [vCard](/trending/vcard?since=daily) [VCL](/trending/vcl?since=daily) [Velocity Template Language](/trending/velocity-template-language?since=daily) [Vento](/trending/vento?since=daily) [Verilog](/trending/verilog?since=daily) [VHDL](/trending/vhdl?since=daily) [Vim Help File](/trending/vim-help-file?since=daily) [Vim Script](/trending/vim-script?since=daily) [Vim Snippet](/trending/vim-snippet?since=daily) [Visual Basic .NET](/trending/visual-basic-.net?since=daily) [Visual Basic .NET](/trending/visual-basic-.net?since=daily) [Visual Basic 6.0](/trending/visual-basic-6.0?since=daily) [Volt](/trending/volt?since=daily) [Vue](/trending/vue?since=daily) [Vyper](/trending/vyper?since=daily) [Wavefront Material](/trending/wavefront-material?since=daily) [Wavefront Object](/trending/wavefront-object?since=daily) [WDL](/trending/wdl?since=daily) [Web Ontology Language](/trending/web-ontology-language?since=daily) [WebAssembly](/trending/webassembly?since=daily) [WebAssembly Interface Type](/trending/webassembly-interface-type?since=daily) [WebIDL](/trending/webidl?since=daily) [WebVTT](/trending/webvtt?since=daily) [Wget Config](/trending/wget-config?since=daily) [WGSL](/trending/wgsl?since=daily) [Whiley](/trending/whiley?since=daily) [Wikitext](/trending/wikitext?since=daily) [Win32 Message File](/trending/win32-message-file?since=daily) [Windows Registry Entries](/trending/windows-registry-entries?since=daily) [wisp](/trending/wisp?since=daily) [Witcher Script](/trending/witcher-script?since=daily) [Wollok](/trending/wollok?since=daily) [World of Warcraft Addon Data](/trending/world-of-warcraft-addon-data?since=daily) [Wren](/trending/wren?since=daily) [X BitMap](/trending/x-bitmap?since=daily) [X Font Directory Index](/trending/x-font-directory-index?since=daily) [X PixMap](/trending/x-pixmap?since=daily) [X10](/trending/x10?since=daily) [xBase](/trending/xbase?since=daily) [XC](/trending/xc?since=daily) [XCompose](/trending/xcompose?since=daily) [Xmake](/trending/xmake?since=daily) [XML](/trending/xml?since=daily) [XML Property List](/trending/xml-property-list?since=daily) [Xojo](/trending/xojo?since=daily) [Xonsh](/trending/xonsh?since=daily) [XPages](/trending/xpages?since=daily) [XProc](/trending/xproc?since=daily) [XQuery](/trending/xquery?since=daily) [XS](/trending/xs?since=daily) [XSLT](/trending/xslt?since=daily) [Xtend](/trending/xtend?since=daily) [Yacc](/trending/yacc?since=daily) [YAML](/trending/yaml?since=daily) [YANG](/trending/yang?since=daily) [YARA](/trending/yara?since=daily) [YASnippet](/trending/yasnippet?since=daily) [Yul](/trending/yul?since=daily) [ZAP](/trending/zap?since=daily) [Zeek](/trending/zeek?since=daily) [ZenScript](/trending/zenscript?since=daily) [Zephir](/trending/zephir?since=daily) [Zig](/trending/zig?since=daily) [ZIL](/trending/zil?since=daily) [Zimpl](/trending/zimpl?since=daily) [Zmodel](/trending/zmodel?since=daily)\n\nLoading\n\nDate range: Today\n\nAdjust time span\n\n[Today](https://github.com/trending?since=daily) [This week](https://github.com/trending?since=weekly) [This month](https://github.com/trending?since=monthly)\n\n[Star](/login?return_to=%2Fanthropics%2Fclaude-code)\n\n## [anthropics / claude-code](/anthropics/claude-code)\n\nClaude Code is an agentic coding tool that lives in your terminal, understands your codebase, and helps you code faster by executing routine tasks, explaining complex code, and handling git workflows - all through natural language commands.\n\nPowerShell [22,361](/anthropics/claude-code/stargazers) [1,227](/anthropics/claude-code/forks) Built by [![@actions-user](https://avatars.githubusercontent.com/u/65916846?s=40&v=4)](/actions-user) [![@bcherny](https://avatars.githubusercontent.com/u/1761758?s=40&v=4)](/bcherny) [![@rboyce-ant](https://avatars.githubusercontent.com/u/176435744?s=40&v=4)](/rboyce-ant) [![@ashwin-ant](https://avatars.githubusercontent.com/u/178951676?s=40&v=4)](/ashwin-ant) [![@8enmann](https://avatars.githubusercontent.com/u/1021104?s=40&v=4)](/8enmann)      1,916 stars today\n\n[Star](/login?return_to=%2Fmicrosoft%2Fmarkitdown)\n\n## [microsoft / markitdown](/microsoft/markitdown)\n\nPython tool for converting files and office documents to Markdown.\n\nPython [61,340](/microsoft/markitdown/stargazers) [3,271](/microsoft/markitdown/forks) Built by [![@afourney](https://avatars.githubusercontent.com/u/4017093?s=40&v=4)](/afourney) [![@gagb](https://avatars.githubusercontent.com/u/13227607?s=40&v=4)](/gagb) [![@sugatoray](https://avatars.githubusercontent.com/u/10201242?s=40&v=4)](/sugatoray) [![@PetrAPConsulting](https://avatars.githubusercontent.com/u/173082609?s=40&v=4)](/PetrAPConsulting) [![@l-lumin](https://avatars.githubusercontent.com/u/71011125?s=40&v=4)](/l-lumin)      547 stars today\n\n[Star](/login?return_to=%2Fgorhill%2FuBlock)\n\n## [gorhill / uBlock](/gorhill/uBlock)\n\nuBlock Origin - An efficient blocker for Chromium and Firefox. Fast and lean.\n\nJavaScript [56,004](/gorhill/uBlock/stargazers) [3,581](/gorhill/uBlock/forks) Built by [![@gorhill](https://avatars.githubusercontent.com/u/585534?s=40&v=4)](/gorhill) [![@Deathamns](https://avatars.githubusercontent.com/u/5853030?s=40&v=4)](/Deathamns) [![@chrisaljoudi](https://avatars.githubusercontent.com/u/2351359?s=40&v=4)](/chrisaljoudi) [![@mjethani](https://avatars.githubusercontent.com/u/617358?s=40&v=4)](/mjethani) [![@AlexVallat](https://avatars.githubusercontent.com/u/531706?s=40&v=4)](/AlexVallat)      302 stars today\n\n[Star](/login?return_to=%2Fmicrosoft%2Fqlib)\n\n## [microsoft / qlib](/microsoft/qlib)\n\nQlib is an AI-oriented Quant investment platform that aims to use AI tech to empower Quant Research, from exploring ideas to implementing productions. Qlib supports diverse ML modeling paradigms, including supervised learning, market dynamics modeling, and RL, and is now equipped with [https://github.com/microsoft/RD-Agent](https://github.com/microsoft/RD-Agent) to automate R&D process.\n\nPython [26,985](/microsoft/qlib/stargazers) [4,137](/microsoft/qlib/forks) Built by [![@you-n-g](https://avatars.githubusercontent.com/u/465606?s=40&v=4)](/you-n-g) [![@bxdd](https://avatars.githubusercontent.com/u/45119470?s=40&v=4)](/bxdd) [![@Derek-Wds](https://avatars.githubusercontent.com/u/26081991?s=40&v=4)](/Derek-Wds) [![@zhupr](https://avatars.githubusercontent.com/u/18660661?s=40&v=4)](/zhupr) [![@SunsetWolf](https://avatars.githubusercontent.com/u/30293408?s=40&v=4)](/SunsetWolf)      241 stars today\n\n[Star](/login?return_to=%2Fvercel%2Fcommerce)\n\n## [vercel / commerce](/vercel/commerce)\n\nNext.js Commerce\n\nTypeScript [13,139](/vercel/commerce/stargazers) [4,879](/vercel/commerce/forks) Built by [![@okbel](https://avatars.githubusercontent.com/u/1401559?s=40&v=4)](/okbel) [![@lfades](https://avatars.githubusercontent.com/u/4278345?s=40&v=4)](/lfades) [![@arzafran](https://avatars.githubusercontent.com/u/466367?s=40&v=4)](/arzafran) [![@cond0r](https://avatars.githubusercontent.com/u/1243434?s=40&v=4)](/cond0r) [![@julianbenegas](https://avatars.githubusercontent.com/u/40034115?s=40&v=4)](/julianbenegas)      367 stars today\n\n[Star](/login?return_to=%2Fmindsdb%2Fmindsdb)\n\n## [mindsdb / mindsdb](/mindsdb/mindsdb)\n\nAI's query engine - Platform for building AI that can answer questions over large scale federated data. - The only MCP Server you'll ever need\n\nPython [34,168](/mindsdb/mindsdb/stargazers) [5,534](/mindsdb/mindsdb/forks) Built by [![@StpMax](https://avatars.githubusercontent.com/u/4068133?s=40&v=4)](/StpMax) [![@ZoranPandovski](https://avatars.githubusercontent.com/u/7192539?s=40&v=4)](/ZoranPandovski) [![@George3d6](https://avatars.githubusercontent.com/u/23587658?s=40&v=4)](/George3d6) [![@ea-rus](https://avatars.githubusercontent.com/u/8502631?s=40&v=4)](/ea-rus) [![@MinuraPunchihewa](https://avatars.githubusercontent.com/u/49385643?s=40&v=4)](/MinuraPunchihewa)      363 stars today\n\n[Star](/login?return_to=%2Fripienaar%2Ffree-for-dev)\n\n## [ripienaar / free-for-dev](/ripienaar/free-for-dev)\n\nA list of SaaS, PaaS and IaaS offerings that have free tiers of interest to devops and infradev\n\nHTML [105,473](/ripienaar/free-for-dev/stargazers) [10,997](/ripienaar/free-for-dev/forks) Built by [![@ripienaar](https://avatars.githubusercontent.com/u/82342?s=40&v=4)](/ripienaar) [![@FatGrizzly](https://avatars.githubusercontent.com/u/66728880?s=40&v=4)](/FatGrizzly) [![@Skxxtz](https://avatars.githubusercontent.com/u/63540046?s=40&v=4)](/Skxxtz) [![@thispsj](https://avatars.githubusercontent.com/u/51746608?s=40&v=4)](/thispsj) [![@TraderStf](https://avatars.githubusercontent.com/u/5954335?s=40&v=4)](/TraderStf)      728 stars today\n\n[Star](/login?return_to=%2Fleaningtech%2Fwebvm)\n\n## [leaningtech / webvm](/leaningtech/webvm)\n\nVirtual Machine for the Web\n\nJavaScript [14,995](/leaningtech/webvm/stargazers) [2,650](/leaningtech/webvm/forks) Built by [![@alexp-sssup](https://avatars.githubusercontent.com/u/191061?s=40&v=4)](/alexp-sssup) [![@carlopi](https://avatars.githubusercontent.com/u/842657?s=40&v=4)](/carlopi) [![@yuri91](https://avatars.githubusercontent.com/u/1506147?s=40&v=4)](/yuri91) [![@AtibQur](https://avatars.githubusercontent.com/u/76216657?s=40&v=4)](/AtibQur) [![@sere](https://avatars.githubusercontent.com/u/2992193?s=40&v=4)](/sere)      109 stars today\n\n[Star](/login?return_to=%2FOpenPipe%2FART)\n\n## [OpenPipe / ART](/OpenPipe/ART)\n\nAgent Reinforcement Trainer: train multi-step agents for real-world tasks using GRPO. Give your agents on-the-job training. Reinforcement learning for Qwen2.5, Qwen3, Llama, Kimi, and more!\n\nPython [1,916](/OpenPipe/ART/stargazers) [127](/OpenPipe/ART/forks) Built by [![@bradhilton](https://avatars.githubusercontent.com/u/4875137?s=40&v=4)](/bradhilton) [![@arcticfly](https://avatars.githubusercontent.com/u/41524992?s=40&v=4)](/arcticfly) [![@corbt](https://avatars.githubusercontent.com/u/176426?s=40&v=4)](/corbt) [![@saum7800](https://avatars.githubusercontent.com/u/15959394?s=40&v=4)](/saum7800) [![@claude](https://avatars.githubusercontent.com/u/81847?s=40&v=4)](/claude)      201 stars today\n\n[Star](/login?return_to=%2Ftrimstray%2Fthe-book-of-secret-knowledge)\n\n## [trimstray / the-book-of-secret-knowledge](/trimstray/the-book-of-secret-knowledge)\n\nA collection of inspiring lists, manuals, cheatsheets, blogs, hacks, one-liners, cli/web tools and more.\n\n[177,941](/trimstray/the-book-of-secret-knowledge/stargazers) [11,099](/trimstray/the-book-of-secret-knowledge/forks) Built by [![@trimstray](https://avatars.githubusercontent.com/u/31127917?s=40&v=4)](/trimstray) [![@lbonanomi](https://avatars.githubusercontent.com/u/5369016?s=40&v=4)](/lbonanomi) [![@loweryaustin](https://avatars.githubusercontent.com/u/2003860?s=40&v=4)](/loweryaustin) [![@fabidick22](https://avatars.githubusercontent.com/u/8176821?s=40&v=4)](/fabidick22) [![@naeluh](https://avatars.githubusercontent.com/u/477860?s=40&v=4)](/naeluh)      795 stars today\n\n[Sponsor](/sponsors/nisargjhaveri)\n\n[Star](/login?return_to=%2Fnisargjhaveri%2FWirelessAndroidAutoDongle)\n\n## [nisargjhaveri / WirelessAndroidAutoDongle](/nisargjhaveri/WirelessAndroidAutoDongle)\n\nUse Wireless Android Auto with a car that supports only wired Android Auto using a Raspberry Pi.\n\nC++ [1,549](/nisargjhaveri/WirelessAndroidAutoDongle/stargazers) [144](/nisargjhaveri/WirelessAndroidAutoDongle/forks) Built by [![@nisargjhaveri](https://avatars.githubusercontent.com/u/6381721?s=40&v=4)](/nisargjhaveri) [![@manio](https://avatars.githubusercontent.com/u/583157?s=40&v=4)](/manio) [![@BluemediaDev](https://avatars.githubusercontent.com/u/43730110?s=40&v=4)](/BluemediaDev) [![@Ioniq3](https://avatars.githubusercontent.com/u/156689157?s=40&v=4)](/Ioniq3) [![@cheese1](https://avatars.githubusercontent.com/u/6437726?s=40&v=4)](/cheese1)      115 stars today\n\n[Star](/login?return_to=%2Fmusistudio%2Fclaude-code-router)\n\n## [musistudio / claude-code-router](/musistudio/claude-code-router)\n\nUse Claude Code as the foundation for coding infrastructure, allowing you to decide how to interact with the model while enjoying updates from Anthropic.\n\nTypeScript [4,150](/musistudio/claude-code-router/stargazers) [320](/musistudio/claude-code-router/forks) Built by [![@musistudio](https://avatars.githubusercontent.com/u/21162426?s=40&v=4)](/musistudio) [![@sbtobb](https://avatars.githubusercontent.com/u/8125099?s=40&v=4)](/sbtobb) [![@stonega](https://avatars.githubusercontent.com/u/2262007?s=40&v=4)](/stonega) [![@Evyatar108](https://avatars.githubusercontent.com/u/15522223?s=40&v=4)](/Evyatar108) [![@LinHayaii](https://avatars.githubusercontent.com/u/90039703?s=40&v=4)](/LinHayaii)      326 stars today\n\n[Sponsor](/sponsors/TomBursch)\n\n[Star](/login?return_to=%2FTomBursch%2Fkitchenowl)\n\n## [TomBursch / kitchenowl](/TomBursch/kitchenowl)\n\nKitchenOwl is a self-hosted grocery list and recipe manager. The backend is made with Flask and the frontend with Flutter. Easily add items to your shopping list before you go shopping. You can also create recipes and add items based on what you want to cook.\n\nDart [2,180](/TomBursch/kitchenowl/stargazers) [128](/TomBursch/kitchenowl/forks) Built by [![@TomBursch](https://avatars.githubusercontent.com/u/9092682?s=40&v=4)](/TomBursch) [![@weblate](https://avatars.githubusercontent.com/u/1607653?s=40&v=4)](/weblate) [![@cMensendiek](https://avatars.githubusercontent.com/u/38719631?s=40&v=4)](/cMensendiek) [![@pHamala](https://avatars.githubusercontent.com/u/18518012?s=40&v=4)](/pHamala)     55 stars today\n\n[Star](/login?return_to=%2Fcomfyanonymous%2FComfyUI)\n\n## [comfyanonymous / ComfyUI](/comfyanonymous/ComfyUI)\n\nThe most powerful and modular diffusion model GUI, api and backend with a graph/nodes interface.\n\nPython [82,551](/comfyanonymous/ComfyUI/stargazers) [9,141](/comfyanonymous/ComfyUI/forks) Built by [![@comfyanonymous](https://avatars.githubusercontent.com/u/121283862?s=40&v=4)](/comfyanonymous) [![@pythongosssss](https://avatars.githubusercontent.com/u/125205205?s=40&v=4)](/pythongosssss) [![@huchenlei](https://avatars.githubusercontent.com/u/20929282?s=40&v=4)](/huchenlei) [![@ltdrdata](https://avatars.githubusercontent.com/u/128333288?s=40&v=4)](/ltdrdata) [![@christian-byrne](https://avatars.githubusercontent.com/u/72887196?s=40&v=4)](/christian-byrne)      99 stars today\n\n[Star](/login?return_to=%2Fgetzep%2Fgraphiti)\n\n## [getzep / graphiti](/getzep/graphiti)\n\nBuild Real-Time Knowledge Graphs for AI Agents\n\nPython [13,328](/getzep/graphiti/stargazers) [1,122](/getzep/graphiti/forks) Built by [![@prasmussen15](https://avatars.githubusercontent.com/u/109292228?s=40&v=4)](/prasmussen15) [![@danielchalef](https://avatars.githubusercontent.com/u/131175?s=40&v=4)](/danielchalef) [![@dependabot](https://avatars.githubusercontent.com/in/29110?s=40&v=4)](/apps/dependabot) [![@paul-paliychuk](https://avatars.githubusercontent.com/u/26054637?s=40&v=4)](/paul-paliychuk) [![@ellipsis-dev](https://avatars.githubusercontent.com/in/64358?s=40&v=4)](/apps/ellipsis-dev)      337 stars today\n\n## Footer\n\n[](https://github.com)© 2025 GitHub, Inc.\n\n### Footer navigation\n\n*   [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)\n*   [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)\n*   [Security](https://github.com/security)\n*   [Status](https://www.githubstatus.com/)\n*   [Docs](https://docs.github.com/)\n*   [Contact](https://support.github.com?tags=dotcom-footer)\n*   Manage cookies\n*   Do not share my personal information\n\nYou can’t perform that action at this time.\n\n0\n\n1\n\n2\n\n3\n\n4\n\n5\n\n6\n\n7\n\n8\n\n9\n\n10\n\n11\n\n12\n\n13\n\n14\n\n15\n\n16\n\n17\n\n18\n\n19\n\n20\n\n21\n\n22\n\n23\n\n24\n\n25\n\n26\n\n27\n\n28\n\n29\n\n30\n\n31\n\n32\n\n33\n\n34\n\n35\n\n36\n\n37\n\n38\n\n39\n\n40\n\n41\n\n42\n\n43\n\n44\n\n45\n\n46\n\n47\n\n48\n\n49\n\n50\n\n51\n\n52\n\n53\n\n54\n\n55\n\n56\n\n57\n\n58\n\n59\n\n60\n\n61\n\n62\n\n63\n\n64\n\n65\n\n66\n\n67\n\n68"}], "executionTime": 88}]