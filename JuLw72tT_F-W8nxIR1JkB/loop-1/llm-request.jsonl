{"provider": "deepseek", "request": {"model": "deepseek-chat", "messages": [{"role": "system", "content": "\nYou are Agent TARS, a multimodal AI agent created by the ByteDance.\n\n<intro>\nYou excel at the following tasks:\n1. Information gathering, fact-checking, and documentation\n2. Data processing, analysis, and visualization\n3. Writing multi-chapter articles and in-depth research reports\n4. Creating websites, applications, and tools\n5. Using programming to solve various problems beyond development\n6. Various tasks that can be accomplished using computers and the internet\n</intro>\n\n<language_settings>\nUse the language specified by user in messages as the working language when explicitly provided\nAll thinking and responses must be in the working language\nNatural language arguments in tool calls must be in the working language\nAvoid using pure lists and bullet points format in any language\n</language_settings>\n\n<multimodal_understanding>\nWhen processing images, it's crucial to understand the difference between image types:\n1. Browser Screenshots: These are images showing the browser interface that you can interact with using browser tools\n   - Appear as part of the browser_vision_control tool output or environment input labeled as \"Browser Screenshot\"\n   - ONLY these screenshots represent interfaces you can operate on with browser tools\n   - Use these for navigation, clicking elements, scrolling, and other browser interactions\n\n2. User-Uploaded Images: These are regular images the user has shared but are NOT browser interfaces\n   - May include photos, diagrams, charts, documents, or any other visual content\n   - Cannot be operated on with browser tools - don't try to click elements in these images\n   - Should be analyzed for information only (objects, text, context, meaning)\n   - Respond to user questions about these images with observations and analysis\n\nDistinguish between these types by context and environment input descriptions to avoid confusion.\nWhen you see a new image, first determine which type it is before deciding how to interact with it.\n</multimodal_understanding>\n\n<system_capability>\nSystem capabilities:\n- Communicate with users through message tools\n- Access a Linux sandbox environment with internet connection\n- Use shell, text editor, browser, and other software\n- Write and run code in Python and various programming languages\n- Independently install required software packages and dependencies via shell\n- Deploy websites or applications and provide public access\n- Suggest users to temporarily take control of the browser for sensitive operations when necessary\n- Utilize various tools to complete user-assigned tasks step by step\n</system_capability>\n\n<agent_loop>\nYou operate in an agent loop, iteratively completing tasks through these steps:\n1. Analyze Events: Understand user needs and current state through event stream, focusing on latest user messages and execution results\n2. Select Tools: Choose next tool call based on current state, task planning, relevant knowledge and available data APIs\n3. Wait for Execution: Selected tool action will be executed by sandbox environment with new observations added to event stream\n4. Iterate: Choose only one tool call per iteration, patiently repeat above steps until task completion\n5. Submit Results: Send results to user via message tools, providing deliverables and related files as message attachments\n6. Enter Standby: Enter idle state when all tasks are completed or user explicitly requests to stop, and wait for new tasks\n</agent_loop>\n\n<file_rules>\n- Use file tools for reading, writing, appending, and editing to avoid string escape issues in shell commands\n- Actively save intermediate results and store different types of reference information in separate files\n- When merging text files, must use append mode of file writing tool to concatenate content to target file\n- Strictly follow requirements in <writing_rules>, and avoid using list formats in any files except todo.md\n</file_rules>\n\n<shell_rules>\n- Avoid commands requiring confirmation; actively use -y or -f flags for automatic confirmation\n- Avoid commands with excessive output; save to files when necessary\n- Chain multiple commands with && operator to minimize interruptions\n- Use pipe operator to pass command outputs, simplifying operations\n- Use non-interactive `bc` for simple calculations, Python for complex math; never calculate mentally\n- Use `uptime` command when users explicitly request sandbox status check or wake-up\n</shell_rules>\n\n\n\n\n<planning_approach>\nYou are a methodical agent that follows a plan-and-solve approach for complex tasks. When handling tasks:\n\n1. Analyze if the task requires a multi-step plan:\n   - For complex research, analysis, or multi-part tasks → Create a plan\n   - For simple questions or tasks → Skip planning and answer directly\n\n2. If a plan is needed:\n   - Create a clear, step-by-step plan with specific goals\n   - Execute each step in order, using appropriate tools\n   - Update the plan as you learn new information\n   - Mark steps as completed when done\n   - Once ALL steps are complete, call the \"final_answer\" tool\n\n3. During execution:\n   - Adapt your plan as needed based on new findings\n   - Be willing to simplify the plan if the task turns out simpler than expected\n   - Always complete your plan before providing final answers\n</planning_approach>\n\n<planning_constraints>\nIMPORTANT CONSTRAINTS:\n- Create AT MOST 3 key steps in your plan\n- Focus on information gathering and research steps\n- Call the \"final_answer\" tool once ALL plan steps are complete\n- For simple questions, you can skip planning entirely\n</planning_constraints>\n \n\n \n<browser_rules>\nYou have access to various browser tools to interact with web pages and extract information.\n\nYou have DOM-based browser control tools that work directly with the page structure:\n\n- Navigation: `browser_navigate`, `browser_back`, `browser_forward`, `browser_refresh`\n- Interaction: `browser_click`, `browser_type`, `browser_press`, `browser_hover`, `browser_drag`, `browser_scroll`\n- Content extraction: `browser_get_markdown`\n- Status checking: `browser_get_url`, `browser_get_title`, `browser_get_elements`\n- Tab management: `browser_tab_list`, `browser_new_tab`, `browser_close_tab`, `browser_switch_tab`\n\nUSAGE GUIDELINES:\n- Use CSS selectors or element indices to precisely target elements\n- Extract content with `browser_get_markdown` for efficient analysis\n- Find and verify elements with `browser_get_elements` before interacting\n- Leverage browser state tools to keep track of navigation\n\n- Must use browser tools to access and comprehend all URLs provided by users in messages\n- Must use browser tools to access URLs from search tool results\n- Actively explore valuable links for deeper information, either by clicking elements or accessing URLs directly\n- Browser tools only return elements in visible viewport by default\n- Due to technical limitations, not all interactive elements may be identified; use coordinates to interact with unlisted elements\n- Browser tools automatically attempt to extract page content, providing it in Markdown format if successful\n- Extracted Markdown includes text beyond viewport but omits links and images; completeness not guaranteed\n- If extracted Markdown is complete and sufficient for the task, no scrolling is needed; otherwise, must actively scroll to view the entire page\n- Use message tools to suggest user to take over the browser for sensitive operations or actions with side effects when necessary\n</browser_rules>\n\n<envirnoment>\nCurrent Working Directory: /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub\n</envirnoment>\n\n    \n\nCurrent time: 5/20/2025, 10:00:00 AM"}, {"role": "user", "content": "\"用DuckDuckGo搜索Python机器学习教程\""}, {"role": "system", "content": "Current plan status:\n1. [TODO] Open DuckDuckGo in a web browser.\n2. [TODO] Enter 'Python机器学习教程' in the search bar and press enter.\n3. [TODO] Browse through the search results to find relevant tutorials or resources on Python machine learning.\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_cb5b2d66-f54e-475c-9112-046d3a1aba4f", "type": "function", "function": {"name": "browser_navigate", "arguments": "{\"url\":\"https://duckduckgo.com/\"}"}}]}, {"role": "tool", "tool_call_id": "call_0_cb5b2d66-f54e-475c-9112-046d3a1aba4f", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \"Navigated to https://duckduckgo.com/\\nclickable elements(Might be outdated, if an error occurs with the index element, use browser_get_clickable_elements to refresh it): [0]<a></a>\\n[1]<img></img>\\n[2]<input></input>\\n[3]<button></button>\\n[4]<a>Duck.ai</a>\\n[5]<button>Menu</button>\\n[]改用 DuckDuckGo，私密且免费！\\n[6]<img></img>\\n[]将 DuckDuckGo 设为你的默认搜索引擎。\\n[7]<a>设为默认搜索。</a>\\n[]最佳隐私保护\\n[8]<img></img>\\n[]下载我们的免费浏览器，享受更多隐私保护。\\n[9]<a>下载浏览器</a>\\n[]了解详情\\n[10]<a></a>\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n1. [DONE] Open DuckDuckGo in a web browser.\n2. [DONE] Enter 'Python机器学习教程' in the search bar and press enter.\n3. [DON<PERSON>] Browse through the search results to find relevant tutorials or resources on Python machine learning.\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_e82925aa-ff65-4a54-ae93-728a1b9e991f", "type": "function", "function": {"name": "browser_form_input_fill", "arguments": "{\"index\":2,\"value\":\"Python机器学习教程\"}"}}]}, {"role": "tool", "tool_call_id": "call_0_e82925aa-ff65-4a54-ae93-728a1b9e991f", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \"Filled 2 with: Python机器学习教程\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_89789973-a12e-4ff7-84ec-88df109e7af8", "type": "function", "function": {"name": "browser_press_key", "arguments": "{\"key\":\"Enter\"}"}}]}, {"role": "tool", "tool_call_id": "call_0_89789973-a12e-4ff7-84ec-88df109e7af8", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \"Pressed key: Enter\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n1. [TODO] Unknown step\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_872208cd-b1c9-4e41-ba2d-f745f84e376c", "type": "function", "function": {"name": "browser_get_markdown", "arguments": "{}"}}]}, {"role": "tool", "tool_call_id": "call_0_872208cd-b1c9-4e41-ba2d-f745f84e376c", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \"Python机器学习教程 at DuckDuckGo \\n\\n[DuckDuckGo](/?t=h_)\\n\\nX\\n\\n![](data:image/svg+xml;base64,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)\\n\\n升级到我们的浏览器。\\n\\n![](/dist/react-assets/96adebdbdbc5d4e75d7f.png)\\n\\n![](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTgiIGhlaWdodD0iNTkiIHZpZXdCb3g9IjAgMCA1OCA1OSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggaWQ9IlJlY3RhbmdsZSAyNTgyIiBkPSJNMC41MDEzNDMgMTkuOTU3NkMwLjUwMTM0MyAxNi40NzU0IDAuNTAxNzU4IDEzLjg5NDkgMC42NzM5MjMgMTEuODU0N0MwLjg0NTU3MSA5LjgyMDY2IDEuMTg1MjUgOC4zNjkyOCAxLjgzODA0IDcuMTM2MzlDMi45MTY3NCA1LjA5OTA3IDQuNTgyODQgMy40MzI5NyA2LjYyMDE1IDIuMzU0MjdDNy44NTMwNCAxLjcwMTQ5IDkuMzA0NDIgMS4zNjE4MSAxMS4zMzg1IDEuMTkwMTZDMTMuMzc4NyAxLjAxNzk5IDE1Ljk1OTIgMS4wMTc1OCAxOS40NDEzIDEuMDE3NThMMzguNTYxMyAxLjAxNzU4QzQyLjA0MzUgMS4wMTc1OCA0NC42MjQgMS4wMTc5OSA0Ni42NjQyIDEuMTkwMTZDNDguNjk4MyAxLjM2MTgxIDUwLjE0OTYgMS43MDE0OSA1MS4zODI1IDIuMzU0MjdDNTMuNDE5OSAzLjQzMjk3IDU1LjA4NTkgNS4wOTkwNyA1Ni4xNjQ3IDcuMTM2MzlDNTYuODE3NCA4LjM2OTI4IDU3LjE1NzEgOS44MjA2NiA1Ny4zMjg4IDExLjg1NDdDNTcuNTAwOSAxMy44OTQ5IDU3LjUwMTMgMTYuNDc1NCA1Ny41MDEzIDE5Ljk1NzZWMzkuMDc3NkM1Ny41MDEzIDQyLjU1OTcgNTcuNTAwOSA0NS4xNDAyIDU3LjMyODggNDcuMTgwNEM1Ny4xNTcxIDQ5LjIxNDUgNTYuODE3NCA1MC42NjU5IDU2LjE2NDcgNTEuODk4OEM1NS4wODU5IDUzLjkzNjEgNTMuNDE5OSA1NS42MDIyIDUxLjM4MjUgNTYuNjgwOUM1MC4xNDk2IDU3LjMzMzcgNDguNjk4MyA1Ny42NzM0IDQ2LjY2NDIgNTcuODQ1QzQ0LjYyNCA1OC4wMTcyIDQyLjA0MzUgNTguMDE3NiAzOC41NjEzIDU4LjAxNzZIMTkuNDQxM0MxNS45NTkyIDU4LjAxNzYgMTMuMzc4NyA1OC4wMTcyIDExLjMzODUgNTcuODQ1QzkuMzA0NDIgNTcuNjczNCA3Ljg1MzA0IDU3LjMzMzcgNi42MjAxNSA1Ni42ODA5QzQuNTgyODQgNTUuNjAyMiAyLjkxNjc0IDUzLjkzNjEgMS44MzgwNCA1MS44OTg4QzEuMTg1MjUgNTAuNjY1OSAwLjg0NTU3MSA0OS4yMTQ1IDAuNjczOTIzIDQ3LjE4MDRDMC41MDE3NTggNDUuMTQwMiAwLjUwMTM0MyA0Mi41NTk3IDAuNTAxMzQzIDM5LjA3NzZMMC41MDEzNDMgMTkuOTU3NloiIGZpbGw9IiNERTU4MzMiIHN0cm9rZT0idXJsKCNwYWludDBfbGluZWFyXzE0OTJfMjc5OCkiLz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0icGFpbnQwX2xpbmVhcl8xNDkyXzI3OTgiIHgxPSIxMy4xMDA1IiB5MT0iNTEuNDAxMiIgeDI9IjY3LjAyNjUiIHkyPSIxLjA2NTc3IiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+CjxzdG9wIHN0b3AtY29sb3I9IiNDODM5MTEiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjRkVDNEI0IiBzdG9wLW9wYWNpdHk9IjAuNCIvPgo8L2xpbmVhckdyYWRpZW50Pgo8L2RlZnM+Cjwvc3ZnPgo=)![](data:image/svg+xml;base64,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)\\n\\n试用 **DuckDuckGo 浏览器。**快速。免费。私密。\\n\\n[下载浏览器](https://duckduckgo.com/mac?origin=funnel_browser_searchresults__popover)\\n\\n![](/dist/react-assets/1600f109ce3c4b73fd9c.png)\\n\\n免费\\n\\n打开菜单\\n\\n*   [全部](/?t=h_&q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B&ia=web)\\n*   [图片](/?t=h_&q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B&ia=images&iax=images)\\n*   [视频](/?t=h_&q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B&ia=videos&iax=videos)\\n*   [新闻](/?t=h_&q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B&ia=news&iar=news)\\n*   更多\\n    \\n    [地图](/?t=h_&q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B&iaxm=maps)\\n    \\n    [购物](/?t=h_&q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B&ia=shopping&iax=shopping)\\n    \\n\\n*   [Duck.ai](/?t=h_&q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B&ia=chat)\\n*   搜索设置\\n    \\n\\nYou are being redirected to the non-JavaScript site.\\n\\nClick [这里](/html/?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B) if it doesn't happen automatically.\\n\\n全天候安全保护\\n\\nDuckDuckGo 绝不会跟踪你的搜索记录。\\n\\n[详细了解](https://duckduckgo.com/duckduckgo-help-pages/search-privacy/)\\n\\n可以在[搜索设置](/settings#appearance)中隐藏此提醒\\n\\n不限地区\\n\\n阿根廷\\n\\n爱尔兰\\n\\n爱沙尼亚\\n\\n奥地利\\n\\n澳大利亚\\n\\n巴基斯坦（英语）\\n\\n巴西\\n\\n保加利亚\\n\\n比利时（法语）\\n\\n比利时（荷兰语）\\n\\n冰岛\\n\\n波兰\\n\\n不限地区\\n\\n丹麦\\n\\n德国\\n\\n俄罗斯\\n\\n法国\\n\\n菲律宾（英语）\\n\\n芬兰\\n\\n哥伦比亚\\n\\n韩国\\n\\n荷兰\\n\\n加拿大（法语）\\n\\n加拿大（英语）\\n\\n加泰罗尼亚\\n\\n捷克\\n\\n克罗地亚\\n\\n拉脱维亚\\n\\n立陶宛\\n\\n罗马尼亚\\n\\n马来西亚（英语）\\n\\n美国\\n\\n美国（西班牙语）\\n\\n秘鲁\\n\\n墨西哥\\n\\n南非\\n\\n挪威\\n\\n葡萄牙\\n\\n日本\\n\\n瑞典\\n\\n瑞士（德语）\\n\\n瑞士（法语）\\n\\n沙特阿拉伯\\n\\n斯洛伐克\\n\\n斯洛文尼亚\\n\\n台湾\\n\\n泰国（英语）\\n\\n土耳其\\n\\n乌克兰\\n\\n西班牙（加泰罗尼亚语）\\n\\n西班牙（西班牙语）\\n\\n希腊\\n\\n香港\\n\\n新加坡\\n\\n新西兰\\n\\n匈牙利\\n\\n以色列（英语）\\n\\n意大利\\n\\n印度（英语）\\n\\n印度尼西亚（英语）\\n\\n英国\\n\\n越南（英语）\\n\\n智利\\n\\n中国\\n\\n安全搜索： 适中\\n\\n严格\\n\\n适中\\n\\n关闭\\n\\n不限时间\\n\\n不限时间\\n\\n一天内\\n\\n一周内\\n\\n一月内\\n\\n一年内\\n\\n自定义日期范围\\n\\n1.  cornell.edu\\n    \\n    举报广告\\n    \\n    [\\n    \\n    ![](//external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fwww.bing.com%2Fth%3Fid%3DOADD2.8864831007293_1AKAYKG1M2T8E6XU69%26pid%3D21.2%26h%3D32&f=1)\\n    \\n    Cornell University\\n    \\n    https://online.cornell.edu\\n    \\n    \\n    \\n    ](https://duckduckgo.com/y.js?ad_domain=cornell.edu&ad_provider=bingv7aa&ad_type=txad&click_metadata=E3or1Yjh%2DsVvjNfz2oO9KXjf_l90hOEv0GDO_0xQltLD1A_P4C_LbLwtPNrveEYzBiD%2DyY2uOjAz4OO60WO8NMKEpyUhHb5xAhKst83kGrqBlQ4VG%2DJ0n_O5B38H6LbT.vrohuH9hGYdmtT_PIUDDWA&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=2404dbc49221fa62dd0162b3567d5fba542d0379bed776c6e95dc017a9044fe8&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8j%2DHsgFiigqWNZks5GPrj3zVUCUyu9X1cIX7pKD5yQR0jaoLDve94tjzUgtcgfq%2DxImkEPWy5xrfHO50Ts4_1dia5dkgVb_LwFeXUDGlTF0OBObKZmny5Vls9IcqNVJGQe44GN3kzKgdhJ6yeJZ4lnuU%2DfQE%2DzHPm3u%2Dtqhdpw6HDzSBC2pr5Fh2OxODT1IRVGj2lVSDICOEPOefbspO7j%2Dwxdz3rB8TuP2v2EAcFlZnYCxBqMIDoExgKnnXZuiOQ2ad3DvFLCYO0aco5mkFYOIffGrZFvxmBUpS0SEFoHxu1bYyf5CDflOuQo7uxuzysDrNdrim8fRbD5%2Dlc4QA1yLt03RobN83_foO78RnEv4seRkMPpzKgtBXhItmXHMWoVtscYlcivn_j6wUMEBkmoTdX8LRtMKoZiKTCN8vLwiChUFKyctoiTo4XZzEplxFVbaF%2DDzDolly4BmhbYL2poQG__6SLh7toTS5mv6ITQvRwvu4e_iGrlCmr0YfenbnI0FkcfNtIf_113XElf%2DWRDc%2DmNEtBW0gnAdd4JqY8cGaeH5M2QwZIdth3NnpA9MxYLOt44QEOheTHAxxgVFsVF2M7d_Qwy0oBrb0pJdJoOzDEKmd_Nj1E22Fby2blXKzDZFubLjDUfog2zPIaIVCspw75EXm%2DsVBCuAls3eDhDY7yYc_3cb7E5xS7GI_xuF6G7Lfa5jr_UFUq3q75XN_flbf68dxuG2qo_tQAxT99vRs7Xuq0hH6stw5J1pPIe8lAcq0gRQ%26u%3DaHR0cHMlM2ElMmYlMmZvbmxpbmUuY29ybmVsbC5lZHUlMmZweXRob24tY2VydGlmaWNhdGVzJTJmJTNmY3JlYXRpdmUlM2QlMjZrZXl3b3JkJTNkcHl0aG9uJTI1MjBjb3Vyc2UlMjZtYXRjaHR5cGUlM2RwJTI2bmV0d29yayUzZG8lMjZkZXZpY2UlM2RjJTI2dXRtX3NvdXJjZSUzZGJpbmclMjZ1dG1fbWVkaXVtJTNkY3BjJTI2dXRtX2NhbXBhaWduJTNkZGF0YXNjaWVuY2VfVVMlMmItJTJiUHl0aG9uJTJiZm9yJTJiRGF0YSUyYlNjaWVuY2UlMmItJTJiQmluZyUyNnV0bV90ZXJtJTNkcHl0aG9uJTI1MjBjb3Vyc2UlMjZ1dG1fY29udGVudCUzZGdfX3AlMjZjcmVhdGl2ZSUzZCUyNmtleXdvcmQlM2RweXRob24lMjUyMGNvdXJzZSUyNm1hdGNodHlwZSUzZHAlMjZuZXR3b3JrJTNkbyUyNmRldmljZSUzZGMlMjZ1cmwlM2RodHRwcyUyNTNBJTI1MkYlMjUyRm9ubGluZS5jb3JuZWxsLmVkdSUyNTJGcHl0aG9uLWNlcnRpZmljYXRlcyUyNTJGJTI1M0ZjcmVhdGl2ZSUyNTNEJTI1MjZrZXl3b3JkJTI1M0RweXRob24lMjUyNTIwY291cnNlJTI1MjZtYXRjaHR5cGUlMjUzRHAlMjUyNm5ldHdvcmslMjUzRG8lMjUyNmRldmljZSUyNTNEYyUyNnRhcmdldCUzZCUyNnBvc2l0aW9uJTNkJTI2bXNjbGtpZCUzZDhmZjk4ODZkZjU1NDEzNDIyZWIyZTAxODIwZTVmNjBk%26rlid%3D8ff9886df55413422eb2e01820e5f60d&vqd=4-67380395571001360237724092863580197405&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5047.1)\\n    \\n    ## [Python Certificates - Become a Python Expert](https://duckduckgo.com/y.js?ad_domain=cornell.edu&ad_provider=bingv7aa&ad_type=txad&click_metadata=E3or1Yjh%2DsVvjNfz2oO9KXjf_l90hOEv0GDO_0xQltLD1A_P4C_LbLwtPNrveEYzBiD%2DyY2uOjAz4OO60WO8NMKEpyUhHb5xAhKst83kGrqBlQ4VG%2DJ0n_O5B38H6LbT.vrohuH9hGYdmtT_PIUDDWA&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=2404dbc49221fa62dd0162b3567d5fba542d0379bed776c6e95dc017a9044fe8&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8j%2DHsgFiigqWNZks5GPrj3zVUCUyu9X1cIX7pKD5yQR0jaoLDve94tjzUgtcgfq%2DxImkEPWy5xrfHO50Ts4_1dia5dkgVb_LwFeXUDGlTF0OBObKZmny5Vls9IcqNVJGQe44GN3kzKgdhJ6yeJZ4lnuU%2DfQE%2DzHPm3u%2Dtqhdpw6HDzSBC2pr5Fh2OxODT1IRVGj2lVSDICOEPOefbspO7j%2Dwxdz3rB8TuP2v2EAcFlZnYCxBqMIDoExgKnnXZuiOQ2ad3DvFLCYO0aco5mkFYOIffGrZFvxmBUpS0SEFoHxu1bYyf5CDflOuQo7uxuzysDrNdrim8fRbD5%2Dlc4QA1yLt03RobN83_foO78RnEv4seRkMPpzKgtBXhItmXHMWoVtscYlcivn_j6wUMEBkmoTdX8LRtMKoZiKTCN8vLwiChUFKyctoiTo4XZzEplxFVbaF%2DDzDolly4BmhbYL2poQG__6SLh7toTS5mv6ITQvRwvu4e_iGrlCmr0YfenbnI0FkcfNtIf_113XElf%2DWRDc%2DmNEtBW0gnAdd4JqY8cGaeH5M2QwZIdth3NnpA9MxYLOt44QEOheTHAxxgVFsVF2M7d_Qwy0oBrb0pJdJoOzDEKmd_Nj1E22Fby2blXKzDZFubLjDUfog2zPIaIVCspw75EXm%2DsVBCuAls3eDhDY7yYc_3cb7E5xS7GI_xuF6G7Lfa5jr_UFUq3q75XN_flbf68dxuG2qo_tQAxT99vRs7Xuq0hH6stw5J1pPIe8lAcq0gRQ%26u%3DaHR0cHMlM2ElMmYlMmZvbmxpbmUuY29ybmVsbC5lZHUlMmZweXRob24tY2VydGlmaWNhdGVzJTJmJTNmY3JlYXRpdmUlM2QlMjZrZXl3b3JkJTNkcHl0aG9uJTI1MjBjb3Vyc2UlMjZtYXRjaHR5cGUlM2RwJTI2bmV0d29yayUzZG8lMjZkZXZpY2UlM2RjJTI2dXRtX3NvdXJjZSUzZGJpbmclMjZ1dG1fbWVkaXVtJTNkY3BjJTI2dXRtX2NhbXBhaWduJTNkZGF0YXNjaWVuY2VfVVMlMmItJTJiUHl0aG9uJTJiZm9yJTJiRGF0YSUyYlNjaWVuY2UlMmItJTJiQmluZyUyNnV0bV90ZXJtJTNkcHl0aG9uJTI1MjBjb3Vyc2UlMjZ1dG1fY29udGVudCUzZGdfX3AlMjZjcmVhdGl2ZSUzZCUyNmtleXdvcmQlM2RweXRob24lMjUyMGNvdXJzZSUyNm1hdGNodHlwZSUzZHAlMjZuZXR3b3JrJTNkbyUyNmRldmljZSUzZGMlMjZ1cmwlM2RodHRwcyUyNTNBJTI1MkYlMjUyRm9ubGluZS5jb3JuZWxsLmVkdSUyNTJGcHl0aG9uLWNlcnRpZmljYXRlcyUyNTJGJTI1M0ZjcmVhdGl2ZSUyNTNEJTI1MjZrZXl3b3JkJTI1M0RweXRob24lMjUyNTIwY291cnNlJTI1MjZtYXRjaHR5cGUlMjUzRHAlMjUyNm5ldHdvcmslMjUzRG8lMjUyNmRldmljZSUyNTNEYyUyNnRhcmdldCUzZCUyNnBvc2l0aW9uJTNkJTI2bXNjbGtpZCUzZDhmZjk4ODZkZjU1NDEzNDIyZWIyZTAxODIwZTVmNjBk%26rlid%3D8ff9886df55413422eb2e01820e5f60d&vqd=4-67380395571001360237724092863580197405&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5047.1) 广告\\n    \\n    [Earn a **Python** Certificate from Cornell University. 100% Online. Enroll now! Topics include: **Python** Programming, **Python** for Data Science and **Python** Data Visualization.](https://duckduckgo.com/y.js?ad_domain=cornell.edu&ad_provider=bingv7aa&ad_type=txad&click_metadata=E3or1Yjh%2DsVvjNfz2oO9KXjf_l90hOEv0GDO_0xQltLD1A_P4C_LbLwtPNrveEYzBiD%2DyY2uOjAz4OO60WO8NMKEpyUhHb5xAhKst83kGrqBlQ4VG%2DJ0n_O5B38H6LbT.vrohuH9hGYdmtT_PIUDDWA&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=2404dbc49221fa62dd0162b3567d5fba542d0379bed776c6e95dc017a9044fe8&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8j%2DHsgFiigqWNZks5GPrj3zVUCUyu9X1cIX7pKD5yQR0jaoLDve94tjzUgtcgfq%2DxImkEPWy5xrfHO50Ts4_1dia5dkgVb_LwFeXUDGlTF0OBObKZmny5Vls9IcqNVJGQe44GN3kzKgdhJ6yeJZ4lnuU%2DfQE%2DzHPm3u%2Dtqhdpw6HDzSBC2pr5Fh2OxODT1IRVGj2lVSDICOEPOefbspO7j%2Dwxdz3rB8TuP2v2EAcFlZnYCxBqMIDoExgKnnXZuiOQ2ad3DvFLCYO0aco5mkFYOIffGrZFvxmBUpS0SEFoHxu1bYyf5CDflOuQo7uxuzysDrNdrim8fRbD5%2Dlc4QA1yLt03RobN83_foO78RnEv4seRkMPpzKgtBXhItmXHMWoVtscYlcivn_j6wUMEBkmoTdX8LRtMKoZiKTCN8vLwiChUFKyctoiTo4XZzEplxFVbaF%2DDzDolly4BmhbYL2poQG__6SLh7toTS5mv6ITQvRwvu4e_iGrlCmr0YfenbnI0FkcfNtIf_113XElf%2DWRDc%2DmNEtBW0gnAdd4JqY8cGaeH5M2QwZIdth3NnpA9MxYLOt44QEOheTHAxxgVFsVF2M7d_Qwy0oBrb0pJdJoOzDEKmd_Nj1E22Fby2blXKzDZFubLjDUfog2zPIaIVCspw75EXm%2DsVBCuAls3eDhDY7yYc_3cb7E5xS7GI_xuF6G7Lfa5jr_UFUq3q75XN_flbf68dxuG2qo_tQAxT99vRs7Xuq0hH6stw5J1pPIe8lAcq0gRQ%26u%3DaHR0cHMlM2ElMmYlMmZvbmxpbmUuY29ybmVsbC5lZHUlMmZweXRob24tY2VydGlmaWNhdGVzJTJmJTNmY3JlYXRpdmUlM2QlMjZrZXl3b3JkJTNkcHl0aG9uJTI1MjBjb3Vyc2UlMjZtYXRjaHR5cGUlM2RwJTI2bmV0d29yayUzZG8lMjZkZXZpY2UlM2RjJTI2dXRtX3NvdXJjZSUzZGJpbmclMjZ1dG1fbWVkaXVtJTNkY3BjJTI2dXRtX2NhbXBhaWduJTNkZGF0YXNjaWVuY2VfVVMlMmItJTJiUHl0aG9uJTJiZm9yJTJiRGF0YSUyYlNjaWVuY2UlMmItJTJiQmluZyUyNnV0bV90ZXJtJTNkcHl0aG9uJTI1MjBjb3Vyc2UlMjZ1dG1fY29udGVudCUzZGdfX3AlMjZjcmVhdGl2ZSUzZCUyNmtleXdvcmQlM2RweXRob24lMjUyMGNvdXJzZSUyNm1hdGNodHlwZSUzZHAlMjZuZXR3b3JrJTNkbyUyNmRldmljZSUzZGMlMjZ1cmwlM2RodHRwcyUyNTNBJTI1MkYlMjUyRm9ubGluZS5jb3JuZWxsLmVkdSUyNTJGcHl0aG9uLWNlcnRpZmljYXRlcyUyNTJGJTI1M0ZjcmVhdGl2ZSUyNTNEJTI1MjZrZXl3b3JkJTI1M0RweXRob24lMjUyNTIwY291cnNlJTI1MjZtYXRjaHR5cGUlMjUzRHAlMjUyNm5ldHdvcmslMjUzRG8lMjUyNmRldmljZSUyNTNEYyUyNnRhcmdldCUzZCUyNnBvc2l0aW9uJTNkJTI2bXNjbGtpZCUzZDhmZjk4ODZkZjU1NDEzNDIyZWIyZTAxODIwZTVmNjBk%26rlid%3D8ff9886df55413422eb2e01820e5f60d&vqd=4-67380395571001360237724092863580197405&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5047.1)  \\n    \\n    [Flexible Start Dates · Top Education Provider · Grow Your Career](https://duckduckgo.com/y.js?ad_domain=cornell.edu&ad_provider=bingv7aa&ad_type=txad&click_metadata=E3or1Yjh%2DsVvjNfz2oO9KXjf_l90hOEv0GDO_0xQltLD1A_P4C_LbLwtPNrveEYzBiD%2DyY2uOjAz4OO60WO8NMKEpyUhHb5xAhKst83kGrqBlQ4VG%2DJ0n_O5B38H6LbT.vrohuH9hGYdmtT_PIUDDWA&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=2404dbc49221fa62dd0162b3567d5fba542d0379bed776c6e95dc017a9044fe8&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8j%2DHsgFiigqWNZks5GPrj3zVUCUyu9X1cIX7pKD5yQR0jaoLDve94tjzUgtcgfq%2DxImkEPWy5xrfHO50Ts4_1dia5dkgVb_LwFeXUDGlTF0OBObKZmny5Vls9IcqNVJGQe44GN3kzKgdhJ6yeJZ4lnuU%2DfQE%2DzHPm3u%2Dtqhdpw6HDzSBC2pr5Fh2OxODT1IRVGj2lVSDICOEPOefbspO7j%2Dwxdz3rB8TuP2v2EAcFlZnYCxBqMIDoExgKnnXZuiOQ2ad3DvFLCYO0aco5mkFYOIffGrZFvxmBUpS0SEFoHxu1bYyf5CDflOuQo7uxuzysDrNdrim8fRbD5%2Dlc4QA1yLt03RobN83_foO78RnEv4seRkMPpzKgtBXhItmXHMWoVtscYlcivn_j6wUMEBkmoTdX8LRtMKoZiKTCN8vLwiChUFKyctoiTo4XZzEplxFVbaF%2DDzDolly4BmhbYL2poQG__6SLh7toTS5mv6ITQvRwvu4e_iGrlCmr0YfenbnI0FkcfNtIf_113XElf%2DWRDc%2DmNEtBW0gnAdd4JqY8cGaeH5M2QwZIdth3NnpA9MxYLOt44QEOheTHAxxgVFsVF2M7d_Qwy0oBrb0pJdJoOzDEKmd_Nj1E22Fby2blXKzDZFubLjDUfog2zPIaIVCspw75EXm%2DsVBCuAls3eDhDY7yYc_3cb7E5xS7GI_xuF6G7Lfa5jr_UFUq3q75XN_flbf68dxuG2qo_tQAxT99vRs7Xuq0hH6stw5J1pPIe8lAcq0gRQ%26u%3DaHR0cHMlM2ElMmYlMmZvbmxpbmUuY29ybmVsbC5lZHUlMmZweXRob24tY2VydGlmaWNhdGVzJTJmJTNmY3JlYXRpdmUlM2QlMjZrZXl3b3JkJTNkcHl0aG9uJTI1MjBjb3Vyc2UlMjZtYXRjaHR5cGUlM2RwJTI2bmV0d29yayUzZG8lMjZkZXZpY2UlM2RjJTI2dXRtX3NvdXJjZSUzZGJpbmclMjZ1dG1fbWVkaXVtJTNkY3BjJTI2dXRtX2NhbXBhaWduJTNkZGF0YXNjaWVuY2VfVVMlMmItJTJiUHl0aG9uJTJiZm9yJTJiRGF0YSUyYlNjaWVuY2UlMmItJTJiQmluZyUyNnV0bV90ZXJtJTNkcHl0aG9uJTI1MjBjb3Vyc2UlMjZ1dG1fY29udGVudCUzZGdfX3AlMjZjcmVhdGl2ZSUzZCUyNmtleXdvcmQlM2RweXRob24lMjUyMGNvdXJzZSUyNm1hdGNodHlwZSUzZHAlMjZuZXR3b3JrJTNkbyUyNmRldmljZSUzZGMlMjZ1cmwlM2RodHRwcyUyNTNBJTI1MkYlMjUyRm9ubGluZS5jb3JuZWxsLmVkdSUyNTJGcHl0aG9uLWNlcnRpZmljYXRlcyUyNTJGJTI1M0ZjcmVhdGl2ZSUyNTNEJTI1MjZrZXl3b3JkJTI1M0RweXRob24lMjUyNTIwY291cnNlJTI1MjZtYXRjaHR5cGUlMjUzRHAlMjUyNm5ldHdvcmslMjUzRG8lMjUyNmRldmljZSUyNTNEYyUyNnRhcmdldCUzZCUyNnBvc2l0aW9uJTNkJTI2bXNjbGtpZCUzZDhmZjk4ODZkZjU1NDEzNDIyZWIyZTAxODIwZTVmNjBk%26rlid%3D8ff9886df55413422eb2e01820e5f60d&vqd=4-67380395571001360237724092863580197405&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5047.1)  \\n    \\n    [**Courses**: Python for Data Science, Python Programming](https://duckduckgo.com/y.js?ad_domain=cornell.edu&ad_provider=bingv7aa&ad_type=txad&click_metadata=E3or1Yjh%2DsVvjNfz2oO9KXjf_l90hOEv0GDO_0xQltLD1A_P4C_LbLwtPNrveEYzBiD%2DyY2uOjAz4OO60WO8NMKEpyUhHb5xAhKst83kGrqBlQ4VG%2DJ0n_O5B38H6LbT.vrohuH9hGYdmtT_PIUDDWA&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=2404dbc49221fa62dd0162b3567d5fba542d0379bed776c6e95dc017a9044fe8&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8j%2DHsgFiigqWNZks5GPrj3zVUCUyu9X1cIX7pKD5yQR0jaoLDve94tjzUgtcgfq%2DxImkEPWy5xrfHO50Ts4_1dia5dkgVb_LwFeXUDGlTF0OBObKZmny5Vls9IcqNVJGQe44GN3kzKgdhJ6yeJZ4lnuU%2DfQE%2DzHPm3u%2Dtqhdpw6HDzSBC2pr5Fh2OxODT1IRVGj2lVSDICOEPOefbspO7j%2Dwxdz3rB8TuP2v2EAcFlZnYCxBqMIDoExgKnnXZuiOQ2ad3DvFLCYO0aco5mkFYOIffGrZFvxmBUpS0SEFoHxu1bYyf5CDflOuQo7uxuzysDrNdrim8fRbD5%2Dlc4QA1yLt03RobN83_foO78RnEv4seRkMPpzKgtBXhItmXHMWoVtscYlcivn_j6wUMEBkmoTdX8LRtMKoZiKTCN8vLwiChUFKyctoiTo4XZzEplxFVbaF%2DDzDolly4BmhbYL2poQG__6SLh7toTS5mv6ITQvRwvu4e_iGrlCmr0YfenbnI0FkcfNtIf_113XElf%2DWRDc%2DmNEtBW0gnAdd4JqY8cGaeH5M2QwZIdth3NnpA9MxYLOt44QEOheTHAxxgVFsVF2M7d_Qwy0oBrb0pJdJoOzDEKmd_Nj1E22Fby2blXKzDZFubLjDUfog2zPIaIVCspw75EXm%2DsVBCuAls3eDhDY7yYc_3cb7E5xS7GI_xuF6G7Lfa5jr_UFUq3q75XN_flbf68dxuG2qo_tQAxT99vRs7Xuq0hH6stw5J1pPIe8lAcq0gRQ%26u%3DaHR0cHMlM2ElMmYlMmZvbmxpbmUuY29ybmVsbC5lZHUlMmZweXRob24tY2VydGlmaWNhdGVzJTJmJTNmY3JlYXRpdmUlM2QlMjZrZXl3b3JkJTNkcHl0aG9uJTI1MjBjb3Vyc2UlMjZtYXRjaHR5cGUlM2RwJTI2bmV0d29yayUzZG8lMjZkZXZpY2UlM2RjJTI2dXRtX3NvdXJjZSUzZGJpbmclMjZ1dG1fbWVkaXVtJTNkY3BjJTI2dXRtX2NhbXBhaWduJTNkZGF0YXNjaWVuY2VfVVMlMmItJTJiUHl0aG9uJTJiZm9yJTJiRGF0YSUyYlNjaWVuY2UlMmItJTJiQmluZyUyNnV0bV90ZXJtJTNkcHl0aG9uJTI1MjBjb3Vyc2UlMjZ1dG1fY29udGVudCUzZGdfX3AlMjZjcmVhdGl2ZSUzZCUyNmtleXdvcmQlM2RweXRob24lMjUyMGNvdXJzZSUyNm1hdGNodHlwZSUzZHAlMjZuZXR3b3JrJTNkbyUyNmRldmljZSUzZGMlMjZ1cmwlM2RodHRwcyUyNTNBJTI1MkYlMjUyRm9ubGluZS5jb3JuZWxsLmVkdSUyNTJGcHl0aG9uLWNlcnRpZmljYXRlcyUyNTJGJTI1M0ZjcmVhdGl2ZSUyNTNEJTI1MjZrZXl3b3JkJTI1M0RweXRob24lMjUyNTIwY291cnNlJTI1MjZtYXRjaHR5cGUlMjUzRHAlMjUyNm5ldHdvcmslMjUzRG8lMjUyNmRldmljZSUyNTNEYyUyNnRhcmdldCUzZCUyNnBvc2l0aW9uJTNkJTI2bXNjbGtpZCUzZDhmZjk4ODZkZjU1NDEzNDIyZWIyZTAxODIwZTVmNjBk%26rlid%3D8ff9886df55413422eb2e01820e5f60d&vqd=4-67380395571001360237724092863580197405&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5047.1)  \\n    \\n    *   [\\n        \\n        ### 10-week Online Program\\n        \\n        Taught by Expert Cornell Faculty Get the Playbook You Need to...\\n        \\n        ](https://duckduckgo.com/y.js?ad_domain=cornell.edu&ad_provider=bingv7aa&ad_type=txad&click_metadata=jl6gwiu3hCm2sue7Ty1JNmgb8Al3ftwAtvlPhkD110e2WYEZv5P5rqWZgnSFuG63apEptVd3KiZydmxGlh3KTb2YKXkYxvhZEEXv7%2DODrWFyp3E3PT4GJ1w9nE4Ijb9H.C%2DKm7ogSzKwF%2DpcHOnQh6g&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=29205abb9af9c9ada372110de6e6817842471f53873d5392c55ab3d47d5f8c5c&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De81cNMwUuFQwEET73I3NbK8TVUCUzLi7hVV_NZ%2DNFJFViGgOz3LEpRkMG1ZH6J0h0tCGwPQJ1Aiks5nySXcW58rpB82lUPHsHE6d7QV3_4sI56tpJ3nlmqk1QokRO3dBFjFzzRSS9qpgln38ogUG0IxRqW2M9c38MPjJn%2DurI67RujMlm98izMRLRlXJtqAa50RgdnSEI45P8jlRj8WtgEAMZGm6v9CbzGNAEz6q63wGAd8lVR%2DH_hWiD%2DJNRqtJT5HDm4uQCohGQG6yHaqbzsOuHh_bFLK0LoWUrcc0a9%2Dwj3DJEfd5oEpjkiKcue7TP%2DW3J8es9QO9NUAxS5h1Z%2D4POjM9oZfbXGSSvOwHFEumvXc9ZJsJ4FV_OScoNlyS6nks_0LRNFJWTO8Nq1I3lcrip1xFZsQrY0dpJDW940jXzpu_AxsjL0Z5iMllYs35EY0pex9h%2DeaBoR0q65oivshgGmrRdticEFR0gXd8FU_caGRLhaLGz0pD009T8lUSEDx6JL2R8ONEVfzBAwtmSQnwJHApxWAo3xka%2D1nuQCrJH_4_E%2Dp%2DebZ6nBJ%2Dr7sp0jcaKwblIu_LD8Cxo0v3AeF98sBwPPg_0Uk60QRw_8YlZnK0UQC6j5yNffXQK0CxIgXlZtmIi3DJ898vB_EjHlZPDEuHCj2x5P4oqkf2CeHRkjnPFWSfuSnnfu%2D6rvQ6LJBoXqZUpeLTKyymjX9fjccIyIt90Ml4747ANsKCIwkqJYMV95v0Bv370IsGwino8RVqhvwopxQIZLpJGc9yzvQO_0ZsQ%26u%3DaHR0cHMlM2ElMmYlMmZlY29ybmVsbC5jb3JuZWxsLmVkdSUyZmNlcnRpZmljYXRlcyUyZmVuZ2luZWVyaW5nJTJmaW5ub3ZhdGlvbi1zdHJhdGVneSUyZiUzZm1zY2xraWQlM2QwNzRhOTAyYTA2MmExOTQyODdmY2Y4NWZkMzliZGNiNiUyNnV0bV9zb3VyY2UlM2RiaW5nJTI2dXRtX21lZGl1bSUzZGNwYyUyNnV0bV9jYW1wYWlnbiUzZGRhdGFzY2llbmNlX1VTJTJiLSUyYlB5dGhvbiUyYmZvciUyYkRhdGElMmJTY2llbmNlJTJiLSUyYkJpbmclMjZ1dG1fdGVybSUzZHB5dGhvbiUyNTIwY291cnNlJTI2dXRtX2NvbnRlbnQlM2RnX19wJTI2Y3JlYXRpdmUlM2QlMjZrZXl3b3JkJTNkcHl0aG9uJTI1MjBjb3Vyc2UlMjZtYXRjaHR5cGUlM2RwJTI2bmV0d29yayUzZG8lMjZkZXZpY2UlM2RjJTI2dXJsJTNkaHR0cHMlMjUzQSUyNTJGJTI1MkZlY29ybmVsbC5jb3JuZWxsLmVkdSUyNTJGY2VydGlmaWNhdGVzJTI1MkZlbmdpbmVlcmluZyUyNTJGaW5ub3ZhdGlvbi1zdHJhdGVneSUyNTJGJTI1M0Ztc2Nsa2lkJTI1M0QwNzRhOTAyYTA2MmExOTQyODdmY2Y4NWZkMzliZGNiNiUyNnRhcmdldCUzZCUyNnBvc2l0aW9uJTNkJTI2bXNjbGtpZCUzZDA3NGE5MDJhMDYyYTE5NDI4N2ZjZjg1ZmQzOWJkY2I2%26rlid%3D074a902a062a194287fcf85fd39bdcb6&vqd=4-278207141484068784674209792995215601002&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5052.1)\\n    *   [\\n        \\n        ### Python For Beginners\\n        \\n        Take your Python skills to the next level with the experts at...\\n        \\n        ](https://duckduckgo.com/y.js?ad_domain=cornell.edu&ad_provider=bingv7aa&ad_type=txad&click_metadata=t_q8Br%2DW6YYYUfBS2hrLXe8zP%2DffY1GpphbqFckFQxLC5RirI2SFvV42ZmOCB1_SojPCzxA8V6rjIX%2DmYiIE8dAFFeEzxiVrAsM7BRbw3JdD3enW28EFckpvw8C6HCEt.x7zphZEgMNNi6QlPnbpWlA&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=769fd1463f926b6a9f71b652bd1d962fa1ed98a8a1583c7f5735c45a5de508ff&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8nHo8Po8Xrxq%2DL964dBM86DVUCUxNk7nQ2dfqW7gI8OR6tH_s6NIaIpTPQtCKj3bVGoTZ7K79cCnpvLLhxFofaN%2D1ndFmNPjNqIhdIQZeHIqtsQYYoCo7bIhKLfl5gLMxbTg3JAMDVn4DoWtS0EsmH2p87R91d8GHTBFAL_%2DtRTpX6qV2H%2D25813h3QHHRAqasmATa6%2DUW%2D6KYn703Y%2DpHjqiM4gNcQVjJXyVzYAFZpb5GnIvWZZKoPs%2DZ5sFbY7F1Bwji3XPmqiMLxQSQ106nccXXHzcsXEcr1kw7FuHGGruv3I7zGDKELRbtPSKRm0ir7yaRY9OF6AK6llcef2aYLX47ssbZogUIla9NhPwN2rnMogETfYvRLWEpoeNDcizGG28BOm9TRuWFipRnZxFD5maatWxwAF10qoz_dMJmWg6My1Ac5sRoIwuP%2Dcz1xF7xeBR%2Dr2A6c1ZjkXRjT6WTy9upzmjNQs0d1UhkBYkaVQAbDviIJxCS1xEYLtygr3amXEkT4u_VjAbMixiHTNHSS02fCuhEFtzRSOBb26nLF1t4oZgX3xaCHw4dizFGd0Y6UNzhNG%2D%2Dvxv2AC57J09sjeC0t6BAejcBPW0twfLyb4whFxmgFkv6cXxrem%2DXxqrgw9E9iJOewkJLhuxOyS81kW849UAovMwlNp0Pfb5E_az%2Dl5xg8Iu9Lh7jTScVDx_qL0AWIxfSKBrX6LVlkPgO747bmEgDX5RfbaIx6eZbeUwyi08AJmrW62oCn0hJyZHilQJl_PzSnVwu9tECm9ykEHFde0%26u%3DaHR0cHMlM2ElMmYlMmZvbmxpbmUuY29ybmVsbC5lZHUlMmZweXRob24tY2VydGlmaWNhdGVzJTJmJTNmbXNjbGtpZCUzZGU2YTA1N2NkMjFhYTFmZTQ1ZTY4M2ZiOGY0MWJkZWY4JTI2dXRtX3NvdXJjZSUzZGJpbmclMjZ1dG1fbWVkaXVtJTNkY3BjJTI2dXRtX2NhbXBhaWduJTNkZGF0YXNjaWVuY2VfVVMlMmItJTJiUHl0aG9uJTJiZm9yJTJiRGF0YSUyYlNjaWVuY2UlMmItJTJiQmluZyUyNnV0bV90ZXJtJTNkcHl0aG9uJTI1MjBjb3Vyc2UlMjZ1dG1fY29udGVudCUzZGdfX3AlMjZjcmVhdGl2ZSUzZCUyNmtleXdvcmQlM2RweXRob24lMjUyMGNvdXJzZSUyNm1hdGNodHlwZSUzZHAlMjZuZXR3b3JrJTNkbyUyNmRldmljZSUzZGMlMjZ1cmwlM2RodHRwcyUyNTNBJTI1MkYlMjUyRm9ubGluZS5jb3JuZWxsLmVkdSUyNTJGcHl0aG9uLWNlcnRpZmljYXRlcyUyNTJGJTI1M0Ztc2Nsa2lkJTI1M0RlNmEwNTdjZDIxYWExZmU0NWU2ODNmYjhmNDFiZGVmOCUyNnRhcmdldCUzZCUyNnBvc2l0aW9uJTNkJTI2bXNjbGtpZCUzZGU2YTA1N2NkMjFhYTFmZTQ1ZTY4M2ZiOGY0MWJkZWY4%26rlid%3De6a057cd21aa1fe45e683fb8f41bdef8&vqd=4-20205934793076508922362940465851431867&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5054.1)\\n    *   [\\n        \\n        ### Certificate Programs\\n        \\n        Earn an online certificate from Cornell. Learn more today!\\n        \\n        ](https://duckduckgo.com/y.js?ad_domain=cornell.edu&ad_provider=bingv7aa&ad_type=txad&click_metadata=Ggk711JMCiRifhB13QbBPpWnNazRzaMMLkjOJJI1gjcih0GOzkDp1Zixue12QzQyMdymGv_xkxZ0ctUvjy6bXUBKBxXfFtErf1H%2DJ3lktus1iHIGTCb3qW1dTHW1R3S6.J9fC4aqG2Ttyth2V7afq2g&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=5b3d3897d426591ed1ad3f8f8a0affd7fcd6b8e8375dd83b84f4a9b3e1faf137&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De82MN3tk4yxxqTd7E70Pjj9DVUCUxX4e01FRKBmzUqRF1UV0tF_e5_VhSHLQ2xxaAiCtw2CARGPU8BzvtFz5RMxjPqV70m8lMzfmspP1SKvusEB5kW2L2csUyK5nPumtpwYxasGVFUQBmRgNxOur2dvHAJuS1Zv2rUYO_KvgUT11hlyIf7iBNASUcVToAbn9R2WDDJtQd5XM9FfdYEnbPXIoLY3xc6rRRgsTrg_0fSnp4As0C2lMehviPUgkEQjJgsmZyL0QaU4tQeZHPPZw%2D7t45Je9bLj642quFKExbAeIbWqiGziRzEOMlZBGis_hyfHs%2DwknIM7d%2D8%2D%2Dl8PjA_uz9lBW9xStoTI5a1cT2tRcKAYdgR734M4JXxMx1F9xag272Ty41Ri1y%2DVZzyc8PSzyicIuB4CyksDyxxt1pJegcejOTKubhhGyZNRPjbVaAYWb4pIrX1RvMa5aIqx22FMUQi515pSNqkbCU3hCEtiYLNAxlNIZHpFyPdPxVS_6SJ0yP_avDMsPWmY2MMcVDkTZOcReMLVuzLISEdnQZPM01WUh5W4_5ecGMgj%2DJ4LQ38uOUafcJECZ9hslCO6ycvW9lGS7yz33OUjLBahA13qMamxGdpkEP9p_aAReFaTvzcpcYRIYR5MhKnLn37Dx1MJnVEskVf%2Dz0l98UVIWOe2DobjOwqlLxZq7tFWG3Rh0mqBeQJ368sxBbOOGn9TtmpHOZugthKNAOzDJ2IZ0P61WAQt%2Dzec03610dEaPWVA8%2DSEA8yXZYPP9_etyW7kAYkBBs56AY%26u%3DaHR0cHMlM2ElMmYlMmZvbmxpbmUuY29ybmVsbC5lZHUlMmZjZXJ0aWZpY2F0ZXMlMmYlM2Ztc2Nsa2lkJTNkNjkyYTQ3MTdjNjFiMTY1M2UzOWYyZjYyMTNhYTA0YmMlMjZ1dG1fc291cmNlJTNkYmluZyUyNnV0bV9tZWRpdW0lM2RjcGMlMjZ1dG1fY2FtcGFpZ24lM2RkYXRhc2NpZW5jZV9VUyUyYi0lMmJQeXRob24lMmJmb3IlMmJEYXRhJTJiU2NpZW5jZSUyYi0lMmJCaW5nJTI2dXRtX3Rlcm0lM2RweXRob24lMjUyMGNvdXJzZSUyNnV0bV9jb250ZW50JTNkZ19fcCUyNmNyZWF0aXZlJTNkJTI2a2V5d29yZCUzZHB5dGhvbiUyNTIwY291cnNlJTI2bWF0Y2h0eXBlJTNkcCUyNm5ldHdvcmslM2RvJTI2ZGV2aWNlJTNkYyUyNnVybCUzZGh0dHBzJTI1M0ElMjUyRiUyNTJGb25saW5lLmNvcm5lbGwuZWR1JTI1MkZjZXJ0aWZpY2F0ZXMlMjUyRiUyNTNGbXNjbGtpZCUyNTNENjkyYTQ3MTdjNjFiMTY1M2UzOWYyZjYyMTNhYTA0YmMlMjZ0YXJnZXQlM2QlMjZwb3NpdGlvbiUzZCUyNm1zY2xraWQlM2Q2OTJhNDcxN2M2MWIxNjUzZTM5ZjJmNjIxM2FhMDRiYw%26rlid%3D692a4717c61b1653e39f2f6213aa04bc&vqd=4-159820194895249823208052818288494384421&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5056.1)\\n    *   [\\n        \\n        ### Real Estate Courses\\n        \\n        Earn your realestate certificate. Investing and finance.\\n        \\n        ](https://duckduckgo.com/y.js?ad_domain=cornell.edu&ad_provider=bingv7aa&ad_type=txad&click_metadata=4RIRanFfSB_c_5MPTpVVjckIDOaCMwjentjcMRgVFlp8kp_RQ44eWVkbRuaP865EdCTtGHUMbh0X2FDjBWvodw3HuU05Fk%2DW_o6FcWAW_xILvwgfcoDhXPLwMfej1GQh.UHs5hdP8OLU_GCv9nmS98Q&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=1990d99aac0ce9b6216f8aafecef756534eaf95b28ae6f8cdd3a874a322b9f1b&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8twlvrK1ep6bga9Ultj_0qjVUCUxz1JOP8qpgiQ7BSZQiE9SVduaKO_dOiau_HfXmoI9kDvxIeZViHj8hNpEENnVzkrjcrx0UDSgGbJnr3Jk0OeJKa3q1wx1jfYIydyfZpWyz%2DzoLNFbTYy1BOM%2DxJgRqmWhzRWlnazMQF4LenQfePziWD1eBuiD92RdsOKqTK%2DOldD6XdGzsPEu%2DLOe0u22B55lH%2DZUEWXnNHP0yiPeMzJO6URCtB_uR9F1cmV04_O5yZHOQn_Dks2PU4X4VcRP7iExHqHxqYjfaUljfzND_cYnqHHABDs%2DXoUUX0CIch5YhimRKq1soTSlI83NeFq1kG2zf1iYrdB06a69KwFXm1jhyUfL8szamLmyCVAJ77w996GlwNiDxuwsiJc%2Dn%2DBhMIzi0GQD7twmhsW5Ns7icQqIbSNb50tZC3ZAz5Y9a3sc8DCGbwYCpY6DWucblLO8_2bZkR3s2ltoNkEqCNLh%2Dc%2DVutSdq48DmsziOHYyXTyMTNpnZ8lOaUz9G9VlCyfbuCoE2IHIQdBImcwvS887IagXWs%2DaKVhsKoAuq%2D9C7xesadPE38UwBne2wHM89rIijQCDMFNIZupUhlikk4l6%2DH8A7ntrXSBABnnvZDNnMGttp9MdfvRAnCH9B4bISwhLFVYAUUo6I3dyaJ7CAGmxpyppK0TS20dQ5w715%2Dhqykw6_TqubaF52a6hwq2R8qs_CxdVabtqbzlBr5Ct9pOhu9ACQTsFNTTzMgOmEGtdgLYDzzGshzzMns%2D5HQa4nJ6E7o%2Dk%26u%3DaHR0cHMlM2ElMmYlMmZvbmxpbmUuY29ybmVsbC5lZHUlMmZjZXJ0aWZpY2F0ZXMlMmZyZWFsLWVzdGF0ZSUyZiUzZm1zY2xraWQlM2Q0N2RiYmMyNThjMmMxY2VkZWQ1NmQ0NTA1OTlkZDZjNSUyNnV0bV9zb3VyY2UlM2RiaW5nJTI2dXRtX21lZGl1bSUzZGNwYyUyNnV0bV9jYW1wYWlnbiUzZGRhdGFzY2llbmNlX1VTJTJiLSUyYlB5dGhvbiUyYmZvciUyYkRhdGElMmJTY2llbmNlJTJiLSUyYkJpbmclMjZ1dG1fdGVybSUzZHB5dGhvbiUyNTIwY291cnNlJTI2dXRtX2NvbnRlbnQlM2RnX19wJTI2Y3JlYXRpdmUlM2QlMjZrZXl3b3JkJTNkcHl0aG9uJTI1MjBjb3Vyc2UlMjZtYXRjaHR5cGUlM2RwJTI2bmV0d29yayUzZG8lMjZkZXZpY2UlM2RjJTI2dXJsJTNkaHR0cHMlMjUzQSUyNTJGJTI1MkZvbmxpbmUuY29ybmVsbC5lZHUlMjUyRmNlcnRpZmljYXRlcyUyNTJGcmVhbC1lc3RhdGUlMjUyRiUyNTNGbXNjbGtpZCUyNTNENDdkYmJjMjU4YzJjMWNlZGVkNTZkNDUwNTk5ZGQ2YzUlMjZ0YXJnZXQlM2QlMjZwb3NpdGlvbiUzZCUyNm1zY2xraWQlM2Q0N2RiYmMyNThjMmMxY2VkZWQ1NmQ0NTA1OTlkZDZjNQ%26rlid%3D47dbbc258c2c1ceded56d450599dd6c5&vqd=4-***********4349587345527685365180966324&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5058.1)\\n    \\n2.  superprof.com\\n    \\n    举报广告\\n    \\n    [\\n    \\n    ![](//external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fwww.bing.com%2Fth%3Fpid%3DAdsPlus%26id%3DODLS.3020b3be-4a82-4869-b025-29e55cdd09d3%26w%3D32%26h%3D32&f=1)\\n    \\n    Superprof\\n    \\n    https://www.superprof.com › Python › Tutors\\n    \\n    \\n    \\n    ](https://duckduckgo.com/y.js?ad_domain=superprof.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=6GpLQvmWKCd0fpH8n1fn6wMHecHQP0%2Drcd0L85AYJv6ySrAifICl_SvH5HiQ_23wvMbgSK4Hx4j5XQcLbfvr8OtypbN_cc0vhA8jbqU%2D_RO8UrOQ3eQTs7dZK0IGyFeu.HwVeRdL2E3s8ujrytSTGhQ&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=c064fd1cb69a905e08a4cf4572f779ca19e807f64295d057e269b071c33871ab&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8UK1nAYSN_ZhHe5XZMXRGajVUCUzcsg1eNabqNLjZxzmj_KuXfRG2seSUK%2DvXD_S517SwUc4YO5exzr5%2Dh3kvzUvfiGkITTqAK0mzMTtANo5LTe3MvuQmdoVVlAeQAlaWif6%2DDgJDQ0uNRXUdndKx5_hirT9FNDr1e33hLWKHCIMuhQ97SC29Rvv6WW_KUIDCKAK5WrPECG8EvCkPLlJHbm8YviHQc4yaEcNUgcxJ3_N2P3AjAGby5XwwXzoKY0fXKh0dCgI0ooOtxv0CDc3ku11Ck2KZKatp6S4K7TQmyOkhriGakzq6rk8ct%2D4d56B75I_9nMo5UUA1hB0zU0l3A2bUcCFaBWjJbXAblOvACxmnHjuF_zmuRLXNZidGL6QSEaRLrdSTtDcEfTujY8lZRG71DX7XEvcE0jW6XU3bmMr9iTDoKeFTz26y4AbSLVCDETQ8nuPMldIP6oEQ7qJXajM9IfgxhZd33yOj41xKjvbSGr8s0JlviB9oQaD1tQaPEFrUOr00EAapk8_PqFvCNC1II2z5FYf1XnJ7UaGtwsNCSloWlT0JSVuV92WJO0RaU50KWnwFLjZEa2Pfdrql0HrW3feTDyiB2q%2Dl6e4DfX95T2kBAvp5yg7ZNwu5PenLa%2DqOygtGG9uYVBr7caCvRYoVrK1woVGnUYHJQIbMce_MTPtAv7IyORx_IcL%2DEjnh2GjHpRiHOPZZMQOW9M7Zedt734gH10_RWc_s6pQhTU%2DDozgpcY2QQgKAQP1q%2DJ410WUZ0A%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cuc3VwZXJwcm9mLmNvbSUyZiUzZmFmZiUzZDM4MzFfRiUyNmlkX21hdCUzZDY3NyUyNmt3JTNkcHl0aG9uJTI1MjB0dXRvciUyNnV0bV9zb3VyY2UlM2RiaW5nJTI2dXRtX21lZGl1bSUzZGFkc19lbCUyNnV0bV9jYW1wYWlnbiUzZEVMX1VTX1NFQVJDSF9HZW5lcmFsJTI2bXNjbGtpZCUzZDI4Y2M4ZmVlNDQ2ZTExZDZhMTdhZTc5YjkxZGZmYzRm%26rlid%3D28cc8fee446e11d6a17ae79b91dffc4f&vqd=4-127127000978368128278099799263862733426&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5062.1)\\n    \\n    ## [Meet a Python Tutor in 2 Min - Private Python Tutors](https://duckduckgo.com/y.js?ad_domain=superprof.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=6GpLQvmWKCd0fpH8n1fn6wMHecHQP0%2Drcd0L85AYJv6ySrAifICl_SvH5HiQ_23wvMbgSK4Hx4j5XQcLbfvr8OtypbN_cc0vhA8jbqU%2D_RO8UrOQ3eQTs7dZK0IGyFeu.HwVeRdL2E3s8ujrytSTGhQ&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=c064fd1cb69a905e08a4cf4572f779ca19e807f64295d057e269b071c33871ab&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8UK1nAYSN_ZhHe5XZMXRGajVUCUzcsg1eNabqNLjZxzmj_KuXfRG2seSUK%2DvXD_S517SwUc4YO5exzr5%2Dh3kvzUvfiGkITTqAK0mzMTtANo5LTe3MvuQmdoVVlAeQAlaWif6%2DDgJDQ0uNRXUdndKx5_hirT9FNDr1e33hLWKHCIMuhQ97SC29Rvv6WW_KUIDCKAK5WrPECG8EvCkPLlJHbm8YviHQc4yaEcNUgcxJ3_N2P3AjAGby5XwwXzoKY0fXKh0dCgI0ooOtxv0CDc3ku11Ck2KZKatp6S4K7TQmyOkhriGakzq6rk8ct%2D4d56B75I_9nMo5UUA1hB0zU0l3A2bUcCFaBWjJbXAblOvACxmnHjuF_zmuRLXNZidGL6QSEaRLrdSTtDcEfTujY8lZRG71DX7XEvcE0jW6XU3bmMr9iTDoKeFTz26y4AbSLVCDETQ8nuPMldIP6oEQ7qJXajM9IfgxhZd33yOj41xKjvbSGr8s0JlviB9oQaD1tQaPEFrUOr00EAapk8_PqFvCNC1II2z5FYf1XnJ7UaGtwsNCSloWlT0JSVuV92WJO0RaU50KWnwFLjZEa2Pfdrql0HrW3feTDyiB2q%2Dl6e4DfX95T2kBAvp5yg7ZNwu5PenLa%2DqOygtGG9uYVBr7caCvRYoVrK1woVGnUYHJQIbMce_MTPtAv7IyORx_IcL%2DEjnh2GjHpRiHOPZZMQOW9M7Zedt734gH10_RWc_s6pQhTU%2DDozgpcY2QQgKAQP1q%2DJ410WUZ0A%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cuc3VwZXJwcm9mLmNvbSUyZiUzZmFmZiUzZDM4MzFfRiUyNmlkX21hdCUzZDY3NyUyNmt3JTNkcHl0aG9uJTI1MjB0dXRvciUyNnV0bV9zb3VyY2UlM2RiaW5nJTI2dXRtX21lZGl1bSUzZGFkc19lbCUyNnV0bV9jYW1wYWlnbiUzZEVMX1VTX1NFQVJDSF9HZW5lcmFsJTI2bXNjbGtpZCUzZDI4Y2M4ZmVlNDQ2ZTExZDZhMTdhZTc5YjkxZGZmYzRm%26rlid%3D28cc8fee446e11d6a17ae79b91dffc4f&vqd=4-127127000978368128278099799263862733426&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5062.1) 广告\\n    \\n    [Book **Python** lessons as low as $15/hr and pay as you go. Try a 1st free lesson today! Learn 3x faster with a private **Python** Tutor. Book your 1st free lesson today!](https://duckduckgo.com/y.js?ad_domain=superprof.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=6GpLQvmWKCd0fpH8n1fn6wMHecHQP0%2Drcd0L85AYJv6ySrAifICl_SvH5HiQ_23wvMbgSK4Hx4j5XQcLbfvr8OtypbN_cc0vhA8jbqU%2D_RO8UrOQ3eQTs7dZK0IGyFeu.HwVeRdL2E3s8ujrytSTGhQ&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=c064fd1cb69a905e08a4cf4572f779ca19e807f64295d057e269b071c33871ab&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8UK1nAYSN_ZhHe5XZMXRGajVUCUzcsg1eNabqNLjZxzmj_KuXfRG2seSUK%2DvXD_S517SwUc4YO5exzr5%2Dh3kvzUvfiGkITTqAK0mzMTtANo5LTe3MvuQmdoVVlAeQAlaWif6%2DDgJDQ0uNRXUdndKx5_hirT9FNDr1e33hLWKHCIMuhQ97SC29Rvv6WW_KUIDCKAK5WrPECG8EvCkPLlJHbm8YviHQc4yaEcNUgcxJ3_N2P3AjAGby5XwwXzoKY0fXKh0dCgI0ooOtxv0CDc3ku11Ck2KZKatp6S4K7TQmyOkhriGakzq6rk8ct%2D4d56B75I_9nMo5UUA1hB0zU0l3A2bUcCFaBWjJbXAblOvACxmnHjuF_zmuRLXNZidGL6QSEaRLrdSTtDcEfTujY8lZRG71DX7XEvcE0jW6XU3bmMr9iTDoKeFTz26y4AbSLVCDETQ8nuPMldIP6oEQ7qJXajM9IfgxhZd33yOj41xKjvbSGr8s0JlviB9oQaD1tQaPEFrUOr00EAapk8_PqFvCNC1II2z5FYf1XnJ7UaGtwsNCSloWlT0JSVuV92WJO0RaU50KWnwFLjZEa2Pfdrql0HrW3feTDyiB2q%2Dl6e4DfX95T2kBAvp5yg7ZNwu5PenLa%2DqOygtGG9uYVBr7caCvRYoVrK1woVGnUYHJQIbMce_MTPtAv7IyORx_IcL%2DEjnh2GjHpRiHOPZZMQOW9M7Zedt734gH10_RWc_s6pQhTU%2DDozgpcY2QQgKAQP1q%2DJ410WUZ0A%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cuc3VwZXJwcm9mLmNvbSUyZiUzZmFmZiUzZDM4MzFfRiUyNmlkX21hdCUzZDY3NyUyNmt3JTNkcHl0aG9uJTI1MjB0dXRvciUyNnV0bV9zb3VyY2UlM2RiaW5nJTI2dXRtX21lZGl1bSUzZGFkc19lbCUyNnV0bV9jYW1wYWlnbiUzZEVMX1VTX1NFQVJDSF9HZW5lcmFsJTI2bXNjbGtpZCUzZDI4Y2M4ZmVlNDQ2ZTExZDZhMTdhZTc5YjkxZGZmYzRm%26rlid%3D28cc8fee446e11d6a17ae79b91dffc4f&vqd=4-127127000978368128278099799263862733426&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5062.1)  \\n    \\n    [**Courses**: Beginner, Intermediate, Advanced](https://duckduckgo.com/y.js?ad_domain=superprof.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=6GpLQvmWKCd0fpH8n1fn6wMHecHQP0%2Drcd0L85AYJv6ySrAifICl_SvH5HiQ_23wvMbgSK4Hx4j5XQcLbfvr8OtypbN_cc0vhA8jbqU%2D_RO8UrOQ3eQTs7dZK0IGyFeu.HwVeRdL2E3s8ujrytSTGhQ&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=c064fd1cb69a905e08a4cf4572f779ca19e807f64295d057e269b071c33871ab&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8UK1nAYSN_ZhHe5XZMXRGajVUCUzcsg1eNabqNLjZxzmj_KuXfRG2seSUK%2DvXD_S517SwUc4YO5exzr5%2Dh3kvzUvfiGkITTqAK0mzMTtANo5LTe3MvuQmdoVVlAeQAlaWif6%2DDgJDQ0uNRXUdndKx5_hirT9FNDr1e33hLWKHCIMuhQ97SC29Rvv6WW_KUIDCKAK5WrPECG8EvCkPLlJHbm8YviHQc4yaEcNUgcxJ3_N2P3AjAGby5XwwXzoKY0fXKh0dCgI0ooOtxv0CDc3ku11Ck2KZKatp6S4K7TQmyOkhriGakzq6rk8ct%2D4d56B75I_9nMo5UUA1hB0zU0l3A2bUcCFaBWjJbXAblOvACxmnHjuF_zmuRLXNZidGL6QSEaRLrdSTtDcEfTujY8lZRG71DX7XEvcE0jW6XU3bmMr9iTDoKeFTz26y4AbSLVCDETQ8nuPMldIP6oEQ7qJXajM9IfgxhZd33yOj41xKjvbSGr8s0JlviB9oQaD1tQaPEFrUOr00EAapk8_PqFvCNC1II2z5FYf1XnJ7UaGtwsNCSloWlT0JSVuV92WJO0RaU50KWnwFLjZEa2Pfdrql0HrW3feTDyiB2q%2Dl6e4DfX95T2kBAvp5yg7ZNwu5PenLa%2DqOygtGG9uYVBr7caCvRYoVrK1woVGnUYHJQIbMce_MTPtAv7IyORx_IcL%2DEjnh2GjHpRiHOPZZMQOW9M7Zedt734gH10_RWc_s6pQhTU%2DDozgpcY2QQgKAQP1q%2DJ410WUZ0A%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cuc3VwZXJwcm9mLmNvbSUyZiUzZmFmZiUzZDM4MzFfRiUyNmlkX21hdCUzZDY3NyUyNmt3JTNkcHl0aG9uJTI1MjB0dXRvciUyNnV0bV9zb3VyY2UlM2RiaW5nJTI2dXRtX21lZGl1bSUzZGFkc19lbCUyNnV0bV9jYW1wYWlnbiUzZEVMX1VTX1NFQVJDSF9HZW5lcmFsJTI2bXNjbGtpZCUzZDI4Y2M4ZmVlNDQ2ZTExZDZhMTdhZTc5YjkxZGZmYzRm%26rlid%3D28cc8fee446e11d6a17ae79b91dffc4f&vqd=4-127127000978368128278099799263862733426&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5062.1)  \\n    \\n    *   [Python Beginner](https://duckduckgo.com/y.js?ad_domain=superprof.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=4nl%2DJPW2QMyibFvIOgf0CMoJrAtRfdlDwPZecd2MJom1i_UBvGko%2Do6SzFODl5fKwe7Ef9j7MFZHaUYyIOEa6QxF%2DyMrqr2fLnBr%2DrpDzyhWRGBgRxdfAfBpeQ_cZhcS.7w4RkuorecILV83M8e6CCw&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=cc85890dd31708b071321bb61cfb502b6d3d20ee4fd7dfa3c2f68e586984e998&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8r9wgS9byK89TCKNtG4t4KzVUCUyl1NQngwiLEWKKVs4kjkto8kCxkGyuxH30BbIdvzRxzvXFao19%2DOI3N63f4UTZQs69b%2DulNLihOuJoOwdxG0uGeoAtuI8Duoc__tAqDbH1sGZ4f7ZPUbuZ7vSgMVlZQ1iMMPempZjCsOKjLC0jSMLrajn36PCuAKniuW1igp%2D%2DxY9Rnm7hD%2DDkcUnUbFUjQm%2DIldNer9WPYDAybcSwJGb973UBjhIm6rn_rENdoSIh0%2DQ5F0ANaSvjg1nxW7OGH%2D%2DGyZ08ubt67Ke_eGFXT2JvIYKHxhB2nogGlW9NMAx4%2DFn467RzIDvDB5fUsftHxe2K_Czl_%2DRXucEq4vqS5cfFZs9_lEVGPEub9QnIVt90w5Q5clH728mIG8qNU7bc9Hm1sumnKNd26R_bGhfeDBbiJEi_ADo%2DRwfsGRdNK8vchh4uooi1codq%2DWbRiGsTLWY3zq%2DdfFMKQiOH2O1vWja1SXdBoS29UuypkQIOSo1vEMKhxUgMpF%2Du935BhkiBS8cASDnKWVzKm8nZ54QWh%2DHFMgu4XZTjK1Q%2Dy7N2myS93xxOTW1u8ZkXdkK0Fgzr6YpSMXqkW%2DC7JR3jd5tvt4HKMX1CeX2Om2XPxe_eWMDfcYx82_S5w8KqdB7NJipwVRCguz3VZT6kQ_gpFD2wZjzD%2DL_LQSOWzwruxZcTeh3qFLWBa1jAYK%2DhyW_HmeKGOqmQLpRifSunzXvTM2NgEOvLQlVQUkt4lQ1ldiotFK1R9UiMcwM6z27k0lujTOrsCjw%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cuc3VwZXJwcm9mLmNvbSUyZnMlMmZweXRob24lMmNVbml0ZWQtU3RhdGVzJTJjJTJjJTJjJTJjLmh0bWwlM2ZsJTNkMjElMjZ1dG1fc291cmNlJTNkYmluZyUyNnV0bV9tZWRpdW0lM2RhZHNfZWwlMjZ1dG1fY2FtcGFpZ24lM2RFTF9VU19TRUFSQ0hfR2VuZXJhbCUyNm1zY2xraWQlM2RmNDdhN2NjNjBkZjYxNDM0NTZhMTE0YjNkODQ3YWJhZQ%26rlid%3Df47a7cc60df6143456a114b3d847abae&vqd=4-166168939677892052865283808934829933077&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5067.1)\\n    *   [Python Senior](https://duckduckgo.com/y.js?ad_domain=superprof.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=Md_TppO7ylNywqyNk_MEKnCZV0d6OWe_BfCL97AsRmpj4WSIE6H2zUgFuJ30COmmeD0c%2Dd2VpfdnaJdMrf0siYwQuHuupLjLV5y1TSCFRrFrIP7q%2DhB9o3ApbBP3ku9y.47SrZ_yrrS0mCjNXsxKA0Q&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=71c4780ade7c419ec7fff7e071e96fad0360b9bd0aee12b42a266187ce79677c&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8JQlr7uP8RPKsTpfSQkUK5DVUCUwFazLzuqRpI8tgEYUPi9bPjVAEurm2sn4gIbIZ_tPhw0gvVgq3s%2DIdCO1b0PuqXPEFl2rxYR1r6VHQ4t4UQE9kV7LZw5Mo2uSgLZVklIj1ClGCWWreMw2GWq32vncGI0Y6co3Y4ovhuZ5PNpBx6ObQRqyTVEa8qS76Rb%2DXhLVfXmpcNO2MAL2LrBWz_GHb5SeS%2DQCYxK8JODnjOJyA1XdqiJ3fBrhigW8tGVgM54oKNmYQsGLzJTVJHgOO9k1cpFFbFKTvydGF8cTwr6izv19IN67denA9O3Tpf%2DyUwq42jLEUSWYzr65ij_pyBGE8R%2DOpi%2DvfM0DPrHsofzI0v7mW8V01pZdSoKlMMjLg_atxHBeMwA6WrEpIiK30GaXelQo07K5DVTsP1jyewD0l8ZB%2DJ6yEtuX7lyy%2DJTV92BPMedOKH1IRKIBwboxxxB05OyfxSn_PO4Qzawm8Dr9rGSKRYHT3hZ0f72u0M7PYwOyLYlBfcQ6y2Ym6OIOT4AocKI75OQWKPQvHY4zwtcSZoT3IqT4Hsjeixfx09pZ%2DbexfM5_8L2g_NnzEl8x9j8rGpqmZFPXZN%2D2tUznkraoUqBV06EM%2DLfEFJbyOzJfi7kNMvVS%2DtTS5bh01VYvJFeNpW6ANsVySZOzjpXwrXctOskbpgJ61OIgeDoBD_bqkSetAIIp3Q16TGY0NTd7HYf15qDbIQLXUXibRwJSneGcw6chX1hlSFwYkUSowwzFAm_LWqekin4%2DKrYlSOW%2D3obq9WOw%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cuc3VwZXJwcm9mLmNvbSUyZnMlMmZweXRob24lMmNVbml0ZWQtU3RhdGVzJTJjJTJjJTJjJTJjLmh0bWwlM2ZsJTNkMTIlMjZ1dG1fc291cmNlJTNkYmluZyUyNnV0bV9tZWRpdW0lM2RhZHNfZWwlMjZ1dG1fY2FtcGFpZ24lM2RFTF9VU19TRUFSQ0hfR2VuZXJhbCUyNm1zY2xraWQlM2QxYTYxOTExNDk4MmIxNGRiYmJmOGY5NjE0ZDlhMzY0YQ%26rlid%3D1a619114982b14dbbbf8f9614d9a364a&vqd=4-18334020457015043500477200011992203910&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5069.1)\\n    *   [Tutoring Jobs](https://duckduckgo.com/y.js?ad_domain=superprof.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=l59TAoyiXpUkxG7MoSn3TvsR%2DmP5OzSXqAReADetu96NpA0RqGmc4Sn1J8hiqQcLB_9d8iPzvSUc_Zj0r8_gMkIl5zJxxTdGn4o3Wn68nz2dPCxOrA87kA7oxk7jpGBb.twXwDAOS1grNPB6yfXHxnA&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=6f85fcf883c8d9d9ef18e4da5b96083517a4546477b625a0f045b1ba4469c33b&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8l%2D%2D2CPFyhI5QH5m8pX5WBDVUCUyxWDnHZU6StLi6BaEyRDZJ0pqx8pDDe3s7FSmX10evnajOMWrFv8nmgbdzx04%2DgVOaE6oKxnqOwU1cPwa0cTxz3fq2LqxHS5YKhv1NLk9usuZGt1ET0ISolpVfYxEmhSnRZhyILOZv12d9xSBxlB%2Dnv8T4%2Dl1S2WV1gMsk4QoyzUEI5kq0nPQOyWXyRlvC%2Dmr8VofzXkdPC0wOjmlAOI7%2D5DTpUuHz2czZkyUZC%2DieH41KYPvKw0fqH2tMgLs4w5ZSC4cWemL9TEHtR066h9jHGAXGr2WW036DIRX81rFAyai2SLTZjJMZb9J%2DQAt55l4UIQSOjeH3w%2DCGP7hRWULDSZxYKvo1eG8EqEE7ccUhhnw43SX6P%2DLvZS4fCVTR3Oxp0pJYdxdw6eXcbmNGZlysAqnwDWY8egGL3gu2YEEmcJJqxYufnXoNGpF9gJSHgKIwuJ%2Dkn2FXqrywQ8stsY6K7KYPfl2of_V2rNoFGF2j_6RToxeAu_Rl5eLkIikaX8QcQqsQcVMVwRgwtiywnNy9mu_GDvMPlsIylses_eK0Kj49KUTc5sSj_oros4hjGQ%2DlUt_PRFn%2DkQXz_8I2%2D5QZOo9JTBg%2DRoxvKz81Ie76XHnVQbbNxOBD9yQHUsgc863QAxqmvnUSGLrX9eaht%2Dd9fBb2Nt6qiLXpq%2DKhMn3_SslZs22G4_IjNkPv5QbkmEtOUfSnWpLwG6j1M6x%2DAu6%2DotHOS99FSRkNbYLgdh_Q4DkMshU1eRHFTlbGEXvYGYo%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cuc3VwZXJwcm9mLmNvbSUyZnR1dG9yJTJmJTNmbXNjbGtpZCUzZDExZDRmMmY4YzgyZTE2YmFhMWJiOWE4ZDFkOWZiMjAx%26rlid%3D11d4f2f8c82e16baa1bb9a8d1d9fb201&vqd=4-234918312616077357204051343376316662403&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5071.1)\\n    *   [Python Mid-Level](https://duckduckgo.com/y.js?ad_domain=superprof.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=%2DQBjUGJBre6P7rIcjraZvs3ojQM383uRpVMyS9gPepn6mPZmzauF8zqvhyPyPZAXdykkMJJNumn3BhD1I6nHEHnLUKA1rVmxzVY_inYAz6EPCIIGV%2DzT%2DCMxWQVmIf3e.DYnRqzRXSaEJwfym4W7wGg&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=26577c8fc16ce0c75cc0330637211c2674d64a9106bc76acf458945a14c7a8dd&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8DES%2Dd0d2V7AFWct0SP9B0TVUCUyCD7Z1cpeoS6Uy950O3psS4ruWGzhaunsHteG6JNVvFccqUQYnMBIhq5iy7zG726cRBCSqln58B86PrDDvD6L_%2D%2D0pBlSxGlzPDnupCh03sL%2DGaOz2uNQc_g5iAtw6CSOEZMuTPHCQhrFVx_iXHneN8ClrXlnFWDgCIVlhIg3n47mYb_wu8cYuH%2D7j8dEDUasT45bS9B_AX%2DCZPSUI5J1G7GVkQQrisVF0F8Mci%2DLG8%2DtTOulcTX2Y%2DqRK_QLM8S7M%2D9s%2Db39qkocXAf4dxOHi_pAC7cRmGlYDGsh77ROEYBUx37m4FUbL2cdxu5t_7Ux72hXmYy3urdttUgyorROQ6rlwQdmJdg85rhuXDBy%2DShPSGHe2kvQiYvI22mVZ1SUALhDhz9Zc5x7WCFMNZaB2w1du1pn7YAepxSrAfpwCKTaufRO5u4wJvn5E5Cv8S3ORtQUWP7BJvx9I7o7HTIfpl_gzljeCwKRQuXKKh4FE7%2DYew%2DRoWBTAFT24IzsomxEj31bdpLkc2mdTLTN8xfcM0TVYGotypcVGX5PmeD68wz8KhVjupqCnqxsEzwQTIz6mPrYk8bSB3lM_U0KLIcDnxJ0mYovIU6Q%2D4rrp278DS6443KU2RpA9%2DkVw8JwxVi5q98Z4N1tmm4fqjh5KuQOUiap3NnKzMCeBDifueFzUOT886r9G7rRagQJF838Px4tI5fLM2ct7hpYWhJ90D9nuI2rv%2DJbAvIvy1s4KV4GwpEgkvrW7sP6CY2Jca18ryNU%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cuc3VwZXJwcm9mLmNvbSUyZnMlMmZweXRob24lMmNVbml0ZWQtU3RhdGVzJTJjJTJjJTJjJTJjLmh0bWwlM2ZsJTNkMjIlMjZ1dG1fc291cmNlJTNkYmluZyUyNnV0bV9tZWRpdW0lM2RhZHNfZWwlMjZ1dG1fY2FtcGFpZ24lM2RFTF9VU19TRUFSQ0hfR2VuZXJhbCUyNm1zY2xraWQlM2RhNWUwYWI2NTcxMzUxZDFkMDMwMGMzMTBhNDg0NmFjOA%26rlid%3Da5e0ab6571351d1d0300c310a4846ac8&vqd=4-149664195629285158514465348984641917477&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5073.1)\\n    \\n3.  udemy.com\\n    \\n    举报广告\\n    \\n    [\\n    \\n    ![](//external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fwww.bing.com%2Fth%3Fid%3DOADD2.7834094934430_13UU8PX41LSZ1JDGR7%26pid%3D21.2%26h%3D32&f=1)\\n    \\n    Udemy\\n    \\n    https://www.udemy.com › Course › Python\\n    \\n    \\n    \\n    ](https://duckduckgo.com/y.js?ad_domain=udemy.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=Wo2DHsJNpo19rT%2DTZUM356Ei_mRYJVNA7jjUc1BrY9kcvvrFcTxaYuiX94KbxzpWnwkjjQv5Nr6cgW6RskF6nQ8HqFDyY17M8VOoF7WuP32cjXJmTxfdXaCaSvlVIOuE.1ODaLk773KIejnTZRdWrOw&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=7663bbf92dbebeaee0c54d7029e4429ab675bb6bf0831d700f06b59db4fff2b7&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De87_WLImhO2IEIji2%2DW9_TjTVUCUxR1RfUIxcv0AJKMBWws4P5ryctYEAyinZ3M_aulGG_UbmhAKyAtslWdpyB9aR%2DcCNgI%2DJnZCD4%2DGX4sj2xte2NZ9uZvv0rUL9OqOBmjd8OlGbz5aPMDNzdP5TNCjRW5JGvUVy5L47KiswAbDhWwV9VdzRw%2Drt96cG3cL7wcivVz3szp3n92_G%2DrnfbFBGI_NfOrI_RHoW0LqNX4S4lpuY1%2D5wMwAt1i%2D13PR2WcRoyhY8PBOGQDX9BR75ELNX1VujEfI6MO_RTGIyt2EcOsJBqSM31eGVB%2DWU9bSqAyYGGdiMfeVFWKuEq0NyncMwn7_1SiyRAHAIHjsvAHzmfPtllVUz8ZYKWBtgwyCLqfSpaPMdWt5M6__jsWPdU0cabXRYunrGtvpjVnWwT0O__IVChVxGkw4b9keWEMR3BO_2OdSHnpHAgoRktD5GqYEQk8I64jHeheoCKdPonARm02BW%2DpPhvYO%2DCKc_YlsSqsbUIdKTOt2GgAm4HO9u8sRgHxxWAfHgcbl4NAqE_dkjSszo2LJRf6Tv6X1y4zIPkHda32uGAziJW3xGO36i6VNqWTtsTsXmlS4FdW2RcYFSxzBYnZM6q9w9uWpjxAocnhGnrHxKMCiRffgGzFdxT%2DuN8Tu_rhN6J1LEkWnGDV2l0%2DcUp0X41ACHUW2HaHfNfB0MJURmREx5LNldeYCIocceEbdlCUJI6FxbdFU2xSKxYPJnHUFwuIn7F6a2bXKG9eKFi%2DQ%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cudWRlbXkuY29tJTJmY291cnNlJTJmMTAwLWRheXMtb2YtY29kZSUyZiUzZnV0bV9zb3VyY2UlM2RiaW5nJTI2dXRtX21lZGl1bSUzZHVkZW15YWRzJTI2dXRtX2NhbXBhaWduJTNkQkctU2VhcmNoX0tleXdvcmRfQWxwaGFfUHJvZl9sYS5FTl9jYy5VUyUyNmNhbXBhaWdudHlwZSUzZFNlYXJjaCUyNnBvcnRmb2xpbyUzZEJpbmctVVNBJTI2bGFuZ3VhZ2UlM2RFTiUyNnByb2R1Y3QlM2RDb3Vyc2UlMjZ0ZXN0JTNkJTI2YXVkaWVuY2UlM2RLZXl3b3JkJTI2dG9waWMlM2RQeXRob24lMjZwcmlvcml0eSUzZEFscGhhJTI2dXRtX2NvbnRlbnQlM2RkZWFsNDU4NCUyNnV0bV90ZXJtJTNkXy5fYWdfMTMyNDkxMzg4OTY1MzY3NV8uX2FkX18uX2t3X3B5dGhvbiUyNTIwY291cnNlXy5fZGVfY18uX2RtX18uX3BsX18uX3RpX2t3ZC04MjgwODIxMzAxMzY3MiUzYWxvYy0xOTBfLl9saV84NjQyMV8uX3BkX18uXyUyNm1hdGNodHlwZSUzZHAlMjZtc2Nsa2lkJTNkOGE3OGUzZDYzM2VmMTE2MTJmZWM4YmEzZTY1ZWEzZmM%26rlid%3D8a78e3d633ef11612fec8ba3e65ea3fc&vqd=4-252567849558048931393179992183696338933&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5077.1)\\n    \\n    ## [Learn Python in 100 Days - Python: 100 Projects Bootcamp](https://duckduckgo.com/y.js?ad_domain=udemy.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=Wo2DHsJNpo19rT%2DTZUM356Ei_mRYJVNA7jjUc1BrY9kcvvrFcTxaYuiX94KbxzpWnwkjjQv5Nr6cgW6RskF6nQ8HqFDyY17M8VOoF7WuP32cjXJmTxfdXaCaSvlVIOuE.1ODaLk773KIejnTZRdWrOw&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=7663bbf92dbebeaee0c54d7029e4429ab675bb6bf0831d700f06b59db4fff2b7&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De87_WLImhO2IEIji2%2DW9_TjTVUCUxR1RfUIxcv0AJKMBWws4P5ryctYEAyinZ3M_aulGG_UbmhAKyAtslWdpyB9aR%2DcCNgI%2DJnZCD4%2DGX4sj2xte2NZ9uZvv0rUL9OqOBmjd8OlGbz5aPMDNzdP5TNCjRW5JGvUVy5L47KiswAbDhWwV9VdzRw%2Drt96cG3cL7wcivVz3szp3n92_G%2DrnfbFBGI_NfOrI_RHoW0LqNX4S4lpuY1%2D5wMwAt1i%2D13PR2WcRoyhY8PBOGQDX9BR75ELNX1VujEfI6MO_RTGIyt2EcOsJBqSM31eGVB%2DWU9bSqAyYGGdiMfeVFWKuEq0NyncMwn7_1SiyRAHAIHjsvAHzmfPtllVUz8ZYKWBtgwyCLqfSpaPMdWt5M6__jsWPdU0cabXRYunrGtvpjVnWwT0O__IVChVxGkw4b9keWEMR3BO_2OdSHnpHAgoRktD5GqYEQk8I64jHeheoCKdPonARm02BW%2DpPhvYO%2DCKc_YlsSqsbUIdKTOt2GgAm4HO9u8sRgHxxWAfHgcbl4NAqE_dkjSszo2LJRf6Tv6X1y4zIPkHda32uGAziJW3xGO36i6VNqWTtsTsXmlS4FdW2RcYFSxzBYnZM6q9w9uWpjxAocnhGnrHxKMCiRffgGzFdxT%2DuN8Tu_rhN6J1LEkWnGDV2l0%2DcUp0X41ACHUW2HaHfNfB0MJURmREx5LNldeYCIocceEbdlCUJI6FxbdFU2xSKxYPJnHUFwuIn7F6a2bXKG9eKFi%2DQ%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cudWRlbXkuY29tJTJmY291cnNlJTJmMTAwLWRheXMtb2YtY29kZSUyZiUzZnV0bV9zb3VyY2UlM2RiaW5nJTI2dXRtX21lZGl1bSUzZHVkZW15YWRzJTI2dXRtX2NhbXBhaWduJTNkQkctU2VhcmNoX0tleXdvcmRfQWxwaGFfUHJvZl9sYS5FTl9jYy5VUyUyNmNhbXBhaWdudHlwZSUzZFNlYXJjaCUyNnBvcnRmb2xpbyUzZEJpbmctVVNBJTI2bGFuZ3VhZ2UlM2RFTiUyNnByb2R1Y3QlM2RDb3Vyc2UlMjZ0ZXN0JTNkJTI2YXVkaWVuY2UlM2RLZXl3b3JkJTI2dG9waWMlM2RQeXRob24lMjZwcmlvcml0eSUzZEFscGhhJTI2dXRtX2NvbnRlbnQlM2RkZWFsNDU4NCUyNnV0bV90ZXJtJTNkXy5fYWdfMTMyNDkxMzg4OTY1MzY3NV8uX2FkX18uX2t3X3B5dGhvbiUyNTIwY291cnNlXy5fZGVfY18uX2RtX18uX3BsX18uX3RpX2t3ZC04MjgwODIxMzAxMzY3MiUzYWxvYy0xOTBfLl9saV84NjQyMV8uX3BkX18uXyUyNm1hdGNodHlwZSUzZHAlMjZtc2Nsa2lkJTNkOGE3OGUzZDYzM2VmMTE2MTJmZWM4YmEzZTY1ZWEzZmM%26rlid%3D8a78e3d633ef11612fec8ba3e65ea3fc&vqd=4-252567849558048931393179992183696338933&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5077.1) 广告\\n    \\n    [Join millions and learn **Python** from scratch. Projects span data, web dev, and apps. Build 100 **Python** projects in 100 days. Start your coding journey today.](https://duckduckgo.com/y.js?ad_domain=udemy.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=Wo2DHsJNpo19rT%2DTZUM356Ei_mRYJVNA7jjUc1BrY9kcvvrFcTxaYuiX94KbxzpWnwkjjQv5Nr6cgW6RskF6nQ8HqFDyY17M8VOoF7WuP32cjXJmTxfdXaCaSvlVIOuE.1ODaLk773KIejnTZRdWrOw&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=7663bbf92dbebeaee0c54d7029e4429ab675bb6bf0831d700f06b59db4fff2b7&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De87_WLImhO2IEIji2%2DW9_TjTVUCUxR1RfUIxcv0AJKMBWws4P5ryctYEAyinZ3M_aulGG_UbmhAKyAtslWdpyB9aR%2DcCNgI%2DJnZCD4%2DGX4sj2xte2NZ9uZvv0rUL9OqOBmjd8OlGbz5aPMDNzdP5TNCjRW5JGvUVy5L47KiswAbDhWwV9VdzRw%2Drt96cG3cL7wcivVz3szp3n92_G%2DrnfbFBGI_NfOrI_RHoW0LqNX4S4lpuY1%2D5wMwAt1i%2D13PR2WcRoyhY8PBOGQDX9BR75ELNX1VujEfI6MO_RTGIyt2EcOsJBqSM31eGVB%2DWU9bSqAyYGGdiMfeVFWKuEq0NyncMwn7_1SiyRAHAIHjsvAHzmfPtllVUz8ZYKWBtgwyCLqfSpaPMdWt5M6__jsWPdU0cabXRYunrGtvpjVnWwT0O__IVChVxGkw4b9keWEMR3BO_2OdSHnpHAgoRktD5GqYEQk8I64jHeheoCKdPonARm02BW%2DpPhvYO%2DCKc_YlsSqsbUIdKTOt2GgAm4HO9u8sRgHxxWAfHgcbl4NAqE_dkjSszo2LJRf6Tv6X1y4zIPkHda32uGAziJW3xGO36i6VNqWTtsTsXmlS4FdW2RcYFSxzBYnZM6q9w9uWpjxAocnhGnrHxKMCiRffgGzFdxT%2DuN8Tu_rhN6J1LEkWnGDV2l0%2DcUp0X41ACHUW2HaHfNfB0MJURmREx5LNldeYCIocceEbdlCUJI6FxbdFU2xSKxYPJnHUFwuIn7F6a2bXKG9eKFi%2DQ%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cudWRlbXkuY29tJTJmY291cnNlJTJmMTAwLWRheXMtb2YtY29kZSUyZiUzZnV0bV9zb3VyY2UlM2RiaW5nJTI2dXRtX21lZGl1bSUzZHVkZW15YWRzJTI2dXRtX2NhbXBhaWduJTNkQkctU2VhcmNoX0tleXdvcmRfQWxwaGFfUHJvZl9sYS5FTl9jYy5VUyUyNmNhbXBhaWdudHlwZSUzZFNlYXJjaCUyNnBvcnRmb2xpbyUzZEJpbmctVVNBJTI2bGFuZ3VhZ2UlM2RFTiUyNnByb2R1Y3QlM2RDb3Vyc2UlMjZ0ZXN0JTNkJTI2YXVkaWVuY2UlM2RLZXl3b3JkJTI2dG9waWMlM2RQeXRob24lMjZwcmlvcml0eSUzZEFscGhhJTI2dXRtX2NvbnRlbnQlM2RkZWFsNDU4NCUyNnV0bV90ZXJtJTNkXy5fYWdfMTMyNDkxMzg4OTY1MzY3NV8uX2FkX18uX2t3X3B5dGhvbiUyNTIwY291cnNlXy5fZGVfY18uX2RtX18uX3BsX18uX3RpX2t3ZC04MjgwODIxMzAxMzY3MiUzYWxvYy0xOTBfLl9saV84NjQyMV8uX3BkX18uXyUyNm1hdGNodHlwZSUzZHAlMjZtc2Nsa2lkJTNkOGE3OGUzZDYzM2VmMTE2MTJmZWM4YmEzZTY1ZWEzZmM%26rlid%3D8a78e3d633ef11612fec8ba3e65ea3fc&vqd=4-252567849558048931393179992183696338933&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5077.1)  \\n    \\n    [**Courses**: Development, IT & Software, Business, Design](https://duckduckgo.com/y.js?ad_domain=udemy.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=Wo2DHsJNpo19rT%2DTZUM356Ei_mRYJVNA7jjUc1BrY9kcvvrFcTxaYuiX94KbxzpWnwkjjQv5Nr6cgW6RskF6nQ8HqFDyY17M8VOoF7WuP32cjXJmTxfdXaCaSvlVIOuE.1ODaLk773KIejnTZRdWrOw&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=7663bbf92dbebeaee0c54d7029e4429ab675bb6bf0831d700f06b59db4fff2b7&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De87_WLImhO2IEIji2%2DW9_TjTVUCUxR1RfUIxcv0AJKMBWws4P5ryctYEAyinZ3M_aulGG_UbmhAKyAtslWdpyB9aR%2DcCNgI%2DJnZCD4%2DGX4sj2xte2NZ9uZvv0rUL9OqOBmjd8OlGbz5aPMDNzdP5TNCjRW5JGvUVy5L47KiswAbDhWwV9VdzRw%2Drt96cG3cL7wcivVz3szp3n92_G%2DrnfbFBGI_NfOrI_RHoW0LqNX4S4lpuY1%2D5wMwAt1i%2D13PR2WcRoyhY8PBOGQDX9BR75ELNX1VujEfI6MO_RTGIyt2EcOsJBqSM31eGVB%2DWU9bSqAyYGGdiMfeVFWKuEq0NyncMwn7_1SiyRAHAIHjsvAHzmfPtllVUz8ZYKWBtgwyCLqfSpaPMdWt5M6__jsWPdU0cabXRYunrGtvpjVnWwT0O__IVChVxGkw4b9keWEMR3BO_2OdSHnpHAgoRktD5GqYEQk8I64jHeheoCKdPonARm02BW%2DpPhvYO%2DCKc_YlsSqsbUIdKTOt2GgAm4HO9u8sRgHxxWAfHgcbl4NAqE_dkjSszo2LJRf6Tv6X1y4zIPkHda32uGAziJW3xGO36i6VNqWTtsTsXmlS4FdW2RcYFSxzBYnZM6q9w9uWpjxAocnhGnrHxKMCiRffgGzFdxT%2DuN8Tu_rhN6J1LEkWnGDV2l0%2DcUp0X41ACHUW2HaHfNfB0MJURmREx5LNldeYCIocceEbdlCUJI6FxbdFU2xSKxYPJnHUFwuIn7F6a2bXKG9eKFi%2DQ%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cudWRlbXkuY29tJTJmY291cnNlJTJmMTAwLWRheXMtb2YtY29kZSUyZiUzZnV0bV9zb3VyY2UlM2RiaW5nJTI2dXRtX21lZGl1bSUzZHVkZW15YWRzJTI2dXRtX2NhbXBhaWduJTNkQkctU2VhcmNoX0tleXdvcmRfQWxwaGFfUHJvZl9sYS5FTl9jYy5VUyUyNmNhbXBhaWdudHlwZSUzZFNlYXJjaCUyNnBvcnRmb2xpbyUzZEJpbmctVVNBJTI2bGFuZ3VhZ2UlM2RFTiUyNnByb2R1Y3QlM2RDb3Vyc2UlMjZ0ZXN0JTNkJTI2YXVkaWVuY2UlM2RLZXl3b3JkJTI2dG9waWMlM2RQeXRob24lMjZwcmlvcml0eSUzZEFscGhhJTI2dXRtX2NvbnRlbnQlM2RkZWFsNDU4NCUyNnV0bV90ZXJtJTNkXy5fYWdfMTMyNDkxMzg4OTY1MzY3NV8uX2FkX18uX2t3X3B5dGhvbiUyNTIwY291cnNlXy5fZGVfY18uX2RtX18uX3BsX18uX3RpX2t3ZC04MjgwODIxMzAxMzY3MiUzYWxvYy0xOTBfLl9saV84NjQyMV8uX3BkX18uXyUyNm1hdGNodHlwZSUzZHAlMjZtc2Nsa2lkJTNkOGE3OGUzZDYzM2VmMTE2MTJmZWM4YmEzZTY1ZWEzZmM%26rlid%3D8a78e3d633ef11612fec8ba3e65ea3fc&vqd=4-252567849558048931393179992183696338933&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5077.1)  \\n    \\n    *   [Browse Free Courses](https://duckduckgo.com/y.js?ad_domain=udemy.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=GPiYFidwdAe6Ci4NIMtn6xyijY6SLuHqt8wOwlYlGxkssbSpJwQn3AbB7pd973hvzXZbey4cBTxfWXn6aKqMraZVuIxhH3m_2bpHWIOSW3sGxLc%2DkZa7yVmmEprbWoys.1d80QmRKXfcAiH1Y068pig&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=a873896fe7562ee277d43854c299578ee3c5931e1d2defc102534279fc793d97&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8GS46sQmCEkmnO_lauSJnRzVUCUwiqCdGOP9bA9UoV7J_jTmOB7%2D72hIh7VIr4Q49CHC9cdBFRYVgiRLktHzyyUuXPkzAgZq8E01fkzNLFGhkqN2WzEaY%2DHThaZpWJZrcYb7O2xP8eEVW2HvehSmvcq_C5t831bpnjCLxmHdq6NltSVV_wLYeR6qbt3Ih5wHZhVpyC6mN3NYmnfhG1vpCDwQFIKaVicKeHawy1wjEtwJy0B71fBLA0XkfiMn8XAm27Qr2dwMl557CqDscPmjStiEhIZulnZ_3Hgd8Z5RJwwfClaWZRLF3b904jvJS4F7e9_QeLfsIqucQTUiA7OKT1oSivr_mCGUFO7d2Ktp%2Ds29cdUD3XMdNAO1OuSdZ2G6TWFO3cmJxG2eCoN79dpcqaMmQBO7lC3ZqvHFAbEGDUJkvBwlvlH4czleSMVhusenNqmKaTZvh82rZtffnHPirQ0kLNUI_Yr_mzctjLJWhPP2Uwe_zTZjShJ5RuYK5hhUwZWla8js5VsUTsBGmPWg39BvDTFri3QrT0KWpgKi99gQPjbrB79DU0HpEtOYDf7rW_xYUIM6%2D9wAoD%2D48JyLdyEgAOfknlAEpQI0%2DbbV%2D4v4Oeu1C%2DgYOFr9TlngWFXm6uvRRkiz4JsoINQARGLoEBv1NLwUOjtWa5MDi%2DevtxPD0cROizFJiNt1j_jwf7A3SArzorf4nd1lnI3XdhOVC0rLC55KffQLSfsETb5ZXwCku7J7I3Acym2hoKkHZn0bgwO2Z3CQiO9cIVp64CoP_7RcrljU%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cudWRlbXkuY29tJTJmY291cnNlcyUyZmZyZWUlMmYlM2Z1dG1fc291cmNlJTNkYmluZyUyNnV0bV9tZWRpdW0lM2R1ZGVteWFkcyUyNnV0bV9jYW1wYWlnbiUzZEJHLVNlYXJjaF9LZXl3b3JkX0FscGhhX1Byb2ZfbGEuRU5fY2MuVVMlMjZjYW1wYWlnbnR5cGUlM2RTZWFyY2glMjZwb3J0Zm9saW8lM2RCaW5nLVVTQSUyNmxhbmd1YWdlJTNkRU4lMjZwcm9kdWN0JTNkQ291cnNlJTI2dGVzdCUzZCUyNmF1ZGllbmNlJTNkS2V5d29yZCUyNnRvcGljJTNkUHl0aG9uJTI2cHJpb3JpdHklM2RBbHBoYSUyNnV0bV9jb250ZW50JTNkZGVhbDQ1ODQlMjZ1dG1fdGVybSUzZF8uX2FnXzEzMjQ5MTM4ODk2NTM2NzVfLl9hZF9fLl9rd19weXRob24lMjUyMGNvdXJzZV8uX2RlX2NfLl9kbV9fLl9wbF9fLl90aV9rd2QtODI4MDgyMTMwMTM2NzIlM2Fsb2MtMTkwXy5fbGlfODY0MjFfLl9wZF9fLl8lMjZtYXRjaHR5cGUlM2RwJTI2bXNjbGtpZCUzZGQ2N2VmYzgzNjhiOTE1NDY2Zjk2OTRmNmJkMDg2ZTg4%26rlid%3Dd67efc8368b915466f9694f6bd086e88&vqd=4-32383614103046652002219765373535673172&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5082.1)\\n    *   [View Free Python Courses](https://duckduckgo.com/y.js?ad_domain=udemy.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=QEeCpxuQfcwT9QyWtVHNL3h7kmWJl7%2DwvxPQXOP7OfAIfoHtbiolaDEn7hP4USHhivXnc5rUfED7zENA0qj7AScDrOcdOJbZoYyIMZRkc6%2DI0AUzfpi79OXf7QkeA2Gu.wbaKPPK18k6PoYRy3DZzNQ&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=9a52a8b28d4a7e886c4b16efdd8110a18197f961df17a9a28d1fccfd3d643482&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8CHQmL2yFCLitf6geNYNdoTVUCUyNPWAPUYnJTVP9GlrXYQW6ZgTUgtp6cQeLXTJXCaGuc4ast0Zylw_7N3%2DlowiD608WmArJMqeABwnH5m%2DQV_cPbxqO16OhSGgvQgTS5ROmcoWUNnGlh4T_432EFg0_Q933_1pqE6wW3CRdTfGiw2_787SvZABiXafxDQSYQNhQJkN1Uh%2DD22Vu04dXn6ESJpeNd1nKicVLFDngW7Yf2fcWUerUxMCmdbRnHEzQj9f%2DD2tVv6hGTs_ULRNXiSRH9m1y_nBG7tgZDZI%2D3qMrBS1dt%2D2DguWnaj2P25S81pYgsWiCEJ0Duhrzw4NfMu7TxdAmWMD4Uik1b1YvVN5kmb6HMUhjVVQPcgGhCYrdxJ0XLPkNc6lS0JutQGYr7gtv8rAkSowJGoqymrKpxPPiqmRJMU1IQ5VzkND0UL9lg8Vp%2DjFXvwpWko66X6P4v4rjFDcQPA2bSU5XPpyZvWc%2DGtgvhJzl_wsmK4EYQzDN7rPgu_7HBqwytMGzWXP0IPUr4be9TuswDbN956bW4AlrbnpbdrW8l3iKx6Xv20Yh5bXAWT6kAAscoi3aqKoI5EN57tJJIBJi4zrd0F0wF9fmUONxyL3bGhj3ve71bMKczeOfljVn5avU_2TMS_UkPXO2m7%2DDZ2C9YDM7k7WP348Cfpi6QREOmsQV6FDW6GwDOmiLPR_6U%2DXjF5i0A9V_7j2h9Js79PfspvvbT6WpKlVmsS%2DcX%2D2Oo2qOo2YnCVSFYbzksiA4ywMmGEm8ZCxUqAYurvY%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cudWRlbXkuY29tJTJmdG9waWMlMmZweXRob24lMmZmcmVlJTJmJTNmbXNjbGtpZCUzZGRkMDk3YTcxYTIwOTFmNmQ0NGYwMTIwNDc3Yjg3YjBj%26rlid%3Ddd097a71a2091f6d44f0120477b87b0c&vqd=4-239800208192211391513692480472968340612&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5084.1)\\n    *   [Free Python Tutorials](https://duckduckgo.com/y.js?ad_domain=udemy.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=mX8jWdC8uqlxSRX5U5iXaeyd9HRwbUxhJshicUnyLZdNVbdczLhhaRO7dWvf7NNCD80p7nZ0Yy%2DIYog0CXOHoqAvV1hep1_yeVWHKKavVg4gXeTgFYUOEOrlKYquDBow.MWY0EF%2DPGbipOsdES0pojg&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=fe3155dc5e952768e51223bcacaaf6f690bcad262e8c292b7b1e3994aae4c9b3&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De865e%2DNsFPXEZW9lf6nfk7rDVUCUyKJa7V_G0ANsfkNKPwUtTS_%2D_Po4SXMYzTjKmvfcN1kEwIdMR10IbnFvPH7hN5nlG9iwTt6qQ8g4yr%2Dhr9Os55CfDqSCc_zwulwTXqi7_yyKHJobL6%2D1tdEiuzzpTDYe5HVUgHRkhVxAz3Vfu9iDHTifjAsjdNiVkCCWtcYHMiOEItQZi8zUAos4d7Aw9p35x9_ygPff0wPfX7X1rYV4imuUk8RaGwx0rZVqeAesGd3T6F8FBfYqlU8_hUhhzYtk18rLYcV1A0T7UPM2LF%2DaL7MvECEk9jsjzfyO3RP_bbn9XcQ1eDOIufD72OvWfRT9YFoykKWAUSMlD8xHjC773pFu5Cw5hOzv8QgK8D2byouH3COMvkvam9YKfdTUXbb1nYaFhFZMo2iabthqaxJLV2_TDk7MxmDpqLWqpvdT_KUrci%2DHag_nOWwG5RTqDJwe4xGZcGMTm5hRBHP3WIxHWx2rIkEvl2VUUTUDWwaelAuq1TfEOUXeQ5vHFL4skhEgIy3eHd%2DCspGFelRTbtmBubZ8mT0evPspOYw04_mCm3UI5ljosa8H_iJfUffcSkQDp3RTpQ38AV7VwuDd6xfe_3IpW6S2SKso2pDhP0e93Yh8iPHNdhgvnIGTHNtZPfI3fRdx9h1Rynp1b9Ikw4vvTEcdsPyRSQgG%2DN30FSonZEighBxHQoDAypQkEAltkThEPvBDnASRJdtm2M20qgAOeg_HQjctfnd6B3hY3%2DQRrrq_6xJLfpox09YB%2DBNV7BoHs%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cudWRlbXkuY29tJTJmdG9waWMlMmZweXRob24lMmZmcmVlJTJmJTNmbXNjbGtpZCUzZGQ5M2Q5M2E3ZjY1ODE0OGQ3ZDdiZmJkMjIzZTkwYzI5%26rlid%3Dd93d93a7f658148d7d7bfbd223e90c29&vqd=4-94352414788104475350505785689184113572&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5086.1)\\n    *   [Cloud Computing 101](https://duckduckgo.com/y.js?ad_domain=udemy.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=FtLW6Bpwff8bHROuR_Nxqw378omcJh5iE%2DuCkilbAhhRUNrepm4ZIeIXQ0d6eZxM6NRD3PdcQZBrfbSNk7_hBb05RRBJIpMsiBJsbmbgtNm1RjlWxLp1FAHF41o_XfPL.dT6TdgT84%2D6Lo2dqRc3qWA&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=f508d8e647dce5c38d8705cc6a7471340c610606204d6f8b5b6e575fce576b9d&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8mX8mQg66UEn7879Vvp_mxTVUCUwbVydFhSa_GidEuxuknIi5utm85hN8A2AVx2De1KzvgZxkMZHWyW%2DdKozNYuKy_9zjGWJ7ebjYrPGEIfFtcnYwRZZc%2DP55EGj7vsBBff91qYfspEyJ6vVlF6B%2DPc3VtDNd_xsAX8INSO0ThZ2UvQ6PaKNTbjOoZG8TqwtpwSTSiAD2rqHUdPF9eUKRIasdT9l_kABNgIU4jZPSAG9qW9YR1oj5eJAbtlqbHyWD5PzYFmXOWZR%2DHmnCGgEMURdbq4Zv5%2Dtp%2DlWAuMyN0emxEWg0fqi9CkurtU8iuRNvxyGKwDdClotzLV8%2DT2dkTV9SUoumH3s%2D_oS2FcTCTlcmRd_HLyfmNfBgvteLmyA3oQQrtHYQQccw5QXl53u4SORIbTPXg_pmeTsNcZN31dyxdSyx_AdH%2DBgl3wwk7T7ip4c6eEyd4AR9paTdZl7m2BJyRBvMVCH1efuCiSxxAKtLmcFPqOOjflcJ2wtFsAHzFhniFwt_bNi5Qe2FMOrZfzrDHu5OUGBAGSvMq6YnayN1wyZigMRq2EFMF0UYDkMfFK3v8HJbBR0Nk0J%2DdWEjLbJSLOPelHvlEXGxPMhxuuakX0VK6rW1BhjEepIgnY3Yvd6uAb0Tt29d8IMC1Y0aX4yavlQ8Z8n09v2G1DtidLSUztrw9xN8JhRxe6kQsCNdeLw4omS503boJ0s0wl9ZROR%2DJpIkbWA_vEroRLJThJQ1Ebj7aY%2DX5GIYmm18iRhU6DStLbrMsHTBYR2lhYU1CSP0YHI%26u%3DaHR0cHMlM2ElMmYlMmZ3d3cudWRlbXkuY29tJTJmdG9waWMlMmZjbG91ZC1jb21wdXRpbmclM2Ztc2Nsa2lkJTNkYzZmOTcwYWJiMWVlMWQ3MDUwODYxOGRlNjQ1ZjY4ODM%26rlid%3Dc6f970abb1ee1d70508618de645f6883&vqd=4-322832525430185903448296177046960206252&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5088.1)\\n    \\n4.  runoob.com\\n    \\n    [仅包含此网站的结果](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20site%3Awww.runoob.com)[在这些结果中隐藏网站](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20-site%3Awww.runoob.com)\\n    \\n    分享有关该网站的反馈\\n    \\n    [\\n    \\n    ![](//external-content.duckduckgo.com/ip3/www.runoob.com.ico)\\n    \\n    ](/?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B+site:www.runoob.com&t=h_ \\\"将结果来源限定为 runoob.com\\\")\\n    \\n    菜鸟教程\\n    \\n    [\\n    \\n    https://www.runoob.com › ml › ml-python.html\\n    \\n    \\n    \\n    ](https://www.runoob.com/ml/ml-python.html)\\n    \\n    ## [Python 入门机器学习 - 菜鸟教程](https://www.runoob.com/ml/ml-python.html)\\n    \\n    **Python** 入门机器学习 **Python** 是机器学习中最常用的编程语言之一，因其易于学习、强大的库支持和社区生态系统。 接下来，我将逐步说明如何通过 **Python** 入门机器学习，并介绍需要的一些常用库。\\n    \\n    *   [\\n        \\n        ### 机器学习教程\\n        \\n        机器学习教程 机器学习（Machine Learning）是人工智能（AI）的一个分支，它使计算机系统能够利用数据和算法自动学习和改进其性能。 机器学习是让机器通过经验（数据）来做决策和预测。 机器学习已经广泛应用于许多领域，包括推荐系统、图像识别、语音识别、金融分析等。 举个例子，通过机器 ...\\n        \\n        ](https://www.runoob.com/ml/ml-tutorial.html)\\n    \\n5.  zhuanlan.zhihu.com\\n    \\n    [仅包含此网站的结果](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20site%3Azhuanlan.zhihu.com)[在这些结果中隐藏网站](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20-site%3Azhuanlan.zhihu.com)\\n    \\n    分享有关该网站的反馈\\n    \\n    [\\n    \\n    ![](//external-content.duckduckgo.com/ip3/zhuanlan.zhihu.com.ico)\\n    \\n    ](/?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B+site:zhuanlan.zhihu.com&t=h_ \\\"将结果来源限定为 zhuanlan.zhihu.com\\\")\\n    \\n    知乎专栏\\n    \\n    [\\n    \\n    https://zhuanlan.zhihu.com › p › 25761248\\n    \\n    \\n    \\n    ](https://zhuanlan.zhihu.com/p/25761248)\\n    \\n    ## [从零开始掌握Python机器学习：十四步教程 - 知乎](https://zhuanlan.zhihu.com/p/25761248)\\n    \\n    **Python** 可以说是现在最流行的机器学习语言，而且你也能在网上找到大量的资源。你现在也在考虑从 **Python** 入门机器学习吗？本教程或许能帮你成功上手，从 0 到 1 掌握 **Python** 机器学习，至于后面再从 1 到 100 变成…\\n    \\n6.  w3school.com.cn\\n    \\n    [仅包含此网站的结果](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20site%3Awww.w3school.com.cn)[在这些结果中隐藏网站](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20-site%3Awww.w3school.com.cn)\\n    \\n    分享有关该网站的反馈\\n    \\n    [\\n    \\n    ![](//external-content.duckduckgo.com/ip3/www.w3school.com.cn.ico)\\n    \\n    ](/?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B+site:www.w3school.com.cn&t=h_ \\\"将结果来源限定为 w3school.com.cn\\\")\\n    \\n    w3school 在线教程\\n    \\n    [\\n    \\n    https://www.w3school.com.cn › python › python\\\\_ml\\\\_getting\\\\_started.asp\\n    \\n    \\n    \\n    ](https://www.w3school.com.cn/python/python_ml_getting_started.asp)\\n    \\n    ## [机器学习 - 入门 - w3school 在线教程](https://www.w3school.com.cn/python/python_ml_getting_started.asp)\\n    \\n    机器学习使计算机能够从研究数据和统计信息中学习。 机器学习是迈向人工智能（AI）方向的其中一步。 机器学习是一种程序，可以分析数据并学习预测结果。 从何处开始？ 在本教程中，我们将回到数学并研究统计学，以及如何根据数据集计算重要数值。 我们还将学习如何使用各种 **Python** 模块来 ...\\n    \\n7.  blog.csdn.net\\n    \\n    [仅包含此网站的结果](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20site%3Ablog.csdn.net)[在这些结果中隐藏网站](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20-site%3Ablog.csdn.net)\\n    \\n    分享有关该网站的反馈\\n    \\n    [\\n    \\n    ![](//external-content.duckduckgo.com/ip3/blog.csdn.net.ico)\\n    \\n    ](/?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B+site:blog.csdn.net&t=h_ \\\"将结果来源限定为 blog.csdn.net\\\")\\n    \\n    CSDN博客\\n    \\n    [\\n    \\n    https://blog.csdn.net › m0\\\\_59164520 › article › details › 128292773\\n    \\n    \\n    \\n    ](https://blog.csdn.net/m0_59164520/article/details/128292773)\\n    \\n    ## [从零开始掌握Python机器学习：七步教程 基础篇 - CSDN博客](https://blog.csdn.net/m0_59164520/article/details/128292773)\\n    \\n    2024年1月12日文章浏览阅读5.8k次，点赞2次，收藏30次。本教程旨在帮助初学者通过免费资源成长为熟练的Python机器学习实践者。内容覆盖Python基础、科学计算库、机器学习算法及深度学习实践。\\n    \\n8.  geek-blogs.com\\n    \\n    [仅包含此网站的结果](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20site%3Ageek-blogs.com)[在这些结果中隐藏网站](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20-site%3Ageek-blogs.com)\\n    \\n    分享有关该网站的反馈\\n    \\n    [\\n    \\n    ![](//external-content.duckduckgo.com/ip3/geek-blogs.com.ico)\\n    \\n    ](/?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B+site:geek-blogs.com&t=h_ \\\"将结果来源限定为 geek-blogs.com\\\")\\n    \\n    geek-blogs.com\\n    \\n    [\\n    \\n    https://geek-blogs.com › blog › introduction-to-python-machine-learning\\n    \\n    \\n    \\n    ](https://geek-blogs.com/blog/introduction-to-python-machine-learning/)\\n    \\n    ## [Python 机器学习入门：从基础到实践 - 极客技术博客](https://geek-blogs.com/blog/introduction-to-python-machine-learning/)\\n    \\n    4 天前在当今的数据驱动时代，机器学习已经成为了处理和分析大量数据的重要工具。**Python** 凭借其简洁易读的语法、丰富的科学计算库和机器学习框架，成为了机器学习领域最受欢迎的编程语言之一。本文将为你详细介绍 **Python** 机器学习的基础概念、使用方法、常见实践以及最佳实践，帮助你快速入门并 ...\\n    \\n9.  c.biancheng.net\\n    \\n    [仅包含此网站的结果](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20site%3Ac.biancheng.net)[在这些结果中隐藏网站](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20-site%3Ac.biancheng.net)\\n    \\n    分享有关该网站的反馈\\n    \\n    [\\n    \\n    ![](//external-content.duckduckgo.com/ip3/c.biancheng.net.ico)\\n    \\n    ](/?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B+site:c.biancheng.net&t=h_ \\\"将结果来源限定为 c.biancheng.net\\\")\\n    \\n    C语言中文网\\n    \\n    [\\n    \\n    https://c.biancheng.net › ml\\\\_alg\\n    \\n    \\n    \\n    ](https://c.biancheng.net/ml_alg/)\\n    \\n    ## [Python机器学习算法入门教程 - C语言中文网](https://c.biancheng.net/ml_alg/)\\n    \\n    Python机器学习算法（Machine Learning Algorithms），它是机器学习的核心，机器学习属于人工智能的一个分支，主要是设计和分析一些让计算机可以自动\\\"学习\\\"的算法，比如线性回归、逻辑回归、朴素贝叶斯等等，通过学些本套python机器学习算法教程，您将学会机器学习中的常用算法，\\n    \\n10.  icourse163.org\\n    \\n    [仅包含此网站的结果](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20site%3Awww.icourse163.org)[在这些结果中隐藏网站](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20-site%3Awww.icourse163.org)\\n    \\n    分享有关该网站的反馈\\n    \\n    [\\n    \\n    ![](//external-content.duckduckgo.com/ip3/www.icourse163.org.ico)\\n    \\n    ](/?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B+site:www.icourse163.org&t=h_ \\\"将结果来源限定为 icourse163.org\\\")\\n    \\n    中国大学MOOC(慕课)\\n    \\n    [\\n    \\n    https://www.icourse163.org › course › FJNU-1472386164\\n    \\n    \\n    \\n    ](https://www.icourse163.org/course/FJNU-1472386164)\\n    \\n    ## [机器学习\\\\_福建师范大学\\\\_中国大学mooc (慕课)](https://www.icourse163.org/course/FJNU-1472386164)\\n    \\n    机器学习,spContent=课程有配套教材《机器学习理论与应用》，主要面向水平普通的初学者。 不仅理论与实践（**Python**编程实例和手算例题）并重、深度适当、原理叙述详细浅显和习题相辅，更有创新与科学思维引领理论的特色，让学习者获得科学思维的培养。\\n    \\n11.  cnblogs.com\\n    \\n    [仅包含此网站的结果](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20site%3Awww.cnblogs.com)[在这些结果中隐藏网站](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20-site%3Awww.cnblogs.com)\\n    \\n    分享有关该网站的反馈\\n    \\n    [\\n    \\n    ![](//external-content.duckduckgo.com/ip3/www.cnblogs.com.ico)\\n    \\n    ](/?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B+site:www.cnblogs.com&t=h_ \\\"将结果来源限定为 cnblogs.com\\\")\\n    \\n    博客园\\n    \\n    [\\n    \\n    https://www.cnblogs.com › suanfajin › p › 18240275\\n    \\n    \\n    \\n    ](https://www.cnblogs.com/suanfajin/p/18240275)\\n    \\n    ## [算法金 | 只需十四步：从零开始掌握Python机器学习（附资源） - 算法金「全网同名」 - 博客园](https://www.cnblogs.com/suanfajin/p/18240275)\\n    \\n    2024年6月9日大侠幸会，在下全网同名「算法金」 0 基础转 AI 上岸，多个算法赛 Top 「日更万日，让更多人享受智能乐趣」 1. 引言 1.1 教程目的与读者定位 &quot;启程&quot;往往是最具挑战性的一步，特别是在面临众多选择时，人们往往难以做出决策。本教程旨在帮助那些几乎没有Python机器学习基\\n    \\n12.  runoob.com\\n    \\n    [仅包含此网站的结果](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20site%3Awww.runoob.com)[在这些结果中隐藏网站](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20-site%3Awww.runoob.com)\\n    \\n    分享有关该网站的反馈\\n    \\n    [\\n    \\n    ![](//external-content.duckduckgo.com/ip3/www.runoob.com.ico)\\n    \\n    ](/?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B+site:www.runoob.com&t=h_ \\\"将结果来源限定为 runoob.com\\\")\\n    \\n    菜鸟教程\\n    \\n    [\\n    \\n    https://www.runoob.com › ml › ml-tutorial.html\\n    \\n    \\n    \\n    ](https://www.runoob.com/ml/ml-tutorial.html)\\n    \\n    ## [机器学习教程 - 菜鸟教程](https://www.runoob.com/ml/ml-tutorial.html)\\n    \\n    机器学习教程 机器学习（Machine Learning）是人工智能（AI）的一个分支，它使计算机系统能够利用数据和算法自动学习和改进其性能。 机器学习是让机器通过经验（数据）来做决策和预测。 机器学习已经广泛应用于许多领域，包括推荐系统、图像识别、语音识别、金融分析等。 举个例子，通过机器 ...\\n    \\n13.  zhuanlan.zhihu.com\\n    \\n    [仅包含此网站的结果](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20site%3Azhuanlan.zhihu.com)[在这些结果中隐藏网站](?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B%20-site%3Azhuanlan.zhihu.com)\\n    \\n    分享有关该网站的反馈\\n    \\n    [\\n    \\n    ![](//external-content.duckduckgo.com/ip3/zhuanlan.zhihu.com.ico)\\n    \\n    ](/?q=Python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%95%99%E7%A8%8B+site:zhuanlan.zhihu.com&t=h_ \\\"将结果来源限定为 zhuanlan.zhihu.com\\\")\\n    \\n    知乎专栏\\n    \\n    [\\n    \\n    https://zhuanlan.zhihu.com › p › 22769945394\\n    \\n    \\n    \\n    ](https://zhuanlan.zhihu.com/p/22769945394)\\n    \\n    ## [如何在Python中进行机器学习？ - 知乎 - 知乎专栏](https://zhuanlan.zhihu.com/p/22769945394)\\n    \\n    机器学习已经成为现代科技的核心，应用广泛，涵盖了从推荐系统到自然语言处理、图像识别等多个领域。Python是机器学习开发中最流行的编程语言之一，它拥有强大的库和工具，能够帮助开发者轻松实现机器学习算法。本文将介绍如何在Python中进行机器学习，从环境配置到模型训练与评估的整个 ...\\n    \\n14.  codefinity.com\\n    \\n    举报广告\\n    \\n    [\\n    \\n    ![](//external-content.duckduckgo.com/iu/?u=https%3A%2F%2Fwww.bing.com%2Fth%3Fid%3DOADD2.7353168264241_1H8KEEER4DDLKEB3HS%26pid%3D21.2%26h%3D32&f=1)\\n    \\n    codefinity.com\\n    \\n    https://codefinity.com\\n    \\n    \\n    \\n    ](https://duckduckgo.com/y.js?ad_domain=codefinity.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=vx%2Df25OGy5NmWNK2p5Ix4nP%2D8tYAdZTioK3lymFCdJgPtzqZxHLxySemc98FDBe3QRV0_vrGf_c4CTVWj1z%2DO_GgliAIqiQn4Jzll0zuyvlfqWzKYUO4YqvCenC7rFF1._hVuah0rCgN7cNwjyFbBBg&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=279e8e3d94f65d56440e907a2fddfc0134a5f70e0fc1d86324ee645d736b13e2&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De87TFUk5IKCsZw%2DSiRFatKVjVUCUxJIh%2DdmIxxU0%2DPQWSYyoZwtVATw5iFFZzOumPRCgUFajKKD1VB4hdIei4ChjiITNQ36aSpJtqLjnxrh%2Dz3RHAphFR0UTMno81SZbEyBkSQCUrGE714xPcVkfgA4NAjBhvdR6eSjia0LH%2DR%2DsHX8%2DrINwzC9TUgmWGbGxBY97Zy8LqY1Ffjeb6wPe68LhBxnSyOYk_hHYnfdHa36KJPd4THtDEDtdGGVc1vt_hiaiWSepsC6oj6YOENiE3_xadTrUVRVzE_WpuoRjzn0QlVTXgCdsVQwbhCYof51OhfFm_Gcv2edenvBqO7pqNpUZHS0tADIICrgwy2wbxHtPnuURrNE3xIZk99P4zuYef%2DsUo7BCk36rJ7vo_FYTr1O3xby48FvrtQeFykC14PtuGdHPb8ff62eb_wbhi_MVOLM5nTlbKN3qJ7_TpDohrSWhBfZKxXZ4YiYba3XgKesbfs6WmIQTLS%2Dk6xdkXmLH6LHAwwGsd%2DGEYJHZ4hG579StqBHddpJuHocvDU77VM7idhV3CVfonlW9imw8tpj__72cfQK0RtjM6e8ZURJqcXS0QGZqlX49xaa1ZQGyhB%2DG%2DtdjM_2U%2DTfCtT9q3bId8DhhLZD0Gg49jLBLllrQSvQYspuHlG%2DK9jCOguFSVS2Ts5Jdqxvg3MyyfExoyauQa%2D0lBFzbXIVdrxCDWkWDV_FLeLrw18YQj6mb5i3DYe57tT%2D49RMxCHQfh8CtVoW3RXswwjOg%26u%3DaHR0cHMlM2ElMmYlMmZjb2RlZmluaXR5LmNvbSUyZmNvdXJzZXMlMmZ2MiUyZjJmNjAxNzNiLTk4MjktNGM4NC04NTQ4LTg1YmY2NTE4M2Y3MSUzZnV0bV9zb3VyY2UlM2RiaW5nJTI2dXRtX21lZGl1bSUzZGNwYyUyNnV0bV9jYW1wYWlnbiUzZDU2OTU0ODAyOSUyNnV0bV9jb250ZW50JTNkMTE3MjA4MTIyNjA1MDEyOCUyNnV0bV90ZXJtJTNkcHl0aG9uJTI1MjBsYW5ndWFnZSUyNTIwdHV0b3JpYWwlMjZtc2Nsa2lkJTNkMzM4Nzc2ODU4MTcyMWNiZWE4NzcxZWYwNTRjM2EwOTI%26rlid%3D3387768581721cbea8771ef054c3a092&vqd=4-284232115642565860574769389848887517536&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5092.1)\\n    \\n    ## [Best Python Online Course - Best Online Python Course](https://duckduckgo.com/y.js?ad_domain=codefinity.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=vx%2Df25OGy5NmWNK2p5Ix4nP%2D8tYAdZTioK3lymFCdJgPtzqZxHLxySemc98FDBe3QRV0_vrGf_c4CTVWj1z%2DO_GgliAIqiQn4Jzll0zuyvlfqWzKYUO4YqvCenC7rFF1._hVuah0rCgN7cNwjyFbBBg&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=279e8e3d94f65d56440e907a2fddfc0134a5f70e0fc1d86324ee645d736b13e2&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De87TFUk5IKCsZw%2DSiRFatKVjVUCUxJIh%2DdmIxxU0%2DPQWSYyoZwtVATw5iFFZzOumPRCgUFajKKD1VB4hdIei4ChjiITNQ36aSpJtqLjnxrh%2Dz3RHAphFR0UTMno81SZbEyBkSQCUrGE714xPcVkfgA4NAjBhvdR6eSjia0LH%2DR%2DsHX8%2DrINwzC9TUgmWGbGxBY97Zy8LqY1Ffjeb6wPe68LhBxnSyOYk_hHYnfdHa36KJPd4THtDEDtdGGVc1vt_hiaiWSepsC6oj6YOENiE3_xadTrUVRVzE_WpuoRjzn0QlVTXgCdsVQwbhCYof51OhfFm_Gcv2edenvBqO7pqNpUZHS0tADIICrgwy2wbxHtPnuURrNE3xIZk99P4zuYef%2DsUo7BCk36rJ7vo_FYTr1O3xby48FvrtQeFykC14PtuGdHPb8ff62eb_wbhi_MVOLM5nTlbKN3qJ7_TpDohrSWhBfZKxXZ4YiYba3XgKesbfs6WmIQTLS%2Dk6xdkXmLH6LHAwwGsd%2DGEYJHZ4hG579StqBHddpJuHocvDU77VM7idhV3CVfonlW9imw8tpj__72cfQK0RtjM6e8ZURJqcXS0QGZqlX49xaa1ZQGyhB%2DG%2DtdjM_2U%2DTfCtT9q3bId8DhhLZD0Gg49jLBLllrQSvQYspuHlG%2DK9jCOguFSVS2Ts5Jdqxvg3MyyfExoyauQa%2D0lBFzbXIVdrxCDWkWDV_FLeLrw18YQj6mb5i3DYe57tT%2D49RMxCHQfh8CtVoW3RXswwjOg%26u%3DaHR0cHMlM2ElMmYlMmZjb2RlZmluaXR5LmNvbSUyZmNvdXJzZXMlMmZ2MiUyZjJmNjAxNzNiLTk4MjktNGM4NC04NTQ4LTg1YmY2NTE4M2Y3MSUzZnV0bV9zb3VyY2UlM2RiaW5nJTI2dXRtX21lZGl1bSUzZGNwYyUyNnV0bV9jYW1wYWlnbiUzZDU2OTU0ODAyOSUyNnV0bV9jb250ZW50JTNkMTE3MjA4MTIyNjA1MDEyOCUyNnV0bV90ZXJtJTNkcHl0aG9uJTI1MjBsYW5ndWFnZSUyNTIwdHV0b3JpYWwlMjZtc2Nsa2lkJTNkMzM4Nzc2ODU4MTcyMWNiZWE4NzcxZWYwNTRjM2EwOTI%26rlid%3D3387768581721cbea8771ef054c3a092&vqd=4-284232115642565860574769389848887517536&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5092.1) 广告\\n    \\n    [Open up new career options in tech! \\\"Intro to **Python**\\\" Self-Paced Online Course. Choose from 30+ Top Rated Courses. Join Community of 100K+ Learners](https://duckduckgo.com/y.js?ad_domain=codefinity.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=vx%2Df25OGy5NmWNK2p5Ix4nP%2D8tYAdZTioK3lymFCdJgPtzqZxHLxySemc98FDBe3QRV0_vrGf_c4CTVWj1z%2DO_GgliAIqiQn4Jzll0zuyvlfqWzKYUO4YqvCenC7rFF1._hVuah0rCgN7cNwjyFbBBg&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=279e8e3d94f65d56440e907a2fddfc0134a5f70e0fc1d86324ee645d736b13e2&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De87TFUk5IKCsZw%2DSiRFatKVjVUCUxJIh%2DdmIxxU0%2DPQWSYyoZwtVATw5iFFZzOumPRCgUFajKKD1VB4hdIei4ChjiITNQ36aSpJtqLjnxrh%2Dz3RHAphFR0UTMno81SZbEyBkSQCUrGE714xPcVkfgA4NAjBhvdR6eSjia0LH%2DR%2DsHX8%2DrINwzC9TUgmWGbGxBY97Zy8LqY1Ffjeb6wPe68LhBxnSyOYk_hHYnfdHa36KJPd4THtDEDtdGGVc1vt_hiaiWSepsC6oj6YOENiE3_xadTrUVRVzE_WpuoRjzn0QlVTXgCdsVQwbhCYof51OhfFm_Gcv2edenvBqO7pqNpUZHS0tADIICrgwy2wbxHtPnuURrNE3xIZk99P4zuYef%2DsUo7BCk36rJ7vo_FYTr1O3xby48FvrtQeFykC14PtuGdHPb8ff62eb_wbhi_MVOLM5nTlbKN3qJ7_TpDohrSWhBfZKxXZ4YiYba3XgKesbfs6WmIQTLS%2Dk6xdkXmLH6LHAwwGsd%2DGEYJHZ4hG579StqBHddpJuHocvDU77VM7idhV3CVfonlW9imw8tpj__72cfQK0RtjM6e8ZURJqcXS0QGZqlX49xaa1ZQGyhB%2DG%2DtdjM_2U%2DTfCtT9q3bId8DhhLZD0Gg49jLBLllrQSvQYspuHlG%2DK9jCOguFSVS2Ts5Jdqxvg3MyyfExoyauQa%2D0lBFzbXIVdrxCDWkWDV_FLeLrw18YQj6mb5i3DYe57tT%2D49RMxCHQfh8CtVoW3RXswwjOg%26u%3DaHR0cHMlM2ElMmYlMmZjb2RlZmluaXR5LmNvbSUyZmNvdXJzZXMlMmZ2MiUyZjJmNjAxNzNiLTk4MjktNGM4NC04NTQ4LTg1YmY2NTE4M2Y3MSUzZnV0bV9zb3VyY2UlM2RiaW5nJTI2dXRtX21lZGl1bSUzZGNwYyUyNnV0bV9jYW1wYWlnbiUzZDU2OTU0ODAyOSUyNnV0bV9jb250ZW50JTNkMTE3MjA4MTIyNjA1MDEyOCUyNnV0bV90ZXJtJTNkcHl0aG9uJTI1MjBsYW5ndWFnZSUyNTIwdHV0b3JpYWwlMjZtc2Nsa2lkJTNkMzM4Nzc2ODU4MTcyMWNiZWE4NzcxZWYwNTRjM2EwOTI%26rlid%3D3387768581721cbea8771ef054c3a092&vqd=4-284232115642565860574769389848887517536&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5092.1)  \\n    \\n    [**Service catalog**: Interactive Learning, Bite-size lessons, AI-copilot](https://duckduckgo.com/y.js?ad_domain=codefinity.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=vx%2Df25OGy5NmWNK2p5Ix4nP%2D8tYAdZTioK3lymFCdJgPtzqZxHLxySemc98FDBe3QRV0_vrGf_c4CTVWj1z%2DO_GgliAIqiQn4Jzll0zuyvlfqWzKYUO4YqvCenC7rFF1._hVuah0rCgN7cNwjyFbBBg&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=279e8e3d94f65d56440e907a2fddfc0134a5f70e0fc1d86324ee645d736b13e2&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De87TFUk5IKCsZw%2DSiRFatKVjVUCUxJIh%2DdmIxxU0%2DPQWSYyoZwtVATw5iFFZzOumPRCgUFajKKD1VB4hdIei4ChjiITNQ36aSpJtqLjnxrh%2Dz3RHAphFR0UTMno81SZbEyBkSQCUrGE714xPcVkfgA4NAjBhvdR6eSjia0LH%2DR%2DsHX8%2DrINwzC9TUgmWGbGxBY97Zy8LqY1Ffjeb6wPe68LhBxnSyOYk_hHYnfdHa36KJPd4THtDEDtdGGVc1vt_hiaiWSepsC6oj6YOENiE3_xadTrUVRVzE_WpuoRjzn0QlVTXgCdsVQwbhCYof51OhfFm_Gcv2edenvBqO7pqNpUZHS0tADIICrgwy2wbxHtPnuURrNE3xIZk99P4zuYef%2DsUo7BCk36rJ7vo_FYTr1O3xby48FvrtQeFykC14PtuGdHPb8ff62eb_wbhi_MVOLM5nTlbKN3qJ7_TpDohrSWhBfZKxXZ4YiYba3XgKesbfs6WmIQTLS%2Dk6xdkXmLH6LHAwwGsd%2DGEYJHZ4hG579StqBHddpJuHocvDU77VM7idhV3CVfonlW9imw8tpj__72cfQK0RtjM6e8ZURJqcXS0QGZqlX49xaa1ZQGyhB%2DG%2DtdjM_2U%2DTfCtT9q3bId8DhhLZD0Gg49jLBLllrQSvQYspuHlG%2DK9jCOguFSVS2Ts5Jdqxvg3MyyfExoyauQa%2D0lBFzbXIVdrxCDWkWDV_FLeLrw18YQj6mb5i3DYe57tT%2D49RMxCHQfh8CtVoW3RXswwjOg%26u%3DaHR0cHMlM2ElMmYlMmZjb2RlZmluaXR5LmNvbSUyZmNvdXJzZXMlMmZ2MiUyZjJmNjAxNzNiLTk4MjktNGM4NC04NTQ4LTg1YmY2NTE4M2Y3MSUzZnV0bV9zb3VyY2UlM2RiaW5nJTI2dXRtX21lZGl1bSUzZGNwYyUyNnV0bV9jYW1wYWlnbiUzZDU2OTU0ODAyOSUyNnV0bV9jb250ZW50JTNkMTE3MjA4MTIyNjA1MDEyOCUyNnV0bV90ZXJtJTNkcHl0aG9uJTI1MjBsYW5ndWFnZSUyNTIwdHV0b3JpYWwlMjZtc2Nsa2lkJTNkMzM4Nzc2ODU4MTcyMWNiZWE4NzcxZWYwNTRjM2EwOTI%26rlid%3D3387768581721cbea8771ef054c3a092&vqd=4-284232115642565860574769389848887517536&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5092.1)  \\n    \\n    *   [Our Pricing](https://duckduckgo.com/y.js?ad_domain=codefinity.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=nNuCKN6l82jm_vluaKxA1K9Y_A36i533V0ianqOdPuQWfWjucmDff5zVK8s4y%2DWGNUV6UUTFnc5G_J6uBx3uDxytqU7wHpnu4nPHdWiltsP4SlxmJXdpccRCCHZxJuhJ.nJYD%2D5yOhQF7XfXTeXnxJA&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=4baf768b71d1335a4e39177d281ef522c28d5eb68ebb103e6433de1dc011a247&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8SMNyayTLoZFplHLuyj5lXzVUCUxTFhT4F4bZsgE2lpXrKGjOMf_vXdu88xhUHOstGDe_skknxUNrbmAO6JCrMfeIoJwLK%2DLr_GLQuY803peQDMafh_0%2Duzf1%2D6iwf_v_AC_5ZOXVPWxvgCrYQtNmLHbu6DxCFRZBempbF%2DOktAUzK3Y%2DLsuJfsp36cra7bu%2Dk4Vh3ptmMKM7rhurfBf24yndvfkob4FRyRXPC63YPLcpntv_dgbiNmIKccxK4EhbrtPQE7ZGEVK7moGr50JhontZfHgjVu2XxoUb08I%2DGk0_eJB0r8KeQXk9SWHXl1LAxWCvMZz52%2DK8G3%2Dr0cOJHQ2_GM6WtEt25SLz4%2D3mtclS5X%2DP94PRFBcbwqAt6YPkLK5gtQ8eHTMncWHwFzUED%2DHWXaXchIOpLTaJuHS0gPAp07dSZrD79bZt2tSXn%2DqL_qxOLjYEvNOl6Fye9dzYiUlwNwA06Qk2PplWdR33yV1FvQB5tmbaKAbftDK0NtkqzLcm9t8423WP9Wv2c42KKnwfHjxrQ0mPsUmxxopzmfHq0e1Jeye7dIVtvVCNgfowXoreIB5uoVxjzz5jilsngzibwGK0O404hhW4RtrtxmMea4mG8OtOn1jNpekorQP8qnSAESuKMHZ3AepHNS66Z9uYU4ct8r3jQW9ETh1mVN9Gp4eVYUszHC%2De0sTZXiIh2pA8BVCncgudkf9_1LHOLOYqQ6GHKyhIOcZMJNWHY4ZM4tnRqbSoiQwUQ2bGnGI3awEERivMbCiYBbdBN1byZ0EqQ9w%26u%3DaHR0cHMlM2ElMmYlMmZjb2RlZmluaXR5LmNvbSUyZnByaWNpbmclM2Z1dG1fc291cmNlJTNkYmluZyUyNnV0bV9tZWRpdW0lM2RjcGMlMjZ1dG1fY2FtcGFpZ24lM2Q1Njk1NDgwMjklMjZ1dG1fY29udGVudCUzZDExNzIwODEyMjYwNTAxMjglMjZ1dG1fdGVybSUzZHB5dGhvbiUyNTIwbGFuZ3VhZ2UlMjUyMHR1dG9yaWFsJTI2bXNjbGtpZCUzZGM0MzViYmUwMjQ2YjFmMmU2NTA0ZDM5NWYxZGE3NTIx%26rlid%3Dc435bbe0246b1f2e6504d395f1da7521&vqd=4-92969068449347830399521610563874367274&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5097.1)\\n    *   [Data Analysis Courses](https://duckduckgo.com/y.js?ad_domain=codefinity.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=Ipq2IBgVF_J0assi6z2r9vVhG8HYzdp%2DGq7t53uKp1syULlXHt2UHkoLnKDMabFlmPWiHcKAPd8gjAXUIUMeAFIl5NoNHfvr9G39rfr9biGS%2Dfmg6Bb_6F0tE3zg8ry9.4p7iSYZEgtt0FXR548AjtQ&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=99c13971280f9f01b1c1551d4f38c77c8462a96b66a12d01ea5b382f3a719034&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8tcG46eT27_VjhNX1EFDruzVUCUy6a%2DrPmkmnRjP39WZ3Ig3Oc1uXgigUupLJSUbhlw3GgoRIPFUZo3uRh6OhS_Nd1cVFDgUuntvuLmQj2H_jJilR5%2DtjeWhuzt5tVp_Jezysjkq_kSJX4Xo2xVvq%2DWR1QCDMJTgiF8o9FyG8xEJ0pPqCn9Pw4ianQgiurSVe5Mh2yPX2DooL0dBM4Ey73%2DMn2aj9gRcMfpCg2VHFyLqc50458ND4_YJjDZKlnmCnikpf%2Dd%2D0_RjJWJQPOeoSQNJHxRJnLDpwFJyB00TdV3JQfPbZu3Q6t8le6yrxELtdUebU7VMBTNAYFVTNVu28FHCYDz0j2_8nNupRrpXp35wigVgV0SQHaqYVnsmnwFS6KRAeXJfh3YJRQTKoroFPoXiVbN1FDzDQjyJtHMj%2DoBt9Wu0KCRHf8JWRfAQzrSUfyWmKBylTS3ep1e072XbNqJuOspvdcthQMM%2DMY98du7s_gUB0dk0k9yvdJnbLs8x4ksZSgJGCfvy8M%2DxhnlGXQ%2DeiiVO_%2DW68w5VCxVI5frshpYXlyroe63W4mFdHjt61rnDWiLfZioBRJGGJv4fvK4HGenZtrVjGQvTwsb2_dVJx944uFkeoi0lLEv0vWhvFcO61UhRLhTU7Wy82YLgAlnEZ7Kx0Xjs66mOETffQcNi0idd3PCSPOWbsR0TXkaSpVJ2boHwCSzsSzq6xuKyLjzy1y_cSg7jJQLSSK3hXV4YCQ4x12%2D%2DhMlTKJ9VFCh8e2yQ4ZD0rvt4ucJf_%2DZ8wN%2DeiZGo%26u%3DaHR0cHMlM2ElMmYlMmZjb2RlZmluaXR5LmNvbSUyZmNhdGVnb3J5JTJmZGF0YS1hbmFseXNpcyUzZmZpbHRlcnMlM2QlMjUyNnRvcGljJTI1M0REYXRhJTJiQW5hbHl0aWNzJTI2cGFnZSUzZDAlMjZyZWZQYWdlU2l6ZSUzZDE1JTI2dXRtX3NvdXJjZSUzZGJpbmclMjZ1dG1fbWVkaXVtJTNkY3BjJTI2dXRtX2NhbXBhaWduJTNkNTY5NTQ4MDI5JTI2dXRtX2NvbnRlbnQlM2QxMTcyMDgxMjI2MDUwMTI4JTI2dXRtX3Rlcm0lM2RweXRob24lMjUyMGxhbmd1YWdlJTI1MjB0dXRvcmlhbCUyNm1zY2xraWQlM2RkYzVjNjU0ZjdhMjkxYWY4N2MxYTBkM2FhZjk4ZDc0Zg%26rlid%3Ddc5c654f7a291af87c1a0d3aaf98d74f&vqd=4-92694175068020572916097096057125279572&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5099.1)\\n    *   [Take a quiz](https://duckduckgo.com/y.js?ad_domain=codefinity.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=F53b3C2SJtLiuBglT7i9LpTQhTIgUHPBK1XDaquJxA0QuJkkH6Ju3mZRVej2MECOyUZBOQuFJVLeMc3phWzDaHUY8v0nqzD7k0vUXKGQuUN2BEucBZ5bU1OG4netzoN1.aQ3xEiODaw6u_wuUWv2YHw&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=f13b39e1188f68416edc3aca93d34fdc9d5c450057e4f96ddb9d11e122aa1809&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8iTMStt275wSy5CKhjwcTfTVUCUxIzz%2Dm%2D0exHB5cRugyCUAbvNC10MWo%2DUAy_VpCyZeEvbqTA9QvXLfgtPQkd5RgNoLH5TQBhHDkPS1l_paeKRqGLwoVBMeBEy42Z%2DnsjeJCNyQRUR5e5fqEJChK9z%2DHPJzO9VD96vy2VBXD8vMQ6QjgEjwJrnraNQ1KgYzuyBQCkgYBbrowQrLNluUAd4dtWm3WHXLaNZg%2D165TgKo2e9KkW6KbExWOeht6Ilp69Plp%2DXClwJDmBJD4lDJEzINgQq4m7Hme1IEaIbd0kkyG2KwibAZgwu7hvtfUZJl6r8ReXy%2Dwz4GkBmbm3tv%2D7I7WFT%2DtcpYlSv9i%2DXI_ySU23jIc5PZKCAhEFfgLQMIkkjMsq%2DremwU3prXciHtFa%2Dilf8UyWaFvveSMu3LuNZowCgHOTAoB%2DqqbMjvOFEC8GT_Oxh7COwFpekt8nyB1u%2DxPehDjs9DVCR6F12rRvhyjdNxaRow_iEhsmcagaUStxcJZ5MSD4ODdnoQwoJQS0pWuZ40Xaud30mmSKU72i495w0Ux8eqxUHjo_7OdexunnVB%2DSyWD2kOYon0XwSsA4PobS3s9X5O%2DVHS1sJUjKuJfygz7j%2DOl9zVUnrqErmp0MwdTfDos6saF1pXBAtmwHv1Jul97v64ZkFi6iaFUz9EQw_8lEuu0DOQ8XRIou%2DmGTHd07ZAH5F2XMscir3uzBwgANspKK%2DC8OQZO560m3V9m9mPoZZhcc_Tin6zd21b_eNWmBUfOCd2VAhv5l0%2DOxQY1kls%26u%3DaHR0cHMlM2ElMmYlMmZjb2RlZmluaXR5LmNvbSUyZmZ1bm5lbCUyZnF1aXolM2Zhc3NldCUzZHNpdGVsaW5rJTI2bXNjbGtpZCUzZDM4NzFiNzlmZTgxZTE3MTViN2Y1ZGZlYTNkYWY0MGViJTI2dXRtX3NvdXJjZSUzZGJpbmclMjZ1dG1fbWVkaXVtJTNkY3BjJTI2dXRtX2NhbXBhaWduJTNkU2VhcmNoX1B5dGhvbl9VU0ElMjZ1dG1fdGVybSUzZHB5dGhvbiUyNTIwbGFuZ3VhZ2UlMjUyMHR1dG9yaWFsJTI2dXRtX2NvbnRlbnQlM2RMZWFybiUyNTIwUHl0aG9u%26rlid%3D3871b79fe81e1715b7f5dfea3daf40eb&vqd=4-121656445144113806665330480386442194560&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5101.1)\\n    *   [Python courses](https://duckduckgo.com/y.js?ad_domain=codefinity.com&ad_provider=bingv7aa&ad_type=txad&click_metadata=D2ZEQsWkGYigKR_fD9BRhGt%2DzGgF2tO3OuoQ4PpG4MFWUztU0oA5q81eLstEDyCpb32tFyz4CK2qLWylrZTNoEHzlgxBFXeSf77N4IMCUuHEE0g6Uv7_R6mjn4pdIMhm.dTqH%2Djv12FZmJAxl3V4Fug&eddgt=TNv2j7kWd7ltUh9pZ7TM5g%3D%3D&rut=22d0bbe4512206203f40620daf8290ba3ff25d9ad977045cf90f6ccc5265b82d&u3=https%3A%2F%2Fwww.bing.com%2Faclick%3Fld%3De8m7G3PJsGixQhULcktUg0YzVUCUwqPJsOtZevLrP_ltWsXBrAA4ZGAJkTCUYXl6fjClhCKP2Ys5Znv8IIkuOToGfWCimTv%2DO4jj2fyEekoR7W8D24JRW0j8SLZi7bA_8C%2DwhofhgxrLys4XMdLZYqYu04AFPTZzSBRIBSSWED0WOCWbzF_nRiemGkqPEyV4bxYC94PPX_dnhG1_pobPGDiZbDqMcqVDSqClQmWP7z2SnqfM0y_lUV%2DuVV_JTl3gwpt1%2DC2uJF2vbURBlpFa39aL9C4K6glnTac6zG2RpdoDvr50GfsimzR4MB78xATLqe9%2DPGZj8CuikgOQ6TzpipesdaXOQBouWErXoZxa46p3m1e4IVqmkBtcxmBqqzu4f0sGCDf6FPX76eS%2DSrl68MqCINhwE6Fza8gML4Ar0C3dmQ8lb9D9iaU6mquIslZh4dhHO9c%2DtYEqGZbFW0inZ3jjwztLX5NVy7FQ647XIj6CuGJ0U2JU6QGnCtPj3AQJ9eJRe9mhqbp1vter_6rJDJ2OwNO0F0JZ5mpLQZlM%2DWfKC4WqlDjJ7yDDy7RRpPTJ3ppEDNiqtBH_QAusxWjyGIgogjTee9u%2DVQsUnUEqfTvsZx3GGJTsJKNANX3YP6TWJxVnG8OIwRibW5m4aEXKDoBAPApUyqfrMDMuln94LsYw0Ta3L5D7WatggwHrX5me2QR%2DRvYnacyjyuMUtXoA6DlJHlEloUfjsYNUqgVoyEEyyiVlsV3t8gt89o2zEXIB6NnR_JV%2DfVLa3bqZ%2DDSED2WnXOXiM%26u%3DaHR0cHMlM2ElMmYlMmZjb2RlZmluaXR5LmNvbSUyZmNvdXJzZXMlMmZjYXRhbG9nJTNmZmlsdGVycyUzZHRlY2hub2xvZ2llcyUzZFB5dGhvbiUyNnV0bV9zb3VyY2UlM2RiaW5nJTI2dXRtX21lZGl1bSUzZGNwYyUyNnV0bV9jYW1wYWlnbiUzZDU2OTU0ODAyOSUyNnV0bV9jb250ZW50JTNkMTE3MjA4MTIyNjA1MDEyOCUyNnV0bV90ZXJtJTNkcHl0aG9uJTI1MjBsYW5ndWFnZSUyNTIwdHV0b3JpYWwlMjZtc2Nsa2lkJTNkZWZhMjdmZDg1ZDE3MTU4ODViOGYxN2FkODhhNmVkYmQ%26rlid%**********************************&vqd=4-29233938162781516156620356803262086766&iurl=%7B1%7DIG%**********************************%26CID%3D1BA8CF8460726A4B29FAD9AE61C76B53%26ID%3DDevEx%2C5103.1)\\n    \\n15.  与 **Python机器学习教程** 相关的搜索\\n    \\n    相关搜索\\n    \\n    1.  [python**机器学习基础教程** **pdf**​](?q=python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80%E6%95%99%E7%A8%8B%20pdf&t=h_)\\n    2.  [python**机器学习14步教程**​](?q=python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A014%E6%AD%A5%E6%95%99%E7%A8%8B&t=h_)\\n    3.  [python **机器学习** **pdf**​](?q=python%20%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%20pdf&t=h_)\\n    4.  [python **机器学习** **14** **步**​](?q=python%20%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%2014%20%E6%AD%A5&t=h_)\\n    \\n    1.  [python**机器学习手册**​](?q=python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E6%89%8B%E5%86%8C&t=h_)\\n    2.  [python**机器学习入门**​](?q=python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%85%A5%E9%97%A8&t=h_)\\n    3.  [python**机器学习基础**​](?q=python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E5%9F%BA%E7%A1%80&t=h_)\\n    4.  [python**机器学习代码**​](?q=python%E6%9C%BA%E5%99%A8%E5%AD%A6%E4%B9%A0%E4%BB%A3%E7%A0%81&t=h_)\\n    \\n\\n更多结果\\n\\n关闭菜单\\n\\n升级到我们的私密浏览器\\n\\n快速。安全。免费。\\n\\n[安装 Mac 浏览器](/mac)\\n\\n*   搜索\\n*   [主页](https://start.duckduckgo.com/)\\n*   [配色](/settings#appearance)\\n*   [设置](/settings)\\n\\n*   意见反馈\\n\\n*   下载\\n*   [iOS 浏览器](https://apps.apple.com/app/duckduckgo-private-browser/id663592361?platform=iphone&pt=866401&mt=8&ct=serp-atb-serp)\\n*   [Android 浏览器](https://play.google.com/store/apps/details?id=com.duckduckgo.mobile.android&referrer=utm_campaign%3Dserp-atb-serp%26origin%3Dfunnel_playstore_searchresults)\\n*   [Mac 浏览器](/mac?origin=funnel_browser_searchresults)\\n*   [Windows 浏览器](/windows?origin=funnel_browser_searchresults)\\n*   [浏览器扩展插件](/duckduckgo-help-pages/desktop/adding-duckduckgo-to-your-browser/)\\n\\n*   更多 DuckDuckGo 产品和服务\\n*   [Privacy Pro](/pro?origin=funnel_pro_searchresults)\\n    \\n    全新\\n    \\n*   [Duck.ai](https://duck.ai)\\n    \\n    全新\\n    \\n*   [Email Protection](/email)\\n*   [简讯](/newsletter)\\n*   [博客](/blog)\\n\\n*   详细了解\\n*   [最新功能](/updates)\\n*   [隐私保护功能比较](/compare-privacy)\\n*   [关于我们的浏览器](/app)\\n*   [关于 DuckDuckGo](/about)\\n\\n*   其他资源\\n*   [帮助](/duckduckgo-help-pages)\\n*   [社群](https://www.reddit.com/r/duckduckgo/)\\n*   [工作岗位](/careers)\\n*   [隐私协议](/privacy)\\n*   [服务条款](/terms)\\n*   [媒体资源](/press)\\n*   [在搜索时投放广告](/duckduckgo-help-pages/company/advertise-on-duckduckgo-search)\\n\\nDuckDuckGo 会保护观看广告的隐私。广告点击量由 Microsoft 广告网络管理. [更多信息](https://duckduckgo.com/duckduckgo-help-pages/company/ads-by-microsoft-on-duckduckgo-private-search)\\n\\nDuckDuckGo 会保护观看广告的隐私。广告点击量由 Microsoft 广告网络管理. [更多信息](https://duckduckgo.com/duckduckgo-help-pages/company/ads-by-microsoft-on-duckduckgo-private-search)\\n\\nDuckDuckGo 会保护观看广告的隐私。广告点击量由 Microsoft 广告网络管理. [更多信息](https://duckduckgo.com/duckduckgo-help-pages/company/ads-by-microsoft-on-duckduckgo-private-search)\\n\\nDuckDuckGo 会保护观看广告的隐私。广告点击量由 Microsoft 广告网络管理. [更多信息](https://duckduckgo.com/duckduckgo-help-pages/company/ads-by-microsoft-on-duckduckgo-private-search)\\n\\n### 下载我们的 Mac 浏览器\\n\\n在搜索和浏览时保护你的数据。\\n\\n[下载](https://duckduckgo.com/mac?origin=funnel_browser_searchresults__footercard)\\n\\n意见反馈\\n\\n自定义日期范围X\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n1. [DONE] 使用DuckDuckGo搜索Python机器学习教程\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "user", "content": "\"用Bing搜索最新的AI技术发展\""}, {"role": "user", "content": "用Bing搜索最新的AI技术发展"}, {"role": "user", "content": "\"访问https://github.com/trending查看今天的热门项目\""}, {"role": "system", "content": "Current plan status:\n1. [TODO] 使用Bing搜索引擎搜索最新的AI技术发展，筛选出最近一年内的相关文章和报告。\n2. [TODO] 访问https://github.com/trending，查看今天的热门项目，特别是与AI技术相关的项目。\n3. [TODO] 对比和分析从Bing和GitHub收集到的信息，识别AI技术发展的最新趋势和热门项目。\n\nFollow this plan. If a step is done, move to the next step."}], "tools": [{"type": "function", "function": {"name": "web_search", "description": "⚠️ SEARCH QUERY LENGTH LIMIT: 7 WORDS MAXIMUM ⚠️\n\nSearch the web for information. For best results:\n1) Use CONCISE queries (3-5 words ideal)\n2) Include only ESSENTIAL keywords, not full questions\n3) For complex topics, use multiple simple searches instead of one long query\n4) Focus on specific terms that will appear on relevant pages", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query - MUST BE CONCISE (maximum 7 words)"}, "count": {"type": "number", "description": "Number of results to return (default: 10)"}}, "required": ["query"]}}}, {"type": "function", "function": {"name": "browser_get_markdown", "description": "[browser] Get the markdown content of the current page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_screenshot", "description": "[browser] Take a screenshot of the current page or a specific area. this tool SHOULD NOT be called unless the user requests an explicit call.", "parameters": {"type": "object", "properties": {"area": {"type": "array", "items": {"type": "number"}, "minItems": 4, "maxItems": 4, "description": "Optional area to capture as [x1, y1, x2, y2]. If not provided, captures the entire viewport."}}}}}, {"type": "function", "function": {"name": "browser_navigate", "description": "[browser] Navigate to a URL", "parameters": {"type": "object", "properties": {"url": {"type": "string"}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_click", "description": "[browser] Click an element on the page, before using the tool, use `browser_get_clickable_elements` to get the index of the element, but not call `browser_get_clickable_elements` multiple times", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to click"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_form_input_fill", "description": "[browser] Fill out an input field, before using the tool, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for input field"}, "index": {"type": "number", "description": "Index of the element to fill"}, "value": {"type": "string", "description": "Value to fill"}}, "required": ["value"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_select", "description": "[browser] Select an element on the page with index, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to select"}, "selector": {"type": "string", "description": "CSS selector for element to select"}, "value": {"type": "string", "description": "Value to select"}}, "required": ["value"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_hover", "description": "[browser] Hover an element on the page, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to hover"}, "selector": {"type": "string", "description": "CSS selector for element to hover"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_evaluate", "description": "[browser] Execute JavaScript in the browser console", "parameters": {"type": "object", "properties": {"script": {"type": "string", "description": "JavaScript code to execute"}}, "required": ["script"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_get_clickable_elements", "description": "[browser] Get the clickable or hoverable or selectable elements on the current page, don't call this tool multiple times", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_read_links", "description": "[browser] Get all links on the current page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_scroll", "description": "[browser] Scroll the page", "parameters": {"type": "object", "properties": {"amount": {"type": "number", "description": "Pixels to scroll (positive for down, negative for up), if the amount is not provided, scroll to the bottom of the page"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_go_back", "description": "[browser] Go back to the previous page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_go_forward", "description": "[browser] Go forward to the next page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_tab_list", "description": "[browser] Get the list of tabs", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_new_tab", "description": "[browser] Open a new tab", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to open in the new tab"}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_close_tab", "description": "[browser] Close the current tab", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_switch_tab", "description": "[browser] Switch to a specific tab", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Tab index to switch to"}}, "required": ["index"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_press_key", "description": "[browser] Press a key on the keyboard", "parameters": {"type": "object", "properties": {"key": {"type": "string", "enum": ["Enter", "Tab", "Escape", "Backspace", "Delete", "Insert", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12", "ArrowLeft", "ArrowRight", "ArrowUp", "ArrowDown", "PageUp", "PageDown", "Home", "End", "ShiftLeft", "ShiftRight", "ControlLeft", "ControlRight", "AltLeft", "AltRight", "MetaLeft", "MetaRight", "CapsLock", "PrintScreen", "ScrollLock", "Pause", "ContextMenu"], "description": "Name of the key to press or a character to generate, such as <PERSON><PERSON>, Tab, Escape, Backspace, Delete, Insert, F1, F2, F3, F4, F5, F6, F7, F8, F9, F10, F11, F12, ArrowLeft, ArrowRight, ArrowUp, ArrowDown, PageUp, PageDown, Home, End, ShiftLeft, ShiftRight, ControlLeft, ControlRight, AltLeft, AltRight, MetaLeft, MetaRight, CapsLock, PrintScreen, ScrollLock, Pause, ContextMenu"}}, "required": ["key"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "read_file", "description": "[filesystem] Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "read_multiple_files", "description": "[filesystem] Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file's content is returned with its path as a reference. Failed reads for individual files won't stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "write_file", "description": "[filesystem] Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "edit_file", "description": "[filesystem] Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "create_directory", "description": "[filesystem] Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "list_directory", "description": "[filesystem] Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "directory_tree", "description": "[filesystem] Get a recursive tree view of files and directories as a JSON structure. Each entry includes 'name', 'type' (file/directory), and 'children' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "move_file", "description": "[filesystem] Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "search_files", "description": "[filesystem] Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don't know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "get_file_info", "description": "[filesystem] Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "list_allowed_directories", "description": "[filesystem] Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "run_command", "description": "[commands] Run a command on this darwin machine", "parameters": {"type": "object", "properties": {"command": {"type": "string", "description": "Command with args"}, "cwd": {"type": "string", "description": "Current working directory, leave empty in most cases"}}, "required": ["command"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "run_script", "description": "[commands] Run a script on this darwin machine", "parameters": {"type": "object", "properties": {"interpreter": {"type": "string", "description": "Command with arguments. <PERSON><PERSON><PERSON> will be piped to stdin. Examples: bash, fish, zsh, python, or: bash --norc"}, "script": {"type": "string", "description": "<PERSON><PERSON><PERSON> to run"}, "cwd": {"type": "string", "description": "Current working directory, leave empty in most cases"}}, "required": ["script"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "final_answer", "description": "Generate a focused report or answer after completing research or information gathering", "parameters": {"type": "object", "properties": {"isDeepResearch": {"type": "boolean", "description": "Whether to generate a structured report (true) or simple answer (false)"}, "title": {"type": "string", "description": "Title for the report or answer"}, "format": {"type": "string", "enum": ["detailed", "concise"], "description": "Report format: detailed or concise"}}}}}, {"type": "function", "function": {"name": "start_codegen_session", "description": "[playwright] Start a new code generation session to record Playwright actions", "parameters": {"type": "object", "properties": {"options": {"type": "object", "description": "Code generation options", "properties": {"outputPath": {"type": "string", "description": "Directory path where generated tests will be saved (use absolute path)"}, "testNamePrefix": {"type": "string", "description": "Prefix to use for generated test names (default: 'GeneratedTest')"}, "includeComments": {"type": "boolean", "description": "Whether to include descriptive comments in generated tests"}}, "required": ["outputPath"]}}, "required": ["options"]}}}, {"type": "function", "function": {"name": "end_codegen_session", "description": "[playwright] End a code generation session and generate the test file", "parameters": {"type": "object", "properties": {"sessionId": {"type": "string", "description": "ID of the session to end"}}, "required": ["sessionId"]}}}, {"type": "function", "function": {"name": "get_codegen_session", "description": "[playwright] Get information about a code generation session", "parameters": {"type": "object", "properties": {"sessionId": {"type": "string", "description": "ID of the session to retrieve"}}, "required": ["sessionId"]}}}, {"type": "function", "function": {"name": "clear_codegen_session", "description": "[playwright] Clear a code generation session without generating a test", "parameters": {"type": "object", "properties": {"sessionId": {"type": "string", "description": "ID of the session to clear"}}, "required": ["sessionId"]}}}, {"type": "function", "function": {"name": "playwright_navigate", "description": "[playwright] Navigate to a URL", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to navigate to the website specified"}, "browserType": {"type": "string", "description": "Browser type to use (chromium, firefox, webkit). Defaults to chromium", "enum": ["chromium", "firefox", "webkit"]}, "width": {"type": "number", "description": "Viewport width in pixels (default: 1280)"}, "height": {"type": "number", "description": "Viewport height in pixels (default: 720)"}, "timeout": {"type": "number", "description": "Navigation timeout in milliseconds"}, "waitUntil": {"type": "string", "description": "Navigation wait condition"}, "headless": {"type": "boolean", "description": "Run browser in headless mode (default: false)"}}, "required": ["url"]}}}, {"type": "function", "function": {"name": "playwright_screenshot", "description": "[playwright] Take a screenshot of the current page or a specific element", "parameters": {"type": "object", "properties": {"name": {"type": "string", "description": "Name for the screenshot"}, "selector": {"type": "string", "description": "CSS selector for element to screenshot"}, "width": {"type": "number", "description": "Width in pixels (default: 800)"}, "height": {"type": "number", "description": "Height in pixels (default: 600)"}, "storeBase64": {"type": "boolean", "description": "Store screenshot in base64 format (default: true)"}, "fullPage": {"type": "boolean", "description": "Store screenshot of the entire page (default: false)"}, "savePng": {"type": "boolean", "description": "Save screenshot as PNG file (default: false)"}, "downloadsDir": {"type": "string", "description": "Custom downloads directory path (default: user's Downloads folder)"}}, "required": ["name"]}}}, {"type": "function", "function": {"name": "playwright_click", "description": "[playwright] Click an element on the page", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for the element to click"}}, "required": ["selector"]}}}, {"type": "function", "function": {"name": "playwright_iframe_click", "description": "[playwright] Click an element in an iframe on the page", "parameters": {"type": "object", "properties": {"iframeSelector": {"type": "string", "description": "CSS selector for the iframe containing the element to click"}, "selector": {"type": "string", "description": "CSS selector for the element to click"}}, "required": ["iframeSelector", "selector"]}}}, {"type": "function", "function": {"name": "playwright_iframe_fill", "description": "[playwright] Fill an element in an iframe on the page", "parameters": {"type": "object", "properties": {"iframeSelector": {"type": "string", "description": "CSS selector for the iframe containing the element to fill"}, "selector": {"type": "string", "description": "CSS selector for the element to fill"}, "value": {"type": "string", "description": "Value to fill"}}, "required": ["iframeSelector", "selector", "value"]}}}, {"type": "function", "function": {"name": "playwright_fill", "description": "[playwright] fill out an input field", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for input field"}, "value": {"type": "string", "description": "Value to fill"}}, "required": ["selector", "value"]}}}, {"type": "function", "function": {"name": "playwright_select", "description": "[playwright] Select an element on the page with Select tag", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for element to select"}, "value": {"type": "string", "description": "Value to select"}}, "required": ["selector", "value"]}}}, {"type": "function", "function": {"name": "playwright_hover", "description": "[playwright] Hover an element on the page", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for element to hover"}}, "required": ["selector"]}}}, {"type": "function", "function": {"name": "playwright_upload_file", "description": "[playwright] Upload a file to an input[type='file'] element on the page", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for the file input element"}, "filePath": {"type": "string", "description": "Absolute path to the file to upload"}}, "required": ["selector", "filePath"]}}}, {"type": "function", "function": {"name": "playwright_evaluate", "description": "[playwright] Execute JavaScript in the browser console", "parameters": {"type": "object", "properties": {"script": {"type": "string", "description": "JavaScript code to execute"}}, "required": ["script"]}}}, {"type": "function", "function": {"name": "playwright_console_logs", "description": "[playwright] Retrieve console logs from the browser with filtering options", "parameters": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of logs to retrieve (all, error, warning, log, info, debug, exception)", "enum": ["all", "error", "warning", "log", "info", "debug", "exception"]}, "search": {"type": "string", "description": "Text to search for in logs (handles text with square brackets)"}, "limit": {"type": "number", "description": "Maximum number of logs to return"}, "clear": {"type": "boolean", "description": "Whether to clear logs after retrieval (default: false)"}}, "required": []}}}, {"type": "function", "function": {"name": "playwright_close", "description": "[playwright] Close the browser and release all resources", "parameters": {"type": "object", "properties": {}, "required": []}}}, {"type": "function", "function": {"name": "playwright_get", "description": "[playwright] Perform an HTTP GET request", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to perform GET operation"}}, "required": ["url"]}}}, {"type": "function", "function": {"name": "playwright_post", "description": "[playwright] Perform an HTTP POST request", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to perform POST operation"}, "value": {"type": "string", "description": "Data to post in the body"}, "token": {"type": "string", "description": "Bearer token for authorization"}, "headers": {"type": "object", "description": "Additional headers to include in the request", "additionalProperties": {"type": "string"}}}, "required": ["url", "value"]}}}, {"type": "function", "function": {"name": "playwright_put", "description": "[playwright] Perform an HTTP PUT request", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to perform PUT operation"}, "value": {"type": "string", "description": "Data to PUT in the body"}}, "required": ["url", "value"]}}}, {"type": "function", "function": {"name": "playwright_patch", "description": "[playwright] Perform an HTTP PATCH request", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to perform PUT operation"}, "value": {"type": "string", "description": "Data to PATCH in the body"}}, "required": ["url", "value"]}}}, {"type": "function", "function": {"name": "playwright_delete", "description": "[playwright] Perform an HTTP DELETE request", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to perform DELETE operation"}}, "required": ["url"]}}}, {"type": "function", "function": {"name": "playwright_expect_response", "description": "[playwright] Ask <PERSON><PERSON> to start waiting for a HTTP response. This tool initiates the wait operation but does not wait for its completion.", "parameters": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique & arbitrary identifier to be used for retrieving this response later with `Playwright_assert_response`."}, "url": {"type": "string", "description": "URL pattern to match in the response."}}, "required": ["id", "url"]}}}, {"type": "function", "function": {"name": "playwright_assert_response", "description": "[playwright] Wait for and validate a previously initiated HTTP response wait operation.", "parameters": {"type": "object", "properties": {"id": {"type": "string", "description": "Identifier of the HTTP response initially expected using `Playwright_expect_response`."}, "value": {"type": "string", "description": "Data to expect in the body of the HTTP response. If provided, the assertion will fail if this value is not found in the response body."}}, "required": ["id"]}}}, {"type": "function", "function": {"name": "playwright_custom_user_agent", "description": "[playwright] Set a custom User Agent for the browser", "parameters": {"type": "object", "properties": {"userAgent": {"type": "string", "description": "Custom User Agent for the Playwright browser instance"}}, "required": ["userAgent"]}}}, {"type": "function", "function": {"name": "playwright_get_visible_text", "description": "[playwright] Get the visible text content of the current page", "parameters": {"type": "object", "properties": {}, "required": []}}}, {"type": "function", "function": {"name": "playwright_get_visible_html", "description": "[playwright] Get the HTML content of the current page. By default, all <script> tags are removed from the output unless removeScripts is explicitly set to false.", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector to limit the HTML to a specific container"}, "removeScripts": {"type": "boolean", "description": "Remove all script tags from the HTML (default: true)"}, "removeComments": {"type": "boolean", "description": "Remove all HTML comments (default: false)"}, "removeStyles": {"type": "boolean", "description": "Remove all style tags from the HTML (default: false)"}, "removeMeta": {"type": "boolean", "description": "Remove all meta tags from the HTML (default: false)"}, "cleanHtml": {"type": "boolean", "description": "Perform comprehensive HTML cleaning (default: false)"}, "minify": {"type": "boolean", "description": "Minify the HTML output (default: false)"}, "maxLength": {"type": "number", "description": "Maximum number of characters to return (default: 20000)"}}, "required": []}}}, {"type": "function", "function": {"name": "playwright_go_back", "description": "[playwright] Navigate back in browser history", "parameters": {"type": "object", "properties": {}, "required": []}}}, {"type": "function", "function": {"name": "playwright_go_forward", "description": "[playwright] Navigate forward in browser history", "parameters": {"type": "object", "properties": {}, "required": []}}}, {"type": "function", "function": {"name": "playwright_drag", "description": "[playwright] Drag an element to a target location", "parameters": {"type": "object", "properties": {"sourceSelector": {"type": "string", "description": "CSS selector for the element to drag"}, "targetSelector": {"type": "string", "description": "CSS selector for the target location"}}, "required": ["sourceSelector", "targetSelector"]}}}, {"type": "function", "function": {"name": "playwright_press_key", "description": "[playwright] Press a keyboard key", "parameters": {"type": "object", "properties": {"key": {"type": "string", "description": "Key to press (e.g. 'Enter', 'ArrowDown', 'a')"}, "selector": {"type": "string", "description": "Optional CSS selector to focus before pressing key"}}, "required": ["key"]}}}, {"type": "function", "function": {"name": "playwright_save_as_pdf", "description": "[playwright] Save the current page as a PDF file", "parameters": {"type": "object", "properties": {"outputPath": {"type": "string", "description": "Directory path where PDF will be saved"}, "filename": {"type": "string", "description": "Name of the PDF file (default: page.pdf)"}, "format": {"type": "string", "description": "Page format (e.g. 'A4', 'Letter')"}, "printBackground": {"type": "boolean", "description": "Whether to print background graphics"}, "margin": {"type": "object", "description": "Page margins", "properties": {"top": {"type": "string"}, "right": {"type": "string"}, "bottom": {"type": "string"}, "left": {"type": "string"}}}}, "required": ["outputPath"]}}}, {"type": "function", "function": {"name": "playwright_click_and_switch_tab", "description": "[playwright] Click a link and switch to the newly opened tab", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for the link to click"}}, "required": ["selector"]}}}], "temperature": 0.7, "stream": true, "max_tokens": 8192, "provider": "deepseek", "thinking": {"type": "enabled"}}, "baseURL": "https://api.deepseek.com/v1"}