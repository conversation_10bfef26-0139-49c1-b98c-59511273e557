# 🚀 Agent TARS Startup Commands Reference

## 📋 **Quick Reference - All Startup Methods**

### **🎯 Main Startup Commands (Choose One):**

#### **1. Agent TARS with Chart Capabilities** ⭐ (Recommended)
```bash
# From GitHub root directory
./start-agent-tars-with-charts.sh
```

#### **2. Basic Agent TARS (No Charts)**
```bash
# From GitHub root directory
./start-agent-tars.sh
```

#### **3. Advanced Configuration Options**
```bash
# From GitHub root directory
./agent-tars-config.sh ui        # Interactive UI
./agent-tars-config.sh server    # Headless server
./agent-tars-config.sh debug     # Debug mode
./agent-tars-config.sh help      # Show help
```

#### **4. Direct NPX Commands (Works from any directory)**
```bash
# Basic startup
npx @agent-tars/cli@latest start --open

# With custom port
npx @agent-tars/cli@latest start --open --port 9000

# Headless server
npx @agent-tars/cli@latest serve

# Debug mode
npx @agent-tars/cli@latest start --open --debug
```

---

## 🔧 **Setup Commands (One-time only)**

### **Shell Aliases Setup:**
```bash
# Run once to set up convenient aliases
./setup-agent-tars-alias.sh

# Then restart terminal and use:
agent-tars              # Start with UI
agent-tars-server       # Headless server
agent-tars-debug        # Debug mode
```

### **Environment Setup for Charts:**
```bash
# First time setup for chart capabilities
cd agent-tars-setup
source ./setup-env.sh
nano .env  # Add your OpenAI API key
```

---

## 📊 **Chart-Enabled Startup (Detailed)**

### **Method 1: Main Launcher**
```bash
# From: /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub
./start-agent-tars-with-charts.sh
```

### **Method 2: Direct from Setup Directory**
```bash
# From: /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub/agent-tars-setup
./start-agent-tars-with-chart.sh
```

---

## 🎯 **Most Common Usage Patterns**

### **Daily Startup (Recommended):**
```bash
cd "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub"
./start-agent-tars-with-charts.sh
```

### **Quick Test:**
```bash
npx @agent-tars/cli@latest start --open
```

### **Development/Debug:**
```bash
cd "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub"
./agent-tars-config.sh debug
```

---

## 📁 **File Locations**

### **All Scripts Located In:**
```
/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub/
```

### **Available Scripts:**
- `start-agent-tars.sh` - Basic Agent TARS
- `start-agent-tars-with-charts.sh` - With chart capabilities ⭐
- `agent-tars-config.sh` - Advanced options
- `setup-agent-tars-alias.sh` - Shell aliases setup

### **Chart Setup Directory:**
```
/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub/agent-tars-setup/
```

---

## 🔑 **Environment Variables (Optional)**

```bash
# Add to your shell profile for convenience
export AGENT_TARS_DIR="/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub"
export OPENAI_API_KEY="your_key_here"

# Then you can use:
cd $AGENT_TARS_DIR && ./start-agent-tars-with-charts.sh
```

---

## 🚨 **Troubleshooting Quick Fixes**

### **Permission Issues:**
```bash
chmod +x *.sh
```

### **Can't Find Script:**
```bash
cd "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub"
ls -la *.sh  # List all scripts
```

### **Port Already in Use:**
```bash
npx @agent-tars/cli@latest start --open --port 9000
```

---

## 💡 **Pro Tips**

1. **Bookmark this file** for quick reference
2. **Use the chart-enabled version** for full capabilities
3. **Set up shell aliases** for fastest access
4. **Keep API keys in .env file** in agent-tars-setup directory

---

## 🎉 **Most Recommended Command**

```bash
# Copy and paste this for daily use:
cd "/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub" && ./start-agent-tars-with-charts.sh
```

**This gives you Agent TARS with full chart visualization capabilities! 📊✨**
