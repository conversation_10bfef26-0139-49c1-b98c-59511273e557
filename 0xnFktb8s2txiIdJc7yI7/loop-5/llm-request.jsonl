{"provider": "deepseek", "request": {"model": "deepseek-chat", "messages": [{"role": "system", "content": "\nYou are Agent TARS, a multimodal AI agent created by the ByteDance.\n\n<intro>\nYou excel at the following tasks:\n1. Information gathering, fact-checking, and documentation\n2. Data processing, analysis, and visualization\n3. Writing multi-chapter articles and in-depth research reports\n4. Creating websites, applications, and tools\n5. Using programming to solve various problems beyond development\n6. Various tasks that can be accomplished using computers and the internet\n</intro>\n\n<language_settings>\nUse the language specified by user in messages as the working language when explicitly provided\nAll thinking and responses must be in the working language\nNatural language arguments in tool calls must be in the working language\nAvoid using pure lists and bullet points format in any language\n</language_settings>\n\n<multimodal_understanding>\nWhen processing images, it's crucial to understand the difference between image types:\n1. Browser Screenshots: These are images showing the browser interface that you can interact with using browser tools\n   - Appear as part of the browser_vision_control tool output or environment input labeled as \"Browser Screenshot\"\n   - ONLY these screenshots represent interfaces you can operate on with browser tools\n   - Use these for navigation, clicking elements, scrolling, and other browser interactions\n\n2. User-Uploaded Images: These are regular images the user has shared but are NOT browser interfaces\n   - May include photos, diagrams, charts, documents, or any other visual content\n   - Cannot be operated on with browser tools - don't try to click elements in these images\n   - Should be analyzed for information only (objects, text, context, meaning)\n   - Respond to user questions about these images with observations and analysis\n\nDistinguish between these types by context and environment input descriptions to avoid confusion.\nWhen you see a new image, first determine which type it is before deciding how to interact with it.\n</multimodal_understanding>\n\n<system_capability>\nSystem capabilities:\n- Communicate with users through message tools\n- Access a Linux sandbox environment with internet connection\n- Use shell, text editor, browser, and other software\n- Write and run code in Python and various programming languages\n- Independently install required software packages and dependencies via shell\n- Deploy websites or applications and provide public access\n- Suggest users to temporarily take control of the browser for sensitive operations when necessary\n- Utilize various tools to complete user-assigned tasks step by step\n</system_capability>\n\n<agent_loop>\nYou operate in an agent loop, iteratively completing tasks through these steps:\n1. Analyze Events: Understand user needs and current state through event stream, focusing on latest user messages and execution results\n2. Select Tools: Choose next tool call based on current state, task planning, relevant knowledge and available data APIs\n3. Wait for Execution: Selected tool action will be executed by sandbox environment with new observations added to event stream\n4. Iterate: Choose only one tool call per iteration, patiently repeat above steps until task completion\n5. Submit Results: Send results to user via message tools, providing deliverables and related files as message attachments\n6. Enter Standby: Enter idle state when all tasks are completed or user explicitly requests to stop, and wait for new tasks\n</agent_loop>\n\n<file_rules>\n- Use file tools for reading, writing, appending, and editing to avoid string escape issues in shell commands\n- Actively save intermediate results and store different types of reference information in separate files\n- When merging text files, must use append mode of file writing tool to concatenate content to target file\n- Strictly follow requirements in <writing_rules>, and avoid using list formats in any files except todo.md\n</file_rules>\n\n<shell_rules>\n- Avoid commands requiring confirmation; actively use -y or -f flags for automatic confirmation\n- Avoid commands with excessive output; save to files when necessary\n- Chain multiple commands with && operator to minimize interruptions\n- Use pipe operator to pass command outputs, simplifying operations\n- Use non-interactive `bc` for simple calculations, Python for complex math; never calculate mentally\n- Use `uptime` command when users explicitly request sandbox status check or wake-up\n</shell_rules>\n\n\n\n\n<planning_approach>\nYou are a methodical agent that follows a plan-and-solve approach for complex tasks. When handling tasks:\n\n1. Analyze if the task requires a multi-step plan:\n   - For complex research, analysis, or multi-part tasks → Create a plan\n   - For simple questions or tasks → Skip planning and answer directly\n\n2. If a plan is needed:\n   - Create a clear, step-by-step plan with specific goals\n   - Execute each step in order, using appropriate tools\n   - Update the plan as you learn new information\n   - Mark steps as completed when done\n   - Once ALL steps are complete, call the \"final_answer\" tool\n\n3. During execution:\n   - Adapt your plan as needed based on new findings\n   - Be willing to simplify the plan if the task turns out simpler than expected\n   - Always complete your plan before providing final answers\n</planning_approach>\n\n<planning_constraints>\nIMPORTANT CONSTRAINTS:\n- Create AT MOST 3 key steps in your plan\n- Focus on information gathering and research steps\n- Call the \"final_answer\" tool once ALL plan steps are complete\n- For simple questions, you can skip planning entirely\n</planning_constraints>\n \n\n \n<browser_rules>\nYou have access to various browser tools to interact with web pages and extract information.\n\nYou have DOM-based browser control tools that work directly with the page structure:\n\n- Navigation: `browser_navigate`, `browser_back`, `browser_forward`, `browser_refresh`\n- Interaction: `browser_click`, `browser_type`, `browser_press`, `browser_hover`, `browser_drag`, `browser_scroll`\n- Content extraction: `browser_get_markdown`\n- Status checking: `browser_get_url`, `browser_get_title`, `browser_get_elements`\n- Tab management: `browser_tab_list`, `browser_new_tab`, `browser_close_tab`, `browser_switch_tab`\n\nUSAGE GUIDELINES:\n- Use CSS selectors or element indices to precisely target elements\n- Extract content with `browser_get_markdown` for efficient analysis\n- Find and verify elements with `browser_get_elements` before interacting\n- Leverage browser state tools to keep track of navigation\n\n- Must use browser tools to access and comprehend all URLs provided by users in messages\n- Must use browser tools to access URLs from search tool results\n- Actively explore valuable links for deeper information, either by clicking elements or accessing URLs directly\n- Browser tools only return elements in visible viewport by default\n- Due to technical limitations, not all interactive elements may be identified; use coordinates to interact with unlisted elements\n- Browser tools automatically attempt to extract page content, providing it in Markdown format if successful\n- Extracted Markdown includes text beyond viewport but omits links and images; completeness not guaranteed\n- If extracted Markdown is complete and sufficient for the task, no scrolling is needed; otherwise, must actively scroll to view the entire page\n- Use message tools to suggest user to take over the browser for sensitive operations or actions with side effects when necessary\n</browser_rules>\n\n<envirnoment>\nCurrent Working Directory: /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub\n</envirnoment>\n\n    \n\nCurrent time: 5/20/2025, 10:00:00 AM"}, {"role": "user", "content": "请查询如果下周去北京旅游的天气情况，从深圳出发"}, {"role": "system", "content": "Current plan status:\n1. [TODO] Check the weather forecast for Beijing for the upcoming week to determine the best time to visit.\n2. [TODO] Research flight options from Shenzhen to Beijing, including prices and schedules, to plan the trip accordingly.\n3. [TODO] Look for any travel advisories or requirements for visiting Beijing, such as visa requirements or COVID-19 restrictions, to ensure a smooth trip.\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_b81d18f9-e162-4258-aba3-97657ac3bba7", "type": "function", "function": {"name": "web_search", "arguments": "{\"query\":\"北京下周天气预报\",\"count\":1}"}}]}, {"role": "tool", "tool_call_id": "call_0_b81d18f9-e162-4258-aba3-97657ac3bba7", "content": "{\n  \"error\": \"Search failed: Waiting for selector `#search` failed: Waiting failed: 30000ms exceeded\"\n}"}, {"role": "system", "content": "Current plan status:\n1. [DONE] Check the weather forecast for Beijing for the upcoming week to determine the best time to visit.\n2. [TODO] Research flight options from Shenzhen to Beijing, including prices and schedules, to plan the trip accordingly.\n3. [TODO] Look for any travel advisories or requirements for visiting Beijing, such as visa requirements or COVID-19 restrictions, to ensure a smooth trip.\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_e8dad0f8-714d-4b62-9fe7-1f0dbf307ecb", "type": "function", "function": {"name": "web_search", "arguments": "{\"query\":\"北京下周天气\",\"count\":1}"}}]}, {"role": "tool", "tool_call_id": "call_0_e8dad0f8-714d-4b62-9fe7-1f0dbf307ecb", "content": "{\n  \"error\": \"Search failed: Waiting for selector `#search` failed: Waiting failed: 30000ms exceeded\"\n}"}, {"role": "system", "content": "Current plan status:\n1. [DONE] Check the weather forecast for Beijing for the upcoming week to determine the best time to visit.\n2. [DONE] Research flight options from Shenzhen to Beijing, including prices and schedules, to plan the trip accordingly.\n3. [DONE] Look for any travel advisories or requirements for visiting Beijing, such as visa requirements or COVID-19 restrictions, to ensure a smooth trip.\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_305e2a17-c8b2-450e-977b-bfd68152e787", "type": "function", "function": {"name": "browser_navigate", "arguments": "{\"url\":\"https://www.weather.com/weather/tenday/l/Beijing+China\"}"}}]}, {"role": "tool", "tool_call_id": "call_0_305e2a17-c8b2-450e-977b-bfd68152e787", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \"Navigated to https://www.weather.com/weather/tenday/l/Beijing+China\\nclickable elements(Might be outdated, if an error occurs with the index element, use browser_get_clickable_elements to refresh it): [0]<a></a>\\n[]Type at least three characters to start auto complete. Recently searched locations will be displayed if there is no search query. The first option will be automatically selected. Use up and down arrows to change selection. Use escape to clear.\\n[]Search City or Zip Code\\n[1]<input></input>\\n[2]<button>US\\n°F</button>\\n[3]<button>Menu</button>\\n[4]<a></a>\\n[5]<a></a>\\n[6]<a></a>\\n[7]<a></a>\\n[8]<a></a>\\n[9]<a></a>\\n[10]<a></a>\\n[11]<a></a>\\n[12]<button></button>\\n[]Oops! We are having trouble finding your page.\\n[]Please try again... or Check out some of our favorite areas of the site.\\n[13]<a>Video\\nWatch today's top stories and most popular videos. Looking to watch incredible tornadoes? We've got that too.</a>\\n[14]<img></img>\\n[15]<a>Maps\\nCheck out our interactive maps, which provide you with features like past/future radar and customizable layers.</a>\\n[16]<img></img>\\n[17]<a>Local Weather\\nTrust us to help you plan the best day possible, with the most accurate weather forecast available.</a>\\n[18]<img></img>\\n[19]<a></a>\\n[20]<img></img>\\n[21]<a></a>\\n[22]<img></img>\\n[23]<a></a>\\n[24]<img></img>\\n[25]<a></a>\\n[26]<img></img>\\n[]Feedback\\n[]Mission\\n[]Careers\\n[]News Room\\n[]Advertise With Us\\n[]TV\\n[]Weather Data APIs\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_2fefb353-57a4-40c4-a467-e58859e7d397", "type": "function", "function": {"name": "browser_get_markdown", "arguments": "{}"}}]}, {"role": "tool", "tool_call_id": "call_0_2fefb353-57a4-40c4-a467-e58859e7d397", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \" 404 Not Found          \\n\\n[Skip to Main Content](#MainContent)[Accessibility Help](https://www.essentialaccessibility.com/the-weather-channel?utm_source=theweatherchannelhomepage&utm_medium=iconlarge&utm_term=eachannelpage&utm_content=header&utm_campaign=theweatherchannel) [The Weather Channel](/)\\n\\nType at least three characters to start auto complete. Recently searched locations will be displayed if there is no search query. The first option will be automatically selected. Use up and down arrows to change selection. Use escape to clear.Search City or Zip Code\\n\\n## recents\\n\\nClear All\\n\\nYou have no recent locations\\n\\nGlobe US°FArrow down\\n\\n*   °F\\n*   °C\\n*   Hybrid\\n\\nImperial - F / mph / miles / inches\\n\\nAmericas Add\\n\\n[Antigua and Barbuda | English](/en-AG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Argentina | Español](/es-AR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Bahamas | English](/en-BS/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Barbados | English](/en-BB/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Belize | English](/en-BZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Bolivia | Español](/es-BO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Brazil | Português](/pt-BR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Canada | English](/en-CA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Canada | Français](/fr-CA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Chile | Español](/es-CL/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Colombia | Español](/es-CO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Costa Rica | Español](/es-CR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Dominica | English](/en-DM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Dominican Republic | Español](/es-DO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Ecuador | Español](/es-EC/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[El Salvador | Español](/es-SV/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Grenada | English](/en-GD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Guatemala | Español](/es-GT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Guyana | English](/en-GY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Haiti | Français](/fr-HT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Honduras | Español](/es-HN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Jamaica | English](/en-JM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mexico | Español](/es-MX/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Nicaragua | Español](/es-NI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Panama | Español](/es-PA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Panama | English](/en-PA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Paraguay | Español](/es-PY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Peru | Español](/es-PE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[St. Kitts and Nevis | English](/en-KN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[St. Lucia | English](/en-LC/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[St. Vincent and the Grenadines | English](/en-VC/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Suriname | Nederlands](/nl-SR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Trinidad and Tobago | English](/en-TT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Uruguay | Español](/es-UY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[United States | English](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[United States | Español](/es-US/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Venezuela | Español](/es-VE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nAfrica Add\\n\\n[Algeria | العربية](/ar-DZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Algeria | Français](/fr-DZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Angola | Português](/pt-AO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Benin | Français](/fr-BJ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Burkina Faso | Français](/fr-BF/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Burundi | Français](/fr-BI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cameroon | Français](/fr-CM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cameroon | English](/en-CM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cape Verde | Português](/pt-CV/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Central African Republic | Français](/fr-CF/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Chad | Français](/fr-TD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Chad | العربية](/ar-TD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Comoros | Français](/fr-KM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Comoros | العربية](/ar-KM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Democratic Republic of the Congo | Français](/fr-CD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Republic of Congo | Français](/fr-CG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Côte d'Ivoire | Français](/fr-CI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Djibouti | Français](/fr-DJ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Djibouti | العربية](/ar-DJ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Egypt | العربية](/ar-EG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Equatorial Guinea | Español](/es-GQ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Eritrea | العربية](/ar-ER/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Gabon | Français](/fr-GA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Gambia | English](/en-GM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Ghana | English](/en-GH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Guinea | Français](/fr-GN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Guinea-Bissau | Português](/pt-GW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Kenya | English](/en-KE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Lesotho | English](/en-LS/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Liberia | English](/en-LR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Libya | العربية](/ar-LY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Madagascar | Français](/fr-MG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mali | Français](/fr-ML/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mauritania | العربية](/ar-MR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mauritius | English](/en-MU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mauritius | Français](/fr-MU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Morocco | العربية](/ar-MA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Morocco | Français](/fr-MA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mozambique | Português](/pt-MZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Namibia | English](/en-NA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Niger | Français](/fr-NE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Nigeria | English](/en-NG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Rwanda | Français](/fr-RW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Rwanda | English](/en-RW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Sao Tome and Principe | Português](/pt-ST/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Senegal | Français](/fr-SN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Sierra Leone | English](/en-SL/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Somalia | العربية](/ar-SO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[South Africa | English](/en-ZA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[South Sudan | English](/en-SS/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Sudan | العربية](/ar-SD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Swaziland | English](/en-SZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Tanzania | English](/en-TZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Togo | Français](/fr-TG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Tunisia | العربية](/ar-TN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Uganda | English](/en-UG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nAsia Pacific Add\\n\\n[Australia | English](/en-AU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Bangladesh | বাংলা](/bn-BD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Brunei | Bahasa Melayu](/ms-BN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[China | 中文](/zh-CN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Hong Kong SAR | 中文](/zh-HK/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[East Timor | Português](/pt-TP/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Fiji | English](/en-FJ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[India (English) | English](/en-IN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[India (Hindi) | हिन्दी](/hi-IN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Indonesia | Bahasa Indonesia](/id-ID/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Japan | 日本語](/ja-JP/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Kiribati | English](/en-KI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[South Korea | 한국어](/ko-KR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Kyrgyzstan | Русский](/ru-KG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Malaysia | Bahasa Melayu](/ms-MY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Marshall Islands | English](/en-MH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Micronesia | English](/en-FM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[New Zealand | English](/en-NZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Palau | English](/en-PW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Philippines | English](/en-PH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Philippines | Tagalog](/tl-PH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Samoa | English](/en-AS/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Singapore | English](/en-SG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Singapore | 中文](/zh-SG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Solomon Islands | English](/en-SB/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Taiwan | 中文](/zh-TW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Thailand | ไทย](/th-TH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Tonga | English](/en-TO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Tuvalu | English](/en-TV/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Vanuatu | English](/en-VU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Vanuatu | Français](/fr-VU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Vietnam | Tiếng Việt](/vi-VN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nEurope Add\\n\\n[Andorra | Català](/ca-AD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Andorra | Français](/fr-AD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Austria | Deutsch](/de-AT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Belarus | Русский](/ru-BY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Belgium | Dutch](/nl-BE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Belgium | Français](/fr-BE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Bosnia and Herzegovina | Hrvatski](/hr-BA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Croatia | Hrvatski](/hr-HR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cyprus | Ελληνικά](/el-CY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Czech Republic | Čeština](/cs-CZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Denmark | Dansk](/da-DK/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Estonia | Русский](/ru-EE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Estonia | Eesti](/et-EE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Finland | Suomi](/fi-FI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[France | Français](/fr-FR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Germany | Deutsch](/de-DE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Greece | Ελληνικά](/el-GR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Hungary | Magyar](/hu-HU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Ireland | English](/en-IE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Italy | Italiano](/it-IT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Liechtenstein | Deutsch](/de-LI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Luxembourg | Français](/fr-LU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Malta | English](/en-MT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Monaco | Français](/fr-MC/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Netherlands | Nederlands](/nl-NL/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Norway | Norsk](/no-NO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Poland | Polski](/pl-PL/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Portugal | Português](/pt-PT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Romania | Română](/ro-RO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Russia | Русский](/ru-RU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[San Marino | Italiano](/it-SM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Slovakia | Slovenčina](/sk-SK/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Spain | Español](/es-ES/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Spain | Català](/ca-ES/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Sweden | Svenska](/sv-SE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Switzerland | Deutsch](/de-CH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Turkey | Turkçe](/tr-TR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Ukraine | Українська](/uk-UA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[United Kingdom | English](/en-GB/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[State of Vatican City (Holy See) | Italiano](/it-VA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nMiddle East Add\\n\\n[Bahrain | العربية](/ar-BH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Iran | فارسى](/fa-IR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Iraq | العربية](/ar-IQ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Israel | עִבְרִית](/he-IL/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Jordan | العربية](/ar-JO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Kuwait | العربية](/ar-KW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Lebanon | العربية](/ar-LB/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Oman | العربية](/ar-OM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Pakistan | اردو](/ur-PK/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Pakistan | English](/en-PK/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Qatar | العربية](/ar-QA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Saudi Arabia | العربية](/ar-SA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Syria | العربية](/ar-SY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[United Arab Emirates | العربية](/ar-AE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nMenu\\n\\n## Weather Forecasts\\n\\n[Today](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Hourly](/weather/hourbyhour/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[10 Day](/weather/tenday/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\n## Radar & Maps\\n\\n[Interactive Radar Map](/weather/radar/interactive/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08?animation=true)[US Forecast](/maps/currentusweather)[US Satellite & Radar](/maps/ussatellitemap)[World Satellite](/maps/satellite/caribbeanweathermap)[US Severe Alerts](/maps/severealerts)[US Health](/maps/health/allergies/treepollen)\\n\\n## News & Media\\n\\n[Top Weather Stories](/science/space/video/buy-a-massive-rock-from-planet-mars)[Hurricane Central](/storms/hurricane-central)[Tornado CentralNEW](/storms/tornado-central)[Safety & Prep](/safety/index)[Space & Skywatching](/science/space)[Videos](/science/space/video/buy-a-massive-rock-from-planet-mars)\\n\\n## Products & Account\\n\\n[Alexa SkillExternal Link](https://www.amazon.com/The-Weather-Company-Channel/dp/B07YPYHQ1Q) [Create an Account](/signup)[Sign in](/login)\\n\\n## Lifestyle\\n\\n[Atmosphere Reviews](/atmosphere)[Recipes](/home-garden/recipes)[Home & Garden](/home-garden)[Travel & Outdoors](/travel)[Pets & Animals](/pets)[Health](/health)[Allergy Tracker](/forecast/allergy/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Air Quality Index](/forecast/air-quality/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cold & Flu Tracker](/forecast/cold-flu/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Skin Health](/health/skin-health)\\n\\n[Cloudy 92°](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Yuexiu District, Guangdong, People's Republic of China](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nFavorite Favorite Save Location\\n\\n[\\n\\nToday\\n\\n](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\nHourly\\n\\n](/weather/hourbyhour/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\n10 Day\\n\\n](/weather/tenday/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\nWeekend\\n\\n](/weather/weekend/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\nMonthly\\n\\n](/weather/monthly/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\nRadar\\n\\n](/weather/radar/interactive/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\nVideo Video\\n\\n](/science/space/video/buy-a-massive-rock-from-planet-mars?adRef=nav)\\n\\nMore ForecastsMoreArrow down\\n\\n### Specialty Forecasts\\n\\n[Yesterday's WeatherExternal Link](//www.wunderground.com/history/daily/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08/yesterday) [Allergy Tracker](/forecast/allergy/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cold & Flu](/forecast/cold-flu/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Air Quality Forecast](/forecast/air-quality/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\n# Oops! We are having trouble finding your page.\\n\\n## Please try again... or Check out some of our favorite areas of the site.\\n\\n[![Video](https://s.w-x.co/404-video.jpg)\\n\\n### Video\\n\\nWatch today's top stories and most popular videos. Looking to watch incredible tornadoes? We've got that too.\\n\\n\\n\\n](/video)\\n\\n[![Maps](https://s.w-x.co/404-maps.jpg)\\n\\n### Maps\\n\\nCheck out our interactive maps, which provide you with features like past/future radar and customizable layers.\\n\\n\\n\\n](/weather/radar/interactive/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\n[![Local Weather](https://s.w-x.co/404-local.jpg)\\n\\n### Local Weather\\n\\nTrust us to help you plan the best day possible, with the most accurate weather forecast available.\\n\\n\\n\\n](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\n[![The Weather Company](/not-found/assets/the-weather-company.resource.9f6305c416ac2bdfe238.svg)](https://www.weathercompany.com)[![The Weather Channel](/not-found/assets/the-weather-channel.resource.6fc09ac144dba97e6b32.svg)](https://weather.com)[![Weather Underground](/not-found/assets/weather-underground.resource.b74e28f7947cb0e3a0e5.svg)](https://www.wunderground.com)[![Storm Radar](https://s.w-x.co/Storm-Radar-App-Icon.png)](https://weather.com/storm-radar)\\n\\n*   [Feedback](//support.weather.com/s/?language=en_US&type=TWC)\\n*   [Mission](/mission)\\n*   [Careers](https://www.weathercompany.com/careers)\\n*   [News Room](https://www.weathercompany.com/newsroom)\\n*   [Advertise With Us](https://www.weathercompany.com/advertising/)\\n*   [TV](https://www.weathergroup.com/brands/the-weather-channel/)\\n*   [Weather Data APIs](https://www.weathercompany.com/weather-data-apis/)\\n\\n*   [Terms of Use](/legal)\\n*   [Privacy Policy](/en-US/twc/privacy-policy)\\n*   [AdChoicesAd Choices](/services/ad-choices)\\n*   [Accessibility Statement](/accessibility-statement)\\n*   [Data Vendors](/data-vendors)\\n\\n[![Georgia Org](/not-found/assets/georgia.resource.50fbe85820c759d46005.svg)](http://www.exploregeorgia.org/weather-and-travel-tips)[![Link to eSSENTIAL Accessibility assistive technology](/not-found/assets/eSSENTIAL.resource.728ebae71f894722ea9c.svg)](https://www.essentialaccessibility.com/the-weather-channel?utm_source=theweatherchannelhomepage&utm_medium=iconlarge&utm_term=eachannelpage&utm_content=header&utm_campaign=theweatherchannel)\\n\\nWe recognize our responsibility to use data and technology for good. We may use or share your data with our data vendors. Take control of your data.\\n\\n*   Cookie Settings\\n*   [Data Rights](/data-rights)\\n\\nThe Weather Channel is the world's most accurate forecaster according to ForecastWatch, [Global and Regional Weather Forecast Accuracy Overview](https://forecastwatch.com/AccuracyOverview2021-2024), 2021-2024, commissioned by The Weather Company.\\n\\nWeather Channel\\n\\n© The Weather Company, LLC 2025\\n\\n0\\n\\n1\\n\\n2\\n\\n3\\n\\n4\\n\\n5\\n\\n6\\n\\n7\\n\\n8\\n\\n9\\n\\n10\\n\\n11\\n\\n12\\n\\n13\\n\\n14\\n\\n15\\n\\n16\\n\\n17\\n\\n18\\n\\n19\\n\\n20\\n\\n21\\n\\n22\\n\\n23\\n\\n24\\n\\n25\\n\\n26\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n1. [TODO] Unknown step\n2. [TODO] Unknown step\n\nFollow this plan. If a step is done, move to the next step."}], "tools": [{"type": "function", "function": {"name": "web_search", "description": "⚠️ SEARCH QUERY LENGTH LIMIT: 7 WORDS MAXIMUM ⚠️\n\nSearch the web for information. For best results:\n1) Use CONCISE queries (3-5 words ideal)\n2) Include only ESSENTIAL keywords, not full questions\n3) For complex topics, use multiple simple searches instead of one long query\n4) Focus on specific terms that will appear on relevant pages", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query - MUST BE CONCISE (maximum 7 words)"}, "count": {"type": "number", "description": "Number of results to return (default: 10)"}}, "required": ["query"]}}}, {"type": "function", "function": {"name": "browser_get_markdown", "description": "[browser] Get the markdown content of the current page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_screenshot", "description": "[browser] Take a screenshot of the current page or a specific area. this tool SHOULD NOT be called unless the user requests an explicit call.", "parameters": {"type": "object", "properties": {"area": {"type": "array", "items": {"type": "number"}, "minItems": 4, "maxItems": 4, "description": "Optional area to capture as [x1, y1, x2, y2]. If not provided, captures the entire viewport."}}}}}, {"type": "function", "function": {"name": "browser_navigate", "description": "[browser] Navigate to a URL", "parameters": {"type": "object", "properties": {"url": {"type": "string"}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_click", "description": "[browser] Click an element on the page, before using the tool, use `browser_get_clickable_elements` to get the index of the element, but not call `browser_get_clickable_elements` multiple times", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to click"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_form_input_fill", "description": "[browser] Fill out an input field, before using the tool, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for input field"}, "index": {"type": "number", "description": "Index of the element to fill"}, "value": {"type": "string", "description": "Value to fill"}}, "required": ["value"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_select", "description": "[browser] Select an element on the page with index, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to select"}, "selector": {"type": "string", "description": "CSS selector for element to select"}, "value": {"type": "string", "description": "Value to select"}}, "required": ["value"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_hover", "description": "[browser] Hover an element on the page, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to hover"}, "selector": {"type": "string", "description": "CSS selector for element to hover"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_evaluate", "description": "[browser] Execute JavaScript in the browser console", "parameters": {"type": "object", "properties": {"script": {"type": "string", "description": "JavaScript code to execute"}}, "required": ["script"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_get_clickable_elements", "description": "[browser] Get the clickable or hoverable or selectable elements on the current page, don't call this tool multiple times", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_read_links", "description": "[browser] Get all links on the current page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_scroll", "description": "[browser] Scroll the page", "parameters": {"type": "object", "properties": {"amount": {"type": "number", "description": "Pixels to scroll (positive for down, negative for up), if the amount is not provided, scroll to the bottom of the page"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_go_back", "description": "[browser] Go back to the previous page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_go_forward", "description": "[browser] Go forward to the next page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_tab_list", "description": "[browser] Get the list of tabs", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_new_tab", "description": "[browser] Open a new tab", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to open in the new tab"}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_close_tab", "description": "[browser] Close the current tab", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_switch_tab", "description": "[browser] Switch to a specific tab", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Tab index to switch to"}}, "required": ["index"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_press_key", "description": "[browser] Press a key on the keyboard", "parameters": {"type": "object", "properties": {"key": {"type": "string", "enum": ["Enter", "Tab", "Escape", "Backspace", "Delete", "Insert", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12", "ArrowLeft", "ArrowRight", "ArrowUp", "ArrowDown", "PageUp", "PageDown", "Home", "End", "ShiftLeft", "ShiftRight", "ControlLeft", "ControlRight", "AltLeft", "AltRight", "MetaLeft", "MetaRight", "CapsLock", "PrintScreen", "ScrollLock", "Pause", "ContextMenu"], "description": "Name of the key to press or a character to generate, such as <PERSON><PERSON>, Tab, Escape, Backspace, Delete, Insert, F1, F2, F3, F4, F5, F6, F7, F8, F9, F10, F11, F12, ArrowLeft, ArrowRight, ArrowUp, ArrowDown, PageUp, PageDown, Home, End, ShiftLeft, ShiftRight, ControlLeft, ControlRight, AltLeft, AltRight, MetaLeft, MetaRight, CapsLock, PrintScreen, ScrollLock, Pause, ContextMenu"}}, "required": ["key"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "read_file", "description": "[filesystem] Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "read_multiple_files", "description": "[filesystem] Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file's content is returned with its path as a reference. Failed reads for individual files won't stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "write_file", "description": "[filesystem] Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "edit_file", "description": "[filesystem] Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "create_directory", "description": "[filesystem] Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "list_directory", "description": "[filesystem] Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "directory_tree", "description": "[filesystem] Get a recursive tree view of files and directories as a JSON structure. Each entry includes 'name', 'type' (file/directory), and 'children' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "move_file", "description": "[filesystem] Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "search_files", "description": "[filesystem] Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don't know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "get_file_info", "description": "[filesystem] Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "list_allowed_directories", "description": "[filesystem] Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "run_command", "description": "[commands] Run a command on this darwin machine", "parameters": {"type": "object", "properties": {"command": {"type": "string", "description": "Command with args"}, "cwd": {"type": "string", "description": "Current working directory, leave empty in most cases"}}, "required": ["command"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "run_script", "description": "[commands] Run a script on this darwin machine", "parameters": {"type": "object", "properties": {"interpreter": {"type": "string", "description": "Command with arguments. <PERSON><PERSON><PERSON> will be piped to stdin. Examples: bash, fish, zsh, python, or: bash --norc"}, "script": {"type": "string", "description": "<PERSON><PERSON><PERSON> to run"}, "cwd": {"type": "string", "description": "Current working directory, leave empty in most cases"}}, "required": ["script"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "final_answer", "description": "Generate a focused report or answer after completing research or information gathering", "parameters": {"type": "object", "properties": {"isDeepResearch": {"type": "boolean", "description": "Whether to generate a structured report (true) or simple answer (false)"}, "title": {"type": "string", "description": "Title for the report or answer"}, "format": {"type": "string", "enum": ["detailed", "concise"], "description": "Report format: detailed or concise"}}}}}], "temperature": 0.7, "stream": true, "max_tokens": 8192, "provider": "deepseek", "thinking": {"type": "enabled"}}, "baseURL": "https://api.deepseek.com/v1"}