{"provider": "deepseek", "request": {"model": "deepseek-chat", "messages": [{"role": "system", "content": "\nYou are Agent TARS, a multimodal AI agent created by the ByteDance.\n\n<intro>\nYou excel at the following tasks:\n1. Information gathering, fact-checking, and documentation\n2. Data processing, analysis, and visualization\n3. Writing multi-chapter articles and in-depth research reports\n4. Creating websites, applications, and tools\n5. Using programming to solve various problems beyond development\n6. Various tasks that can be accomplished using computers and the internet\n</intro>\n\n<language_settings>\nUse the language specified by user in messages as the working language when explicitly provided\nAll thinking and responses must be in the working language\nNatural language arguments in tool calls must be in the working language\nAvoid using pure lists and bullet points format in any language\n</language_settings>\n\n<multimodal_understanding>\nWhen processing images, it's crucial to understand the difference between image types:\n1. Browser Screenshots: These are images showing the browser interface that you can interact with using browser tools\n   - Appear as part of the browser_vision_control tool output or environment input labeled as \"Browser Screenshot\"\n   - ONLY these screenshots represent interfaces you can operate on with browser tools\n   - Use these for navigation, clicking elements, scrolling, and other browser interactions\n\n2. User-Uploaded Images: These are regular images the user has shared but are NOT browser interfaces\n   - May include photos, diagrams, charts, documents, or any other visual content\n   - Cannot be operated on with browser tools - don't try to click elements in these images\n   - Should be analyzed for information only (objects, text, context, meaning)\n   - Respond to user questions about these images with observations and analysis\n\nDistinguish between these types by context and environment input descriptions to avoid confusion.\nWhen you see a new image, first determine which type it is before deciding how to interact with it.\n</multimodal_understanding>\n\n<system_capability>\nSystem capabilities:\n- Communicate with users through message tools\n- Access a Linux sandbox environment with internet connection\n- Use shell, text editor, browser, and other software\n- Write and run code in Python and various programming languages\n- Independently install required software packages and dependencies via shell\n- Deploy websites or applications and provide public access\n- Suggest users to temporarily take control of the browser for sensitive operations when necessary\n- Utilize various tools to complete user-assigned tasks step by step\n</system_capability>\n\n<agent_loop>\nYou operate in an agent loop, iteratively completing tasks through these steps:\n1. Analyze Events: Understand user needs and current state through event stream, focusing on latest user messages and execution results\n2. Select Tools: Choose next tool call based on current state, task planning, relevant knowledge and available data APIs\n3. Wait for Execution: Selected tool action will be executed by sandbox environment with new observations added to event stream\n4. Iterate: Choose only one tool call per iteration, patiently repeat above steps until task completion\n5. Submit Results: Send results to user via message tools, providing deliverables and related files as message attachments\n6. Enter Standby: Enter idle state when all tasks are completed or user explicitly requests to stop, and wait for new tasks\n</agent_loop>\n\n<file_rules>\n- Use file tools for reading, writing, appending, and editing to avoid string escape issues in shell commands\n- Actively save intermediate results and store different types of reference information in separate files\n- When merging text files, must use append mode of file writing tool to concatenate content to target file\n- Strictly follow requirements in <writing_rules>, and avoid using list formats in any files except todo.md\n</file_rules>\n\n<shell_rules>\n- Avoid commands requiring confirmation; actively use -y or -f flags for automatic confirmation\n- Avoid commands with excessive output; save to files when necessary\n- Chain multiple commands with && operator to minimize interruptions\n- Use pipe operator to pass command outputs, simplifying operations\n- Use non-interactive `bc` for simple calculations, Python for complex math; never calculate mentally\n- Use `uptime` command when users explicitly request sandbox status check or wake-up\n</shell_rules>\n\n\n\n\n<planning_approach>\nYou are a methodical agent that follows a plan-and-solve approach for complex tasks. When handling tasks:\n\n1. Analyze if the task requires a multi-step plan:\n   - For complex research, analysis, or multi-part tasks → Create a plan\n   - For simple questions or tasks → Skip planning and answer directly\n\n2. If a plan is needed:\n   - Create a clear, step-by-step plan with specific goals\n   - Execute each step in order, using appropriate tools\n   - Update the plan as you learn new information\n   - Mark steps as completed when done\n   - Once ALL steps are complete, call the \"final_answer\" tool\n\n3. During execution:\n   - Adapt your plan as needed based on new findings\n   - Be willing to simplify the plan if the task turns out simpler than expected\n   - Always complete your plan before providing final answers\n</planning_approach>\n\n<planning_constraints>\nIMPORTANT CONSTRAINTS:\n- Create AT MOST 3 key steps in your plan\n- Focus on information gathering and research steps\n- Call the \"final_answer\" tool once ALL plan steps are complete\n- For simple questions, you can skip planning entirely\n</planning_constraints>\n \n\n \n<browser_rules>\nYou have access to various browser tools to interact with web pages and extract information.\n\nYou have DOM-based browser control tools that work directly with the page structure:\n\n- Navigation: `browser_navigate`, `browser_back`, `browser_forward`, `browser_refresh`\n- Interaction: `browser_click`, `browser_type`, `browser_press`, `browser_hover`, `browser_drag`, `browser_scroll`\n- Content extraction: `browser_get_markdown`\n- Status checking: `browser_get_url`, `browser_get_title`, `browser_get_elements`\n- Tab management: `browser_tab_list`, `browser_new_tab`, `browser_close_tab`, `browser_switch_tab`\n\nUSAGE GUIDELINES:\n- Use CSS selectors or element indices to precisely target elements\n- Extract content with `browser_get_markdown` for efficient analysis\n- Find and verify elements with `browser_get_elements` before interacting\n- Leverage browser state tools to keep track of navigation\n\n- Must use browser tools to access and comprehend all URLs provided by users in messages\n- Must use browser tools to access URLs from search tool results\n- Actively explore valuable links for deeper information, either by clicking elements or accessing URLs directly\n- Browser tools only return elements in visible viewport by default\n- Due to technical limitations, not all interactive elements may be identified; use coordinates to interact with unlisted elements\n- Browser tools automatically attempt to extract page content, providing it in Markdown format if successful\n- Extracted Markdown includes text beyond viewport but omits links and images; completeness not guaranteed\n- If extracted Markdown is complete and sufficient for the task, no scrolling is needed; otherwise, must actively scroll to view the entire page\n- Use message tools to suggest user to take over the browser for sensitive operations or actions with side effects when necessary\n</browser_rules>\n\n<envirnoment>\nCurrent Working Directory: /Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Documents/GitHub\n</envirnoment>\n\n    \n\nCurrent time: 5/20/2025, 10:00:00 AM"}, {"role": "user", "content": "请查询如果下周去北京旅游的天气情况，从深圳出发"}, {"role": "system", "content": "Current plan status:\n1. [TODO] Check the weather forecast for Beijing for the upcoming week to determine the best time to visit.\n2. [TODO] Research flight options from Shenzhen to Beijing, including prices and schedules, to plan the trip accordingly.\n3. [TODO] Look for any travel advisories or requirements for visiting Beijing, such as visa requirements or COVID-19 restrictions, to ensure a smooth trip.\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_b81d18f9-e162-4258-aba3-97657ac3bba7", "type": "function", "function": {"name": "web_search", "arguments": "{\"query\":\"北京下周天气预报\",\"count\":1}"}}]}, {"role": "tool", "tool_call_id": "call_0_b81d18f9-e162-4258-aba3-97657ac3bba7", "content": "{\n  \"error\": \"Search failed: Waiting for selector `#search` failed: Waiting failed: 30000ms exceeded\"\n}"}, {"role": "system", "content": "Current plan status:\n1. [DONE] Check the weather forecast for Beijing for the upcoming week to determine the best time to visit.\n2. [TODO] Research flight options from Shenzhen to Beijing, including prices and schedules, to plan the trip accordingly.\n3. [TODO] Look for any travel advisories or requirements for visiting Beijing, such as visa requirements or COVID-19 restrictions, to ensure a smooth trip.\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_e8dad0f8-714d-4b62-9fe7-1f0dbf307ecb", "type": "function", "function": {"name": "web_search", "arguments": "{\"query\":\"北京下周天气\",\"count\":1}"}}]}, {"role": "tool", "tool_call_id": "call_0_e8dad0f8-714d-4b62-9fe7-1f0dbf307ecb", "content": "{\n  \"error\": \"Search failed: Waiting for selector `#search` failed: Waiting failed: 30000ms exceeded\"\n}"}, {"role": "system", "content": "Current plan status:\n1. [DONE] Check the weather forecast for Beijing for the upcoming week to determine the best time to visit.\n2. [DONE] Research flight options from Shenzhen to Beijing, including prices and schedules, to plan the trip accordingly.\n3. [DONE] Look for any travel advisories or requirements for visiting Beijing, such as visa requirements or COVID-19 restrictions, to ensure a smooth trip.\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_305e2a17-c8b2-450e-977b-bfd68152e787", "type": "function", "function": {"name": "browser_navigate", "arguments": "{\"url\":\"https://www.weather.com/weather/tenday/l/Beijing+China\"}"}}]}, {"role": "tool", "tool_call_id": "call_0_305e2a17-c8b2-450e-977b-bfd68152e787", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \"Navigated to https://www.weather.com/weather/tenday/l/Beijing+China\\nclickable elements(Might be outdated, if an error occurs with the index element, use browser_get_clickable_elements to refresh it): [0]<a></a>\\n[]Type at least three characters to start auto complete. Recently searched locations will be displayed if there is no search query. The first option will be automatically selected. Use up and down arrows to change selection. Use escape to clear.\\n[]Search City or Zip Code\\n[1]<input></input>\\n[2]<button>US\\n°F</button>\\n[3]<button>Menu</button>\\n[4]<a></a>\\n[5]<a></a>\\n[6]<a></a>\\n[7]<a></a>\\n[8]<a></a>\\n[9]<a></a>\\n[10]<a></a>\\n[11]<a></a>\\n[12]<button></button>\\n[]Oops! We are having trouble finding your page.\\n[]Please try again... or Check out some of our favorite areas of the site.\\n[13]<a>Video\\nWatch today's top stories and most popular videos. Looking to watch incredible tornadoes? We've got that too.</a>\\n[14]<img></img>\\n[15]<a>Maps\\nCheck out our interactive maps, which provide you with features like past/future radar and customizable layers.</a>\\n[16]<img></img>\\n[17]<a>Local Weather\\nTrust us to help you plan the best day possible, with the most accurate weather forecast available.</a>\\n[18]<img></img>\\n[19]<a></a>\\n[20]<img></img>\\n[21]<a></a>\\n[22]<img></img>\\n[23]<a></a>\\n[24]<img></img>\\n[25]<a></a>\\n[26]<img></img>\\n[]Feedback\\n[]Mission\\n[]Careers\\n[]News Room\\n[]Advertise With Us\\n[]TV\\n[]Weather Data APIs\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_2fefb353-57a4-40c4-a467-e58859e7d397", "type": "function", "function": {"name": "browser_get_markdown", "arguments": "{}"}}]}, {"role": "tool", "tool_call_id": "call_0_2fefb353-57a4-40c4-a467-e58859e7d397", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \" 404 Not Found          \\n\\n[Skip to Main Content](#MainContent)[Accessibility Help](https://www.essentialaccessibility.com/the-weather-channel?utm_source=theweatherchannelhomepage&utm_medium=iconlarge&utm_term=eachannelpage&utm_content=header&utm_campaign=theweatherchannel) [The Weather Channel](/)\\n\\nType at least three characters to start auto complete. Recently searched locations will be displayed if there is no search query. The first option will be automatically selected. Use up and down arrows to change selection. Use escape to clear.Search City or Zip Code\\n\\n## recents\\n\\nClear All\\n\\nYou have no recent locations\\n\\nGlobe US°FArrow down\\n\\n*   °F\\n*   °C\\n*   Hybrid\\n\\nImperial - F / mph / miles / inches\\n\\nAmericas Add\\n\\n[Antigua and Barbuda | English](/en-AG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Argentina | Español](/es-AR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Bahamas | English](/en-BS/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Barbados | English](/en-BB/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Belize | English](/en-BZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Bolivia | Español](/es-BO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Brazil | Português](/pt-BR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Canada | English](/en-CA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Canada | Français](/fr-CA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Chile | Español](/es-CL/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Colombia | Español](/es-CO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Costa Rica | Español](/es-CR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Dominica | English](/en-DM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Dominican Republic | Español](/es-DO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Ecuador | Español](/es-EC/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[El Salvador | Español](/es-SV/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Grenada | English](/en-GD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Guatemala | Español](/es-GT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Guyana | English](/en-GY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Haiti | Français](/fr-HT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Honduras | Español](/es-HN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Jamaica | English](/en-JM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mexico | Español](/es-MX/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Nicaragua | Español](/es-NI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Panama | Español](/es-PA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Panama | English](/en-PA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Paraguay | Español](/es-PY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Peru | Español](/es-PE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[St. Kitts and Nevis | English](/en-KN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[St. Lucia | English](/en-LC/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[St. Vincent and the Grenadines | English](/en-VC/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Suriname | Nederlands](/nl-SR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Trinidad and Tobago | English](/en-TT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Uruguay | Español](/es-UY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[United States | English](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[United States | Español](/es-US/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Venezuela | Español](/es-VE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nAfrica Add\\n\\n[Algeria | العربية](/ar-DZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Algeria | Français](/fr-DZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Angola | Português](/pt-AO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Benin | Français](/fr-BJ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Burkina Faso | Français](/fr-BF/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Burundi | Français](/fr-BI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cameroon | Français](/fr-CM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cameroon | English](/en-CM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cape Verde | Português](/pt-CV/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Central African Republic | Français](/fr-CF/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Chad | Français](/fr-TD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Chad | العربية](/ar-TD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Comoros | Français](/fr-KM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Comoros | العربية](/ar-KM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Democratic Republic of the Congo | Français](/fr-CD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Republic of Congo | Français](/fr-CG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Côte d'Ivoire | Français](/fr-CI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Djibouti | Français](/fr-DJ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Djibouti | العربية](/ar-DJ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Egypt | العربية](/ar-EG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Equatorial Guinea | Español](/es-GQ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Eritrea | العربية](/ar-ER/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Gabon | Français](/fr-GA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Gambia | English](/en-GM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Ghana | English](/en-GH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Guinea | Français](/fr-GN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Guinea-Bissau | Português](/pt-GW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Kenya | English](/en-KE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Lesotho | English](/en-LS/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Liberia | English](/en-LR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Libya | العربية](/ar-LY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Madagascar | Français](/fr-MG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mali | Français](/fr-ML/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mauritania | العربية](/ar-MR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mauritius | English](/en-MU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mauritius | Français](/fr-MU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Morocco | العربية](/ar-MA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Morocco | Français](/fr-MA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Mozambique | Português](/pt-MZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Namibia | English](/en-NA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Niger | Français](/fr-NE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Nigeria | English](/en-NG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Rwanda | Français](/fr-RW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Rwanda | English](/en-RW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Sao Tome and Principe | Português](/pt-ST/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Senegal | Français](/fr-SN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Sierra Leone | English](/en-SL/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Somalia | العربية](/ar-SO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[South Africa | English](/en-ZA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[South Sudan | English](/en-SS/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Sudan | العربية](/ar-SD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Swaziland | English](/en-SZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Tanzania | English](/en-TZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Togo | Français](/fr-TG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Tunisia | العربية](/ar-TN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Uganda | English](/en-UG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nAsia Pacific Add\\n\\n[Australia | English](/en-AU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Bangladesh | বাংলা](/bn-BD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Brunei | Bahasa Melayu](/ms-BN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[China | 中文](/zh-CN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Hong Kong SAR | 中文](/zh-HK/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[East Timor | Português](/pt-TP/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Fiji | English](/en-FJ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[India (English) | English](/en-IN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[India (Hindi) | हिन्दी](/hi-IN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Indonesia | Bahasa Indonesia](/id-ID/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Japan | 日本語](/ja-JP/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Kiribati | English](/en-KI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[South Korea | 한국어](/ko-KR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Kyrgyzstan | Русский](/ru-KG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Malaysia | Bahasa Melayu](/ms-MY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Marshall Islands | English](/en-MH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Micronesia | English](/en-FM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[New Zealand | English](/en-NZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Palau | English](/en-PW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Philippines | English](/en-PH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Philippines | Tagalog](/tl-PH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Samoa | English](/en-AS/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Singapore | English](/en-SG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Singapore | 中文](/zh-SG/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Solomon Islands | English](/en-SB/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Taiwan | 中文](/zh-TW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Thailand | ไทย](/th-TH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Tonga | English](/en-TO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Tuvalu | English](/en-TV/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Vanuatu | English](/en-VU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Vanuatu | Français](/fr-VU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Vietnam | Tiếng Việt](/vi-VN/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nEurope Add\\n\\n[Andorra | Català](/ca-AD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Andorra | Français](/fr-AD/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Austria | Deutsch](/de-AT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Belarus | Русский](/ru-BY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Belgium | Dutch](/nl-BE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Belgium | Français](/fr-BE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Bosnia and Herzegovina | Hrvatski](/hr-BA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Croatia | Hrvatski](/hr-HR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cyprus | Ελληνικά](/el-CY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Czech Republic | Čeština](/cs-CZ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Denmark | Dansk](/da-DK/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Estonia | Русский](/ru-EE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Estonia | Eesti](/et-EE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Finland | Suomi](/fi-FI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[France | Français](/fr-FR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Germany | Deutsch](/de-DE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Greece | Ελληνικά](/el-GR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Hungary | Magyar](/hu-HU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Ireland | English](/en-IE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Italy | Italiano](/it-IT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Liechtenstein | Deutsch](/de-LI/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Luxembourg | Français](/fr-LU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Malta | English](/en-MT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Monaco | Français](/fr-MC/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Netherlands | Nederlands](/nl-NL/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Norway | Norsk](/no-NO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Poland | Polski](/pl-PL/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Portugal | Português](/pt-PT/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Romania | Română](/ro-RO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Russia | Русский](/ru-RU/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[San Marino | Italiano](/it-SM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Slovakia | Slovenčina](/sk-SK/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Spain | Español](/es-ES/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Spain | Català](/ca-ES/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Sweden | Svenska](/sv-SE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Switzerland | Deutsch](/de-CH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Turkey | Turkçe](/tr-TR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Ukraine | Українська](/uk-UA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[United Kingdom | English](/en-GB/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[State of Vatican City (Holy See) | Italiano](/it-VA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nMiddle East Add\\n\\n[Bahrain | العربية](/ar-BH/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Iran | فارسى](/fa-IR/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Iraq | العربية](/ar-IQ/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Israel | עִבְרִית](/he-IL/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Jordan | العربية](/ar-JO/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Kuwait | العربية](/ar-KW/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Lebanon | العربية](/ar-LB/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Oman | العربية](/ar-OM/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Pakistan | اردو](/ur-PK/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Pakistan | English](/en-PK/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Qatar | العربية](/ar-QA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Saudi Arabia | العربية](/ar-SA/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Syria | العربية](/ar-SY/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[United Arab Emirates | العربية](/ar-AE/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nMenu\\n\\n## Weather Forecasts\\n\\n[Today](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Hourly](/weather/hourbyhour/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[10 Day](/weather/tenday/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\n## Radar & Maps\\n\\n[Interactive Radar Map](/weather/radar/interactive/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08?animation=true)[US Forecast](/maps/currentusweather)[US Satellite & Radar](/maps/ussatellitemap)[World Satellite](/maps/satellite/caribbeanweathermap)[US Severe Alerts](/maps/severealerts)[US Health](/maps/health/allergies/treepollen)\\n\\n## News & Media\\n\\n[Top Weather Stories](/science/space/video/buy-a-massive-rock-from-planet-mars)[Hurricane Central](/storms/hurricane-central)[Tornado CentralNEW](/storms/tornado-central)[Safety & Prep](/safety/index)[Space & Skywatching](/science/space)[Videos](/science/space/video/buy-a-massive-rock-from-planet-mars)\\n\\n## Products & Account\\n\\n[Alexa SkillExternal Link](https://www.amazon.com/The-Weather-Company-Channel/dp/B07YPYHQ1Q) [Create an Account](/signup)[Sign in](/login)\\n\\n## Lifestyle\\n\\n[Atmosphere Reviews](/atmosphere)[Recipes](/home-garden/recipes)[Home & Garden](/home-garden)[Travel & Outdoors](/travel)[Pets & Animals](/pets)[Health](/health)[Allergy Tracker](/forecast/allergy/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Air Quality Index](/forecast/air-quality/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cold & Flu Tracker](/forecast/cold-flu/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Skin Health](/health/skin-health)\\n\\n[Cloudy 92°](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Yuexiu District, Guangdong, People's Republic of China](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\nFavorite Favorite Save Location\\n\\n[\\n\\nToday\\n\\n](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\nHourly\\n\\n](/weather/hourbyhour/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\n10 Day\\n\\n](/weather/tenday/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\nWeekend\\n\\n](/weather/weekend/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\nMonthly\\n\\n](/weather/monthly/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\nRadar\\n\\n](/weather/radar/interactive/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[\\n\\nVideo Video\\n\\n](/science/space/video/buy-a-massive-rock-from-planet-mars?adRef=nav)\\n\\nMore ForecastsMoreArrow down\\n\\n### Specialty Forecasts\\n\\n[Yesterday's WeatherExternal Link](//www.wunderground.com/history/daily/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08/yesterday) [Allergy Tracker](/forecast/allergy/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Cold & Flu](/forecast/cold-flu/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)[Air Quality Forecast](/forecast/air-quality/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\n# Oops! We are having trouble finding your page.\\n\\n## Please try again... or Check out some of our favorite areas of the site.\\n\\n[![Video](https://s.w-x.co/404-video.jpg)\\n\\n### Video\\n\\nWatch today's top stories and most popular videos. Looking to watch incredible tornadoes? We've got that too.\\n\\n\\n\\n](/video)\\n\\n[![Maps](https://s.w-x.co/404-maps.jpg)\\n\\n### Maps\\n\\nCheck out our interactive maps, which provide you with features like past/future radar and customizable layers.\\n\\n\\n\\n](/weather/radar/interactive/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\n[![Local Weather](https://s.w-x.co/404-local.jpg)\\n\\n### Local Weather\\n\\nTrust us to help you plan the best day possible, with the most accurate weather forecast available.\\n\\n\\n\\n](/weather/today/l/4939833995e046f1dbd8c6ecfc4b7dcd3abab37263e4c67369530d5923424f08)\\n\\n[![The Weather Company](/not-found/assets/the-weather-company.resource.9f6305c416ac2bdfe238.svg)](https://www.weathercompany.com)[![The Weather Channel](/not-found/assets/the-weather-channel.resource.6fc09ac144dba97e6b32.svg)](https://weather.com)[![Weather Underground](/not-found/assets/weather-underground.resource.b74e28f7947cb0e3a0e5.svg)](https://www.wunderground.com)[![Storm Radar](https://s.w-x.co/Storm-Radar-App-Icon.png)](https://weather.com/storm-radar)\\n\\n*   [Feedback](//support.weather.com/s/?language=en_US&type=TWC)\\n*   [Mission](/mission)\\n*   [Careers](https://www.weathercompany.com/careers)\\n*   [News Room](https://www.weathercompany.com/newsroom)\\n*   [Advertise With Us](https://www.weathercompany.com/advertising/)\\n*   [TV](https://www.weathergroup.com/brands/the-weather-channel/)\\n*   [Weather Data APIs](https://www.weathercompany.com/weather-data-apis/)\\n\\n*   [Terms of Use](/legal)\\n*   [Privacy Policy](/en-US/twc/privacy-policy)\\n*   [AdChoicesAd Choices](/services/ad-choices)\\n*   [Accessibility Statement](/accessibility-statement)\\n*   [Data Vendors](/data-vendors)\\n\\n[![Georgia Org](/not-found/assets/georgia.resource.50fbe85820c759d46005.svg)](http://www.exploregeorgia.org/weather-and-travel-tips)[![Link to eSSENTIAL Accessibility assistive technology](/not-found/assets/eSSENTIAL.resource.728ebae71f894722ea9c.svg)](https://www.essentialaccessibility.com/the-weather-channel?utm_source=theweatherchannelhomepage&utm_medium=iconlarge&utm_term=eachannelpage&utm_content=header&utm_campaign=theweatherchannel)\\n\\nWe recognize our responsibility to use data and technology for good. We may use or share your data with our data vendors. Take control of your data.\\n\\n*   Cookie Settings\\n*   [Data Rights](/data-rights)\\n\\nThe Weather Channel is the world's most accurate forecaster according to ForecastWatch, [Global and Regional Weather Forecast Accuracy Overview](https://forecastwatch.com/AccuracyOverview2021-2024), 2021-2024, commissioned by The Weather Company.\\n\\nWeather Channel\\n\\n© The Weather Company, LLC 2025\\n\\n0\\n\\n1\\n\\n2\\n\\n3\\n\\n4\\n\\n5\\n\\n6\\n\\n7\\n\\n8\\n\\n9\\n\\n10\\n\\n11\\n\\n12\\n\\n13\\n\\n14\\n\\n15\\n\\n16\\n\\n17\\n\\n18\\n\\n19\\n\\n20\\n\\n21\\n\\n22\\n\\n23\\n\\n24\\n\\n25\\n\\n26\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n1. [TODO] Unknown step\n2. [TODO] Unknown step\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_64d74e0b-c86b-48f1-8f6a-adbfc2285a0d", "type": "function", "function": {"name": "browser_navigate", "arguments": "{\"url\":\"https://www.accuweather.com/en/cn/beijing/106571/weather-forecast/106571\"}"}}]}, {"role": "tool", "tool_call_id": "call_0_64d74e0b-c86b-48f1-8f6a-adbfc2285a0d", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \"Navigated to https://www.accuweather.com/en/cn/beijing/106571/weather-forecast/106571\\nclickable elements(Might be outdated, if an error occurs with the index element, use browser_get_clickable_elements to refresh it): [0]<a>For Business</a>\\n[]|\\n[1]<a>Warnings</a>\\n[2]<a>Data Suite</a>\\n[3]<a>Forensics</a>\\n[4]<a>Advertising</a>\\n[5]<a>Superior Accuracy™</a>\\n[6]<a></a>\\n[7]<a>Tai'an, Shandong\\n87°\\nF</a>\\n[8]<span></span>\\n[9]<img></img>\\n[10]<input></input>\\n[11]<div>Location</div>\\n[12]<a></a>\\n[13]<a>Today</a>\\n[14]<a>Hourly</a>\\n[15]<a>Daily</a>\\n[16]<a>Radar</a>\\n[17]<a>MinuteCast</a>\\n[18]<a>Monthly</a>\\n[19]<a>Air Quality</a>\\n[20]<a>Health & Activities</a>\\n[21]<a>Today's Weather\\nTue, Jul 15\\nMostly cloudy and hot; caution advised if doing strenuous activities outside\\nHi: 94°\\nTonight: A heavy thunderstorm around this evening; otherwise, partly cloudy, warm and humid\\nLo: 76°</a>\\n[22]<img></img>\\n[23]<img></img>\\n[24]<a>Current Weather\\n12:04 PM\\n87°\\nF\\nRealFeel Shade™\\n86°\\nWind\\nSSE 10 mph</a>\\n[]Top Stories\\n[25]<a>Weather Forecasts</a>\\n[26]<img></img>\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n1. [TODO] Check the weather forecast for Beijing for next week\n2. [TODO] Check flight availability from Shenzhen to Beijing for next week\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_c347a723-60be-4ccc-99e4-094a8ae2ff6d", "type": "function", "function": {"name": "browser_get_markdown", "arguments": "{}"}}]}, {"role": "tool", "tool_call_id": "call_0_c347a723-60be-4ccc-99e4-094a8ae2ff6d", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \"   Tai'an, Shandong, China Weather Forecast | AccuWeather                                                           \\n\\n[Go Back](/pwa)\\n\\n*   [For Business](https://afb.accuweather.com)\\n*   |\\n*   [Warnings](https://afb.accuweather.com/accuweather-skyguard)\\n*   [Data Suite](https://afb.accuweather.com/accuweather-data-suite)\\n*   [Forensics](https://afb.accuweather.com/accuweather-forensicservices)\\n*   [Advertising](https://advertising.accuweather.com/for-advertising/digital-advertising/)\\n*   [Superior Accuracy™](https://afb.accuweather.com/accuweathers-proven-superior-accuracy)\\n\\n[](/)[# Tai'an, Shandong\\n\\n87°F](/en/cn/taian/106571/weather-forecast/106571) ![](https://vortex.accuweather.com/adc2010/images/branding-partners/china_weather_logo.png)\\n\\nLocation Chevron down\\n\\nLocation News Videos\\n\\nUse Current Location\\n\\nRecent\\n\\n[\\n\\nTai'an\\n\\nShandong\\n\\n87°](/web-api/three-day-redirect?key=106571&target=)\\n\\nNo results found.\\n\\nTry searching for a city, zip code or point of interest.\\n\\n[](/en/videos)\\n\\n[settings](/en/settings)\\n\\nTai'an, Shandong Weather\\n\\n[Today](/en/cn/taian/106571/weather-forecast/106571) [WinterCast](/en/cn/taian/106571/winter-weather-forecast/106571) [Local {stormName} Tracker](/en/cn/taian/106571/tropical-weather-forecast/106571?eventkey={eventkey}) [Hourly](/en/cn/taian/106571/hourly-weather-forecast/106571) [Daily](/en/cn/taian/106571/daily-weather-forecast/106571) [Radar](/en/cn/taian/106571/weather-radar/106571) [MinuteCast](/en/cn/taian/106571/minute-weather-forecast/106571) [Monthly](/en/cn/taian/106571/july-weather/106571) [Air Quality](/en/cn/taian/106571/air-quality-index/106571) [Health & Activities](/en/cn/taian/106571/health-activities/106571)\\n\\n## \\n\\nAround the Globe\\n\\n[\\n\\n### Hurricane Tracker\\n\\n](/en/hurricane)[\\n\\n### Severe Weather\\n\\n](/en/cn/severe-weather)[\\n\\n### Radar & Maps\\n\\n](/en/cn/shandong/weather-radar)\\n\\nNews\\n\\n[\\n\\n### News & Features\\n\\n](/en/weather-news)[\\n\\n### Astronomy\\n\\n](/en/space-news)[\\n\\n### Business\\n\\n](/en/business)[\\n\\n### Climate\\n\\n](/en/climate)[\\n\\n### Health\\n\\n](/en/health-wellness)[\\n\\n### Recreation\\n\\n](/en/leisure-recreation)[\\n\\n### Sports\\n\\n](/en/sports)[\\n\\n### Travel\\n\\n](/en/travel)\\n\\nFor Business\\n\\n[\\n\\n### Warnings\\n\\n](https://afb.accuweather.com/accuweather-skyguard)[\\n\\n### Data Suite\\n\\n](https://afb.accuweather.com/accuweather-data-suite)[\\n\\n### Forensics\\n\\n](https://afb.accuweather.com/accuweather-forensicservices)[\\n\\n### Advertising\\n\\n](https://advertising.accuweather.com/for-advertising/digital-advertising/)[\\n\\n### Superior Accuracy™\\n\\n](https://afb.accuweather.com/accuweathers-proven-superior-accuracy)\\n\\n[\\n\\n### Video\\n\\n](/en/videos)\\n\\n[\\n\\n## Today\\n\\n](/en/cn/taian/106571/weather-forecast/106571)[Hourly](/en/cn/taian/106571/hourly-weather-forecast/106571) [Daily](/en/cn/taian/106571/daily-weather-forecast/106571) [Radar](/en/cn/taian/106571/weather-radar/106571) [MinuteCast](/en/cn/taian/106571/minute-weather-forecast/106571) [Monthly](/en/cn/taian/106571/july-weather/106571) [Air Quality](/en/cn/taian/106571/air-quality-index/106571) [Health & Activities](/en/cn/taian/106571/health-activities/106571)\\n\\n[\\n\\n## Today's Weather\\n\\nTue, Jul 15\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/6.svg)\\n\\nMostly cloudy and hot; caution advised if doing strenuous activities outside **Hi: 94°**\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/41.svg)\\n\\nTonight: A heavy thunderstorm around this evening; otherwise, partly cloudy, warm and humid **Lo: 76°**\\n\\n\\n\\n\\n\\n](/en/cn/taian/106571/weather-today/106571)\\n\\n[\\n\\n## Current Weather\\n\\n12:04 PM\\n\\n87°F\\n\\nRealFeel® 89°\\n\\nCloudy More Details\\n\\nRealFeel Shade™ 86°\\n\\nWind SSE 10 mph\\n\\nWind Gusts 23 mph\\n\\nAir Quality Poor\\n\\n\\n\\n\\n\\n](/en/cn/taian/106571/current-weather/106571)[\\n\\n### MINUTECAST™\\n\\nRain starting in 55 min\\n\\nNow\\n\\n1:04 PM\\n\\n\\n\\n](/en/cn/taian/106571/minute-weather-forecast/106571)[\\n\\n## Looking Ahead\\n\\nWatch for a heavy thunderstorm Tuesday evening\\n\\n](/en/cn/taian/106571/weather-today/106571)\\n\\n## Tai'an Weather Radar\\n\\n[![Tai'an Weather Radar](data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAJAQMAAAAB5D5xAAAAA1BMVEUAAACnej3aAAAAAXRSTlMAQObYZgAAAApJREFUCNdjwA0AABsAAQrj5HwAAAAASUVORK5CYII= \\\"Tai'an Weather Radar\\\")\\n\\nStatic Radar Temporarily Unavailable\\n\\nThank you for your patience as we work to get everything up and running again.\\n\\nRefresh Page\\n\\n\\n\\n\\n\\n](/en/cn/taian/106571/weather-radar/106571)\\n\\n [![](https://www.awxcdn.com/adc-assets/images/components/maps/mapping-layers/realvue-satellite.jpg) Clouds](/en/cn/taian/106571/satellite/106571) [![](https://www.awxcdn.com/adc-assets/images/components/maps/mapping-layers/temperature.jpg) Temperature](/en/cn/shandong/current-weather-maps)\\n\\n## Hourly Weather\\n\\nChevron left\\n\\n[1 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/7.svg) 90°\\n\\n 5%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752555600)[2 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/6.svg) 92°\\n\\n 5%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752559200)[3 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/6.svg) 93°\\n\\n 6%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752562800)[4 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/6.svg) 94°\\n\\n 7%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752566400)[5 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/6.svg) 93°\\n\\n 7%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752570000)[6 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/4.svg) 91°\\n\\n 11%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752573600)[7 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/4.svg) 89°\\n\\n 20%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752577200)[8 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/35.svg) 87°\\n\\n 20%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752580800)[9 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/36.svg) 85°\\n\\n 25%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752584400)[10 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/36.svg) 84°\\n\\n 49%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752588000)[11 PM ![](https://www.awxcdn.com/adc-assets/images/weathericons/36.svg) 83°\\n\\n 49%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752591600)[12 AM ![](https://www.awxcdn.com/adc-assets/images/weathericons/42.svg) 82°\\n\\n 61%\\n\\n](/en/cn/taian/106571/hourly-weather-forecast/106571?hour=1752595200&day=2)\\n\\nChevron right\\n\\n## 10-Day Weather Forecast\\n\\n[\\n\\nToday\\n\\n7/15\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/6.svg)\\n\\n94° 76°\\n\\nMostly cloudy and hot\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/41.svg)\\n\\nNight: A heavy t-storm this evening\\n\\n25%\\n\\n](/en/cn/taian/106571/weather-today/106571)[\\n\\nWed\\n\\n7/16\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/5.svg)\\n\\n94° 72°\\n\\nHazy sun, hot and humid\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/33.svg)\\n\\nClear\\n\\n4%\\n\\n](/en/cn/taian/106571/weather-tomorrow/106571)[\\n\\nThu\\n\\n7/17\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/5.svg)\\n\\n93° 70°\\n\\nHazy, hot and less humid\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/34.svg)\\n\\nClear to partly cloudy\\n\\n1%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=3)[\\n\\nFri\\n\\n7/18\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/30.svg)\\n\\n97° 72°\\n\\nVery hot with hazy sunshine\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/33.svg)\\n\\nClear\\n\\n1%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=4)[\\n\\nSat\\n\\n7/19\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/2.svg)\\n\\n96° 76°\\n\\nMostly sunny and hot\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/38.svg)\\n\\nBecoming cloudy; humid\\n\\n1%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=5)[\\n\\nSun\\n\\n7/20\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/12.svg)\\n\\n78° 74°\\n\\nSome rain and a thunderstorm\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/15.svg)\\n\\nStrong thunderstorms; humid\\n\\n68%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=6)[\\n\\nMon\\n\\n7/21\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/15.svg)\\n\\n83° 78°\\n\\nHumid; a p.m. t-storm or two\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/15.svg)\\n\\nOvercast, a t-storm or two\\n\\n75%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=7)[\\n\\nTue\\n\\n7/22\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/15.svg)\\n\\n84° 77°\\n\\nAn afternoon t-storm or two\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/7.svg)\\n\\nAn evening thunderstorm\\n\\n76%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=8)[\\n\\nWed\\n\\n7/23\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/18.svg)\\n\\n88° 76°\\n\\nOccasional rain and a t-storm\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/12.svg)\\n\\nA bit of late-night rain\\n\\n55%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=9)[\\n\\nThu\\n\\n7/24\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/18.svg)\\n\\n87° 77°\\n\\nHumid with periods of rain\\n\\n![](https://www.awxcdn.com/adc-assets/images/weathericons/35.svg)\\n\\nPartly cloudy, warm and humid\\n\\n65%\\n\\n](/en/cn/taian/106571/daily-weather-forecast/106571?day=10)\\n\\n## Sun & Moon\\n\\n![](/images/whiteweathericons/1.svg) 14 hrs 24 mins\\n\\nRise 5:05 AM\\n\\nSet 7:29 PM\\n\\n![](/images/moon-phases/light/WaningGibbous.svg) Waning Gibbous\\n\\nRise 10:16 PM\\n\\nSet 10:29 AM\\n\\n[\\n\\n## Air Quality\\n\\nSee More\\n\\nAir Quality Poor\\n\\nThe air has reached a high level of pollution and is unhealthy for sensitive groups. Reduce time spent outside if you are feeling symptoms such as difficulty breathing or throat irritation.\\n\\n\\n\\n](/en/cn/taian/106571/air-quality-index/106571)\\n\\n## Allergy Outlook\\n\\n[See All](/en/cn/taian/106571/health-activities/106571)\\n\\n[Dust & Dander Extreme Data is not supported in this location](/en/cn/taian/106571/allergies-weather/106571?name=dust-dander)\\n\\nTop Stories\\n\\n[\\n\\nWeather Forecasts\\n\\nFlood-weary Texas finally drying out, but near 100-degree heat looms\\n\\n8 hours ago\\n\\n![](https://cms.accuweather.com/wp-content/uploads/2025/07/CentralPattern.jpeg?w=64&h=64&crop=1)](https://www.accuweather.com/en/weather-forecasts/flood-weary-texas-finally-drying-out-but-near-100-degree-heat-looms/1794693)[\\n\\nWeather News\\n\\nThunderstorms bring more flooding, evacuations to central Texas\\n\\n9 hours ago\\n\\n](https://www.accuweather.com/en/weather-news/slow-moving-thunderstorms-bring-more-flooding-and-some-evacuations-to-central-texas/1794648)[\\n\\nHurricane\\n\\nTropical Rainstorm to drench Florida, Gulf Coast\\n\\n3 hours ago\\n\\n](https://www.accuweather.com/en/hurricane/tropical-rainstorm-soaks-florida-could-strengthen-in-gulf-before-targeting-louisiana/1794844)[\\n\\nWeather News\\n\\nGrand Canyon fires force closures\\n\\n9 hours ago\\n\\n](https://www.accuweather.com/en/weather-news/grand-canyon-lodge-rim-wildfire/1794665)[Severe Weather\\n\\nWildfire smoke may follow storms, severe weather in eastern US\\n\\n8 hours ago](https://www.accuweather.com/en/severe-weather/wildfire-smoke-may-follow-storms-severe-weather-in-eastern-us/1793854) [More Stories](https://www.accuweather.com/en/weather-news)\\n\\nFeatured Stories\\n\\n[\\n\\nAstronomy\\n\\nNoctilucent clouds shimmer in summer sky\\n\\n9 hours ago\\n\\n](https://www.accuweather.com/en/space-news/noctilucent-clouds-shimmer-in-summer-sky-with-a-possible-boost-from-rockets/1794926)[\\n\\nRecreation\\n\\nYellowstone’s landscape shifts again with discovery of new hot pool\\n\\n11 hours ago\\n\\n](https://www.accuweather.com/en/leisure-recreation/yellowstone-national-park-new-thermal-pool/1794749)[\\n\\nWeather News\\n\\nOrcas are bringing humans gifts of food – but why?\\n\\n3 days ago\\n\\n](https://www.accuweather.com/en/weather-news/orcas-are-bringing-humans-gifts-of-food-but-why/1793505)[\\n\\nWeather News\\n\\nGiant shoes found spark mystery around the soldiers of ancient Rome\\n\\n9 hours ago\\n\\n](https://www.accuweather.com/en/weather-news/giant-shoes-found-near-hadrians-wall-spark-mystery-around-the-soldiers-of-ancient-rome/1794955)[\\n\\nWeather News\\n\\nIt’s not just humans – chimpanzees also like to follow trends\\n\\n10 hours ago\\n\\n](https://www.accuweather.com/en/weather-news/its-not-just-humans-chimpanzees-also-like-to-follow-trends-study-shows/1794900)\\n\\n[World](/en/world-weather) [Asia](/en/asia-weather) [China](/en/cn/china-weather) [Shandong](/en/cn/sd/shandong-weather) [Tai'an](/en/cn/taian/106571/weather-forecast/106571)\\n\\n## Weather Near Tai'an:\\n\\n*   [Jinan, Shandong](/en/cn/jinan/106570/weather-forecast/106570)\\n*   [Jining, Shandong](/en/cn/jining/60634/weather-forecast/60634)\\n*   [Liaocheng, Shandong](/en/cn/liaocheng/60636/weather-forecast/60636)\\n\\nCompany\\n\\n[Proven Superior Accuracy](https://corporate.accuweather.com/) [About AccuWeather](https://corporate.accuweather.com/company/about-us/) [Digital Advertising](https://advertising.accuweather.com/for-advertising/digital-advertising/) [Careers](https://corporate.accuweather.com/company/careers/) [Press](https://corporate.accuweather.com/newsroom/press-releases/) [Contact Us](/en/contact)\\n\\nProducts & Services\\n\\n[For Business](https://afb.accuweather.com/) [For Partners](https://partners.accuweather.com/) [For Advertising](https://advertising.accuweather.com/) [AccuWeather APIs](https://developer.accuweather.com/) [AccuWeather Connect](https://afb.accuweather.com/accuweather-connect-and-content-syndication) RealFeel® and RealFeel Shade™ [Personal Weather Stations](https://app.accuweather.com/products/ambient-weather-stations)\\n\\nApps & Downloads\\n\\n[iPhone App](https://go.onelink.me/app/ef253ee1) [Android App](https://go.onelink.me/app/85d14e58) [See all Apps & Downloads](https://7482826.hs-sites.com/premium-plus)\\n\\nSubscription Services\\n\\n[AccuWeather Premium](https://wwwl.accuweather.com/premium_login.php) [AccuWeather Professional](https://wwwl.accuweather.com/pro_login.php)\\n\\nMore\\n\\n[AccuWeather Ready](/en/accuweather-ready) [Business](/en/business) [Health](/en/health-wellness) [Hurricane](/en/hurricane) [Leisure and Recreation](/en/leisure-recreation) [Severe Weather](/en/cn/severe-weather) [Space and Astronomy](/en/space-news) [Sports](/en/sports) [Travel](/en/travel) [Weather News](/en/weather-news)\\n\\n[](https://www.facebook.com/AccuWeather)[](https://twitter.com/BreakingWeather)\\n\\nCompany\\n\\n[Proven Superior Accuracy](https://corporate.accuweather.com/) [About AccuWeather](https://corporate.accuweather.com/company/about-us/) [Digital Advertising](https://advertising.accuweather.com/for-advertising/digital-advertising/) [Careers](https://corporate.accuweather.com/company/careers/) [Press](https://corporate.accuweather.com/newsroom/press-releases/) [Contact Us](/en/contact)\\n\\n[](https://www.facebook.com/AccuWeather)[](https://twitter.com/BreakingWeather)\\n\\nProducts & Services\\n\\n[For Business](https://afb.accuweather.com/) [For Partners](https://partners.accuweather.com/) [For Advertising](https://advertising.accuweather.com/) [AccuWeather APIs](https://developer.accuweather.com/) [AccuWeather Connect](https://afb.accuweather.com/accuweather-connect-and-content-syndication) RealFeel® and RealFeel Shade™ [Personal Weather Stations](https://app.accuweather.com/products/ambient-weather-stations)\\n\\nApps & Downloads\\n\\n[iPhone App](https://go.onelink.me/app/ef253ee1) [Android App](https://go.onelink.me/app/85d14e58) [See all Apps & Downloads](https://7482826.hs-sites.com/premium-plus)\\n\\nSubscription Services\\n\\n[AccuWeather Premium](https://wwwl.accuweather.com/premium_login.php) [AccuWeather Professional](https://wwwl.accuweather.com/pro_login.php)\\n\\nMore\\n\\n[AccuWeather Ready](/en/accuweather-ready) [Business](/en/business) [Health](/en/health-wellness) [Hurricane](/en/hurricane) [Leisure and Recreation](/en/leisure-recreation) [Severe Weather](/en/cn/severe-weather) [Space and Astronomy](/en/space-news) [Sports](/en/sports) [Travel](/en/travel) [Weather News](/en/weather-news)\\n\\n© 2025 AccuWeather, Inc. \\\"AccuWeather\\\" and sun design are registered trademarks of AccuWeather, Inc. All Rights Reserved.\\n\\n[Terms of Use](/en/legal) | [Privacy Policy](/en/privacy) | [Cookie Policy](/en/cookiepolicy) | About Your Privacy Do Not Sell or Share My Personal Information\\n\\n      \\n\\nGet AccuWeather alerts as they happen with our browser notifications.\\n\\nEnable Notifications\\n\\nNo, Thanks\\n\\nNotifications Enabled\\n\\nThanks! We'll keep you informed.\\n\\n![](https://www.accuweather.com/akam/13/pixel_398ee9b5?a=dD03N2Q0YWI5NTBmODU0NjE2NGM0OGVlODk3Njg0ODQ5YmI0YTg5N2IzJmpzPW9mZg==)\\n\\n0\\n\\n1\\n\\n2\\n\\n3\\n\\n4\\n\\n5\\n\\n6\\n\\n7\\n\\n8\\n\\n9\\n\\n10\\n\\n11\\n\\n12\\n\\n13\\n\\n14\\n\\n15\\n\\n16\\n\\n17\\n\\n18\\n\\n19\\n\\n20\\n\\n21\\n\\n22\\n\\n23\\n\\n24\\n\\n25\\n\\n26\\n\\n![](https://ad-delivery.net/px.gif?ch=2)![](https://ad.doubleclick.net/favicon.ico?ad=300x250&ad_box_=1&adnet=1&showad=1&size=250x250)![](https://ad-delivery.net/px.gif?ch=1&e=0.040286891082188636)\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n1. [DONE] Check the weather forecast for Beijing for next week\n2. [DONE] Check flight availability from Shenzhen to Beijing for next week\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_743e524d-ac60-4626-af46-4962150261c4", "type": "function", "function": {"name": "browser_navigate", "arguments": "{\"url\":\"https://www.weather.com/weather/tenday/l/Beijing+China\"}"}}]}, {"role": "tool", "tool_call_id": "call_0_743e524d-ac60-4626-af46-4962150261c4", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \"Navigated to https://www.weather.com/weather/tenday/l/Beijing+China\\nclickable elements(Might be outdated, if an error occurs with the index element, use browser_get_clickable_elements to refresh it): [0]<a></a>\\n[]Type at least three characters to start auto complete. Recently searched locations will be displayed if there is no search query. The first option will be automatically selected. Use up and down arrows to change selection. Use escape to clear.\\n[]Search City or Zip Code\\n[1]<input></input>\\n[2]<button>US\\n°F</button>\\n[3]<button>Menu</button>\\n[4]<a></a>\\n[5]<a></a>\\n[6]<a></a>\\n[7]<a></a>\\n[8]<a></a>\\n[9]<a></a>\\n[10]<a></a>\\n[11]<a></a>\\n[12]<button></button>\\n[]Oops! We are having trouble finding your page.\\n[]Please try again... or Check out some of our favorite areas of the site.\\n[13]<a>Video\\nWatch today's top stories and most popular videos. Looking to watch incredible tornadoes? We've got that too.</a>\\n[14]<img></img>\\n[15]<a>Maps\\nCheck out our interactive maps, which provide you with features like past/future radar and customizable layers.</a>\\n[16]<img></img>\\n[17]<a>Local Weather\\nTrust us to help you plan the best day possible, with the most accurate weather forecast available.</a>\\n[18]<img></img>\\n[19]<a></a>\\n[20]<img></img>\\n[21]<a></a>\\n[22]<img></img>\\n[23]<a></a>\\n[24]<img></img>\\n[25]<a></a>\\n[26]<img></img>\\n[]Feedback\\n[]Mission\\n[]Careers\\n[]News Room\\n[]Advertise With Us\\n[]TV\\n[]Weather Data APIs\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n\n\nFollow this plan. If a step is done, move to the next step."}, {"role": "assistant", "content": "", "tool_calls": [{"id": "call_0_a8e65129-03b9-4bfd-8dc4-c0fc90fb96b8", "type": "function", "function": {"name": "browser_get_markdown", "arguments": "{}"}}]}, {"role": "tool", "tool_call_id": "call_0_a8e65129-03b9-4bfd-8dc4-c0fc90fb96b8", "content": "[\n  {\n    \"type\": \"text\",\n    \"text\": \" 404 Not Found         \\n\\n[Skip to Main Content](#MainContent)[Accessibility Help](https://www.essentialaccessibility.com/the-weather-channel?utm_source=theweatherchannelhomepage&utm_medium=iconlarge&utm_term=eachannelpage&utm_content=header&utm_campaign=theweatherchannel) [The Weather Channel](/)\\n\\nType at least three characters to start auto complete. Recently searched locations will be displayed if there is no search query. The first option will be automatically selected. Use up and down arrows to change selection. Use escape to clear.Search City or Zip Code\\n\\n## recents\\n\\nClear All\\n\\nYou have no recent locations\\n\\nGlobe US°FArrow down\\n\\n*   °F\\n*   °C\\n*   Hybrid\\n\\nImperial - F / mph / miles / inches\\n\\nAmericas Add\\n\\n[Antigua and Barbuda | English](#noloc_/en-AG/weather/today/l/)[Argentina | Español](#noloc_/es-AR/weather/today/l/)[Bahamas | English](#noloc_/en-BS/weather/today/l/)[Barbados | English](#noloc_/en-BB/weather/today/l/)[Belize | English](#noloc_/en-BZ/weather/today/l/)[Bolivia | Español](#noloc_/es-BO/weather/today/l/)[Brazil | Português](#noloc_/pt-BR/weather/today/l/)[Canada | English](#noloc_/en-CA/weather/today/l/)[Canada | Français](#noloc_/fr-CA/weather/today/l/)[Chile | Español](#noloc_/es-CL/weather/today/l/)[Colombia | Español](#noloc_/es-CO/weather/today/l/)[Costa Rica | Español](#noloc_/es-CR/weather/today/l/)[Dominica | English](#noloc_/en-DM/weather/today/l/)[Dominican Republic | Español](#noloc_/es-DO/weather/today/l/)[Ecuador | Español](#noloc_/es-EC/weather/today/l/)[El Salvador | Español](#noloc_/es-SV/weather/today/l/)[Grenada | English](#noloc_/en-GD/weather/today/l/)[Guatemala | Español](#noloc_/es-GT/weather/today/l/)[Guyana | English](#noloc_/en-GY/weather/today/l/)[Haiti | Français](#noloc_/fr-HT/weather/today/l/)[Honduras | Español](#noloc_/es-HN/weather/today/l/)[Jamaica | English](#noloc_/en-JM/weather/today/l/)[Mexico | Español](#noloc_/es-MX/weather/today/l/)[Nicaragua | Español](#noloc_/es-NI/weather/today/l/)[Panama | Español](#noloc_/es-PA/weather/today/l/)[Panama | English](#noloc_/en-PA/weather/today/l/)[Paraguay | Español](#noloc_/es-PY/weather/today/l/)[Peru | Español](#noloc_/es-PE/weather/today/l/)[St. Kitts and Nevis | English](#noloc_/en-KN/weather/today/l/)[St. Lucia | English](#noloc_/en-LC/weather/today/l/)[St. Vincent and the Grenadines | English](#noloc_/en-VC/weather/today/l/)[Suriname | Nederlands](#noloc_/nl-SR/weather/today/l/)[Trinidad and Tobago | English](#noloc_/en-TT/weather/today/l/)[Uruguay | Español](#noloc_/es-UY/weather/today/l/)[United States | English](#noloc_/weather/today/l/)[United States | Español](#noloc_/es-US/weather/today/l/)[Venezuela | Español](#noloc_/es-VE/weather/today/l/)\\n\\nAfrica Add\\n\\n[Algeria | العربية](#noloc_/ar-DZ/weather/today/l/)[Algeria | Français](#noloc_/fr-DZ/weather/today/l/)[Angola | Português](#noloc_/pt-AO/weather/today/l/)[Benin | Français](#noloc_/fr-BJ/weather/today/l/)[Burkina Faso | Français](#noloc_/fr-BF/weather/today/l/)[Burundi | Français](#noloc_/fr-BI/weather/today/l/)[Cameroon | Français](#noloc_/fr-CM/weather/today/l/)[Cameroon | English](#noloc_/en-CM/weather/today/l/)[Cape Verde | Português](#noloc_/pt-CV/weather/today/l/)[Central African Republic | Français](#noloc_/fr-CF/weather/today/l/)[Chad | Français](#noloc_/fr-TD/weather/today/l/)[Chad | العربية](#noloc_/ar-TD/weather/today/l/)[Comoros | Français](#noloc_/fr-KM/weather/today/l/)[Comoros | العربية](#noloc_/ar-KM/weather/today/l/)[Democratic Republic of the Congo | Français](#noloc_/fr-CD/weather/today/l/)[Republic of Congo | Français](#noloc_/fr-CG/weather/today/l/)[Côte d'Ivoire | Français](#noloc_/fr-CI/weather/today/l/)[Djibouti | Français](#noloc_/fr-DJ/weather/today/l/)[Djibouti | العربية](#noloc_/ar-DJ/weather/today/l/)[Egypt | العربية](#noloc_/ar-EG/weather/today/l/)[Equatorial Guinea | Español](#noloc_/es-GQ/weather/today/l/)[Eritrea | العربية](#noloc_/ar-ER/weather/today/l/)[Gabon | Français](#noloc_/fr-GA/weather/today/l/)[Gambia | English](#noloc_/en-GM/weather/today/l/)[Ghana | English](#noloc_/en-GH/weather/today/l/)[Guinea | Français](#noloc_/fr-GN/weather/today/l/)[Guinea-Bissau | Português](#noloc_/pt-GW/weather/today/l/)[Kenya | English](#noloc_/en-KE/weather/today/l/)[Lesotho | English](#noloc_/en-LS/weather/today/l/)[Liberia | English](#noloc_/en-LR/weather/today/l/)[Libya | العربية](#noloc_/ar-LY/weather/today/l/)[Madagascar | Français](#noloc_/fr-MG/weather/today/l/)[Mali | Français](#noloc_/fr-ML/weather/today/l/)[Mauritania | العربية](#noloc_/ar-MR/weather/today/l/)[Mauritius | English](#noloc_/en-MU/weather/today/l/)[Mauritius | Français](#noloc_/fr-MU/weather/today/l/)[Morocco | العربية](#noloc_/ar-MA/weather/today/l/)[Morocco | Français](#noloc_/fr-MA/weather/today/l/)[Mozambique | Português](#noloc_/pt-MZ/weather/today/l/)[Namibia | English](#noloc_/en-NA/weather/today/l/)[Niger | Français](#noloc_/fr-NE/weather/today/l/)[Nigeria | English](#noloc_/en-NG/weather/today/l/)[Rwanda | Français](#noloc_/fr-RW/weather/today/l/)[Rwanda | English](#noloc_/en-RW/weather/today/l/)[Sao Tome and Principe | Português](#noloc_/pt-ST/weather/today/l/)[Senegal | Français](#noloc_/fr-SN/weather/today/l/)[Sierra Leone | English](#noloc_/en-SL/weather/today/l/)[Somalia | العربية](#noloc_/ar-SO/weather/today/l/)[South Africa | English](#noloc_/en-ZA/weather/today/l/)[South Sudan | English](#noloc_/en-SS/weather/today/l/)[Sudan | العربية](#noloc_/ar-SD/weather/today/l/)[Swaziland | English](#noloc_/en-SZ/weather/today/l/)[Tanzania | English](#noloc_/en-TZ/weather/today/l/)[Togo | Français](#noloc_/fr-TG/weather/today/l/)[Tunisia | العربية](#noloc_/ar-TN/weather/today/l/)[Uganda | English](#noloc_/en-UG/weather/today/l/)\\n\\nAsia Pacific Add\\n\\n[Australia | English](#noloc_/en-AU/weather/today/l/)[Bangladesh | বাংলা](#noloc_/bn-BD/weather/today/l/)[Brunei | Bahasa Melayu](#noloc_/ms-BN/weather/today/l/)[China | 中文](#noloc_/zh-CN/weather/today/l/)[Hong Kong SAR | 中文](#noloc_/zh-HK/weather/today/l/)[East Timor | Português](#noloc_/pt-TP/weather/today/l/)[Fiji | English](#noloc_/en-FJ/weather/today/l/)[India (English) | English](#noloc_/en-IN/weather/today/l/)[India (Hindi) | हिन्दी](#noloc_/hi-IN/weather/today/l/)[Indonesia | Bahasa Indonesia](#noloc_/id-ID/weather/today/l/)[Japan | 日本語](#noloc_/ja-JP/weather/today/l/)[Kiribati | English](#noloc_/en-KI/weather/today/l/)[South Korea | 한국어](#noloc_/ko-KR/weather/today/l/)[Kyrgyzstan | Русский](#noloc_/ru-KG/weather/today/l/)[Malaysia | Bahasa Melayu](#noloc_/ms-MY/weather/today/l/)[Marshall Islands | English](#noloc_/en-MH/weather/today/l/)[Micronesia | English](#noloc_/en-FM/weather/today/l/)[New Zealand | English](#noloc_/en-NZ/weather/today/l/)[Palau | English](#noloc_/en-PW/weather/today/l/)[Philippines | English](#noloc_/en-PH/weather/today/l/)[Philippines | Tagalog](#noloc_/tl-PH/weather/today/l/)[Samoa | English](#noloc_/en-AS/weather/today/l/)[Singapore | English](#noloc_/en-SG/weather/today/l/)[Singapore | 中文](#noloc_/zh-SG/weather/today/l/)[Solomon Islands | English](#noloc_/en-SB/weather/today/l/)[Taiwan | 中文](#noloc_/zh-TW/weather/today/l/)[Thailand | ไทย](#noloc_/th-TH/weather/today/l/)[Tonga | English](#noloc_/en-TO/weather/today/l/)[Tuvalu | English](#noloc_/en-TV/weather/today/l/)[Vanuatu | English](#noloc_/en-VU/weather/today/l/)[Vanuatu | Français](#noloc_/fr-VU/weather/today/l/)[Vietnam | Tiếng Việt](#noloc_/vi-VN/weather/today/l/)\\n\\nEurope Add\\n\\n[Andorra | Català](#noloc_/ca-AD/weather/today/l/)[Andorra | Français](#noloc_/fr-AD/weather/today/l/)[Austria | Deutsch](#noloc_/de-AT/weather/today/l/)[Belarus | Русский](#noloc_/ru-BY/weather/today/l/)[Belgium | Dutch](#noloc_/nl-BE/weather/today/l/)[Belgium | Français](#noloc_/fr-BE/weather/today/l/)[Bosnia and Herzegovina | Hrvatski](#noloc_/hr-BA/weather/today/l/)[Croatia | Hrvatski](#noloc_/hr-HR/weather/today/l/)[Cyprus | Ελληνικά](#noloc_/el-CY/weather/today/l/)[Czech Republic | Čeština](#noloc_/cs-CZ/weather/today/l/)[Denmark | Dansk](#noloc_/da-DK/weather/today/l/)[Estonia | Русский](#noloc_/ru-EE/weather/today/l/)[Estonia | Eesti](#noloc_/et-EE/weather/today/l/)[Finland | Suomi](#noloc_/fi-FI/weather/today/l/)[France | Français](#noloc_/fr-FR/weather/today/l/)[Germany | Deutsch](#noloc_/de-DE/weather/today/l/)[Greece | Ελληνικά](#noloc_/el-GR/weather/today/l/)[Hungary | Magyar](#noloc_/hu-HU/weather/today/l/)[Ireland | English](#noloc_/en-IE/weather/today/l/)[Italy | Italiano](#noloc_/it-IT/weather/today/l/)[Liechtenstein | Deutsch](#noloc_/de-LI/weather/today/l/)[Luxembourg | Français](#noloc_/fr-LU/weather/today/l/)[Malta | English](#noloc_/en-MT/weather/today/l/)[Monaco | Français](#noloc_/fr-MC/weather/today/l/)[Netherlands | Nederlands](#noloc_/nl-NL/weather/today/l/)[Norway | Norsk](#noloc_/no-NO/weather/today/l/)[Poland | Polski](#noloc_/pl-PL/weather/today/l/)[Portugal | Português](#noloc_/pt-PT/weather/today/l/)[Romania | Română](#noloc_/ro-RO/weather/today/l/)[Russia | Русский](#noloc_/ru-RU/weather/today/l/)[San Marino | Italiano](#noloc_/it-SM/weather/today/l/)[Slovakia | Slovenčina](#noloc_/sk-SK/weather/today/l/)[Spain | Español](#noloc_/es-ES/weather/today/l/)[Spain | Català](#noloc_/ca-ES/weather/today/l/)[Sweden | Svenska](#noloc_/sv-SE/weather/today/l/)[Switzerland | Deutsch](#noloc_/de-CH/weather/today/l/)[Turkey | Turkçe](#noloc_/tr-TR/weather/today/l/)[Ukraine | Українська](#noloc_/uk-UA/weather/today/l/)[United Kingdom | English](#noloc_/en-GB/weather/today/l/)[State of Vatican City (Holy See) | Italiano](#noloc_/it-VA/weather/today/l/)\\n\\nMiddle East Add\\n\\n[Bahrain | العربية](#noloc_/ar-BH/weather/today/l/)[Iran | فارسى](#noloc_/fa-IR/weather/today/l/)[Iraq | العربية](#noloc_/ar-IQ/weather/today/l/)[Israel | עִבְרִית](#noloc_/he-IL/weather/today/l/)[Jordan | العربية](#noloc_/ar-JO/weather/today/l/)[Kuwait | العربية](#noloc_/ar-KW/weather/today/l/)[Lebanon | العربية](#noloc_/ar-LB/weather/today/l/)[Oman | العربية](#noloc_/ar-OM/weather/today/l/)[Pakistan | اردو](#noloc_/ur-PK/weather/today/l/)[Pakistan | English](#noloc_/en-PK/weather/today/l/)[Qatar | العربية](#noloc_/ar-QA/weather/today/l/)[Saudi Arabia | العربية](#noloc_/ar-SA/weather/today/l/)[Syria | العربية](#noloc_/ar-SY/weather/today/l/)[United Arab Emirates | العربية](#noloc_/ar-AE/weather/today/l/)\\n\\nMenu\\n\\n## Weather Forecasts\\n\\n[My Dashboard](/mydashboard)[Today](#noloc_/weather/today/l/)[Hourly](#noloc_/weather/hourbyhour/l/)[10 Day](#noloc_/weather/tenday/l/)\\n\\n## Radar & Maps\\n\\n[Interactive Radar Map](#noloc_/weather/radar/interactive/l/?animation=true)[US Forecast](/maps/currentusweather)[US Satellite & Radar](/maps/ussatellitemap)[World Satellite](/maps/satellite/caribbeanweathermap)[US Severe Alerts](/maps/severealerts)[US Health](/maps/health/allergies/treepollen)\\n\\n## News & Media\\n\\n[Top Weather Stories](/science/space/video/buy-a-massive-rock-from-planet-mars)[Hurricane Central](/storms/hurricane-central)[Tornado CentralNEW](/storms/tornado-central)[Safety & Prep](/safety/index)[Space & Skywatching](/science/space)[Videos](/science/space/video/buy-a-massive-rock-from-planet-mars)\\n\\n## Products & Account\\n\\n[Alexa SkillExternal Link](https://www.amazon.com/The-Weather-Company-Channel/dp/B07YPYHQ1Q) [Create an Account](/signup)[Sign in](/login)\\n\\n## Lifestyle\\n\\n[Atmosphere Reviews](/atmosphere)[Recipes](/home-garden/recipes)[Home & Garden](/home-garden)[Travel & Outdoors](/travel)[Pets & Animals](/pets)[Health](/health)[Allergy Tracker](#noloc_/forecast/allergy/l/)[Air Quality Index](#noloc_/forecast/air-quality/l/)[Cold & Flu Tracker](#noloc_/forecast/cold-flu/l/)[Skin Health](/health/skin-health)\\n\\n[\\n\\nMy Dashboard\\n\\n](/mydashboard)[\\n\\nToday\\n\\n](#noloc_/weather/today/l/)[\\n\\nHourly\\n\\n](#noloc_/weather/hourbyhour/l/)[\\n\\n10 Day\\n\\n](#noloc_/weather/tenday/l/)[\\n\\nWeekend\\n\\n](#noloc_/weather/weekend/l/)[\\n\\nMonthly\\n\\n](#noloc_/weather/monthly/l/)[\\n\\nRadar\\n\\n](#noloc_/weather/radar/interactive/l/)[\\n\\nVideo Video\\n\\n](/science/space/video/buy-a-massive-rock-from-planet-mars?adRef=nav)\\n\\nMore ForecastsMoreArrow down\\n\\n### Specialty Forecasts\\n\\n[Yesterday's WeatherExternal Link](//www.wunderground.com/history/daily//yesterday) [Allergy Tracker](#noloc_/forecast/allergy/l/)[Cold & Flu](#noloc_/forecast/cold-flu/l/)[Air Quality Forecast](#noloc_/forecast/air-quality/l/)\\n\\n# Oops! We are having trouble finding your page.\\n\\n## Please try again... or Check out some of our favorite areas of the site.\\n\\n[![Video](https://s.w-x.co/404-video.jpg)\\n\\n### Video\\n\\nWatch today's top stories and most popular videos. Looking to watch incredible tornadoes? We've got that too.\\n\\n\\n\\n](/video)\\n\\n[![Maps](https://s.w-x.co/404-maps.jpg)\\n\\n### Maps\\n\\nCheck out our interactive maps, which provide you with features like past/future radar and customizable layers.\\n\\n\\n\\n](/weather/radar/interactive/l/)\\n\\n[![Local Weather](https://s.w-x.co/404-local.jpg)\\n\\n### Local Weather\\n\\nTrust us to help you plan the best day possible, with the most accurate weather forecast available.\\n\\n\\n\\n](/weather/today/l/)\\n\\n[![The Weather Company](/not-found/assets/the-weather-company.resource.9f6305c416ac2bdfe238.svg)](https://www.weathercompany.com)[![The Weather Channel](/not-found/assets/the-weather-channel.resource.6fc09ac144dba97e6b32.svg)](https://weather.com)[![Weather Underground](/not-found/assets/weather-underground.resource.b74e28f7947cb0e3a0e5.svg)](https://www.wunderground.com)[![Storm Radar](https://s.w-x.co/Storm-Radar-App-Icon.png)](https://weather.com/storm-radar)\\n\\n*   [Feedback](//support.weather.com/s/?language=en_US&type=TWC)\\n*   [Mission](/mission)\\n*   [Careers](https://www.weathercompany.com/careers)\\n*   [News Room](https://www.weathercompany.com/newsroom)\\n*   [Advertise With Us](https://www.weathercompany.com/advertising/)\\n*   [TV](https://www.weathergroup.com/brands/the-weather-channel/)\\n*   [Weather Data APIs](https://www.weathercompany.com/weather-data-apis/)\\n\\n*   [Terms of Use](/legal)\\n*   [Privacy Policy](/en-US/twc/privacy-policy)\\n*   [AdChoicesAd Choices](/services/ad-choices)\\n*   [Accessibility Statement](/accessibility-statement)\\n*   [Data Vendors](/data-vendors)\\n\\n[![Georgia Org](/not-found/assets/georgia.resource.50fbe85820c759d46005.svg)](http://www.exploregeorgia.org/weather-and-travel-tips)[![Link to eSSENTIAL Accessibility assistive technology](/not-found/assets/eSSENTIAL.resource.728ebae71f894722ea9c.svg)](https://www.essentialaccessibility.com/the-weather-channel?utm_source=theweatherchannelhomepage&utm_medium=iconlarge&utm_term=eachannelpage&utm_content=header&utm_campaign=theweatherchannel)\\n\\nWe recognize our responsibility to use data and technology for good. We may use or share your data with our data vendors. Take control of your data.\\n\\n*   Cookie Settings\\n*   [Data Rights](/data-rights)\\n\\nThe Weather Channel is the world's most accurate forecaster according to ForecastWatch, [Global and Regional Weather Forecast Accuracy Overview](https://forecastwatch.com/AccuracyOverview2021-2024), 2021-2024, commissioned by The Weather Company.\\n\\nWeather Channel\\n\\n© The Weather Company, LLC 2025\\n\\n0\\n\\n1\\n\\n2\\n\\n3\\n\\n4\\n\\n5\\n\\n6\\n\\n7\\n\\n8\\n\\n9\\n\\n10\\n\\n11\\n\\n12\\n\\n13\\n\\n14\\n\\n15\\n\\n16\\n\\n17\\n\\n18\\n\\n19\\n\\n20\\n\\n21\\n\\n22\\n\\n23\\n\\n24\\n\\n25\\n\\n26\"\n  }\n]"}, {"role": "system", "content": "Current plan status:\n1. [TODO] Unknown step\n2. [TODO] Unknown step\n\nFollow this plan. If a step is done, move to the next step."}], "tools": [{"type": "function", "function": {"name": "web_search", "description": "⚠️ SEARCH QUERY LENGTH LIMIT: 7 WORDS MAXIMUM ⚠️\n\nSearch the web for information. For best results:\n1) Use CONCISE queries (3-5 words ideal)\n2) Include only ESSENTIAL keywords, not full questions\n3) For complex topics, use multiple simple searches instead of one long query\n4) Focus on specific terms that will appear on relevant pages", "parameters": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query - MUST BE CONCISE (maximum 7 words)"}, "count": {"type": "number", "description": "Number of results to return (default: 10)"}}, "required": ["query"]}}}, {"type": "function", "function": {"name": "browser_get_markdown", "description": "[browser] Get the markdown content of the current page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_screenshot", "description": "[browser] Take a screenshot of the current page or a specific area. this tool SHOULD NOT be called unless the user requests an explicit call.", "parameters": {"type": "object", "properties": {"area": {"type": "array", "items": {"type": "number"}, "minItems": 4, "maxItems": 4, "description": "Optional area to capture as [x1, y1, x2, y2]. If not provided, captures the entire viewport."}}}}}, {"type": "function", "function": {"name": "browser_navigate", "description": "[browser] Navigate to a URL", "parameters": {"type": "object", "properties": {"url": {"type": "string"}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_click", "description": "[browser] Click an element on the page, before using the tool, use `browser_get_clickable_elements` to get the index of the element, but not call `browser_get_clickable_elements` multiple times", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to click"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_form_input_fill", "description": "[browser] Fill out an input field, before using the tool, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"selector": {"type": "string", "description": "CSS selector for input field"}, "index": {"type": "number", "description": "Index of the element to fill"}, "value": {"type": "string", "description": "Value to fill"}}, "required": ["value"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_select", "description": "[browser] Select an element on the page with index, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to select"}, "selector": {"type": "string", "description": "CSS selector for element to select"}, "value": {"type": "string", "description": "Value to select"}}, "required": ["value"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_hover", "description": "[browser] Hover an element on the page, Either 'index' or 'selector' must be provided", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Index of the element to hover"}, "selector": {"type": "string", "description": "CSS selector for element to hover"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_evaluate", "description": "[browser] Execute JavaScript in the browser console", "parameters": {"type": "object", "properties": {"script": {"type": "string", "description": "JavaScript code to execute"}}, "required": ["script"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_get_clickable_elements", "description": "[browser] Get the clickable or hoverable or selectable elements on the current page, don't call this tool multiple times", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_read_links", "description": "[browser] Get all links on the current page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_scroll", "description": "[browser] Scroll the page", "parameters": {"type": "object", "properties": {"amount": {"type": "number", "description": "Pixels to scroll (positive for down, negative for up), if the amount is not provided, scroll to the bottom of the page"}}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_go_back", "description": "[browser] Go back to the previous page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_go_forward", "description": "[browser] Go forward to the next page", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_tab_list", "description": "[browser] Get the list of tabs", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_new_tab", "description": "[browser] Open a new tab", "parameters": {"type": "object", "properties": {"url": {"type": "string", "description": "URL to open in the new tab"}}, "required": ["url"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_close_tab", "description": "[browser] Close the current tab", "parameters": {"type": "object", "properties": {}}}}, {"type": "function", "function": {"name": "browser_switch_tab", "description": "[browser] Switch to a specific tab", "parameters": {"type": "object", "properties": {"index": {"type": "number", "description": "Tab index to switch to"}}, "required": ["index"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "browser_press_key", "description": "[browser] Press a key on the keyboard", "parameters": {"type": "object", "properties": {"key": {"type": "string", "enum": ["Enter", "Tab", "Escape", "Backspace", "Delete", "Insert", "F1", "F2", "F3", "F4", "F5", "F6", "F7", "F8", "F9", "F10", "F11", "F12", "ArrowLeft", "ArrowRight", "ArrowUp", "ArrowDown", "PageUp", "PageDown", "Home", "End", "ShiftLeft", "ShiftRight", "ControlLeft", "ControlRight", "AltLeft", "AltRight", "MetaLeft", "MetaRight", "CapsLock", "PrintScreen", "ScrollLock", "Pause", "ContextMenu"], "description": "Name of the key to press or a character to generate, such as <PERSON><PERSON>, Tab, Escape, Backspace, Delete, Insert, F1, F2, F3, F4, F5, F6, F7, F8, F9, F10, F11, F12, ArrowLeft, ArrowRight, ArrowUp, ArrowDown, PageUp, PageDown, Home, End, ShiftLeft, ShiftRight, ControlLeft, ControlRight, AltLeft, AltRight, MetaLeft, MetaRight, CapsLock, PrintScreen, ScrollLock, Pause, ContextMenu"}}, "required": ["key"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "read_file", "description": "[filesystem] Read the complete contents of a file from the file system. Handles various text encodings and provides detailed error messages if the file cannot be read. Use this tool when you need to examine the contents of a single file. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "read_multiple_files", "description": "[filesystem] Read the contents of multiple files simultaneously. This is more efficient than reading files one by one when you need to analyze or compare multiple files. Each file's content is returned with its path as a reference. Failed reads for individual files won't stop the entire operation. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"paths": {"type": "array", "items": {"type": "string"}}}, "required": ["paths"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "write_file", "description": "[filesystem] Create a new file or completely overwrite an existing file with new content. Use with caution as it will overwrite existing files without warning. Handles text content with proper encoding. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "content": {"type": "string"}}, "required": ["path", "content"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "edit_file", "description": "[filesystem] Make line-based edits to a text file. Each edit replaces exact line sequences with new content. Returns a git-style diff showing the changes made. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "edits": {"type": "array", "items": {"type": "object", "properties": {"oldText": {"type": "string", "description": "Text to search for - must match exactly"}, "newText": {"type": "string", "description": "Text to replace with"}}, "required": ["oldText", "newText"], "additionalProperties": false}}, "dryRun": {"type": "boolean", "default": false, "description": "Preview changes using git-style diff format"}}, "required": ["path", "edits"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "create_directory", "description": "[filesystem] Create a new directory or ensure a directory exists. Can create multiple nested directories in one operation. If the directory already exists, this operation will succeed silently. Perfect for setting up directory structures for projects or ensuring required paths exist. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "list_directory", "description": "[filesystem] Get a detailed listing of all files and directories in a specified path. Results clearly distinguish between files and directories with [FILE] and [DIR] prefixes. This tool is essential for understanding directory structure and finding specific files within a directory. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "directory_tree", "description": "[filesystem] Get a recursive tree view of files and directories as a JSON structure. Each entry includes 'name', 'type' (file/directory), and 'children' for directories. Files have no children array, while directories always have a children array (which may be empty). The output is formatted with 2-space indentation for readability. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "move_file", "description": "[filesystem] Move or rename files and directories. Can move files between directories and rename them in a single operation. If the destination exists, the operation will fail. Works across different directories and can be used for simple renaming within the same directory. Both source and destination must be within allowed directories.", "parameters": {"type": "object", "properties": {"source": {"type": "string"}, "destination": {"type": "string"}}, "required": ["source", "destination"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "search_files", "description": "[filesystem] Recursively search for files and directories matching a pattern. Searches through all subdirectories from the starting path. The search is case-insensitive and matches partial names. Returns full paths to all matching items. Great for finding files when you don't know their exact location. Only searches within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}, "pattern": {"type": "string"}, "excludePatterns": {"type": "array", "items": {"type": "string"}, "default": []}}, "required": ["path", "pattern"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "get_file_info", "description": "[filesystem] Retrieve detailed metadata about a file or directory. Returns comprehensive information including size, creation time, last modified time, permissions, and type. This tool is perfect for understanding file characteristics without reading the actual content. Only works within allowed directories.", "parameters": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "list_allowed_directories", "description": "[filesystem] Returns the list of directories that this server is allowed to access. Use this to understand which directories are available before trying to access files.", "parameters": {"type": "object", "properties": {}, "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "run_command", "description": "[commands] Run a command on this darwin machine", "parameters": {"type": "object", "properties": {"command": {"type": "string", "description": "Command with args"}, "cwd": {"type": "string", "description": "Current working directory, leave empty in most cases"}}, "required": ["command"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "run_script", "description": "[commands] Run a script on this darwin machine", "parameters": {"type": "object", "properties": {"interpreter": {"type": "string", "description": "Command with arguments. <PERSON><PERSON><PERSON> will be piped to stdin. Examples: bash, fish, zsh, python, or: bash --norc"}, "script": {"type": "string", "description": "<PERSON><PERSON><PERSON> to run"}, "cwd": {"type": "string", "description": "Current working directory, leave empty in most cases"}}, "required": ["script"], "additionalProperties": false, "$schema": "http://json-schema.org/draft-07/schema#"}}}, {"type": "function", "function": {"name": "final_answer", "description": "Generate a focused report or answer after completing research or information gathering", "parameters": {"type": "object", "properties": {"isDeepResearch": {"type": "boolean", "description": "Whether to generate a structured report (true) or simple answer (false)"}, "title": {"type": "string", "description": "Title for the report or answer"}, "format": {"type": "string", "enum": ["detailed", "concise"], "description": "Report format: detailed or concise"}}}}}], "temperature": 0.7, "stream": true, "max_tokens": 8192, "provider": "deepseek", "thinking": {"type": "enabled"}}, "baseURL": "https://api.deepseek.com/v1"}