#!/bin/bash

# Agent TARS with Chart Server Launcher
# Usage: ./start-agent-tars-with-charts.sh

echo "🚀 Launching Agent TARS with Chart Visualization..."
echo ""

# Navigate to the setup directory
cd "agent-tars-setup"

# Check if setup is complete
if [ ! -f ".env" ]; then
    echo "⚠️  First-time setup required!"
    echo "Running setup..."
    source ./setup-env.sh
    echo ""
    echo "📝 Please edit the .env file with your API keys:"
    echo "   nano agent-tars-setup/.env"
    echo ""
    echo "Then run this script again to start Agent TARS."
    exit 0
fi

# Start Agent TARS with chart capabilities
./start-agent-tars-with-chart.sh
